plugins {
    `kotlin-dsl`
}

group = "com.yu.questicle.buildlogic"

// Configure the build-logic plugins to target JDK 21
// This matches the JDK used to build the project, and is not related to what is running on device.
java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

kotlin {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21
    }
}

dependencies {
    // ============================================================================
    // Build Logic 版本管理 - 2025年6月最新稳定版本
    // ============================================================================
    // 注意：build-logic 无法访问版本目录，因此使用硬编码版本
    // 这些版本必须与 gradle/libs.versions.toml 中的版本保持一致
    // 参考：BuildLogicVersions.kt 中的版本常量
    compileOnly("com.android.tools.build:gradle:8.11.1")
    compileOnly("com.android.tools:common:31.11.0")
    compileOnly("org.jetbrains.kotlin:kotlin-gradle-plugin:2.1.21")
    compileOnly("org.jetbrains.kotlin:compose-compiler-gradle-plugin:2.1.21")
    compileOnly("com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:2.1.21-2.0.1")
    compileOnly("androidx.room:room-gradle-plugin:2.7.0")
    compileOnly("com.google.dagger:hilt-android-gradle-plugin:2.56.2")

    // Firebase plugins (if needed)
    compileOnly("com.google.firebase:firebase-crashlytics-gradle:3.0.2")
    compileOnly("com.google.firebase:perf-plugin:1.4.2")

    // Testing and other tools
    compileOnly("de.mannodermaus.gradle.plugins:android-junit5:1.13.0.0")
    implementation("com.google.truth:truth:1.4.4")
}

tasks {
    validatePlugins {
        enableStricterValidation = true
        failOnWarning = true
    }
}


