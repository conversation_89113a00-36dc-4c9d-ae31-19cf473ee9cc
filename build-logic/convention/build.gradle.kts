plugins {
    `kotlin-dsl`
}

group = "com.yu.questicle.buildlogic"

// Configure the build-logic plugins to target JDK 21
// This matches the JDK used to build the project, and is not related to what is running on device.
java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

kotlin {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21
        // 编译器优化选项
        freeCompilerArgs.addAll(
            "-Xjvm-default=all"
        )
    }
}

dependencies {
    // 使用统一的版本管理 - 2025年6月最新稳定版本
    compileOnly("com.android.tools.build:gradle:8.11.0")
    compileOnly("com.android.tools:common:31.10.1")
    compileOnly("org.jetbrains.kotlin:compose-compiler-gradle-plugin:2.1.21")
    compileOnly("com.google.firebase:firebase-crashlytics-gradle:3.0.2")
    compileOnly("com.google.firebase:perf-plugin:1.4.2")
    compileOnly("org.jetbrains.kotlin:kotlin-gradle-plugin:2.1.21")
    compileOnly("com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:2.1.21-2.0.1")
    compileOnly("androidx.room:room-gradle-plugin:2.7.0")
    compileOnly("com.google.dagger:hilt-android-gradle-plugin:2.56.2")
    compileOnly("de.mannodermaus.gradle.plugins:android-junit5:1.13.0.0")
    implementation("com.google.truth:truth:1.4.4")
}

gradlePlugin {
    plugins {
        register("androidApplicationCompose") {
            id = "questicle.android.application.compose"
            implementationClass = "AndroidApplicationComposeConventionPlugin"
        }
        register("androidApplication") {
            id = "questicle.android.application"
            implementationClass = "AndroidApplicationConventionPlugin"
        }
        register("androidApplicationFlavors") {
            id = "questicle.android.application.flavors"
            implementationClass = "AndroidApplicationFlavorsConventionPlugin"
        }
        register("androidApplicationFirebase") {
            id = "questicle.android.application.firebase"
            implementationClass = "AndroidApplicationFirebaseConventionPlugin"
        }
        register("androidFeature") {
            id = "questicle.android.feature"
            implementationClass = "AndroidFeatureConventionPlugin"
        }
        register("androidLibraryCompose") {
            id = "questicle.android.library.compose"
            implementationClass = "AndroidLibraryComposeConventionPlugin"
        }
        register("androidLibrary") {
            id = "questicle.android.library"
            implementationClass = "AndroidLibraryConventionPlugin"
        }
        register("androidLint") {
            id = "questicle.android.lint"
            implementationClass = "AndroidLintConventionPlugin"
        }
        register("androidRoom") {
            id = "questicle.android.room"
            implementationClass = "AndroidRoomConventionPlugin"
        }
        register("androidTest") {
            id = "questicle.android.test"
            implementationClass = "AndroidTestConventionPlugin"
        }
        register("hilt") {
            id = "questicle.hilt"
            implementationClass = "HiltConventionPlugin"
        }
        register("jvmLibrary") {
            id = "questicle.jvm.library"
            implementationClass = "JvmLibraryConventionPlugin"
        }
    }
}

tasks {
    validatePlugins {
        enableStricterValidation = true
        failOnWarning = true
    }
}


