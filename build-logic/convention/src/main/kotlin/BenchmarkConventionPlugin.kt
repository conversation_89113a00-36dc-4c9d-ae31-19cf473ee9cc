import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * JMH基准测试配置插件
 * 
 * 功能:
 * - 配置JMH基准测试框架
 * - 设置性能测试环境
 * - 配置基准测试依赖
 */
class BenchmarkConventionPlugin : Plugin<Project> {
    
    override fun apply(target: Project) {
        with(target) {
            configureJMH()
        }
    }
}

/**
 * 配置JMH基准测试
 */
private fun Project.configureJMH() {
    // 只有在JMH插件可用时才应用
    try {
        pluginManager.apply("me.champeau.jmh")
        
        dependencies {
            // JMH核心依赖
            "jmhImplementation"("org.openjdk.jmh:jmh-core:1.37")
            "jmhImplementation"("org.openjdk.jmh:jmh-generator-annprocess:1.37")
            
            // 协程支持
            "jmhImplementation"("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
            
            // JSON处理（用于结果分析）
            "jmhImplementation"("com.fasterxml.jackson.core:jackson-databind:2.15.2")
            "jmhImplementation"("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.2")
        }
    } catch (e: Exception) {
        // JMH插件不可用时，只添加基本依赖
        dependencies {
            "testImplementation"("org.openjdk.jmh:jmh-core:1.37")
            "testImplementation"("org.openjdk.jmh:jmh-generator-annprocess:1.37")
        }
    }
}