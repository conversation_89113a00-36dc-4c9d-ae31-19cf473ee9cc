import com.android.build.api.dsl.ApplicationExtension
import com.yu.questicle.buildlogic.configureKotlinAndroid
import com.yu.questicle.buildlogic.configureFlavors
import com.yu.questicle.buildlogic.configureDependencyResolution
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

class AndroidApplicationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.application")
                apply("org.jetbrains.kotlin.android")
                apply("questicle.android.lint")
            }

            extensions.configure<ApplicationExtension> {
                configureKotlinAndroid(this)
                defaultConfig.targetSdk = 35
                configureFlavors(this)
            }
            
            // 应用统一的依赖解析策略
            configureDependencyResolution()
        }
    }
}
