import com.android.build.api.dsl.LibraryExtension
import com.yu.questicle.buildlogic.configureKotlinAndroid
import com.yu.questicle.buildlogic.configureFlavors
import com.yu.questicle.buildlogic.configureDependencyResolution
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidLibraryConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.library")
                apply("org.jetbrains.kotlin.android")
                apply("questicle.android.lint")
            }

            extensions.configure<LibraryExtension> {
                configureKotlinAndroid(this)
                defaultConfig.targetSdk = 35
                configureFlavors(this)
            }

            dependencies {
                add("androidTestImplementation", project(":core:testing"))
            }
            
            // 应用统一的依赖解析策略
            configureDependencyResolution()
        }
    }
}
