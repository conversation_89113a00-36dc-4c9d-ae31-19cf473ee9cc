package com.yu.questicle.buildlogic

import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * 统一依赖解析策略配置 - 统一配置版本
 * 避免在各个模块中重复配置 resolutionStrategy
 */
internal fun Project.configureDependencyResolution() {
    val commonDeps = UnifiedBuildConfig.getCommonDependencies()
    val testDeps = UnifiedBuildConfig.getTestDependencies()
    
    dependencies {
        // 统一解决 espresso 版本冲突 - 使用统一版本管理
        val composeBom = commonDeps["compose-bom"]!!
        add("androidTestImplementation", platform("androidx.compose:compose-bom:$composeBom"))
        
        // 强制使用统一版本的测试依赖
        val espressoCore = testDeps["androidx-test-espresso-core"]!!
        add("androidTestImplementation", espressoCore)
        add("androidTestImplementation", "androidx.test.espresso:espresso-idling-resource:${BuildLogicVersions.ESPRESSO}")
    }
    
    // 全局依赖解析策略 - 使用正确的Gradle API
    configurations.all {
        val espressoCore = testDeps["androidx-test-espresso-core"]!!
        resolutionStrategy.force(
            espressoCore,
            "androidx.test.espresso:espresso-idling-resource:${BuildLogicVersions.ESPRESSO}"
        )
    }
}

/**
 * 配置测试依赖 - 统一测试依赖管理
 */
internal fun Project.configureTestDependencies() {
    val testDeps = UnifiedBuildConfig.getTestDependencies()
    
    dependencies {
        // 标准化测试依赖 - 使用统一版本管理
        add("testImplementation", project(":core:testing"))
        
        val junitBom = testDeps["junit-bom"]!!
        val junitApi = testDeps["junit-jupiter-api"]!!
        val junitEngine = testDeps["junit-jupiter-engine"]!!
        val mockk = testDeps["mockk"]!!
        val turbine = testDeps["turbine"]!!
        val coroutinesTest = testDeps["kotlinx-coroutines-test"]!!
        
        add("testImplementation", platform(junitBom))
        add("testImplementation", junitApi)
        add("testImplementation", junitEngine)
        add("testImplementation", mockk)
        add("testImplementation", turbine)
        add("testImplementation", coroutinesTest)
        
        // Android 测试依赖
        val extJunit = testDeps["androidx-test-ext-junit"]!!
        val espressoCore = testDeps["androidx-test-espresso-core"]!!
        add("androidTestImplementation", extJunit)
        add("androidTestImplementation", espressoCore)
        
        // Compose 测试依赖
        val composeTestAndroid = testDeps["compose-ui-test-android"]!!
        val composeTestManifest = testDeps["compose-ui-test-manifest"]!!
        add("androidTestImplementation", composeTestAndroid)
        add("debugImplementation", composeTestManifest)
    }
} 