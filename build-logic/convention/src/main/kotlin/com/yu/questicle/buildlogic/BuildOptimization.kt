package com.yu.questicle.buildlogic

/**
 * 构建优化配置系统
 * 根据系统资源动态调整构建参数
 */
object BuildOptimization {
    
    fun getOptimalConfiguration(): BuildConfiguration {
        val systemInfo = SystemInfo.current()
        return BuildConfiguration(
            parallelForks = calculateOptimalForks(systemInfo),
            memorySettings = calculateMemorySettings(systemInfo),
            cacheSettings = getCacheConfiguration(systemInfo),
            compilerArgs = getOptimizedCompilerArgs(systemInfo)
        )
    }
    
    private fun calculateOptimalForks(systemInfo: SystemInfo): Int {
        return when {
            systemInfo.isCI -> minOf(systemInfo.availableProcessors, 4) // CI环境限制
            systemInfo.isHighPerformanceSystem -> systemInfo.availableProcessors - 2
            systemInfo.availableProcessors >= 8 -> 6
            systemInfo.availableProcessors >= 4 -> 4
            else -> 2
        }.coerceAtLeast(1)
    }
    
    private fun calculateMemorySettings(systemInfo: SystemInfo): MemorySettings {
        val maxMemoryMB = systemInfo.maxMemory / (1024 * 1024)
        val heapSize = when {
            systemInfo.isCI -> "4g" // CI环境保守配置
            maxMemoryMB >= 16384 -> "8g"
            maxMemoryMB >= 8192 -> "6g"
            maxMemoryMB >= 4096 -> "4g"
            else -> "2g"
        }
        
        return MemorySettings(
            heapSize = heapSize,
            metaspaceSize = if (systemInfo.isHighPerformanceSystem) "1g" else "512m",
            codeCache = if (systemInfo.isHighPerformanceSystem) "512m" else "256m",
            directMemory = if (systemInfo.isHighPerformanceSystem) "1g" else "512m"
        )
    }
    
    private fun getCacheConfiguration(systemInfo: SystemInfo): CacheConfiguration {
        return CacheConfiguration(
            buildCache = true,
            configurationCache = !systemInfo.isLowResourceSystem,
            parallelGC = systemInfo.isHighPerformanceSystem,
            daemonEnabled = !systemInfo.isCI
        )
    }
    
    private fun getOptimizedCompilerArgs(systemInfo: SystemInfo): List<String> {
        val baseArgs = listOf(
            "-Xopt-in=kotlin.RequiresOptIn",
            "-Xjvm-default=all"
        )
        
        return if (systemInfo.isHighPerformanceSystem) {
            baseArgs + listOf(
                "-Xuse-ir",
                "-Xbackend-threads=${systemInfo.availableProcessors}"
            )
        } else {
            baseArgs
        }
    }
}

data class BuildConfiguration(
    val parallelForks: Int,
    val memorySettings: MemorySettings,
    val cacheSettings: CacheConfiguration,
    val compilerArgs: List<String>
)

data class MemorySettings(
    val heapSize: String,
    val metaspaceSize: String,
    val codeCache: String,
    val directMemory: String
) {
    fun toJvmArgs(): List<String> {
        return listOf(
            "-Xmx$heapSize",
            "-XX:MetaspaceSize=$metaspaceSize",
            "-XX:ReservedCodeCacheSize=$codeCache",
            "-XX:MaxDirectMemorySize=$directMemory",
            "-XX:+UseG1GC",
            "-XX:+UseStringDeduplication"
        )
    }
}

data class CacheConfiguration(
    val buildCache: Boolean,
    val configurationCache: Boolean,
    val parallelGC: Boolean,
    val daemonEnabled: Boolean
)
