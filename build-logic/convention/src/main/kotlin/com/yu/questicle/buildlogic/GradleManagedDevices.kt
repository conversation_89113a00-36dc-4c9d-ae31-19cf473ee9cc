package com.yu.questicle.buildlogic

import com.android.build.api.dsl.CommonExtension
import com.android.build.api.dsl.ManagedVirtualDevice
import org.gradle.kotlin.dsl.invoke

/**
 * Configure project for Gradle managed devices
 */
internal fun configureGradleManagedDevices(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    val pixel6 = DeviceConfig("Pixel 6", 31, "aosp-atd")
    val pixel6Api33 = DeviceConfig("Pixel 6", 33, "aosp")
    val pixel6Api34 = DeviceConfig("Pixel 6", 34, "aosp")

    val allDevices = listOf(pixel6, pixel6Api33, pixel6Api34)
    val ciDevices = listOf(pixel6, pixel6Api33)

    commonExtension.testOptions {
        managedDevices {
                devices {
                allDevices.forEach { deviceConfig ->
                    maybeCreate(deviceConfig.taskName, ManagedVirtualDevice::class.java).apply {
                        device = deviceConfig.device
                        apiLevel = deviceConfig.apiLevel
                        systemImageSource = deviceConfig.systemImageSource
                    }
                }
            }
            groups {
                maybeCreate("ci").apply {
                    ciDevices.forEach { deviceConfig ->
                        targetDevices.add(devices.getByName(deviceConfig.taskName))
                    }
                }
            }
        }
    }
}

private data class DeviceConfig(
    val device: String,
    val apiLevel: Int,
    val systemImageSource: String,
) {
    val taskName = buildString {
        append(device.lowercase().replace(" ", ""))
        append("api")
        append(apiLevel.toString())
        append(systemImageSource.replace("-", ""))
    }
}
