package com.yu.questicle.buildlogic

/**
 * Build Logic 版本管理 - 2025年6月最新版本
 * 
 * 由于build-logic模块无法直接访问版本目录，
 * 这里集中管理所有构建逻辑相关的版本号
 * 
 * 注意：这些版本必须与 gradle/libs.versions.toml 中的版本保持一致
 */
object BuildLogicVersions {
    
    // ============================================================================
    // 核心构建工具版本 - 2025年6月最新稳定版本
    // ============================================================================
    const val AGP = "8.11.0"
    const val KOTLIN = "2.1.21"
    const val KSP = "2.1.21-2.0.1"
    const val ANDROID_TOOLS_COMMON = "31.10.1"
    const val COMPOSE_BOM = "2025.06.00"
    
    // ============================================================================
    // 依赖注入和数据层
    // ============================================================================
    const val HILT = "2.56.2"
    const val ROOM = "2.7.0"
    
    // ============================================================================
    // Firebase 工具
    // ============================================================================
    const val FIREBASE_CRASHLYTICS_GRADLE = "3.0.2"
    const val FIREBASE_PERF_PLUGIN = "1.4.2"
    
    // ============================================================================
    // 测试工具
    // ============================================================================
    const val ANDROID_JUNIT5 = "1.13.0.0"
    const val TRUTH = "1.4.4"
    const val JUNIT5 = "5.11.3"
    const val MOCKK = "1.13.13"
    const val TURBINE = "1.1.0"
    const val KOTLINX_COROUTINES = "1.9.0"
    const val ESPRESSO = "3.6.1"
    
    // ============================================================================
    // 版本一致性验证
    // ============================================================================
    
    /**
     * 验证关键版本是否一致
     */
    fun validateCriticalVersions() {
        // 验证关键版本组合
        require(KOTLIN.startsWith("2.1")) { 
            "Kotlin version must be 2.1.x for compatibility with current setup" 
        }
        require(AGP.startsWith("8.1")) { 
            "AGP version must be 8.1x.x for Gradle 8.14.3 compatibility" 
        }
        require(KSP.startsWith(KOTLIN)) { 
            "KSP version must match Kotlin version: $KSP vs $KOTLIN" 
        }
    }
    
    /**
     * 获取所有版本信息
     */
    fun getAllVersions(): Map<String, String> = mapOf(
        "AGP" to AGP,
        "Kotlin" to KOTLIN,
        "KSP" to KSP,
        "Compose BOM" to COMPOSE_BOM,
        "Hilt" to HILT,
        "Room" to ROOM,
        "JUnit5" to JUNIT5,
        "Mockk" to MOCKK,
        "Turbine" to TURBINE,
        "Coroutines" to KOTLINX_COROUTINES,
        "Espresso" to ESPRESSO
    )
    
    /**
     * 打印版本信息
     * 用于构建时的版本报告
     */
    fun printVersionInfo() {
        println("=".repeat(80))
        println("Build Logic Versions - 2025年6月")
        println("=".repeat(80))
        getAllVersions().forEach { (name, version) ->
            println("$name: $version")
        }
        println("=".repeat(80))
    }
}
