package com.yu.questicle.buildlogic

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/**
 * Configure base Kotlin with Android options - 统一配置版本
 */
internal fun Project.configureKotlinAndroid(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    // 验证构建配置
    UnifiedBuildConfig.validateBuildConfiguration(this)
    
    commonExtension.apply {
        // 使用统一的SDK配置
        compileSdk = UnifiedBuildConfig.COMPILE_SDK

        defaultConfig {
            minSdk = UnifiedBuildConfig.MIN_SDK
        }

        compileOptions {
            sourceCompatibility = JavaVersion.VERSION_21
            targetCompatibility = JavaVersion.VERSION_21
            isCoreLibraryDesugaringEnabled = true
        }

        testOptions {
            unitTests {
                isIncludeAndroidResources = true
                all {
                    it.useJUnitPlatform()
                }
            }
        }

        // 编译优化配置 - 使用统一的特性开关
        buildFeatures {
            buildConfig = false
            resValues = false
            aidl = false
            renderScript = false
            shaders = false
        }

        // 打包优化 - 使用统一的排除列表
        packaging {
            resources {
                excludes += UnifiedBuildConfig.PACKAGING_EXCLUDES
            }
        }
    }

    configureKotlin()

    dependencies {
        val commonDeps = UnifiedBuildConfig.getCommonDependencies()
        add("implementation", platform("androidx.compose:compose-bom:${commonDeps["compose-bom"]}"))
        add("coreLibraryDesugaring", "com.android.tools:desugar_jdk_libs:${commonDeps["desugar-jdk-libs"]}")
    }
}

/**
 * Configure base Kotlin options for Kotlin 2.1.21 with modern compiler options
 */
private fun Project.configureKotlin() {
    // Use withType to workaround https://youtrack.jetbrains.com/issue/KT-55947
    tasks.withType<KotlinCompile>().configureEach {
        // 使用现代化的 compilerOptions API 和统一配置
        compilerOptions {
            jvmTarget.set(JvmTarget.fromTarget(UnifiedBuildConfig.JVM_TARGET))

            // 开发环境优化 - 禁用警告作为错误以加快编译
            allWarningsAsErrors.set(false)

            // 使用统一的编译器参数
            freeCompilerArgs.addAll(UnifiedBuildConfig.KOTLIN_COMPILER_ARGS)
        }
    }
}
