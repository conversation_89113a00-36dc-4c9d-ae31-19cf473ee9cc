package com.yu.questicle.buildlogic

import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * 依赖管理插件
 * 自动配置依赖解析策略、仓库优化和依赖锁定
 */
class DependencyManagementPlugin : Plugin<Project> {
    
    override fun apply(target: Project) {
        // 配置依赖解析策略
        DependencyManagement.configureDependencyResolution(target)
        
        // 配置仓库优化
        DependencyManagement.configureRepositories(target)
        
        // 创建依赖锁定任务
        DependencyManagement.createDependencyLockingTasks(target)
        
        // 分析依赖冲突
        DependencyManagement.analyzeDependencyConflicts(target)
        
        // 生成依赖报告
        DependencyManagement.createDependencyReports(target)
        
        // 记录配置信息
        logDependencyConfiguration(target)
        
        // 配置依赖验证
        configureDependencyVerification(target)
    }
    
    private fun logDependencyConfiguration(project: Project) {
        project.logger.lifecycle("""
            🔧 依赖管理配置已应用:
            ├── 依赖锁定: 启用
            ├── 缓存策略: 动态版本10分钟，变更模块4小时
            ├── 仓库优化: Google, Maven Central, Gradle Plugin Portal
            ├── 冲突解决: 强制Kotlin版本统一
            └── 依赖替换: 自动替换过时依赖
        """.trimIndent())
    }
    
    private fun configureDependencyVerification(project: Project) {
        // 创建依赖验证任务
        project.tasks.register("verifyDependencies") {
            group = "dependency management"
            description = "验证项目依赖的完整性和安全性"
            
            doLast {
                project.logger.lifecycle("🔍 验证项目依赖...")
                
                val problematicDependencies = mutableListOf<String>()
                
                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        config.resolvedConfiguration.resolvedArtifacts.forEach { artifact ->
                            val moduleId = artifact.moduleVersion.id
                            
                            // 检查已知的问题依赖
                            when {
                                moduleId.group.startsWith("org.jetbrains.kotlin") && 
                                moduleId.version != BuildLogicVersions.KOTLIN -> {
                                    problematicDependencies.add(
                                        "Kotlin版本不一致: ${moduleId.group}:${moduleId.name}:${moduleId.version} (期望: ${BuildLogicVersions.KOTLIN})"
                                    )
                                }
                                
                                moduleId.name.contains("kotlin-stdlib-jre") -> {
                                    problematicDependencies.add(
                                        "过时的Kotlin标准库: ${moduleId.group}:${moduleId.name}:${moduleId.version}"
                                    )
                                }
                                
                                moduleId.group == "com.android.support" -> {
                                    problematicDependencies.add(
                                        "使用了过时的Support库: ${moduleId.group}:${moduleId.name}:${moduleId.version} (应使用AndroidX)"
                                    )
                                }
                            }
                        }
                    } catch (e: Exception) {
                        project.logger.debug("无法验证配置 ${config.name}: ${e.message}")
                    }
                }
                
                if (problematicDependencies.isNotEmpty()) {
                    project.logger.warn("⚠️ 发现 ${problematicDependencies.size} 个依赖问题:")
                    problematicDependencies.forEach { problem ->
                        project.logger.warn("  - $problem")
                    }
                } else {
                    project.logger.lifecycle("✅ 所有依赖验证通过")
                }
            }
        }
        
        // 创建依赖清理任务
        project.tasks.register("cleanupDependencies") {
            group = "dependency management"
            description = "清理过时和冗余的依赖"
            
            doLast {
                project.logger.lifecycle("🧹 清理依赖缓存...")
                
                // 清理Gradle缓存中的过时依赖
                val gradleUserHome = project.gradle.gradleUserHomeDir
                val cacheDir = gradleUserHome.resolve("caches/modules-2/files-2.1")
                
                if (cacheDir.exists()) {
                    project.logger.lifecycle("📁 Gradle缓存目录: ${cacheDir.absolutePath}")
                    
                    // 这里可以添加具体的清理逻辑
                    // 例如删除特定版本的依赖或过时的快照
                    
                    project.logger.lifecycle("✅ 依赖清理完成")
                } else {
                    project.logger.lifecycle("ℹ️ 未找到Gradle缓存目录")
                }
            }
        }
        
        // 创建依赖安全扫描任务
        project.tasks.register("securityScanDependencies") {
            group = "dependency management"
            description = "扫描依赖中的安全漏洞"
            
            doLast {
                project.logger.lifecycle("🔒 扫描依赖安全漏洞...")
                
                val vulnerabilities = mutableListOf<String>()
                
                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        config.resolvedConfiguration.resolvedArtifacts.forEach { artifact ->
                            val moduleId = artifact.moduleVersion.id
                            
                            // 检查已知的安全问题（这里是示例，实际应该集成专业的安全扫描工具）
                            when {
                                moduleId.group == "org.apache.logging.log4j" && 
                                moduleId.version.startsWith("2.") && 
                                moduleId.version < "2.17.0" -> {
                                    vulnerabilities.add(
                                        "Log4j安全漏洞: ${moduleId.group}:${moduleId.name}:${moduleId.version} (建议升级到2.17.0+)"
                                    )
                                }
                                
                                moduleId.group == "com.fasterxml.jackson.core" && 
                                moduleId.version < "2.13.0" -> {
                                    vulnerabilities.add(
                                        "Jackson安全漏洞: ${moduleId.group}:${moduleId.name}:${moduleId.version} (建议升级到2.13.0+)"
                                    )
                                }
                            }
                        }
                    } catch (e: Exception) {
                        project.logger.debug("无法扫描配置 ${config.name}: ${e.message}")
                    }
                }
                
                if (vulnerabilities.isNotEmpty()) {
                    project.logger.error("🚨 发现 ${vulnerabilities.size} 个安全漏洞:")
                    vulnerabilities.forEach { vulnerability ->
                        project.logger.error("  - $vulnerability")
                    }
                } else {
                    project.logger.lifecycle("✅ 未发现已知安全漏洞")
                }
            }
        }
    }
}
