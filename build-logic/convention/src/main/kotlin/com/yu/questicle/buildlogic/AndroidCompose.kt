package com.yu.questicle.buildlogic

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * Configure Compose-specific options - 统一配置版本
 * 使用懒加载配置和现代化依赖管理
 */
internal fun Project.configureAndroidCompose(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    val commonDeps = UnifiedBuildConfig.getCommonDependencies()
    
    commonExtension.apply {
        buildFeatures {
            compose = UnifiedBuildConfig.Features.ENABLE_COMPOSE
        }

        composeOptions {
            // 使用统一的编译器版本管理
            kotlinCompilerExtensionVersion = BuildLogicVersions.KOTLIN
        }
    }

    dependencies {
        // 使用统一的 BOM 版本管理
        val composeBom = commonDeps["compose-bom"]!!
        add("implementation", platform("androidx.compose:compose-bom:$composeBom"))
        add("androidTestImplementation", platform("androidx.compose:compose-bom:$composeBom"))
        add("debugImplementation", platform("androidx.compose:compose-bom:$composeBom"))

        // Core Compose dependencies - 由 BOM 管理版本
        add("implementation", "androidx.compose.ui:ui")
        add("implementation", "androidx.compose.ui:ui-graphics")
        add("implementation", "androidx.compose.ui:ui-tooling-preview")
        add("implementation", "androidx.compose.material3:material3")
        add("implementation", "androidx.compose.runtime:runtime")

        // Animation dependencies - 支持实验性动画 API
        add("implementation", "androidx.compose.animation:animation")
        add("implementation", "androidx.compose.animation:animation-graphics")
        add("implementation", "androidx.compose.animation:animation-core")

        // Activity Compose - 使用统一版本管理
        val activityCompose = commonDeps["androidx-activity-compose"]!!
        add("implementation", "androidx.activity:activity-compose:$activityCompose")

        // Lifecycle - 使用统一版本管理
        val lifecycleVersion = commonDeps["androidx-lifecycle-runtime-ktx"]!!
        add("implementation", "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycleVersion")

        // Debug dependencies - 由 BOM 管理版本
        add("debugImplementation", "androidx.compose.ui:ui-tooling")
        add("debugImplementation", "androidx.compose.ui:ui-test-manifest")

        // Test dependencies - 使用统一的测试依赖管理
        val testDeps = UnifiedBuildConfig.getTestDependencies()
        val composeTestDep = testDeps["compose-ui-test-android"]!!
        add("androidTestImplementation", composeTestDep)
    }
}
