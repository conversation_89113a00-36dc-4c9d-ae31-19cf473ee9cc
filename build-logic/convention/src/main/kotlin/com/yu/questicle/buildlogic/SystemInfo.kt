package com.yu.questicle.buildlogic

/**
 * 系统信息收集器
 * 用于动态配置构建参数
 */
data class SystemInfo(
    val availableProcessors: Int,
    val maxMemory: Long,
    val osName: String,
    val osVersion: String,
    val isCI: Boolean = false
) {
    companion object {
        fun current(): SystemInfo {
            return SystemInfo(
                availableProcessors = Runtime.getRuntime().availableProcessors(),
                maxMemory = Runtime.getRuntime().maxMemory(),
                osName = System.getProperty("os.name"),
                osVersion = System.getProperty("os.version"),
                isCI = System.getenv("CI") != null
            )
        }
    }
    
    val isHighPerformanceSystem: Boolean
        get() = availableProcessors >= 8 && maxMemory >= 8L * 1024 * 1024 * 1024
    
    val isLowResourceSystem: Boolean
        get() = availableProcessors <= 2 || maxMemory <= 2L * 1024 * 1024 * 1024
    
    val isMacIntel: Boolean
        get() = osName.contains("Mac") && System.getProperty("os.arch") == "x86_64"
    
    val isMacAppleSilicon: Boolean
        get() = osName.contains("Mac") && System.getProperty("os.arch") == "aarch64"
    
    val isWindows: Boolean
        get() = osName.contains("Windows")
    
    val isLinux: Boolean
        get() = osName.contains("Linux")
    
    /**
     * 获取系统内存大小（GB）
     */
    val memoryGB: Double
        get() = maxMemory.toDouble() / (1024 * 1024 * 1024)
    
    /**
     * 判断是否为开发机器（非CI环境且资源充足）
     */
    val isDevelopmentMachine: Boolean
        get() = !isCI && availableProcessors >= 4 && memoryGB >= 8.0
    
    /**
     * 获取推荐的并行任务数
     */
    val recommendedParallelTasks: Int
        get() = when {
            isCI -> minOf(availableProcessors, 4) // CI环境限制
            isHighPerformanceSystem -> availableProcessors - 2
            availableProcessors >= 8 -> 6
            availableProcessors >= 4 -> 4
            else -> 2
        }.coerceAtLeast(1)
    
    /**
     * 获取推荐的堆内存大小
     */
    val recommendedHeapSize: String
        get() = when {
            isCI -> "4g" // CI环境保守配置
            memoryGB >= 16 -> "8g"
            memoryGB >= 8 -> "6g"
            memoryGB >= 4 -> "4g"
            else -> "2g"
        }
    
    override fun toString(): String {
        return """
            SystemInfo:
            ├── OS: $osName $osVersion
            ├── Architecture: ${System.getProperty("os.arch")}
            ├── CPU Cores: $availableProcessors
            ├── Memory: ${String.format("%.1f", memoryGB)} GB
            ├── CI Environment: $isCI
            ├── High Performance: $isHighPerformanceSystem
            ├── Development Machine: $isDevelopmentMachine
            └── Recommended Parallel Tasks: $recommendedParallelTasks
        """.trimIndent()
    }
}
