package com.yu.questicle.buildlogic

import org.gradle.api.Project
import org.gradle.api.artifacts.Configuration
import org.gradle.api.artifacts.dsl.DependencyHandler
import org.gradle.api.artifacts.dsl.RepositoryHandler
import java.util.concurrent.TimeUnit

/**
 * 依赖管理系统
 * 统一管理项目依赖，提供版本锁定和解析优化
 */
object DependencyManagement {

    /**
     * 配置依赖解析策略
     */
    fun configureDependencyResolution(project: Project) {
        project.configurations.configureEach {
            if (isCanBeResolved) {
                resolutionStrategy {
                    // 启用依赖锁定
                    activateDependencyLocking()

                    // 配置缓存策略
                    cacheDynamicVersionsFor(10, TimeUnit.MINUTES)
                    cacheChangingModulesFor(4, TimeUnit.HOURS)

                    // 强制使用特定版本解决冲突
                    force(
                        "org.jetbrains.kotlin:kotlin-stdlib:${BuildLogicVersions.KOTLIN}",
                        "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${BuildLogicVersions.KOTLIN}",
                        "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.1",
                        "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"
                    )

                    // 替换过时的依赖
                    dependencySubstitution {
                        substitute(module("org.jetbrains.kotlin:kotlin-stdlib-jre7"))
                            .using(module("org.jetbrains.kotlin:kotlin-stdlib-jdk7:${BuildLogicVersions.KOTLIN}"))
                        substitute(module("org.jetbrains.kotlin:kotlin-stdlib-jre8"))
                            .using(module("org.jetbrains.kotlin:kotlin-stdlib-jdk8:${BuildLogicVersions.KOTLIN}"))
                    }

                    // 排除传递依赖中的问题模块
                    eachDependency {
                        when (requested.group) {
                            "org.jetbrains.kotlin" -> {
                                if (requested.name.startsWith("kotlin-stdlib")) {
                                    useVersion(BuildLogicVersions.KOTLIN)
                                }
                            }
                            "androidx.compose.compiler" -> {
                                useVersion("1.5.15")
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 配置仓库优化
     */
    fun configureRepositories(project: Project) {
        project.repositories.apply {
            // 优先使用本地缓存
            mavenLocal()

            // Google仓库 - Android依赖
            google {
                content {
                    includeGroupByRegex("com\\.android.*")
                    includeGroupByRegex("com\\.google.*")
                    includeGroupByRegex("androidx.*")
                }
            }

            // Maven Central - 主要依赖源
            mavenCentral()

            // Gradle插件门户
            gradlePluginPortal()

            // JetBrains Compose仓库
            maven {
                url = project.uri("https://maven.pkg.jetbrains.space/public/p/compose/dev")
                content {
                    includeGroupByRegex("org\\.jetbrains\\.compose.*")
                }
            }

            // 阿里云镜像（可选，用于加速）
            maven {
                url = project.uri("https://maven.aliyun.com/repository/public")
                content {
                    includeGroup("org.jetbrains.kotlin")
                }
            }
        }
    }
    
    /**
     * 创建依赖锁定任务
     */
    fun createDependencyLockingTasks(project: Project) {
        // 生成依赖锁定文件
        project.tasks.register("generateDependencyLocks") {
            group = "dependency management"
            description = "生成所有配置的依赖锁定文件"

            doLast {
                val resolvableConfigs = project.configurations.filter { it.isCanBeResolved }
                project.logger.lifecycle("🔒 开始生成依赖锁定文件...")

                resolvableConfigs.forEach { config ->
                    try {
                        config.resolve()
                        project.logger.lifecycle("✅ 已锁定配置: ${config.name}")
                    } catch (e: Exception) {
                        project.logger.warn("⚠️ 无法锁定配置 ${config.name}: ${e.message}")
                    }
                }

                project.logger.lifecycle("🎉 依赖锁定文件生成完成")
            }
        }

        // 验证依赖锁定
        project.tasks.register("verifyDependencyLocks") {
            group = "dependency management"
            description = "验证依赖锁定文件的完整性"

            doLast {
                val lockDir = project.file("gradle/dependency-locks")
                if (!lockDir.exists()) {
                    project.logger.warn("⚠️ 依赖锁定目录不存在: ${lockDir.absolutePath}")
                    return@doLast
                }

                val lockFiles = lockDir.listFiles { _, name -> name.endsWith(".lockfile") }
                if (lockFiles.isNullOrEmpty()) {
                    project.logger.warn("⚠️ 未找到依赖锁定文件")
                } else {
                    project.logger.lifecycle("✅ 找到 ${lockFiles.size} 个依赖锁定文件")
                    lockFiles.forEach { file ->
                        project.logger.info("  - ${file.name}")
                    }
                }
            }
        }

        // 更新依赖锁定
        project.tasks.register("updateDependencyLocks") {
            group = "dependency management"
            description = "更新所有依赖锁定文件"

            doLast {
                project.logger.lifecycle("🔄 更新依赖锁定文件...")

                // 删除现有锁定文件
                val lockDir = project.file("gradle/dependency-locks")
                if (lockDir.exists()) {
                    lockDir.listFiles { _, name -> name.endsWith(".lockfile") }?.forEach { file ->
                        file.delete()
                        project.logger.info("删除旧锁定文件: ${file.name}")
                    }
                }

                // 重新生成锁定文件
                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        config.resolve()
                        project.logger.info("重新锁定配置: ${config.name}")
                    } catch (e: Exception) {
                        project.logger.warn("无法重新锁定配置 ${config.name}: ${e.message}")
                    }
                }

                project.logger.lifecycle("✅ 依赖锁定文件更新完成")
            }
        }
    }
    
    /**
     * 分析依赖冲突
     */
    fun analyzeDependencyConflicts(project: Project) {
        project.tasks.register("analyzeDependencyConflicts") {
            group = "dependency management"
            description = "分析项目中的依赖冲突"

            doLast {
                project.logger.lifecycle("🔍 分析依赖冲突...")

                val conflictReport = mutableMapOf<String, MutableList<String>>()

                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        val resolved = config.resolvedConfiguration
                        val conflicts = resolved.lenientConfiguration.unresolvedModuleDependencies

                        if (conflicts.isNotEmpty()) {
                            val configConflicts = mutableListOf<String>()
                            conflicts.forEach { conflict ->
                                configConflicts.add("${conflict.selector} - ${conflict.problem}")
                            }
                            conflictReport[config.name] = configConflicts
                        }
                    } catch (e: Exception) {
                        project.logger.debug("无法分析配置 ${config.name}: ${e.message}")
                    }
                }

                if (conflictReport.isNotEmpty()) {
                    project.logger.warn("⚠️ 发现依赖冲突:")
                    conflictReport.forEach { (configName, conflicts) ->
                        project.logger.warn("  配置 $configName:")
                        conflicts.forEach { conflict ->
                            project.logger.warn("    - $conflict")
                        }
                    }
                } else {
                    project.logger.lifecycle("✅ 未发现依赖冲突")
                }
            }
        }
    }
    
    /**
     * 生成依赖报告
     */
    fun createDependencyReports(project: Project) {
        project.tasks.register("dependencyInsights") {
            group = "dependency management"
            description = "生成详细的依赖分析报告"

            doLast {
                project.logger.lifecycle("📊 生成依赖分析报告...")

                val reportFile = project.file("build/reports/dependencies/dependency-insights.md")
                reportFile.parentFile.mkdirs()

                val lockDir = project.file("gradle/dependency-locks")
                val lockFileCount = if (lockDir.exists()) {
                    lockDir.listFiles { _, name -> name.endsWith(".lockfile") }?.size ?: 0
                } else {
                    0
                }

                val resolvableConfigs = project.configurations.filter { it.isCanBeResolved }
                val totalDependencies = resolvableConfigs.sumOf { config ->
                    try {
                        config.resolvedConfiguration.resolvedArtifacts.size
                    } catch (e: Exception) {
                        0
                    }
                }

                reportFile.writeText("""
                    # 依赖管理报告

                    ## 项目信息
                    - **项目名称**: ${project.name}
                    - **生成时间**: ${java.time.LocalDateTime.now()}
                    - **Gradle版本**: ${project.gradle.gradleVersion}

                    ## 配置统计
                    - **总配置数**: ${project.configurations.size}
                    - **可解析配置数**: ${resolvableConfigs.size}
                    - **总依赖数**: $totalDependencies

                    ## 依赖锁定状态
                    - **锁定文件目录**: gradle/dependency-locks/
                    - **锁定文件数量**: $lockFileCount
                    - **锁定策略**: 启用

                    ## 仓库配置
                    - Google Maven (Android依赖)
                    - Maven Central (主要依赖源)
                    - Gradle Plugin Portal (插件)
                    - JetBrains Compose (Compose依赖)
                    - 阿里云镜像 (Kotlin加速)

                    ## 优化策略
                    - **缓存策略**: 动态版本10分钟，变更模块4小时
                    - **版本强制**: Kotlin ${BuildLogicVersions.KOTLIN}
                    - **依赖替换**: 自动替换过时JRE库
                    - **内容过滤**: 仓库内容按组织过滤

                    ## 配置详情
                    ${resolvableConfigs.joinToString("\n") { "- ${it.name}" }}

                    ---
                    *报告生成完成 - ${java.time.LocalDateTime.now()}*
                """.trimIndent())

                project.logger.lifecycle("✅ 报告已生成: ${reportFile.absolutePath}")
            }
        }

        // 创建依赖树分析任务
        project.tasks.register("dependencyTreeAnalysis") {
            group = "dependency management"
            description = "分析依赖树结构"

            doLast {
                project.logger.lifecycle("🌳 分析依赖树结构...")

                val treeFile = project.file("build/reports/dependencies/dependency-tree.txt")
                treeFile.parentFile.mkdirs()

                val treeContent = StringBuilder()
                treeContent.appendLine("# 依赖树分析报告")
                treeContent.appendLine()

                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        treeContent.appendLine("## 配置: ${config.name}")
                        val resolved = config.resolvedConfiguration
                        resolved.firstLevelModuleDependencies.forEach { dep ->
                            treeContent.appendLine("├── ${dep.moduleGroup}:${dep.moduleName}:${dep.moduleVersion}")
                            dep.children.forEach { child ->
                                treeContent.appendLine("│   └── ${child.moduleGroup}:${child.moduleName}:${child.moduleVersion}")
                            }
                        }
                        treeContent.appendLine()
                    } catch (e: Exception) {
                        treeContent.appendLine("⚠️ 无法分析配置 ${config.name}: ${e.message}")
                        treeContent.appendLine()
                    }
                }

                treeFile.writeText(treeContent.toString())
                project.logger.lifecycle("✅ 依赖树报告已生成: ${treeFile.absolutePath}")
            }
        }
    }
}
