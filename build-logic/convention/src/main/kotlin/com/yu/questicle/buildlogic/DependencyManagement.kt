package com.yu.questicle.buildlogic

import org.gradle.api.Project
import org.gradle.api.artifacts.Configuration
import org.gradle.api.artifacts.dsl.DependencyHandler

/**
 * 依赖管理系统
 * 统一管理项目依赖，提供版本锁定和解析优化
 */
object DependencyManagement {
    
    /**
     * 配置依赖解析策略
     */
    fun configureDependencyResolution(project: Project) {
        project.logger.info("配置依赖解析策略...")

        // 简化的依赖解析配置
        project.configurations.configureEach {
            if (isCanBeResolved) {
                resolutionStrategy.activateDependencyLocking()
            }
        }
    }
    
    /**
     * 配置仓库优化
     */
    fun configureRepositories(project: Project) {
        project.repositories.apply {
            // 优先使用本地缓存
            mavenLocal()

            // Google仓库 - Android依赖
            google()

            // Maven Central - 主要依赖源
            mavenCentral()

            // Gradle插件门户
            gradlePluginPortal()

            // JetBrains Compose仓库
            maven {
                url = project.uri("https://maven.pkg.jetbrains.space/public/p/compose/dev")
            }

            // 阿里云镜像（可选，用于加速）
            maven {
                url = project.uri("https://maven.aliyun.com/repository/public")
            }
        }
    }
    
    /**
     * 创建依赖锁定任务
     */
    fun createDependencyLockingTasks(project: Project) {
        // 生成依赖锁定文件
        project.tasks.register("generateDependencyLocks") {
            group = "dependency management"
            description = "生成所有配置的依赖锁定文件"
            
            doLast {
                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        config.resolve()
                        project.logger.lifecycle("✅ 已锁定配置: ${config.name}")
                    } catch (e: Exception) {
                        project.logger.warn("⚠️ 无法锁定配置 ${config.name}: ${e.message}")
                    }
                }
            }
        }
        
        // 验证依赖锁定
        project.tasks.register("verifyDependencyLocks") {
            group = "dependency management"
            description = "验证依赖锁定文件的完整性"
            
            doLast {
                val lockFiles = project.fileTree("gradle/dependency-locks").files
                if (lockFiles.isEmpty()) {
                    project.logger.warn("⚠️ 未找到依赖锁定文件")
                } else {
                    project.logger.lifecycle("✅ 找到 ${lockFiles.size} 个依赖锁定文件")
                    lockFiles.forEach { file ->
                        project.logger.info("  - ${file.name}")
                    }
                }
            }
        }
        
        // 更新依赖锁定
        project.tasks.register("updateDependencyLocks") {
            group = "dependency management"
            description = "更新所有依赖锁定文件"
            
            doLast {
                project.logger.lifecycle("🔄 更新依赖锁定文件...")
                // 这里可以添加具体的更新逻辑
            }
        }
    }
    
    /**
     * 分析依赖冲突
     */
    fun analyzeDependencyConflicts(project: Project) {
        project.tasks.register("analyzeDependencyConflicts") {
            group = "dependency management"
            description = "分析项目中的依赖冲突"
            
            doLast {
                project.logger.lifecycle("🔍 分析依赖冲突...")
                
                project.configurations.filter { it.isCanBeResolved }.forEach { config ->
                    try {
                        val resolved = config.resolvedConfiguration
                        val conflicts = resolved.lenientConfiguration.unresolvedModuleDependencies
                        
                        if (conflicts.isNotEmpty()) {
                            project.logger.warn("⚠️ 配置 ${config.name} 存在 ${conflicts.size} 个冲突:")
                            conflicts.forEach { conflict ->
                                project.logger.warn("  - ${conflict.selector}")
                            }
                        } else {
                            project.logger.lifecycle("✅ 配置 ${config.name} 无冲突")
                        }
                    } catch (e: Exception) {
                        project.logger.debug("无法分析配置 ${config.name}: ${e.message}")
                    }
                }
            }
        }
    }
    
    /**
     * 生成依赖报告
     */
    fun createDependencyReports(project: Project) {
        project.tasks.register("dependencyInsights") {
            group = "dependency management"
            description = "生成详细的依赖分析报告"
            
            doLast {
                project.logger.lifecycle("📊 生成依赖分析报告...")
                
                val reportFile = project.file("build/reports/dependencies/dependency-insights.txt")
                reportFile.parentFile.mkdirs()
                
                reportFile.writeText("""
                    # 依赖管理报告
                    
                    ## 项目信息
                    - 项目名称: ${project.name}
                    - 生成时间: ${java.time.LocalDateTime.now()}
                    
                    ## 配置统计
                    - 总配置数: ${project.configurations.size}
                    - 可解析配置数: ${project.configurations.count { it.isCanBeResolved }}
                    
                    ## 依赖锁定状态
                    - 锁定文件目录: gradle/dependency-locks/
                    - 锁定策略: 启用
                    
                    ## 仓库配置
                    - Google Maven
                    - Maven Central
                    - Gradle Plugin Portal
                    - JetBrains Compose
                    
                    ---
                    报告生成完成
                """.trimIndent())
                
                project.logger.lifecycle("✅ 报告已生成: ${reportFile.absolutePath}")
            }
        }
    }
}
