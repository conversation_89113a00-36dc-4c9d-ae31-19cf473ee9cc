package com.yu.questicle.buildlogic

import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * 构建优化插件
 * 自动应用最优构建配置
 */
class BuildOptimizationPlugin : Plugin<Project> {
    
    override fun apply(target: Project) {
        val config = BuildOptimization.getOptimalConfiguration()
        val systemInfo = SystemInfo.current()
        
        // 应用内存设置
        applyMemorySettings(target, config.memorySettings)
        
        // 应用缓存设置
        applyCacheSettings(target, config.cacheSettings)
        
        // 应用并行设置
        applyParallelSettings(target, config.parallelForks)
        
        // 记录配置信息
        logConfiguration(target, config, systemInfo)
        
        // 创建优化任务
        createOptimizationTasks(target, config, systemInfo)
    }
    
    private fun applyMemorySettings(project: Project, settings: MemorySettings) {
        // 内存设置通过 gradle.properties 或启动参数应用
        // 这里我们记录推荐的设置
        project.logger.info("推荐的JVM参数: ${settings.toJvmArgs().joinToString(" ")}")
    }

    private fun applyCacheSettings(project: Project, settings: CacheConfiguration) {
        // 缓存设置通过 gradle.properties 应用
        project.logger.info("推荐的缓存设置: 构建缓存=${settings.buildCache}, 配置缓存=${settings.configurationCache}")
    }

    private fun applyParallelSettings(project: Project, forks: Int) {
        // 并行设置通过 gradle.properties 应用
        project.logger.info("推荐的并行设置: 最大工作线程=${forks}")
    }
    
    private fun logConfiguration(project: Project, config: BuildConfiguration, systemInfo: SystemInfo) {
        project.logger.lifecycle("""
            🚀 构建优化配置已应用:
            ├── 系统信息: ${systemInfo.osName} (${systemInfo.availableProcessors} cores, ${String.format("%.1f", systemInfo.memoryGB)} GB)
            ├── 并行任务数: ${config.parallelForks}
            ├── 堆内存大小: ${config.memorySettings.heapSize}
            ├── 配置缓存: ${if (config.cacheSettings.configurationCache) "启用" else "禁用"}
            ├── 构建缓存: ${if (config.cacheSettings.buildCache) "启用" else "禁用"}
            └── 性能模式: ${if (systemInfo.isHighPerformanceSystem) "高性能" else "标准"}
        """.trimIndent())
    }
    
    private fun createOptimizationTasks(project: Project, config: BuildConfiguration, systemInfo: SystemInfo) {
        // 创建系统信息任务
        project.tasks.register("systemInfo") {
            group = "build optimization"
            description = "显示系统信息和构建配置"
            
            doLast {
                println(systemInfo.toString())
                println("\n构建配置:")
                println("├── 并行任务数: ${config.parallelForks}")
                println("├── 内存设置: ${config.memorySettings.heapSize}")
                println("├── 编译器参数: ${config.compilerArgs.joinToString(", ")}")
                println("└── 缓存配置: 构建缓存=${config.cacheSettings.buildCache}, 配置缓存=${config.cacheSettings.configurationCache}")
            }
        }
        
        // 创建构建性能测试任务
        project.tasks.register("buildPerformanceTest") {
            group = "build optimization"
            description = "测试构建性能"
            
            doLast {
                val startTime = System.currentTimeMillis()
                project.logger.lifecycle("🚀 开始构建性能测试...")
                
                // 这里可以添加具体的性能测试逻辑
                project.logger.lifecycle("✅ 构建性能测试完成")
                
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                project.logger.lifecycle("⏱️ 测试耗时: ${duration}ms")
            }
        }
        
        // 创建优化建议任务
        project.tasks.register("optimizationSuggestions") {
            group = "build optimization"
            description = "提供构建优化建议"
            
            doLast {
                val suggestions = mutableListOf<String>()
                
                if (!systemInfo.isDevelopmentMachine) {
                    suggestions.add("考虑升级硬件配置以获得更好的构建性能")
                }
                
                if (systemInfo.isLowResourceSystem) {
                    suggestions.add("当前系统资源较低，建议关闭配置缓存以节省内存")
                }
                
                if (systemInfo.availableProcessors > config.parallelForks + 2) {
                    suggestions.add("可以考虑增加并行任务数以充分利用CPU资源")
                }
                
                if (suggestions.isNotEmpty()) {
                    project.logger.lifecycle("💡 优化建议:")
                    suggestions.forEachIndexed { index, suggestion ->
                        project.logger.lifecycle("${index + 1}. $suggestion")
                    }
                } else {
                    project.logger.lifecycle("✅ 当前配置已经是最优的！")
                }
            }
        }
    }
}
