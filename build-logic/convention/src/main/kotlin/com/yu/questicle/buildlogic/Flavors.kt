package com.yu.questicle.buildlogic

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.ApplicationProductFlavor
import com.android.build.api.dsl.CommonExtension
import com.android.build.api.dsl.ProductFlavor

@Suppress("EnumEntryName")
enum class FlavorDimension {
    contentType
}

// The content for the app can either come from local static data which is useful for demo
// purposes, or from a production backend server which supplies up-to-date, real content.
// These two product flavors reflect this behaviour.
@Suppress("EnumEntryName")
enum class QuesticleFlavor(val dimension: FlavorDimension, val applicationIdSuffix: String? = null) {
    demo(FlavorDimension.contentType, applicationIdSuffix = ".demo"),
    prod(FlavorDimension.contentType)
}

fun configureFlavors(
    commonExtension: CommonExtension<*, *, *, *, *, *>
) {
    commonExtension.apply {
        // 检查是否已经配置过flavors - 确保幂等性
        if (flavorDimensions.contains(FlavorDimension.contentType.name)) {
            return@apply
        }
        
        flavorDimensions += FlavorDimension.contentType.name
        productFlavors {
            QuesticleFlavor.values().forEach { flavor ->
                // 只有当flavor不存在时才创建
                if (findByName(flavor.name) == null) {
                    create(flavor.name) {
                        dimension = flavor.dimension.name
                        if (this@apply is ApplicationExtension && this is ApplicationProductFlavor) {
                            if (flavor.applicationIdSuffix != null) {
                                applicationIdSuffix = flavor.applicationIdSuffix
                            }
                        }
                    }
                }
            }
        }
    }
}
