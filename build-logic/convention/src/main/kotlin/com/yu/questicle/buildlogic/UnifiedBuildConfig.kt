package com.yu.questicle.buildlogic

import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

/**
 * 统一构建配置管理 - 2025年6月最新版本
 * 集中管理所有构建相关的配置，确保一致性和可维护性
 * 修复配置缓存问题：移除非序列化对象
 */
object UnifiedBuildConfig {
    
    // ============================================================================
    // 构建配置常量
    // ============================================================================
    const val COMPILE_SDK = 35
    const val MIN_SDK = 31
    const val TARGET_SDK = 35
    const val JVM_TARGET = "21"
    
    // ============================================================================
    // 编译器配置
    // ============================================================================
    val KOTLIN_COMPILER_ARGS = listOf(
        "-Xjsr305=strict",
        "-Xskip-prerelease-check",
        "-Xallow-unstable-dependencies",
        "-Xcontext-receivers",
        "-Xexpect-actual-classes",
        "-opt-in=kotlin.RequiresOptIn",
        "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
        "-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
        "-opt-in=androidx.compose.animation.graphics.ExperimentalAnimationGraphicsApi"
    )
    
    val JAVA_COMPILER_ARGS = listOf(
        "-Xlint:unchecked",
        "-Xlint:deprecation",
        "-parameters"
    )
    
    // ============================================================================
    // 打包优化配置
    // ============================================================================
    val PACKAGING_EXCLUDES = listOf(
        "/META-INF/{AL2.0,LGPL2.1}",
        "/META-INF/gradle/incremental.annotation.processors",
        "META-INF/*.kotlin_module",
        "META-INF/gradle/**",
        "META-INF/maven/**",
        "META-INF/proguard/**",
        "META-INF/com.android.tools/**",
        "META-INF/services/**",
        "META-INF/versions/**",
        "**/*.proto",
        "**/*.properties",
        "DebugProbesKt.bin"
    )
    
    // ============================================================================
    // 统一依赖版本管理
    // ============================================================================
    fun getCommonDependencies(): Map<String, String> = mapOf(
        "compose-bom" to BuildLogicVersions.COMPOSE_BOM,
        "desugar-jdk-libs" to "2.1.5",
        "kotlin-stdlib" to BuildLogicVersions.KOTLIN,
        "kotlinx-coroutines-core" to BuildLogicVersions.KOTLINX_COROUTINES,
        "kotlinx-coroutines-android" to BuildLogicVersions.KOTLINX_COROUTINES,
        "androidx-core-ktx" to "1.16.0",
        "androidx-lifecycle-runtime-ktx" to "2.8.7",
        "androidx-activity-compose" to "1.9.3",
        "androidx-navigation-compose" to "2.8.5"
    )
    
    // ============================================================================
    // 测试依赖版本管理
    // ============================================================================
    fun getTestDependencies(): Map<String, String> = mapOf(
        "junit-bom" to "org.junit:junit-bom:${BuildLogicVersions.JUNIT5}",
        "junit-jupiter-api" to "org.junit.jupiter:junit-jupiter-api",
        "junit-jupiter-engine" to "org.junit.jupiter:junit-jupiter-engine",
        "mockk" to "io.mockk:mockk:${BuildLogicVersions.MOCKK}",
        "turbine" to "app.cash.turbine:turbine:${BuildLogicVersions.TURBINE}",
        "kotlinx-coroutines-test" to "org.jetbrains.kotlinx:kotlinx-coroutines-test:${BuildLogicVersions.KOTLINX_COROUTINES}",
        "androidx-test-ext-junit" to "androidx.test.ext:junit:1.2.1",
        "androidx-test-espresso-core" to "androidx.test.espresso:espresso-core:${BuildLogicVersions.ESPRESSO}",
        "compose-ui-test-android" to "androidx.compose.ui:ui-test-android:1.8.3",
        "compose-ui-test-manifest" to "androidx.compose.ui:ui-test-manifest"
    )
    
    // ============================================================================
    // 构建验证 - 修复配置缓存问题
    // ============================================================================
    fun validateBuildConfiguration(project: Project) {
        // 验证版本一致性
        BuildLogicVersions.validateCriticalVersions()
        
        // 验证项目配置
        require(project.name.isNotBlank()) { "Project name cannot be blank" }
        
        // 移除logger调用 - 导致配置缓存序列化问题
        // project.logger.lifecycle("🔧 Unified Build Config validated for ${project.name}") (移除)
        // project.logger.lifecycle("📦 Using Kotlin ${BuildLogicVersions.KOTLIN}, AGP ${BuildLogicVersions.AGP}") (移除)
        
        // 使用静态验证替代动态日志输出 - 配置缓存友好
        // 验证结果存储到项目属性中，在执行阶段输出
        project.extensions.extraProperties.set("build.config.validated", true)
        project.extensions.extraProperties.set("build.config.kotlin.version", BuildLogicVersions.KOTLIN)
        project.extensions.extraProperties.set("build.config.agp.version", BuildLogicVersions.AGP)
    }
    
    // ============================================================================
    // 构建特性开关
    // ============================================================================
    object Features {
        const val ENABLE_COMPOSE = true
        const val ENABLE_R8_FULL_MODE = false
        const val ENABLE_RESOURCE_OPTIMIZATIONS = true
        const val ENABLE_PARALLEL_BUILD = true
        const val ENABLE_BUILD_CACHE = true
        const val ENABLE_CONFIGURATION_CACHE = true
    }
} 