package com.yu.questicle.buildlogic

import org.gradle.api.Project

/**
 * 企业级Kotlin配置策略
 * 根据不同环境和需求配置实验性特性的使用
 */
object EnterpriseKotlinConfig {
    
    /**
     * 获取企业级安全的编译器参数
     * 这些特性已经稳定，适合企业级应用 (2025年6月更新)
     */
    fun getStableOptInFlags(): List<String> = listOf(
        "-opt-in=kotlin.RequiresOptIn",

        // 已稳定的协程API (Kotlin 1.6+)
        "-opt-in=kotlinx.coroutines.FlowPreview", // Flow API已稳定

        // 已稳定的标准库API (Kotlin 1.6+)
        // kotlin.time.ExperimentalTime 在 Kotlin 1.6+ 中已移除，时间API已稳定
        "-opt-in=kotlin.ExperimentalUnsignedTypes", // 无符号类型已稳定

        // 编译优化（稳定）
        "-Xjsr305=strict",
        "-Xjvm-default=all"
    )
    
    /**
     * 获取需要谨慎使用的实验性特性
     * 这些特性在企业级应用中需要评估风险
     */
    fun getCautiousOptInFlags(): List<String> = listOf(
        // 协程相关（需要谨慎评估）
        "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi", // 可能有API变更
        "-opt-in=kotlinx.coroutines.DelicateCoroutinesApi", // 明确标记为危险
        
        // 标准库实验性API
        "-opt-in=kotlin.ExperimentalStdlibApi", // 可能有变更
        "-opt-in=kotlin.contracts.ExperimentalContracts", // 合约API不稳定
        
        // 序列化实验性API
        "-opt-in=kotlinx.serialization.ExperimentalSerializationApi" // 序列化新特性
    )
    
    /**
     * 获取Compose相关的实验性特性 (2025年6月更新)
     * 基于Compose BOM 2025.06.00的稳定性评估
     */
    fun getComposeOptInFlags(): List<String> = listOf(
        // Material 3 大部分API已稳定 (TopAppBar, Card等核心组件已稳定)
        "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",

        // 基础功能已相对稳定
        "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
        "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi",

        // UI相关（仍需谨慎，但稳定性提升）
        "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
        "-opt-in=androidx.compose.runtime.ExperimentalComposeApi",

        // 动画和图形（稳定性显著提升）
        "-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
        "-opt-in=androidx.compose.ui.graphics.ExperimentalGraphicsApi",
        "-opt-in=androidx.compose.animation.graphics.ExperimentalAnimationGraphicsApi"
    )

    /**
     * 获取已稳定的Compose特性 (2025年6月)
     * 这些特性在最新版本中已不再需要opt-in
     */
    fun getStableComposeFeatures(): List<String> = listOf(
        // 以下特性在Compose 1.8.x中已稳定，无需opt-in
        // "androidx.compose.material3.TopAppBar", // 已稳定
        // "androidx.compose.material3.Card", // 已稳定
        // "androidx.compose.animation.core.*", // 核心动画API已稳定
        // "androidx.compose.foundation.layout.WindowInsets", // 已稳定
    )
    
    /**
     * 获取2025年6月优化的编译器参数
     * 基于最新稳定版本的智能配置
     */
    fun getOptimizedOptInFlags2025(project: Project): List<String> {
        val hasComposeFeatures = hasComposeFeatures(project)
        val hasAnimationFeatures = hasAnimationFeatures(project)
        val isProductionBuild = project.findProperty("questicle.build.type")?.toString() == "production"

        val baseFlags = mutableListOf<String>().apply {
            // 核心稳定特性
            add("-opt-in=kotlin.RequiresOptIn")
            add("-opt-in=kotlinx.coroutines.FlowPreview")
            add("-opt-in=kotlin.ExperimentalUnsignedTypes")

            // 编译优化
            add("-Xjsr305=strict")
            add("-Xjvm-default=all")

            // 生产环境避免高风险API
            if (!isProductionBuild) {
                add("-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi")
                add("-opt-in=kotlin.ExperimentalStdlibApi")
            }
        }

        // Compose相关配置
        if (hasComposeFeatures) {
            baseFlags.apply {
                // Material3 API (大部分已稳定，但保留以防万一)
                add("-opt-in=androidx.compose.material3.ExperimentalMaterial3Api")

                // 基础功能 (相对稳定)
                add("-opt-in=androidx.compose.foundation.ExperimentalFoundationApi")

                // 仅在需要动画功能时添加动画相关opt-in
                if (hasAnimationFeatures) {
                    add("-opt-in=androidx.compose.animation.ExperimentalAnimationApi")
                    add("-opt-in=androidx.compose.animation.graphics.ExperimentalAnimationGraphicsApi")
                }

                // UI API (谨慎使用)
                if (!isProductionBuild) {
                    add("-opt-in=androidx.compose.ui.ExperimentalComposeUiApi")
                    add("-opt-in=androidx.compose.runtime.ExperimentalComposeApi")
                }
            }
        }

        return baseFlags
    }

    /**
     * 根据构建类型和模块特性获取合适的编译器参数 (传统方法)
     */
    fun getOptInFlagsForBuildType(project: Project): List<String> {
        val buildType = project.findProperty("questicle.build.type")?.toString() ?: "development"
        val hasComposeFeatures = hasComposeFeatures(project)
        val hasAnimationFeatures = hasAnimationFeatures(project)

        val baseFlags = when (buildType) {
            "production" -> {
                // 生产环境：只使用稳定特性
                getStableOptInFlags()
            }
            "staging" -> {
                // 预发布环境：稳定特性 + 谨慎的实验性特性
                getStableOptInFlags() + getCautiousOptInFlags()
            }
            "development" -> {
                // 开发环境：稳定特性 + 谨慎的实验性特性
                getStableOptInFlags() + getCautiousOptInFlags()
            }
            else -> getStableOptInFlags()
        }

        // 根据模块特性添加相应的Compose opt-in flags
        val composeFlags = if (hasComposeFeatures) {
            getComposeOptInFlags().filter { flag ->
                // 只有当模块真正使用动画功能时才添加动画相关的opt-in
                if (flag.contains("animation.graphics") && !hasAnimationFeatures) {
                    false
                } else {
                    true
                }
            }
        } else {
            emptyList()
        }

        return baseFlags + composeFlags
    }

    /**
     * 检查项目是否使用Compose功能
     */
    private fun hasComposeFeatures(project: Project): Boolean {
        // 检查是否应用了Compose相关插件或依赖
        return project.plugins.hasPlugin("questicle.android.library.compose") ||
               project.plugins.hasPlugin("questicle.android.application.compose") ||
               project.name.contains("compose") ||
               project.path.contains("designsystem")
    }

    /**
     * 检查项目是否使用动画功能
     */
    private fun hasAnimationFeatures(project: Project): Boolean {
        // 检查是否是UI相关模块或明确使用动画的模块
        return project.name.contains("tetris") ||
               project.name.contains("game") ||
               project.path.contains("designsystem") ||
               project.path.contains("ui")
    }
    
    /**
     * 获取企业级推荐的Gradle配置
     */
    fun getEnterpriseGradleProperties(): Map<String, String> = mapOf(
        // 稳定性优先的配置
        "org.gradle.configuration-cache" to "false", // 企业环境可能需要禁用
        "org.gradle.configuration-cache.problems" to "fail", // 严格模式
        "org.gradle.warning.mode" to "fail", // 警告即失败
        
        // 性能与稳定性平衡
        "kotlin.incremental" to "true",
        "kotlin.incremental.java" to "true",
        "kotlin.caching.enabled" to "true",
        "kotlin.parallel.tasks.in.project" to "false", // 企业环境可能需要禁用并行
        
        // 构建报告（便于问题排查）
        "kotlin.build.report.enable" to "true",
        "kotlin.build.report.output" to "file"
    )
}

/**
 * 企业级特性评估结果
 */
data class FeatureAssessment(
    val feature: String,
    val riskLevel: RiskLevel,
    val recommendation: String,
    val alternatives: List<String> = emptyList()
)

enum class RiskLevel {
    LOW,      // 适合企业级应用
    MEDIUM,   // 需要评估和测试
    HIGH,     // 不推荐用于生产环境
    CRITICAL, // 禁止用于企业级应用
    OBSOLETE  // 已过时，无需opt-in (2025年新增)
}

/**
 * 实验性特性风险评估 (2025年6月更新)
 */
object ExperimentalFeatureAssessment {

    fun assessFeatures2025(): List<FeatureAssessment> = listOf(
        // 协程相关
        FeatureAssessment(
            "kotlinx.coroutines.ExperimentalCoroutinesApi",
            RiskLevel.MEDIUM,
            "部分API可能变更，建议在非核心功能中使用",
            listOf("使用稳定的协程API", "等待API稳定")
        ),
        
        FeatureAssessment(
            "kotlinx.coroutines.DelicateCoroutinesApi",
            RiskLevel.HIGH,
            "明确标记为危险API，可能导致内存泄漏或性能问题",
            listOf("使用结构化并发", "重新设计架构避免使用")
        ),
        
        // Compose相关 (2025年6月更新)
        FeatureAssessment(
            "androidx.compose.material3.ExperimentalMaterial3Api",
            RiskLevel.LOW,
            "Material 3大部分API已稳定，TopAppBar、Card等核心组件可安全使用",
            listOf("使用Material 2稳定版本", "逐步迁移到稳定的Material 3 API")
        ),

        FeatureAssessment(
            "androidx.compose.ui.ExperimentalComposeUiApi",
            RiskLevel.LOW, // 从MEDIUM降级到LOW
            "UI API在Compose 1.8.x中稳定性显著提升，大部分功能可安全使用",
            listOf("使用稳定的Compose UI API", "关注版本更新日志")
        ),

        FeatureAssessment(
            "androidx.compose.animation.graphics.ExperimentalAnimationGraphicsApi",
            RiskLevel.LOW, // 新增评估
            "动画图形API在2025年版本中稳定性大幅提升，适合游戏和动画应用",
            listOf("使用传统动画API", "等待完全稳定")
        ),
        
        // 标准库相关
        FeatureAssessment(
            "kotlin.ExperimentalStdlibApi",
            RiskLevel.MEDIUM,
            "标准库实验性API可能在未来版本中变更",
            listOf("使用稳定的标准库API", "自行实现相关功能")
        ),
        
        FeatureAssessment(
            "kotlin.time.ExperimentalTime",
            RiskLevel.OBSOLETE, // 新增状态
            "时间API在Kotlin 1.6+中已完全稳定，无需opt-in",
            listOf("直接使用kotlin.time包，无需opt-in")
        )
    )

    /**
     * 获取已过时的opt-in配置 (2025年6月)
     * 这些配置可以安全移除
     */
    fun getObsoleteOptIns(): List<String> = listOf(
        "kotlin.time.ExperimentalTime", // Kotlin 1.6+中已稳定
        // 可能还有其他已稳定的API
    )

    /**
     * 获取推荐移除的opt-in配置
     * 基于2025年6月的稳定性评估
     */
    fun getRecommendedRemovals(): Map<String, String> = mapOf(
        "kotlin.time.ExperimentalTime" to "时间API已在Kotlin 1.6+中稳定，可直接使用",
        // 未来可能稳定的API将添加到这里
    )
}
