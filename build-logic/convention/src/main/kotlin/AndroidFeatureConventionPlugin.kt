import com.android.build.api.dsl.LibraryExtension
import com.yu.questicle.buildlogic.configureKotlinAndroid
import com.yu.questicle.buildlogic.configureDependencyResolution
import com.yu.questicle.buildlogic.configureTestDependencies
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidFeatureConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("questicle.android.library")
                apply("questicle.android.library.compose")
                apply("questicle.hilt")
                apply("questicle.android.lint")
            }

            extensions.configure<LibraryExtension> {
                defaultConfig.targetSdk = 35
                // configureFlavors已经在questicle.android.library中配置过了
            }

            dependencies {
                add("implementation", project(":core:designsystem"))
                add("implementation", project(":core:ui"))
                add("implementation", project(":core:data"))
                add("implementation", project(":core:domain"))
                add("implementation", project(":core:common"))
                add("implementation", project(":core:testing"))
            }
            
            // 应用统一的依赖解析策略和测试依赖
            configureDependencyResolution()
            configureTestDependencies()
        }
    }
}
