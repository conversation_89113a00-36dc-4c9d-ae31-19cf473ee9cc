import com.android.build.api.dsl.ApplicationExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidApplicationFirebaseConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.google.gms.google-services")
                apply("com.google.firebase.firebase-perf")
                apply("com.google.firebase.crashlytics")
            }

            dependencies {
                add("implementation", platform("com.google.firebase:firebase-bom:33.14.0"))
                add("implementation", "com.google.firebase:firebase-analytics-ktx")
                add("implementation", "com.google.firebase:firebase-crashlytics-ktx")
            }
        }
    }
}
