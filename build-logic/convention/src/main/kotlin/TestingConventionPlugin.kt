import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.withType
import org.gradle.kotlin.dsl.register
import org.gradle.testing.jacoco.tasks.JacocoReport

/**
 * 测试框架统一配置插件
 * 
 * 功能:
 * - 配置JUnit 5测试框架
 * - 集成MockK和Kotest
 * - 配置测试覆盖率
 * - 统一测试配置
 * - 支持性能测试和基准测试
 * - 配置测试分类和标签
 */
class TestingConventionPlugin : Plugin<Project> {
    
    override fun apply(target: Project) {
        with(target) {
            configureJUnit5()
            configureMockK()
            configureKotest()
            configureTestCoverage()
            configurePerformanceTesting()
            configureTestCategories()
        }
    }
}

/**
 * 配置JUnit 5测试框架 (Enhanced for Day 10)
 */
private fun Project.configureJUnit5() {
    tasks.withType<Test>().configureEach {
        useJUnitPlatform()

        // 并行执行配置 - 优化并行度
        val availableProcessors = Runtime.getRuntime().availableProcessors()
        maxParallelForks = when {
            availableProcessors >= 8 -> availableProcessors - 2  // 高性能机器
            availableProcessors >= 4 -> availableProcessors - 1  // 中等性能机器
            else -> 2  // 低性能机器最少2个进程
        }.coerceAtLeast(1)

        // JUnit 5并行测试配置 - 增强配置
        systemProperty("junit.jupiter.execution.parallel.enabled", "true")
        systemProperty("junit.jupiter.execution.parallel.mode.default", "concurrent")
        systemProperty("junit.jupiter.execution.parallel.mode.classes.default", "concurrent")
        systemProperty("junit.jupiter.execution.parallel.config.strategy", "dynamic")
        systemProperty("junit.jupiter.execution.parallel.config.dynamic.factor", "2.0")

        // 测试输出配置 - 增强日志
        testLogging {
            events("passed", "skipped", "failed", "standardOut", "standardError")
            showExceptions = true
            showCauses = true
            showStackTraces = true
            showStandardStreams = project.hasProperty("showTestOutput")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }

        // 测试JVM配置 - 优化内存和GC
        jvmArgs(
            "-Xmx3g",                    // 增加内存到3GB
            "-Xms1g",                    // 设置初始内存
            "-XX:+UseG1GC",              // 使用G1垃圾收集器
            "-XX:MaxGCPauseMillis=50",   // 减少GC暂停时间
            "-XX:+UseStringDeduplication", // 启用字符串去重
            "-XX:+OptimizeStringConcat",  // 优化字符串连接
            "-XX:+UnlockExperimentalVMOptions",
            "-XX:+UseCGroupMemoryLimitForHeap"
        )

        // 测试超时配置 - 分层超时
        systemProperty("junit.jupiter.execution.timeout.default", "5m")
        systemProperty("junit.jupiter.execution.timeout.testable.method.default", "2m")
        systemProperty("junit.jupiter.execution.timeout.testtemplate.method.default", "2m")

        // 测试实例生命周期
        systemProperty("junit.jupiter.testinstance.lifecycle.default", "per_class")

        // 测试环境配置
        environment("TEST_ENV", "true")
        environment("PARALLEL_TESTS", "true")

        // 测试报告配置
        reports {
            junitXml.required.set(true)
            html.required.set(true)
            html.outputLocation.set(layout.buildDirectory.dir("reports/tests/${name}"))
        }

        // 测试数据目录
        systemProperty("test.data.dir", layout.projectDirectory.dir("src/test/resources").asFile.absolutePath)

        // 失败时继续执行其他测试
        ignoreFailures = false

        // 测试缓存配置
        outputs.upToDateWhen { false }  // 确保测试总是运行
    }
}

/**
 * 配置MockK测试框架
 */
private fun Project.configureMockK() {
    dependencies {
        "testImplementation"("io.mockk:mockk:1.13.8")
        "testImplementation"("io.mockk:mockk-android:1.13.8")
        "androidTestImplementation"("io.mockk:mockk-android:1.13.8")
    }
}

/**
 * 配置Kotest测试框架
 */
private fun Project.configureKotest() {
    dependencies {
        "testImplementation"("io.kotest:kotest-runner-junit5:5.8.0")
        "testImplementation"("io.kotest:kotest-assertions-core:5.8.0")
        "testImplementation"("io.kotest:kotest-property:5.8.0")
        "testImplementation"("io.kotest:kotest-framework-datatest:5.8.0")
    }
}

/**
 * 配置测试覆盖率
 */
private fun Project.configureTestCoverage() {
    pluginManager.apply("jacoco")
    
    tasks.register<JacocoReport>("jacocoTestReport") {
        dependsOn("test")
        
        reports {
            xml.required.set(true)
            html.required.set(true)
            csv.required.set(true)
        }
        
        // 排除不需要覆盖的文件
        val excludes = listOf(
            "**/R.class",
            "**/R$*.class",
            "**/BuildConfig.*",
            "**/Manifest*.*",
            "**/*Test*.*",
            "android/**/*.*",
            "**/di/**/*.*",
            "**/hilt_aggregated_deps/**",
            "**/*_Factory.*",
            "**/*_MembersInjector.*",
            "**/Dagger*Component*.*",
            "**/*Module_*Factory.*"
        )
        
        classDirectories.setFrom(
            files(classDirectories.files.map {
                fileTree(it) { exclude(excludes) }
            })
        )
    }
}

/**
 * 配置性能测试
 * 
 * 功能:
 * - 添加性能测试依赖
 * - 配置性能测试任务
 * - 设置性能测试JVM参数
 */
private fun Project.configurePerformanceTesting() {
    dependencies {
        // JMH 微基准测试框架
        "testImplementation"("org.openjdk.jmh:jmh-core:1.37")
        "testImplementation"("org.openjdk.jmh:jmh-generator-annprocess:1.37")
        
        // 性能测试工具
        "testImplementation"("io.github.microutils:kotlin-logging:3.0.5")
        "testImplementation"("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
        "testImplementation"("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
    }
    
    // 创建性能测试任务
    tasks.register<Test>("performanceTest") {
        description = "Runs performance tests."
        group = "verification"
        
        useJUnitPlatform {
            includeTags("performance")
        }
        
        // 性能测试JVM配置
        jvmArgs(
            "-XX:+UseG1GC",
            "-XX:MaxGCPauseMillis=50",
            "-Xmx4g",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:+UnlockExperimentalVMOptions",
            "-XX:+UseCGroupMemoryLimitForHeap",
            "-XX:+DisableExplicitGC"
        )
        
        // 禁用并行执行以避免干扰
        systemProperty("junit.jupiter.execution.parallel.enabled", "false")
        
        // 增加超时时间
        systemProperty("junit.jupiter.execution.timeout.default", "5m")
        
        // 性能测试输出配置
        testLogging {
            events("passed", "skipped", "failed", "standardOut", "standardError")
            showStandardStreams = true
        }
        
        // 排除性能测试不计入常规测试覆盖率
        filter {
            includeTestsMatching("*PerformanceTest")
            includeTestsMatching("*BenchmarkTest")
            includeTestsMatching("*PerformanceValidationTest")
        }
        
        // 性能测试结果输出到单独目录
        reports {
            html.required.set(true)
            junitXml.required.set(true)
        }
    }
    
    // 创建内存性能测试任务
    tasks.register<Test>("memoryTest") {
        description = "Runs memory usage tests."
        group = "verification"
        
        useJUnitPlatform {
            includeTags("memory")
        }
        
        // 内存测试JVM配置
        jvmArgs(
            "-XX:+UseG1GC",
            "-Xmx2g",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:+UnlockDiagnosticVMOptions",
            "-XX:+DebugNonSafepoints",
            "-XX:NativeMemoryTracking=summary"
        )
        
        // 禁用并行执行以避免干扰
        systemProperty("junit.jupiter.execution.parallel.enabled", "false")
        
        filter {
            includeTestsMatching("*MemoryTest")
        }
    }
}

/**
 * 配置测试分类和标签
 * 
 * 功能:
 * - 配置单元测试标签
 * - 配置集成测试标签
 * - 配置性能测试标签
 */
private fun Project.configureTestCategories() {
    dependencies {
        // JUnit 5 标签支持
        "testImplementation"("org.junit.jupiter:junit-jupiter-api:5.10.0")
    }
    
    // 创建单元测试任务
    tasks.register<Test>("unitTest") {
        description = "Runs unit tests."
        group = "verification"
        
        useJUnitPlatform {
            includeTags("unit")
            excludeTags("integration", "performance", "memory")
        }
    }
    
    // 创建集成测试任务
    tasks.register<Test>("integrationTest") {
        description = "Runs integration tests."
        group = "verification"
        
        useJUnitPlatform {
            includeTags("integration")
        }
        
        // 集成测试可能需要更多时间
        systemProperty("junit.jupiter.execution.timeout.default", "60s")
    }
    
    // 配置主测试任务排除性能测试
    tasks.withType<Test>().named("test") {
        useJUnitPlatform {
            excludeTags("performance", "memory")
        }
    }
}