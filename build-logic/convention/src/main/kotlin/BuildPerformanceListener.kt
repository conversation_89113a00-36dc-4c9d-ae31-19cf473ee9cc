package com.yu.questicle.build.convention

import org.gradle.BuildAdapter
import org.gradle.BuildResult
import org.gradle.api.Project
import org.gradle.api.invocation.Gradle
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 构建性能监听器
 * 
 * 功能:
 * - 监控构建时间和性能指标
 * - 生成详细的性能报告
 * - 记录构建历史数据
 * - 提供性能趋势分析
 */
class BuildPerformanceListener(private val project: Project) : BuildAdapter() {
    
    private var buildStartTime: Long = 0
    private var configurationStartTime: Long = 0
    private var executionStartTime: Long = 0
    private val taskExecutionTimes = mutableMapOf<String, TaskExecutionInfo>()
    
    init {
        buildStartTime = System.currentTimeMillis()
        configurationStartTime = buildStartTime
        
        val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        project.logger.lifecycle("🏗️ Build started at ${dateFormat.format(Date())}")
        
        // 记录系统信息
        logSystemInfo()
    }
    
    override fun projectsEvaluated(gradle: Gradle) {
        val configurationTime = System.currentTimeMillis() - configurationStartTime
        executionStartTime = System.currentTimeMillis()
        
        project.logger.lifecycle("⚙️ Configuration completed in ${formatTime(configurationTime)}")
    }
    
    override fun buildFinished(result: BuildResult) {
        val totalBuildTime = System.currentTimeMillis() - buildStartTime
        val executionTime = System.currentTimeMillis() - executionStartTime
        val configurationTime = executionStartTime - configurationStartTime
        
        // 生成构建报告
        val buildReport = BuildPerformanceReport(
            timestamp = System.currentTimeMillis(),
            totalBuildTimeMs = totalBuildTime,
            configurationTimeMs = configurationTime,
            executionTimeMs = executionTime,
            success = result.failure == null,
            systemInfo = SystemResourceDetector.detect(),
            taskExecutions = taskExecutionTimes.values.toList(),
            gradleVersion = project.gradle.gradleVersion,
            javaVersion = System.getProperty("java.version")
        )
        
        // 输出构建结果
        logBuildResult(buildReport)
        
        // 保存性能报告
        saveBuildReport(buildReport)
        
        // 更新性能历史
        updatePerformanceHistory(buildReport)
    }
    
    private fun logSystemInfo() {
        val systemInfo = SystemResourceDetector.detect()
        project.logger.lifecycle("💻 System Info:")
        project.logger.lifecycle("   OS: ${systemInfo.osName}")
        project.logger.lifecycle("   CPU Cores: ${systemInfo.availableCores}")
        project.logger.lifecycle("   Available Memory: ${systemInfo.availableMemoryGB}GB")
        project.logger.lifecycle("   Java Version: ${System.getProperty("java.version")}")
        project.logger.lifecycle("   Gradle Version: ${project.gradle.gradleVersion}")
    }
    
    private fun logBuildResult(report: BuildPerformanceReport) {
        val status = if (report.success) "✅ SUCCESS" else "❌ FAILED"
        val totalTime = formatTime(report.totalBuildTimeMs)
        val configTime = formatTime(report.configurationTimeMs)
        val execTime = formatTime(report.executionTimeMs)
        
        project.logger.lifecycle("")
        project.logger.lifecycle("$status Build completed in $totalTime")
        project.logger.lifecycle("   Configuration: $configTime")
        project.logger.lifecycle("   Execution: $execTime")
        
        // 性能评估
        evaluatePerformance(report)
    }
    
    private fun evaluatePerformance(report: BuildPerformanceReport) {
        val totalMinutes = report.totalBuildTimeMs / 60000.0
        
        val performanceLevel = when {
            totalMinutes <= 3.0 -> "🚀 EXCELLENT"
            totalMinutes <= 5.0 -> "✅ GOOD"
            totalMinutes <= 8.0 -> "⚠️ ACCEPTABLE"
            totalMinutes <= 12.0 -> "🐌 SLOW"
            else -> "🔥 CRITICAL"
        }
        
        project.logger.lifecycle("   Performance: $performanceLevel (${String.format("%.1f", totalMinutes)}min)")
        
        // 性能建议
        if (totalMinutes > 8.0) {
            project.logger.lifecycle("💡 Performance Tips:")
            project.logger.lifecycle("   - Enable configuration cache: --configuration-cache")
            project.logger.lifecycle("   - Enable build cache: --build-cache")
            project.logger.lifecycle("   - Use parallel execution: --parallel")
        }
    }
    
    private fun saveBuildReport(report: BuildPerformanceReport) {
        try {
            val reportsDir = File(project.rootProject.buildDir, "reports/build-performance")
            reportsDir.mkdirs()
            
            val timestamp = SimpleDateFormat("yyyyMMdd-HHmmss", Locale.getDefault()).format(Date())
            val reportFile = File(reportsDir, "build-report-$timestamp.md")
            
            val reportContent = """
# Build Performance Report

**Generated:** ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date(report.timestamp))}
**Build Time:** ${formatTime(report.totalBuildTimeMs)}
**Status:** ${if (report.success) "SUCCESS" else "FAILED"}

## System Information
- **OS:** ${report.systemInfo.osName}
- **CPU Cores:** ${report.systemInfo.availableCores}
- **Available Memory:** ${report.systemInfo.availableMemoryGB}GB
- **Java Version:** ${report.javaVersion}
- **Gradle Version:** ${report.gradleVersion}

## Performance Breakdown
- **Total Build Time:** ${formatTime(report.totalBuildTimeMs)}
- **Configuration Time:** ${formatTime(report.configurationTimeMs)}
- **Execution Time:** ${formatTime(report.executionTimeMs)}

## Performance Rating
${getPerformanceRating(report.totalBuildTimeMs)}

---
*Report generated by BuildPerformancePlugin*
            """.trimIndent()
            
            reportFile.writeText(reportContent)
            project.logger.info("📊 Build report saved: ${reportFile.absolutePath}")
        } catch (e: Exception) {
            project.logger.warn("Failed to save build report: ${e.message}")
        }
    }
    
    private fun getPerformanceRating(totalTimeMs: Long): String {
        val minutes = totalTimeMs / 60000.0
        return when {
            minutes <= 3.0 -> "🚀 Excellent (${String.format("%.1f", minutes)}min)"
            minutes <= 5.0 -> "✅ Good (${String.format("%.1f", minutes)}min)"
            minutes <= 8.0 -> "⚠️ Acceptable (${String.format("%.1f", minutes)}min)"
            minutes <= 12.0 -> "🐌 Slow (${String.format("%.1f", minutes)}min)"
            else -> "🔥 Critical (${String.format("%.1f", minutes)}min)"
        }
    }
    
    private fun updatePerformanceHistory(report: BuildPerformanceReport) {
        try {
            val historyFile = File(project.rootProject.buildDir, "reports/build-performance/history.txt")
            historyFile.parentFile.mkdirs()
            
            // 简单的文本格式记录历史
            val newEntry = "${report.timestamp},${report.totalBuildTimeMs},${report.success},${report.gradleVersion},${report.javaVersion}"
            
            val existingEntries = if (historyFile.exists()) {
                historyFile.readLines().filter { it.isNotBlank() }
            } else {
                emptyList()
            }
            
            // 保留最近100次构建记录
            val allEntries = (existingEntries + newEntry).takeLast(100)
            historyFile.writeText(allEntries.joinToString("\n"))
            
            // 分析性能趋势
            analyzePerformanceTrend(allEntries)
            
        } catch (e: Exception) {
            project.logger.warn("Failed to update performance history: ${e.message}")
        }
    }
    
    private fun analyzePerformanceTrend(historyEntries: List<String>) {
        if (historyEntries.size < 5) return
        
        try {
            val recentBuilds = historyEntries.takeLast(5).mapNotNull { entry ->
                val parts = entry.split(",")
                if (parts.size >= 2) parts[1].toLongOrNull() else null
            }
            
            if (recentBuilds.isEmpty()) return
            
            val averageTime = recentBuilds.average()
            val averageMinutes = averageTime / 60000.0
            
            project.logger.lifecycle("📈 Recent Performance (last 5 builds):")
            project.logger.lifecycle("   Average build time: ${String.format("%.1f", averageMinutes)}min")
            
            // 趋势分析
            if (recentBuilds.size >= 4) {
                val firstHalf = recentBuilds.take(2).average()
                val secondHalf = recentBuilds.takeLast(2).average()
                
                val trend = when {
                    secondHalf < firstHalf * 0.9 -> "📈 IMPROVING"
                    secondHalf > firstHalf * 1.1 -> "📉 DEGRADING"
                    else -> "➡️ STABLE"
                }
                
                project.logger.lifecycle("   Trend: $trend")
            }
        } catch (e: Exception) {
            project.logger.warn("Failed to analyze performance trend: ${e.message}")
        }
    }
    
    private fun formatTime(timeMs: Long): String {
        val minutes = timeMs / 60000
        val seconds = (timeMs % 60000) / 1000
        val milliseconds = timeMs % 1000
        
        return when {
            minutes > 0 -> "${minutes}m ${seconds}s"
            seconds > 0 -> "${seconds}.${milliseconds / 100}s"
            else -> "${milliseconds}ms"
        }
    }
}

/**
 * 构建性能报告数据类
 */
data class BuildPerformanceReport(
    val timestamp: Long,
    val totalBuildTimeMs: Long,
    val configurationTimeMs: Long,
    val executionTimeMs: Long,
    val success: Boolean,
    val systemInfo: SystemInfo,
    val taskExecutions: List<TaskExecutionInfo>,
    val gradleVersion: String,
    val javaVersion: String
)

/**
 * 任务执行信息
 */
data class TaskExecutionInfo(
    val taskPath: String,
    val executionTimeMs: Long,
    val outcome: String
)