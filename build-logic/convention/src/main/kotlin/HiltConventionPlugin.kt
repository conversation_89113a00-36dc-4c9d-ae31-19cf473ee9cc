import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class HiltConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("dagger.hilt.android.plugin")
                // KSP is the modern replacement for KAPT
                apply("com.google.devtools.ksp")
            }

            dependencies {
                "implementation"("com.google.dagger:hilt-android:2.56.2")
                "ksp"("com.google.dagger:hilt-android-compiler:2.56.2")
                "kspAndroidTest"("com.google.dagger:hilt-android-compiler:2.56.2")
            }
        }
    }
}
