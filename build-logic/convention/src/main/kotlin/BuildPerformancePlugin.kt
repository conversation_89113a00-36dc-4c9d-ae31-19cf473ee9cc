package com.yu.questicle.build.convention

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.tasks.testing.Test
import org.gradle.kotlin.dsl.withType
import java.util.*

/**
 * 构建性能优化插件
 * 
 * 功能:
 * - 动态检测系统资源并优化配置
 * - 智能调整并行度和内存分配
 * - 提供构建性能监控
 * - 支持不同操作系统的优化策略
 */
class BuildPerformancePlugin : Plugin<Project> {
    
    override fun apply(target: Project) {
        target.configurePerformance()
        target.addPerformanceMonitoring()
    }
}

/**
 * 配置构建性能优化 - 仅在根项目执行，避免重复配置
 */
private fun Project.configurePerformance() {
    // 只在根项目配置，避免子项目重复执行
    if (this != rootProject) return
    
    val systemInfo = SystemResourceDetector.detect()
    val config = PerformanceConfig.create(systemInfo)
    
    // 简化日志输出，只在根项目输出一次
    logger.lifecycle("🚀 Build Performance Optimization Applied")
    logger.lifecycle("   System: ${systemInfo.osName} | CPU: ${systemInfo.availableCores} cores | Memory: ${systemInfo.availableMemoryGB}GB")
    logger.lifecycle("   Optimized Settings: ${config.parallelForks} parallel forks | ${config.heapSize} heap")
    
    // 配置所有子项目的测试任务
    allprojects {
        tasks.withType<Test>().configureEach {
            maxParallelForks = config.parallelForks
            
            jvmArgs(
                "-Xmx${config.heapSize}",
                "-Xms${config.initialHeapSize}",
                "-XX:MaxMetaspaceSize=${config.metaspaceSize}",
                "-XX:+UseG1GC",
                "-XX:MaxGCPauseMillis=100",
                "-XX:+UseStringDeduplication"
            )
            
            // 系统属性优化
            systemProperties(mapOf(
                "junit.jupiter.execution.parallel.enabled" to "true",
                "junit.jupiter.execution.parallel.mode.default" to "concurrent",
                "junit.jupiter.execution.parallel.mode.classes.default" to "concurrent",
                "file.encoding" to "UTF-8"
            ))
            
            // 测试输出优化
            testLogging {
                events("passed", "skipped", "failed")
                showStandardStreams = false
                showExceptions = true
                showCauses = true
                showStackTraces = true
            }
        }
    }
}

/**
 * 添加构建性能监控
 * 注意：配置缓存兼容性 - 移除BuildListener以支持配置缓存
 */
private fun Project.addPerformanceMonitoring() {
    // 配置缓存兼容：使用任务级别的监控而不是全局BuildListener
    tasks.register("buildPerformanceReport") {
        doLast {
            logger.lifecycle("📊 Build performance monitoring completed")
        }
    }
}

/**
 * 系统资源检测器
 */
object SystemResourceDetector {
    
    fun detect(): SystemInfo {
        val runtime = Runtime.getRuntime()
        val osName = System.getProperty("os.name")
        val availableCores = runtime.availableProcessors()
        val maxMemory = runtime.maxMemory()
        val availableMemoryGB = maxMemory / (1024 * 1024 * 1024)
        
        return SystemInfo(
            osName = osName,
            availableCores = availableCores,
            maxMemoryBytes = maxMemory,
            availableMemoryGB = availableMemoryGB.toInt()
        )
    }
}

/**
 * 系统信息数据类
 */
data class SystemInfo(
    val osName: String,
    val availableCores: Int,
    val maxMemoryBytes: Long,
    val availableMemoryGB: Int
)

/**
 * 性能配置数据类
 */
data class PerformanceConfig(
    val parallelForks: Int,
    val heapSize: String,
    val initialHeapSize: String,
    val metaspaceSize: String
) {
    companion object {
        fun create(systemInfo: SystemInfo): PerformanceConfig {
            val parallelForks = calculateOptimalParallelForks(systemInfo)
            val heapSize = calculateOptimalHeapSize(systemInfo)
            val initialHeapSize = calculateInitialHeapSize(heapSize)
            val metaspaceSize = calculateMetaspaceSize(systemInfo)
            
            return PerformanceConfig(
                parallelForks = parallelForks,
                heapSize = heapSize,
                initialHeapSize = initialHeapSize,
                metaspaceSize = metaspaceSize
            )
        }
        
        private fun calculateOptimalParallelForks(systemInfo: SystemInfo): Int {
            return when {
                systemInfo.osName.contains("Mac", ignoreCase = true) -> {
                    // macOS优化策略
                    when {
                        systemInfo.availableCores >= 12 -> minOf(systemInfo.availableCores, 10)
                        systemInfo.availableCores >= 8 -> minOf(systemInfo.availableCores, 8)
                        systemInfo.availableCores >= 4 -> minOf(systemInfo.availableCores, 6)
                        else -> 2
                    }
                }
                systemInfo.osName.contains("Windows", ignoreCase = true) -> {
                    // Windows优化策略
                    when {
                        systemInfo.availableCores >= 8 -> minOf(systemInfo.availableCores / 2, 6)
                        systemInfo.availableCores >= 4 -> minOf(systemInfo.availableCores / 2, 4)
                        else -> 2
                    }
                }
                else -> {
                    // Linux和其他系统
                    when {
                        systemInfo.availableCores >= 8 -> minOf(systemInfo.availableCores / 2, 8)
                        systemInfo.availableCores >= 4 -> minOf(systemInfo.availableCores / 2, 4)
                        else -> 2
                    }
                }
            }
        }
        
        private fun calculateOptimalHeapSize(systemInfo: SystemInfo): String {
            return when {
                systemInfo.availableMemoryGB >= 32 -> "12g"
                systemInfo.availableMemoryGB >= 16 -> "8g"
                systemInfo.availableMemoryGB >= 8 -> "4g"
                systemInfo.availableMemoryGB >= 4 -> "2g"
                else -> "1g"
            }
        }
        
        private fun calculateInitialHeapSize(heapSize: String): String {
            val heapSizeNum = heapSize.replace("g", "").toInt()
            val initialSize = maxOf(heapSizeNum / 4, 1)
            return "${initialSize}g"
        }
        
        private fun calculateMetaspaceSize(systemInfo: SystemInfo): String {
            return when {
                systemInfo.availableMemoryGB >= 16 -> "2g"
                systemInfo.availableMemoryGB >= 8 -> "1g"
                else -> "512m"
            }
        }
    }
}