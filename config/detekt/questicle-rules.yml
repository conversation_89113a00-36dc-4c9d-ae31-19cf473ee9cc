# Questicle项目自定义Detekt规则
# 针对游戏开发和Android架构的特殊要求

questicle-custom:
  active: true
  
  # 游戏性能相关规则
  GamePerformanceRules:
    active: true
    # 避免在游戏循环中创建对象
    NoObjectCreationInGameLoop:
      active: true
      excludes: ['**/test/**']
    # 限制递归深度
    MaxRecursionDepth:
      active: true
      maxDepth: 10
    # 避免频繁的字符串拼接
    AvoidStringConcatenationInLoop:
      active: true
      
  # Android架构规则
  AndroidArchitectureRules:
    active: true
    # ViewModel不应持有Context引用
    ViewModelShouldNotHoldContext:
      active: true
    # Repository应该是单例
    RepositoryShouldBeSingleton:
      active: true
    # UseCase应该有单一职责
    UseCaseSingleResponsibility:
      active: true
      maxPublicMethods: 3
      
  # Compose相关规则
  ComposeRules:
    active: true
    # Composable函数命名规范
    ComposableNaming:
      active: true
      pattern: '^[A-Z][a-zA-Z0-9]*$'
    # 避免在Composable中使用副作用
    NoSideEffectsInComposable:
      active: true
    # Preview函数应该是私有的
    PreviewFunctionsShouldBePrivate:
      active: true
      
  # 测试相关规则
  TestingRules:
    active: true
    # 测试函数命名规范
    TestFunctionNaming:
      active: true
      pattern: '^(test|should)[A-Z].*$'
    # 测试类应该以Test结尾
    TestClassNaming:
      active: true
      pattern: '.*Test$'
    # 避免在测试中使用硬编码值
    NoHardcodedValuesInTests:
      active: true
      excludes: ['**/src/main/**']