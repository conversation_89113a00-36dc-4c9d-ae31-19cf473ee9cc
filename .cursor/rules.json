{"ignore": ["build/", ".gradle/", ".idea/", "captures/", "local.properties", "*.iml", "*.apk", "*.ap_", "*.dex", "*.class", "*.log", "*.tmp", "*.DS_Store", "test-results/", "outputs/", ".cxx/", ".externalNativeBuild/", ".compose-cache/", "roomSchemas/", "docs/", "temp/"], "index": ["app/src/main/java/", "app/src/main/kotlin/", "core/", "feature/"], "languages": ["kotlin", "java", "xml"], "jdk": {"version": "21"}, "kotlin": {"version": "2.1.21"}, "gradle": {"version": "8.14"}, "formatting": {"kotlin": {"style": "official", "maxLineLength": 120, "indentSize": 4}}, "lint": {"kotlin": {"enabled": true, "rules": ["no-unused-vars", "no-wildcard-imports", "explicit-types", "no-magic-numbers"]}}, "ai_assist": {"enabled": true, "contextDepth": 500, "preferCleanArchitecture": true, "androidCompose": true}, "project_rules": {"no_duplicate_files": true, "no_simplified_versions": true}}