{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "files.watcherExclude": {"**/build/**": true, "**/.gradle/**": true, "**/.idea/**": true, "**/.git/**": true, "**/temp/**": true, "**/.cxx/**": true, "**/.externalNativeBuild/**": true}, "search.exclude": {"**/build/**": true, "**/.gradle/**": true, "**/.idea/**": true, "**/.git/**": true, "**/temp/**": true, "**/.cxx/**": true, "**/.externalNativeBuild/**": true}}