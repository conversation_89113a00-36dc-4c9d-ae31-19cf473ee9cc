# 🔄 Pull Request

## 📋 Description
<!-- 简要描述这个PR的目的和内容 -->

## 🎯 Type of Change
<!-- 请选择适用的选项 -->
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Configuration change
- [ ] 🧪 Test improvement
- [ ] ♻️ Code refactoring

## 🧪 Testing
<!-- 描述你如何测试了这些变更 -->
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] No new warnings or errors

## 📝 Checklist
<!-- 请确认以下项目 -->
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes

## 📊 Performance Impact
<!-- 如果适用，描述性能影响 -->
- [ ] No performance impact
- [ ] Performance improved
- [ ] Performance impact analyzed and acceptable
- [ ] Performance benchmarks included

## 📱 Screenshots/Videos
<!-- 如果适用，添加截图或视频 -->

## 🔗 Related Issues
<!-- 链接相关的issue -->
Closes #
Related to #

## 📋 Additional Notes
<!-- 任何额外的信息或上下文 -->