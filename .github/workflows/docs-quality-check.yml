# .github/workflows/docs-quality-check.yml

name: 'Docs Quality Check'

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    paths:
      - 'documents/**.md'
      - '.github/workflows/docs-quality-check.yml'

jobs:
  markdown-lint:
    name: 'Mark<PERSON> Lin<PERSON>'
    runs-on: ubuntu-latest

    steps:
      - name: 'Checkout code'
        uses: actions/checkout@v4

      - name: 'Install Node.js'
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: 'Install markdownlint-cli'
        run: npm install -g markdownlint-cli

      - name: 'Run markdownlint'
        run: markdownlint --config .markdownlint.jsonc 'documents/**/*.md'

  link-checker:
    name: 'Link Checker'
    runs-on: ubuntu-latest
    needs: markdown-lint

    steps:
      - name: 'Checkout code'
        uses: actions/checkout@v4

      - name: 'Run Lychee Link Checker'
        uses: lycheeverse/lychee-action@v1
        with:
          args: --verbose --no-progress './documents/**/*.md'
          # Accept both success and minor failures (e.g. 429 too many requests)
          # Fails on major failures (e.g. 404 not found)
          fail: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 