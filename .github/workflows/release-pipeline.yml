name: 🚀 Release Pipeline

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string
      release_type:
        description: 'Release type'
        required: true
        type: choice
        options:
          - alpha
          - beta
          - release
        default: 'beta'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2

jobs:
  # ============================================================================
  # 发布前检查
  # ============================================================================
  pre-release-checks:
    name: 🔍 Pre-Release Checks
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      release_type: ${{ steps.version.outputs.release_type }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: 🏷️ Determine Version
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          VERSION="${{ github.event.inputs.version }}"
          RELEASE_TYPE="${{ github.event.inputs.release_type }}"
        else
          VERSION="${{ github.ref_name }}"
          if [[ "$VERSION" == *"alpha"* ]]; then
            RELEASE_TYPE="alpha"
          elif [[ "$VERSION" == *"beta"* ]]; then
            RELEASE_TYPE="beta"
          else
            RELEASE_TYPE="release"
          fi
        fi
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "release_type=$RELEASE_TYPE" >> $GITHUB_OUTPUT
        
        echo "## 🏷️ Release Information" >> $GITHUB_STEP_SUMMARY
        echo "- **Version:** $VERSION" >> $GITHUB_STEP_SUMMARY
        echo "- **Type:** $RELEASE_TYPE" >> $GITHUB_STEP_SUMMARY
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # 质量检查
    # ------------------------------------------------------------------------
    - name: 🔍 Run Quality Checks
      run: |
        ./gradlew detekt ktlintCheck --configuration-cache --parallel
        
    - name: 🧪 Run Tests
      run: |
        ./gradlew testDemoDebugUnitTest --configuration-cache --parallel
        
    - name: ✅ Pre-Release Validation Complete
      run: |
        echo "## ✅ Pre-Release Checks Passed" >> $GITHUB_STEP_SUMMARY
        echo "All quality checks and tests have passed successfully." >> $GITHUB_STEP_SUMMARY

  # ============================================================================
  # APK构建
  # ============================================================================
  build-apk:
    name: 🏗️ Build APK
    runs-on: ubuntu-latest
    needs: pre-release-checks
    strategy:
      matrix:
        variant: [demo, prod]
        build-type: [debug, release]
        exclude:
          - variant: prod
            build-type: debug
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # 签名配置 (仅Release版本)
    # ------------------------------------------------------------------------
    - name: 🔐 Setup Keystore
      if: matrix.build-type == 'release'
      run: |
        echo "${{ secrets.KEYSTORE_BASE64 }}" | base64 -d > app/keystore.jks
        echo "KEYSTORE_PATH=keystore.jks" >> $GITHUB_ENV
        echo "KEYSTORE_PASSWORD=${{ secrets.KEYSTORE_PASSWORD }}" >> $GITHUB_ENV
        echo "KEY_ALIAS=${{ secrets.KEY_ALIAS }}" >> $GITHUB_ENV
        echo "KEY_PASSWORD=${{ secrets.KEY_PASSWORD }}" >> $GITHUB_ENV
        
    # ------------------------------------------------------------------------
    # APK构建
    # ------------------------------------------------------------------------
    - name: 🏗️ Build APK (${{ matrix.variant }}-${{ matrix.build-type }})
      run: |
        if [[ "${{ matrix.build-type }}" == "release" ]]; then
          ./gradlew assemble${{ matrix.variant | title }}${{ matrix.build-type | title }} \
            --configuration-cache \
            --parallel \
            --no-daemon \
            -Pandroid.injected.signing.store.file=$KEYSTORE_PATH \
            -Pandroid.injected.signing.store.password=$KEYSTORE_PASSWORD \
            -Pandroid.injected.signing.key.alias=$KEY_ALIAS \
            -Pandroid.injected.signing.key.password=$KEY_PASSWORD
        else
          ./gradlew assemble${{ matrix.variant | title }}${{ matrix.build-type | title }} \
            --configuration-cache \
            --parallel \
            --no-daemon
        fi
        
    # ------------------------------------------------------------------------
    # APK信息提取
    # ------------------------------------------------------------------------
    - name: 📊 Extract APK Info
      id: apk_info
      run: |
        APK_PATH=$(find app/build/outputs/apk -name "*.apk" | head -1)
        APK_SIZE=$(stat -f%z "$APK_PATH" 2>/dev/null || stat -c%s "$APK_PATH")
        APK_SIZE_MB=$(echo "scale=2; $APK_SIZE / 1024 / 1024" | bc)
        
        echo "apk_path=$APK_PATH" >> $GITHUB_OUTPUT
        echo "apk_size_mb=$APK_SIZE_MB" >> $GITHUB_OUTPUT
        
        echo "## 📱 APK Build Info (${{ matrix.variant }}-${{ matrix.build-type }})" >> $GITHUB_STEP_SUMMARY
        echo "- **Size:** ${APK_SIZE_MB}MB" >> $GITHUB_STEP_SUMMARY
        echo "- **Path:** $APK_PATH" >> $GITHUB_STEP_SUMMARY
        
    # ------------------------------------------------------------------------
    # APK上传
    # ------------------------------------------------------------------------
    - name: 📤 Upload APK
      uses: actions/upload-artifact@v4
      with:
        name: apk-${{ matrix.variant }}-${{ matrix.build-type }}-${{ needs.pre-release-checks.outputs.version }}
        path: |
          app/build/outputs/apk/**/*.apk
          app/build/outputs/mapping/**/mapping.txt
        retention-days: 30
        
    # ------------------------------------------------------------------------
    # 清理敏感文件
    # ------------------------------------------------------------------------
    - name: 🧹 Cleanup Keystore
      if: always() && matrix.build-type == 'release'
      run: |
        rm -f app/keystore.jks

  # ============================================================================
  # GitHub Release创建
  # ============================================================================
  create-release:
    name: 🎉 Create GitHub Release
    runs-on: ubuntu-latest
    needs: [pre-release-checks, build-apk]
    if: needs.pre-release-checks.outputs.release_type != 'alpha'
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: 📥 Download APK Artifacts
      uses: actions/download-artifact@v4
      with:
        pattern: apk-*-${{ needs.pre-release-checks.outputs.version }}
        path: ./artifacts
        merge-multiple: true
        
    # ------------------------------------------------------------------------
    # 生成发布说明
    # ------------------------------------------------------------------------
    - name: 📝 Generate Release Notes
      id: release_notes
      run: |
        VERSION="${{ needs.pre-release-checks.outputs.version }}"
        RELEASE_TYPE="${{ needs.pre-release-checks.outputs.release_type }}"
        
        # 获取上一个标签
        PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
        
        # 生成变更日志
        if [[ -n "$PREVIOUS_TAG" ]]; then
          CHANGELOG=$(git log --pretty=format:"- %s" $PREVIOUS_TAG..HEAD)
        else
          CHANGELOG="- Initial release"
        fi
        
        # 创建发布说明
        cat > release_notes.md << EOF
        # Questicle $VERSION
        
        ## 📱 What's New
        
        $CHANGELOG
        
        ## 📦 Downloads
        
        - **Demo Debug APK**: For testing and development
        - **Demo Release APK**: Signed demo version
        - **Prod Release APK**: Production version (if available)
        
        ## 🔧 Technical Details
        
        - **Build Date**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        - **Commit**: ${{ github.sha }}
        - **Release Type**: $RELEASE_TYPE
        
        ## 📋 Installation
        
        1. Download the appropriate APK file
        2. Enable "Install from Unknown Sources" in Android settings
        3. Install the APK file
        
        ---
        
        **Note**: This is a $RELEASE_TYPE release. Please report any issues on our [GitHub Issues](https://github.com/${{ github.repository }}/issues) page.
        EOF
        
        echo "release_notes_path=release_notes.md" >> $GITHUB_OUTPUT
        
    # ------------------------------------------------------------------------
    # 创建GitHub Release
    # ------------------------------------------------------------------------
    - name: 🎉 Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ needs.pre-release-checks.outputs.version }}
        name: Questicle ${{ needs.pre-release-checks.outputs.version }}
        body_path: ${{ steps.release_notes.outputs.release_notes_path }}
        draft: false
        prerelease: ${{ needs.pre-release-checks.outputs.release_type != 'release' }}
        files: |
          ./artifacts/**/*.apk
          ./artifacts/**/mapping.txt
        token: ${{ secrets.GITHUB_TOKEN }}

  # ============================================================================
  # 部署后验证
  # ============================================================================
  post-deployment:
    name: ✅ Post-Deployment Verification
    runs-on: ubuntu-latest
    needs: [pre-release-checks, build-apk, create-release]
    if: always()
    
    steps:
    - name: 📊 Deployment Summary
      run: |
        echo "## 🚀 Release Pipeline Summary" >> $GITHUB_STEP_SUMMARY
        echo "| Stage | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Pre-Release Checks | ${{ needs.pre-release-checks.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| APK Build | ${{ needs.build-apk.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| GitHub Release | ${{ needs.create-release.result == 'success' && '✅ Pass' || needs.create-release.result == 'skipped' && '⏭️ Skip' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ needs.pre-release-checks.result }}" == "success" && "${{ needs.build-apk.result }}" == "success" ]]; then
          echo "## 🎉 Release Successful!" >> $GITHUB_STEP_SUMMARY
          echo "Version ${{ needs.pre-release-checks.outputs.version }} has been successfully released." >> $GITHUB_STEP_SUMMARY
        else
          echo "## ❌ Release Failed" >> $GITHUB_STEP_SUMMARY
          echo "The release pipeline encountered errors. Please check the logs above." >> $GITHUB_STEP_SUMMARY
        fi