name: 🧪 Test Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2

jobs:
  # ============================================================================
  # 单元测试覆盖率
  # ============================================================================
  unit-test-coverage:
    name: 🧪 Unit Test Coverage
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          .gradle/
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties', '**/libs.versions.toml') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # 单元测试执行
    # ------------------------------------------------------------------------
    - name: 🧪 Run Unit Tests with Coverage
      run: |
        ./gradlew testDemoDebugUnitTest jacocoTestReport \
          --configuration-cache \
          --parallel \
          --continue \
          --stacktrace
          
    # ------------------------------------------------------------------------
    # 覆盖率报告生成
    # ------------------------------------------------------------------------
    - name: 📊 Generate Coverage Badge
      id: jacoco
      uses: cicirello/jacoco-badge-generator@v2
      with:
        generate-branches-badge: true
        jacoco-csv-file: build/reports/jacoco/test/jacocoTestReport.csv
        
    - name: 📈 Upload Coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        files: |
          build/reports/jacoco/test/jacocoTestReport.xml
          */build/reports/jacoco/test/jacocoTestReport.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
        verbose: true
        
    - name: 📊 Coverage Summary
      run: |
        echo "## 📊 Test Coverage Summary" >> $GITHUB_STEP_SUMMARY
        echo "| Metric | Coverage |" >> $GITHUB_STEP_SUMMARY
        echo "|--------|----------|" >> $GITHUB_STEP_SUMMARY
        echo "| Instructions | ${{ steps.jacoco.outputs.coverage }}% |" >> $GITHUB_STEP_SUMMARY
        echo "| Branches | ${{ steps.jacoco.outputs.branches }}% |" >> $GITHUB_STEP_SUMMARY
        
        # 检查覆盖率阈值
        COVERAGE=$(echo "${{ steps.jacoco.outputs.coverage }}" | cut -d'%' -f1)
        if (( $(echo "$COVERAGE >= 80" | bc -l) )); then
          echo "✅ Coverage meets minimum threshold (80%)" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ Coverage below minimum threshold (80%)" >> $GITHUB_STEP_SUMMARY
        fi
        
    # ------------------------------------------------------------------------
    # 测试报告上传
    # ------------------------------------------------------------------------
    - name: 📊 Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2
      if: always()
      with:
        files: |
          **/build/test-results/**/*.xml
        check_name: "Unit Test Results"
        comment_mode: create new
        
    - name: 📤 Upload Test Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-coverage-reports
        path: |
          build/reports/tests/
          */build/reports/tests/
          build/reports/jacoco/
          */build/reports/jacoco/
        retention-days: 7

  # ============================================================================
  # 集成测试
  # ============================================================================
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        api-level: [29, 34]
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # Android模拟器设置
    # ------------------------------------------------------------------------
    - name: 📱 Enable KVM group perms
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm
        
    - name: 🤖 AVD cache
      uses: actions/cache@v4
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}
        
    - name: 📱 Create AVD and generate snapshot for caching
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: google_apis
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."
        
    # ------------------------------------------------------------------------
    # 集成测试执行
    # ------------------------------------------------------------------------
    - name: 🧪 Run Integration Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: google_apis
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          ./gradlew connectedDemoDebugAndroidTest \
            --configuration-cache \
            --continue \
            --stacktrace
            
    - name: 📤 Upload Integration Test Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-reports-api${{ matrix.api-level }}
        path: |
          app/build/reports/androidTests/
          */build/reports/androidTests/
        retention-days: 7

  # ============================================================================
  # 测试结果汇总
  # ============================================================================
  test-summary:
    name: 📊 Test Summary
    runs-on: ubuntu-latest
    needs: [unit-test-coverage, integration-tests]
    if: always()
    
    steps:
    - name: 📊 Generate Test Summary
      run: |
        echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ${{ needs.unit-test-coverage.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Integration Tests | ${{ needs.integration-tests.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ needs.unit-test-coverage.result }}" == "success" && "${{ needs.integration-tests.result }}" == "success" ]]; then
          echo "## 🎉 All Tests Passed!" >> $GITHUB_STEP_SUMMARY
          echo "Both unit tests and integration tests have passed successfully." >> $GITHUB_STEP_SUMMARY
        else
          echo "## ❌ Test Failures Detected" >> $GITHUB_STEP_SUMMARY
          echo "One or more test suites have failed. Please review the results above." >> $GITHUB_STEP_SUMMARY
        fi