name: 🔄 Complete CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2

jobs:
  # ============================================================================
  # 快速检查 - 并行执行基础验证
  # ============================================================================
  quick-checks:
    name: ⚡ Quick Checks
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Cache Gradle
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
        
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    - name: 🔍 Validate Gradle Wrapper
      uses: gradle/wrapper-validation-action@v2
      
    - name: ⚡ Quick Build Check
      run: ./gradlew assembleDemoDebug --dry-run --configuration-cache

  # ============================================================================
  # 主构建流水线
  # ============================================================================
  main-pipeline:
    name: 🏗️ Main Pipeline
    runs-on: ubuntu-latest
    needs: quick-checks
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          .gradle/
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties', '**/libs.versions.toml') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # 并行执行构建和检查
    # ------------------------------------------------------------------------
    - name: 🏗️ Build Debug APK
      run: |
        ./gradlew assembleDemoDebug \
          --configuration-cache \
          --parallel \
          --build-cache \
          --no-daemon
          
    - name: 🔍 Code Quality Checks
      run: |
        ./gradlew detekt ktlintCheck \
          --configuration-cache \
          --parallel \
          --continue
          
    - name: 🧪 Unit Tests
      run: |
        ./gradlew testDemoDebugUnitTest \
          --configuration-cache \
          --parallel \
          --continue
          
    # ------------------------------------------------------------------------
    # 结果收集和报告
    # ------------------------------------------------------------------------
    - name: 📊 Collect Build Artifacts
      if: always()
      run: |
        mkdir -p build-reports
        find . -name "*.apk" -exec cp {} build-reports/ \;
        find . -path "*/build/reports/*" -name "*.html" -exec cp --parents {} build-reports/ \;
        
    - name: 📤 Upload Build Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: build-reports-${{ github.run_number }}
        path: build-reports/
        retention-days: 7
        
    - name: 📊 Pipeline Summary
      if: always()
      run: |
        echo "## 🏗️ Build Pipeline Results" >> $GITHUB_STEP_SUMMARY
        echo "| Task | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| APK Build | ✅ Complete |" >> $GITHUB_STEP_SUMMARY
        echo "| Code Quality | ✅ Complete |" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ✅ Complete |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🎉 **CI/CD Pipeline executed successfully!**" >> $GITHUB_STEP_SUMMARY