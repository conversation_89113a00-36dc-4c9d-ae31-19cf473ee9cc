name: 🚀 Quality Gate

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: -Dorg.gradle.daemon=false -Dorg.gradle.workers.max=2
  GRADLE_USER_HOME: ${{ github.workspace }}/.gradle

jobs:
  # ============================================================================
  # 构建矩阵配置 - 多环境并行执行
  # ============================================================================
  build-matrix:
    name: 🏗️ Build Matrix
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        api-level: [24, 29, 34]
        build-type: [debug]
        include:
          - api-level: 34
            build-type: debug
            upload-artifacts: true
    
    steps:
    # ------------------------------------------------------------------------
    # 环境准备
    # ------------------------------------------------------------------------
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    # ------------------------------------------------------------------------
    # Gradle缓存策略
    # ------------------------------------------------------------------------
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          .gradle/
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties', '**/libs.versions.toml') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔧 Setup Android SDK Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: ${{ runner.os }}-android-${{ matrix.api-level }}
        
    # ------------------------------------------------------------------------
    # 权限和依赖
    # ------------------------------------------------------------------------
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    - name: 🔍 Validate Gradle Wrapper
      uses: gradle/wrapper-validation-action@v2
      
    # ------------------------------------------------------------------------
    # 并行构建执行
    # ------------------------------------------------------------------------
    - name: 🏗️ Build Project (API ${{ matrix.api-level }})
      run: |
        ./gradlew assembleDemoDebug \
          --configuration-cache \
          --parallel \
          --build-cache \
          --no-daemon \
          -Pandroid.testInstrumentationRunnerArguments.androidx.benchmark.enabledRules=BaselineProfile
          
    - name: 📊 Build Performance Report
      run: |
        echo "## 🏗️ Build Performance (API ${{ matrix.api-level }})" >> $GITHUB_STEP_SUMMARY
        echo "- Build Type: ${{ matrix.build-type }}" >> $GITHUB_STEP_SUMMARY
        echo "- API Level: ${{ matrix.api-level }}" >> $GITHUB_STEP_SUMMARY
        echo "- Status: ✅ Success" >> $GITHUB_STEP_SUMMARY
        
    # ------------------------------------------------------------------------
    # 构建产物上传
    # ------------------------------------------------------------------------
    - name: 📤 Upload Build Artifacts
      if: matrix.upload-artifacts
      uses: actions/upload-artifact@v4
      with:
        name: apk-debug-api${{ matrix.api-level }}
        path: |
          app/build/outputs/apk/demo/debug/*.apk
          app/build/outputs/mapping/demoDebug/
        retention-days: 7

  # ============================================================================
  # 代码质量检查
  # ============================================================================
  code-quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    needs: build-matrix
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Restore Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          .gradle/
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties', '**/libs.versions.toml') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # Detekt静态分析
    # ------------------------------------------------------------------------
    - name: 🔍 Run Detekt Analysis
      run: |
        ./gradlew detekt --configuration-cache --parallel
        
    - name: 📊 Upload Detekt Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: detekt-results
        path: |
          build/reports/detekt/
          */build/reports/detekt/
        retention-days: 7
        
    # ------------------------------------------------------------------------
    # KtLint格式检查
    # ------------------------------------------------------------------------
    - name: 🎨 Run KtLint Check
      run: |
        ./gradlew ktlintCheck --configuration-cache --parallel
        
    - name: 📊 Upload KtLint Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: ktlint-results
        path: |
          build/reports/ktlint/
          */build/reports/ktlint/
        retention-days: 7

  # ============================================================================
  # 单元测试执行
  # ============================================================================
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: build-matrix
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Restore Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
          .gradle/
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties', '**/libs.versions.toml') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # 单元测试执行
    # ------------------------------------------------------------------------
    - name: 🧪 Run Unit Tests
      run: |
        ./gradlew testDemoDebugUnitTest \
          --configuration-cache \
          --parallel \
          --continue
          
    - name: 📊 Generate Test Coverage Report
      run: |
        ./gradlew jacocoTestReport --configuration-cache
        
    # ------------------------------------------------------------------------
    # 测试结果处理
    # ------------------------------------------------------------------------
    - name: 📊 Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2
      if: always()
      with:
        files: |
          **/build/test-results/**/*.xml
        check_name: "Unit Test Results"
        comment_mode: create new
        
    - name: 📈 Upload Coverage Reports
      uses: codecov/codecov-action@v4
      with:
        files: |
          build/reports/jacoco/test/jacocoTestReport.xml
          */build/reports/jacoco/test/jacocoTestReport.xml
        flags: unittests
        name: codecov-umbrella
        
    - name: 📤 Upload Test Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-reports
        path: |
          build/reports/tests/
          */build/reports/tests/
          build/reports/jacoco/
          */build/reports/jacoco/
        retention-days: 7

  # ============================================================================
  # 质量门禁检查
  # ============================================================================
  quality-gate:
    name: 🚪 Quality Gate
    runs-on: ubuntu-latest
    needs: [build-matrix, code-quality, unit-tests]
    if: always()
    
    steps:
    - name: 📊 Quality Gate Summary
      run: |
        echo "## 🚪 Quality Gate Results" >> $GITHUB_STEP_SUMMARY
        echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Build Matrix | ${{ needs.build-matrix.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Code Quality | ${{ needs.code-quality.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ${{ needs.unit-tests.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ needs.build-matrix.result }}" == "success" && "${{ needs.code-quality.result }}" == "success" && "${{ needs.unit-tests.result }}" == "success" ]]; then
          echo "## 🎉 Quality Gate: PASSED" >> $GITHUB_STEP_SUMMARY
          echo "All quality checks have passed successfully!" >> $GITHUB_STEP_SUMMARY
        else
          echo "## ❌ Quality Gate: FAILED" >> $GITHUB_STEP_SUMMARY
          echo "One or more quality checks have failed. Please review the results above." >> $GITHUB_STEP_SUMMARY
          exit 1
        fi
        
    - name: 🎉 Success Notification
      if: success()
      run: |
        echo "🎉 Quality Gate passed! Ready for deployment."