name: 🔒 Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每周一凌晨2点运行安全扫描
    - cron: '0 2 * * 1'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ============================================================================
  # 依赖安全扫描
  # ============================================================================
  dependency-scan:
    name: 🔍 Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    # ------------------------------------------------------------------------
    # Gradle依赖检查
    # ------------------------------------------------------------------------
    - name: 🔍 Run Dependency Check
      run: |
        ./gradlew dependencyUpdates --configuration-cache
        
    - name: 📊 Upload Dependency Report
      uses: actions/upload-artifact@v4
      with:
        name: dependency-updates-report
        path: build/dependencyUpdates/
        retention-days: 7
        
    # ------------------------------------------------------------------------
    # OWASP依赖检查
    # ------------------------------------------------------------------------
    - name: 🛡️ OWASP Dependency Check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'Questicle'
        path: '.'
        format: 'ALL'
        args: >
          --enableRetired
          --enableExperimental
          --failOnCVSS 7
          --suppression suppression.xml
          
    - name: 📊 Upload OWASP Results
      uses: actions/upload-artifact@v4
      with:
        name: owasp-dependency-check-results
        path: reports/
        retention-days: 7

  # ============================================================================
  # 代码安全扫描
  # ============================================================================
  code-security:
    name: 🔒 Code Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    # ------------------------------------------------------------------------
    # CodeQL分析
    # ------------------------------------------------------------------------
    - name: 🔍 Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: java
        
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 📦 Setup Gradle Cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: 🔑 Grant Execute Permission
      run: chmod +x gradlew
      
    - name: 🏗️ Build for CodeQL
      run: |
        ./gradlew assembleDemoDebug --configuration-cache --no-daemon
        
    - name: 🔍 Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:java"

  # ============================================================================
  # 安全报告汇总
  # ============================================================================
  security-summary:
    name: 📊 Security Summary
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-security]
    if: always()
    
    steps:
    - name: 📊 Generate Security Summary
      run: |
        echo "## 🔒 Security Scan Results" >> $GITHUB_STEP_SUMMARY
        echo "| Scan Type | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Dependency Scan | ${{ needs.dependency-scan.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Code Security | ${{ needs.code-security.result == 'success' && '✅ Pass' || '❌ Fail' }} |" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ needs.dependency-scan.result }}" == "success" && "${{ needs.code-security.result }}" == "success" ]]; then
          echo "## 🎉 Security Status: SECURE" >> $GITHUB_STEP_SUMMARY
          echo "All security scans have passed successfully!" >> $GITHUB_STEP_SUMMARY
        else
          echo "## ⚠️ Security Status: ATTENTION REQUIRED" >> $GITHUB_STEP_SUMMARY
          echo "One or more security scans have detected issues. Please review the results." >> $GITHUB_STEP_SUMMARY
        fi