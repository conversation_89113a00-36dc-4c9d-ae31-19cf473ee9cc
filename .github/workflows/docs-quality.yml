name: Documentation Quality Check

on:
  push:
    paths:
      - 'documents/**/*.md'
      - '.github/workflows/docs-quality.yml'
  pull_request:
    paths:
      - 'documents/**/*.md'
      - '.github/workflows/docs-quality.yml'

jobs:
  docs-quality:
    name: Documentation Quality Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          npm install -g markdownlint-cli2
          npm install -g markdown-link-check
          npm install -g cspell
          npm install -g textlint
          
      - name: Check Markdown format
        run: |
          echo "Checking Markdown format..."
          npx markdownlint-cli2 "documents/**/*.md" --fix
          
      - name: Check links
        run: |
          echo "Checking document links..."
          npx markdown-link-check "documents/**/*.md" --config .markdown-link-check.json
          
      - name: Check spelling
        run: |
          echo "Checking spelling..."
          npx cspell "documents/**/*.md" --config .cspell.json
          
      - name: Check writing style
        run: |
          echo "Checking writing style..."
          npx textlint "documents/**/*.md" --config .textlintrc.json
          
      - name: Validate document structure
        run: |
          echo "Validating document structure..."
          python3 scripts/validate-docs.py
          
      - name: Generate documentation report
        run: |
          echo "Generating documentation report..."
          python3 scripts/generate-docs-report.py
          
      - name: Upload documentation report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: documentation-quality-report
          path: docs-quality-report.md
          
      - name: Comment PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            let report = '## 📚 Documentation Quality Report\n\n';
            
            if (fs.existsSync('docs-quality-report.md')) {
              report += fs.readFileSync('docs-quality-report.md', 'utf8');
            } else {
              report += '✅ All documentation quality checks passed!\n\n';
              report += '- Markdown format: ✅\n';
              report += '- Link validation: ✅\n';
              report += '- Spelling check: ✅\n';
              report += '- Writing style: ✅\n';
              report += '- Document structure: ✅\n';
            }
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            }); 