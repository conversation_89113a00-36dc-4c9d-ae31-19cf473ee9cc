name: 📚 Documentation CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs-ci.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'docs/**'
      - '.github/workflows/docs-ci.yml'

jobs:
  # 文档质量检查
  quality-check:
    name: 📝 Documentation Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📋 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: 📦 Install dependencies
      run: |
        npm install -g markdownlint-cli2
        npm install -g textlint
        npm install -g cspell
        npm install -g markdown-link-check
        
    - name: 🔍 Lint Markdown files
      run: |
        markdownlint-cli2 "docs/**/*.md" || true
        
    - name: 📝 Check text quality
      run: |
        textlint "docs/**/*.md" || true
        
    - name: 🔤 Spell check
      run: |
        cspell "docs/**/*.md" || true
        
    - name: 🔗 Check markdown links
      run: |
        find docs -name "*.md" -exec markdown-link-check {} \; || true

  # 文档构建测试
  build-test:
    name: 🏗️ Documentation Build Test
    runs-on: ubuntu-latest
    needs: quality-check
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📋 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: 📦 Install VitePress
      run: |
        npm install -D vitepress
        
    - name: 🏗️ Build documentation site
      run: |
        # 创建 VitePress 配置文件
        mkdir -p .vitepress
        cat > .vitepress/config.js << 'EOF'
        export default {
          title: 'Questicle Documentation',
          description: 'Complete documentation for Questicle project',
          base: '/questicle/',
          themeConfig: {
            nav: [
              { text: 'Home', link: '/' },
              { text: 'Requirements', link: '/requirements/' },
              { text: 'Architecture', link: '/architecture/' },
              { text: 'API', link: '/api/' },
              { text: 'User Guide', link: '/user/' }
            ],
            sidebar: {
              '/requirements/': [
                {
                  text: 'Requirements',
                  items: [
                    { text: 'Overview', link: '/requirements/' }
                  ]
                }
              ],
              '/architecture/': [
                {
                  text: 'Architecture',
                  items: [
                    { text: 'Overview', link: '/architecture/' }
                  ]
                }
              ]
            }
          }
        }
        EOF
        
        # 构建文档
        npx vitepress build docs || echo "Build completed with warnings"

  # API文档生成
  api-docs:
    name: 🔌 Generate API Documentation
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: ☕ Setup JDK 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: 🐘 Setup Gradle
      uses: gradle/gradle-build-action@v2
      
    - name: 📚 Generate Dokka documentation
      run: |
        ./gradlew dokkaHtml
        
    - name: 📤 Upload API docs
      uses: actions/upload-artifact@v4
      with:
        name: api-documentation
        path: build/dokka/html/

  # 文档部署 (仅在主分支)
  deploy:
    name: 🚀 Deploy Documentation
    runs-on: ubuntu-latest
    needs: [quality-check, build-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    permissions:
      contents: read
      pages: write
      id-token: write
      
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📋 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: 📦 Install VitePress
      run: npm install -D vitepress
      
    - name: 🏗️ Build documentation
      run: |
        # 创建完整的 VitePress 配置
        mkdir -p .vitepress
        cat > .vitepress/config.js << 'EOF'
        export default {
          title: 'Questicle Documentation',
          description: 'Complete documentation for Questicle Tetris game project',
          base: '/questicle/',
          head: [
            ['link', { rel: 'icon', href: '/questicle/favicon.ico' }]
          ],
          themeConfig: {
            logo: '/logo.png',
            nav: [
              { text: 'Home', link: '/' },
              { text: 'Requirements', link: '/requirements/' },
              { text: 'Architecture', link: '/architecture/' },
              { text: 'Design', link: '/design/' },
              { text: 'API', link: '/api/' },
              { text: 'Testing', link: '/testing/' },
              { text: 'User Guide', link: '/user/' }
            ],
            sidebar: {
              '/requirements/': [
                {
                  text: 'Requirements Documentation',
                  items: [
                    { text: 'Product Requirements', link: '/requirements/PRD-产品需求文档' },
                    { text: 'Software Requirements', link: '/requirements/SRS-软件需求规格说明书' },
                    { text: 'User Stories', link: '/requirements/用户故事集' }
                  ]
                }
              ],
              '/architecture/': [
                {
                  text: 'Architecture Documentation',
                  items: [
                    { text: 'System Architecture', link: '/architecture/系统架构设计文档' },
                    { text: 'Technology Stack', link: '/architecture/技术架构选型' },
                    { text: 'Module Architecture', link: '/architecture/模块架构设计' }
                  ]
                }
              ],
              '/design/': [
                {
                  text: 'Design Documentation',
                  items: [
                    { text: 'Detailed Design', link: '/design/详细设计说明书' },
                    { text: 'Database Design', link: '/design/数据库设计文档' },
                    { text: 'UI/UX Design', link: '/design/UI-UX设计规范' }
                  ]
                }
              ],
              '/api/': [
                {
                  text: 'API Documentation',
                  items: [
                    { text: 'API Overview', link: '/api/API总览' },
                    { text: 'RESTful API', link: '/api/RESTful-API规范' },
                    { text: 'API Security', link: '/api/API安全规范' }
                  ]
                }
              ],
              '/testing/': [
                {
                  text: 'Testing Documentation',
                  items: [
                    { text: 'Testing Strategy', link: '/testing/测试策略' },
                    { text: 'Test Plan', link: '/testing/测试计划' },
                    { text: 'Test Cases', link: '/testing/test-cases/' }
                  ]
                }
              ],
              '/user/': [
                {
                  text: 'User Documentation',
                  items: [
                    { text: 'User Manual', link: '/user/用户手册' },
                    { text: 'Quick Start', link: '/user/快速入门指南' },
                    { text: 'FAQ', link: '/user/常见问题FAQ' }
                  ]
                }
              ]
            },
            socialLinks: [
              { icon: 'github', link: 'https://github.com/your-org/questicle' }
            ],
            footer: {
              message: 'Released under the MIT License.',
              copyright: 'Copyright © 2025 Questicle Team'
            },
            search: {
              provider: 'local'
            }
          },
          markdown: {
            theme: 'github-dark',
            lineNumbers: true
          }
        }
        EOF
        
        # 创建首页
        cat > docs/index.md << 'EOF'
        ---
        layout: home
        
        hero:
          name: "Questicle"
          text: "Documentation Center"
          tagline: "Complete documentation for the Questicle Tetris game project"
          actions:
            - theme: brand
              text: Get Started
              link: /user/快速入门指南
            - theme: alt
              text: View on GitHub
              link: https://github.com/your-org/questicle
        
        features:
          - title: 📋 Requirements
            details: Complete product and software requirements documentation
          - title: 🏗️ Architecture
            details: System architecture and technical design documentation
          - title: 🔌 API Reference
            details: Comprehensive API documentation and integration guides
          - title: 🧪 Testing
            details: Testing strategies, plans, and quality assurance documentation
          - title: 👥 User Guide
            details: User manuals, tutorials, and frequently asked questions
          - title: 🚀 Deployment
            details: Deployment guides and operational documentation
        ---
        EOF
        
        # 构建文档站点
        npx vitepress build docs
        
    - name: 📤 Setup Pages
      uses: actions/configure-pages@v4
      
    - name: 📦 Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: docs/.vitepress/dist
        
    - name: 🚀 Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4

  # 文档统计和报告
  stats:
    name: 📊 Documentation Statistics
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 📊 Generate documentation statistics
      run: |
        echo "# 📊 Documentation Statistics Report" > docs-stats.md
        echo "" >> docs-stats.md
        echo "Generated on: $(date)" >> docs-stats.md
        echo "" >> docs-stats.md
        
        echo "## 📁 File Count by Category" >> docs-stats.md
        echo "" >> docs-stats.md
        
        for dir in docs/*/; do
          if [ -d "$dir" ]; then
            category=$(basename "$dir")
            count=$(find "$dir" -name "*.md" | wc -l)
            echo "- **$category**: $count files" >> docs-stats.md
          fi
        done
        
        echo "" >> docs-stats.md
        echo "## 📝 Total Documentation" >> docs-stats.md
        echo "" >> docs-stats.md
        
        total_files=$(find docs -name "*.md" | wc -l)
        total_lines=$(find docs -name "*.md" -exec wc -l {} + | tail -1 | awk '{print $1}')
        total_words=$(find docs -name "*.md" -exec wc -w {} + | tail -1 | awk '{print $1}')
        
        echo "- **Total Files**: $total_files" >> docs-stats.md
        echo "- **Total Lines**: $total_lines" >> docs-stats.md
        echo "- **Total Words**: $total_words" >> docs-stats.md
        
        echo "" >> docs-stats.md
        echo "## 🔗 Recent Updates" >> docs-stats.md
        echo "" >> docs-stats.md
        
        git log --oneline --since="7 days ago" --grep="docs:" --pretty=format:"- %s (%an, %ar)" >> docs-stats.md || echo "No recent documentation updates" >> docs-stats.md
        
    - name: 📤 Upload statistics
      uses: actions/upload-artifact@v4
      with:
        name: documentation-statistics
        path: docs-stats.md

  # 通知
  notify:
    name: 📢 Notification
    runs-on: ubuntu-latest
    needs: [quality-check, build-test, deploy]
    if: always()
    
    steps:
    - name: 📢 Notify on success
      if: needs.deploy.result == 'success'
      run: |
        echo "✅ Documentation successfully deployed!"
        echo "📚 View at: https://your-org.github.io/questicle/"
        
    - name: 📢 Notify on failure
      if: failure()
      run: |
        echo "❌ Documentation workflow failed!"
        echo "Please check the logs for details."
