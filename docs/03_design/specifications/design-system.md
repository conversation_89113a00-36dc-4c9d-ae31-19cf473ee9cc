# 🎨 Questicle 设计系统规范
## 文档信息
- 文档标题: 🎨 Questicle 设计系统规范
- 文档版本: 1.0.0
- 创建日期: 2025-07-11
- 最后更新: 2025-07-11
- 文档状态: 正式发布
- 作者: Documentation Team

## 📖 变更历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | 2025-07-11 | 初始版本 | Documentation Team |

## 📚 目录



---

## 1. 设计理念

- **核心理念**: **简约智能，沉浸体验**。我们的目标是融合现代设计趋势，打造一款世界级的游戏应用。
- **设计原则 (SMART)**:
  - **S**imple (简约): 减少认知负担，界面清晰直观。
  - **M**odern (现代): 拥抱最新技术与美学，如 Material 3, 动态色彩。
  - **A**ccessible (无障碍): 包容所有用户，提供完整的无障碍支持。
  - **R**esponsive (响应式): 适配从手机到平板的各类设备。
  - **T**houghtful (贴心): 关注用户情感，通过微交互和反馈提升体验。

---

## 2. 色彩系统

遵循 Material 3 的色彩规范，定义了浅色、深色以及游戏专用的颜色。

### 2.1. 主色调 (Primary) - 深度蓝色系
```kotlin
// 主品牌色 - 深邃科技蓝
val Primary = Color(0xFF1565C0)
val OnPrimary = Color(0xFFFFFFFF)
val PrimaryContainer = Color(0xFFE3F2FD)
val OnPrimaryContainer = Color(0xFF0D47A1)
```

### 2.2. 辅助色调 (Secondary) - 活力橙色系
```kotlin
// 辅助色 - 活力橙
val Secondary = Color(0xFFFF7043)
val OnSecondary = Color(0xFFFFFFFF)
val SecondaryContainer = Color(0xFFFFE0B2)
val OnSecondaryContainer = Color(0xFFE64A19)
```

### 2.3. 功能色彩 (Functional)
```kotlin
// 成功色
val Success = Color(0xFF2E7D32)
val SuccessContainer = Color(0xFFE8F5E8)

// 警告色
val Warning = Color(0xFFF57C00)
val WarningContainer = Color(0xFFFFF3E0)

// 错误色
val Error = Color(0xFFD32F2F)
val ErrorContainer = Color(0xFFFFEBEE)
```

### 2.4. 游戏专用色彩 (Game-Specific)
```kotlin
// 俄罗斯方块标准色彩 (符合国际标准)
object TetrisColors {
    val I_Piece = Color(0xFF00FFFF)    // I型 - 天蓝色
    val J_Piece = Color(0xFF0000FF)    // J型 - 深蓝色
    val L_Piece = Color(0xFFFFA500)    // L型 - 橙色
    val O_Piece = Color(0xFFFFFF00)    // O型 - 黄色
    val S_Piece = Color(0xFF00FF00)    // S型 - 绿色
    val T_Piece = Color(0xFF800080)    // T型 - 紫色
    val Z_Piece = Color(0xFFFF0000)    // Z型 - 红色
    
    val GameBoardBackground = Color(0xFF1A1A1A) // 游戏板背景
    val GridLine = Color(0xFF333333)           // 网格线
    val GhostPiece = Color(0x4DFFFFFF)        // 幽灵方块
}
```

---

## 3. 字体系统 - 鸿蒙字体

### 3.1. 字体家族
- **主字体**: `HarmonyOS Sans` (包含多种字重)
- **等宽字体**: `JetBrains Mono` (用于分数、计时等数字显示)

### 3.2. Material 3 字体层级 (Typography)
```kotlin
val QuesticleTypography = Typography(
    // 大号标题 (例如：欢迎页面)
    displayLarge = TextStyle(fontFamily = HarmonyOS_Sans, fontWeight = FontWeight.Black, fontSize = 57.sp),
    
    // 页面标题
    headlineLarge = TextStyle(fontFamily = HarmonyOS_Sans, fontWeight = FontWeight.Bold, fontSize = 32.sp),
    
    // 正文
    bodyLarge = TextStyle(fontFamily = HarmonyOS_Sans, fontWeight = FontWeight.Normal, fontSize = 16.sp),
    
    // 按钮/标签
    labelLarge = TextStyle(fontFamily = HarmonyOS_Sans, fontWeight = FontWeight.Medium, fontSize = 14.sp)
)
```

---

## 4. 组件库 (Component Library)

此部分定义了构成UI的基本组件，确保视觉和交互的统一性。

### 4.1. 按钮 (Button)

- **Filled Button (填充按钮)**:
  - **用途**: 主要操作，例如“开始游戏”、“登录”。
  - **样式**: 使用 `Primary` 色作为背景，`OnPrimary` 作为文字颜色。
  - **示例**: `Button(onClick = {}) { Text("主要操作") }`

- **Outlined Button (描边按钮)**:
  - **用途**: 次要操作，例如“查看排行榜”、“取消”。
  - **样式**: 透明背景，边框和文字使用 `Primary` 色。
  - **示例**: `OutlinedButton(onClick = {}) { Text("次要操作") }`

- **Text Button (文本按钮)**:
  - **用途**: 低优先级操作，例如对话框中的“关闭”。
  - **样式**: 透明背景，无边框，文字使用 `Primary` 色。
  - **示例**: `TextButton(onClick = {}) { Text("低优先级操作") }`

### 4.2. 卡片 (Card)

- **用途**: 用于在独立的容器中展示一组相关内容，例如游戏模式选择、个人信息摘要。
- **样式**:
  - **Filled Card**: 使用 `PrimaryContainer` 或 `SecondaryContainer` 作为背景，适用于需要强调的内容。
  - **Elevated Card**: 默认样式，使用 `Surface` 色和轻微阴影。
  - **Outlined Card**: 使用描边，适用于需要清晰边界但不想过分强调的场景。
- **圆角**: 统一使用 `12.dp` 的圆角。

### 4.3. 输入框 (TextField)

- **用途**: 用于用户输入文本，例如登录、注册。
- **样式**: 遵循 Material 3 的 `OutlinedTextField` 样式。
  - **正常状态**: 边框和标签使用 `OnSurfaceVariant`。
  - **激活状态**: 边框、标签和光标使用 `Primary` 色。
  - **错误状态**: 边框、标签和辅助文字使用 `Error` 色。

### 4.4. 对话框 (Dialog)

- **用途**: 用于向用户展示重要信息或要求用户做出选择。
- **样式**:
  - **标题**: 使用 `headlineSmall` 字体样式。
  - **内容**: 使用 `bodyMedium` 字体样式。
  - **操作按钮**: 使用 `TextButton`，并将其放置在对话框的右下角。
- **行为**: 对话框弹出时，背景应变暗以突出对话框本身。

---

## 5. 声音设计系统

定义了游戏内关键事件的音效，以增强沉浸感。

- **基础操作**: `piece_move.wav`, `piece_rotate.wav`, `piece_lock.wav`
- **消除音效**: `line_clear_single.wav`, `line_clear_tetris.wav`
- **关键事件**: `level_up.wav`, `game_over.wav`, `pause.wav`
- **UI交互**: `button_click.wav`, `achievement_unlock.wav`
