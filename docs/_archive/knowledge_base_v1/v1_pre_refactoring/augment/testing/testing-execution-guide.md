# 测试执行指南

## 文档信息
- **创建日期**: 2025-06-20
- **版本**: v1.0
- **项目**: Questicle俄罗斯方块游戏
- **目标**: 提供完整的测试执行指南

## 概述

本指南提供了Questicle项目测试执行的完整说明，包括环境准备、测试命令、结果分析和问题排查。

## 环境准备

### 1. 系统要求
- **JDK**: Java 21 或更高版本
- **Android SDK**: API 35 或更高版本
- **Gradle**: 8.14.1 (项目自带)
- **内存**: 建议 8GB 或更多

### 2. 依赖检查
确保所有测试依赖已正确配置：
```bash
./gradlew dependencies --configuration testDemoDebugImplementation
```

### 3. 构建缓存清理（可选）
如果遇到构建问题，可以清理缓存：
```bash
./gradlew clean
./gradlew --stop  # 停止所有Gradle守护进程
```

## 测试执行命令

### 1. 单元测试执行

#### 执行所有单元测试
```bash
# Demo版本
./gradlew :feature:tetris:impl:testDemoDebugUnitTest

# Prod版本
./gradlew :feature:tetris:impl:testProdDebugUnitTest

# 所有变体
./gradlew test
```

#### 执行特定测试类
```bash
# 执行TetrisEngineImpl测试
./gradlew :feature:tetris:impl:testDemoDebugUnitTest --tests="*TetrisEngineImplComprehensiveTest*"

# 执行TetrisController测试
./gradlew :feature:tetris:impl:testDemoDebugUnitTest --tests="*TetrisControllerImplTest*"
```

#### 执行特定测试方法
```bash
# 执行特定测试方法
./gradlew :feature:tetris:impl:testDemoDebugUnitTest --tests="*TetrisEngineImplComprehensiveTest.should initialize game state successfully"
```

### 2. 集成测试执行

#### 执行数据库测试
```bash
./gradlew :core:database:testDemoDebugUnitTest
```

#### 执行Repository测试
```bash
./gradlew :core:data:testDemoDebugUnitTest
```

### 3. UI测试执行

#### 本地UI测试（Robolectric）
```bash
./gradlew :feature:tetris:impl:testDemoDebugUnitTest --tests="*TetrisGameScreenTest*"
./gradlew :feature:tetris:impl:testDemoDebugUnitTest --tests="*TetrisBoardTest*"
```

#### 设备UI测试（需要连接设备或模拟器）
```bash
./gradlew :feature:tetris:impl:connectedDemoDebugAndroidTest
```

### 4. 测试覆盖率生成

#### 生成覆盖率报告
```bash
# 执行测试并生成覆盖率报告
./gradlew :feature:tetris:impl:testDemoDebugUnitTest
./gradlew :feature:tetris:impl:createDemoDebugUnitTestCoverageReport

# 查看报告
open feature/tetris/impl/build/reports/coverage/test/demoDebug/html/index.html
```

#### 使用Kover生成覆盖率（如果配置了）
```bash
./gradlew koverHtmlReport
open build/reports/kover/html/index.html
```

## 测试结果分析

### 1. 测试报告位置
```
feature/tetris/impl/build/reports/tests/testDemoDebugUnitTest/
├── index.html          # 主报告页面
├── classes/            # 按类分组的报告
├── packages/           # 按包分组的报告
└── css/               # 样式文件
```

### 2. 覆盖率报告位置
```
feature/tetris/impl/build/reports/coverage/test/demoDebug/
├── html/
│   ├── index.html      # 覆盖率主页面
│   └── ...
└── xml/
    └── coverage.xml    # XML格式覆盖率数据
```

### 3. 关键指标解读

#### 测试通过率
- **目标**: 100% 通过
- **位置**: 测试报告首页
- **说明**: 所有测试都应该通过

#### 代码覆盖率
- **单元测试**: 目标 > 80%
- **集成测试**: 目标 > 60%
- **UI测试**: 目标 > 40%

#### 性能指标
- **测试执行时间**: 单个测试 < 1秒
- **总执行时间**: 完整测试套件 < 5分钟
- **内存使用**: 峰值 < 2GB

## 常见问题排查

### 1. 构建性能问题

#### 问题：Gradle构建很慢
**解决方案**：
```bash
# 1. 增加Gradle内存
echo "org.gradle.jvmargs=-Xmx8192m -XX:MaxMetaspaceSize=1024m" >> gradle.properties

# 2. 启用并行构建
echo "org.gradle.parallel=true" >> gradle.properties

# 3. 启用构建缓存
echo "org.gradle.caching=true" >> gradle.properties

# 4. 使用Gradle守护进程
echo "org.gradle.daemon=true" >> gradle.properties
```

#### 问题：依赖下载慢
**解决方案**：
```bash
# 使用国内镜像（在build.gradle.kts中添加）
repositories {
    maven { url = uri("https://maven.aliyun.com/repository/google") }
    maven { url = uri("https://maven.aliyun.com/repository/central") }
    google()
    mavenCentral()
}
```

### 2. 测试执行问题

#### 问题：测试找不到
**解决方案**：
```bash
# 1. 检查测试类路径
./gradlew :feature:tetris:impl:tasks --group=verification

# 2. 确认构建变体
./gradlew projects

# 3. 清理并重新构建
./gradlew clean build
```

#### 问题：Mock对象错误
**解决方案**：
```kotlin
// 确保正确初始化Mock
@BeforeEach
fun setUp() {
    MockKAnnotations.init(this)
    clearAllMocks()
}

// 确保正确清理Mock
@AfterEach
fun tearDown() {
    clearAllMocks()
}
```

#### 问题：协程测试失败
**解决方案**：
```kotlin
// 使用正确的测试规则
@get:Rule
val testRule = TetrisTestRule()

// 使用runTest包装协程测试
@Test
fun `test coroutine function`() = testRule.runTest {
    // 测试代码
}
```

### 3. 依赖问题

#### 问题：JUnit 5不工作
**解决方案**：
```kotlin
// 确保在build.gradle.kts中配置
tasks.withType<Test> {
    useJUnitPlatform()
}
```

#### 问题：Mockk初始化失败
**解决方案**：
```kotlin
// 确保添加了正确的依赖
testImplementation("io.mockk:mockk:1.13.12")
testImplementation("io.mockk:mockk-android:1.13.12")
```

## 性能优化建议

### 1. 构建优化
```properties
# gradle.properties
org.gradle.jvmargs=-Xmx8192m -XX:MaxMetaspaceSize=1024m -XX:+UseG1GC
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
org.gradle.configureondemand=true
```

### 2. 测试优化
```kotlin
// 使用@TestInstance减少对象创建
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MyTest {
    
    // 使用@BeforeAll而不是@BeforeEach
    @BeforeAll
    fun setUpAll() {
        // 一次性初始化
    }
}
```

### 3. Mock优化
```kotlin
// 使用relaxed mock减少配置
val mockObject = mockk<MyClass>(relaxed = true)

// 重用Mock对象
companion object {
    private val sharedMock = mockk<SharedService>(relaxed = true)
}
```

## 持续集成配置

### 1. GitHub Actions示例
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
      
      - name: Cache Gradle
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
      
      - name: Run Tests
        run: ./gradlew test
      
      - name: Generate Coverage Report
        run: ./gradlew koverHtmlReport
      
      - name: Upload Coverage
        uses: codecov/codecov-action@v3
```

### 2. 质量门禁
```bash
# 设置最低覆盖率要求
./gradlew koverVerify --coverage-threshold=80
```

## 最佳实践

### 1. 测试执行顺序
1. **快速反馈**: 先运行单元测试
2. **集成验证**: 再运行集成测试
3. **完整验证**: 最后运行UI测试

### 2. 测试数据管理
- 使用TestDataFactory创建测试数据
- 避免硬编码测试值
- 使用随机数据增加测试覆盖

### 3. 测试维护
- 定期更新测试依赖
- 重构测试代码消除重复
- 保持测试文档更新

## 总结

通过遵循本指南，您可以：
1. **高效执行测试**: 使用正确的命令和参数
2. **快速定位问题**: 通过系统化的排查方法
3. **优化测试性能**: 应用性能优化建议
4. **建立CI/CD**: 集成到持续集成流水线

这个测试执行指南确保了测试体系的有效运行，为项目质量提供了可靠保障。
