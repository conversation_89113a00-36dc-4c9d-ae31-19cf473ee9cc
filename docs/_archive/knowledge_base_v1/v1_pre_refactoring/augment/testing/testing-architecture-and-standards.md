# 测试架构和规范文档

## 文档信息
- **创建日期**: 2025-06-20
- **版本**: v1.0
- **项目**: Questicle俄罗斯方块游戏
- **基于**: 2025年最新测试技术选型

## 测试架构设计

### 整体架构原则

#### 1. 测试金字塔原则
```
        UI Tests (10%)
       ┌─────────────────┐
      │  端到端测试       │
     │  用户界面测试      │
    └─────────────────────┘
         Integration Tests (20%)
    ┌─────────────────────────┐
   │    模块集成测试          │
  │     数据库测试            │
 │      网络测试              │
└─────────────────────────────┘
           Unit Tests (70%)
  ┌─────────────────────────────┐
 │        业务逻辑测试           │
│         数据模型测试           │
│         工具类测试             │
│         ViewModel测试          │
└─────────────────────────────────┘
```

#### 2. 分层测试策略

**单元测试层 (Unit Tests)**
- **目标**: 测试单个类或方法的功能
- **范围**: 业务逻辑、数据模型、工具函数
- **特点**: 快速、独立、可重复
- **覆盖率目标**: > 80%

**集成测试层 (Integration Tests)**
- **目标**: 测试模块间的交互
- **范围**: Repository、Database、Network
- **特点**: 真实依赖、中等速度
- **覆盖率目标**: > 60%

**UI测试层 (UI Tests)**
- **目标**: 测试用户界面和交互
- **范围**: Compose组件、用户流程
- **特点**: 端到端、较慢但真实
- **覆盖率目标**: > 40%

### 测试模块结构

```
testing/
├── unit/                           # 单元测试
│   ├── domain/                    # 领域层测试
│   │   ├── model/                 # 数据模型测试
│   │   ├── usecase/               # 用例测试
│   │   └── repository/            # 仓库接口测试
│   ├── data/                      # 数据层测试
│   │   ├── repository/            # 仓库实现测试
│   │   ├── datasource/            # 数据源测试
│   │   └── mapper/                # 数据映射测试
│   ├── presentation/              # 表现层测试
│   │   ├── viewmodel/             # ViewModel测试
│   │   ├── state/                 # UI状态测试
│   │   └── mapper/                # UI映射测试
│   └── core/                      # 核心组件测试
│       ├── utils/                 # 工具类测试
│       ├── extensions/            # 扩展函数测试
│       └── common/                # 通用组件测试
├── integration/                   # 集成测试
│   ├── repository/                # 仓库集成测试
│   ├── database/                  # 数据库测试
│   ├── network/                   # 网络测试
│   ├── storage/                   # 存储测试
│   └── engine/                    # 游戏引擎测试
├── ui/                           # UI测试
│   ├── screens/                   # 屏幕测试
│   ├── components/                # 组件测试
│   ├── navigation/                # 导航测试
│   └── e2e/                       # 端到端测试
├── performance/                   # 性能测试
│   ├── memory/                    # 内存测试
│   ├── cpu/                       # CPU测试
│   └── battery/                   # 电池测试
├── accessibility/                 # 无障碍测试
├── fixtures/                      # 测试数据
│   ├── builders/                  # 测试数据构建器
│   ├── factories/                 # 测试数据工厂
│   ├── samples/                   # 示例数据
│   └── mocks/                     # Mock对象
└── utils/                         # 测试工具
    ├── rules/                     # 测试规则
    ├── extensions/                # 测试扩展
    ├── matchers/                  # 自定义匹配器
    └── helpers/                   # 测试辅助类
```

## 测试规范和标准

### 命名规范

#### 1. 测试类命名
```kotlin
// 单元测试
class TetrisGameManagerTest
class TetrisPieceTest
class GameStateTest

// 集成测试
class TetrisRepositoryIntegrationTest
class DatabaseIntegrationTest

// UI测试
class TetrisGameScreenTest
class TetrisBoardComponentTest
```

#### 2. 测试方法命名
```kotlin
// Given_When_Then 模式
@Test
fun `given valid piece when move left then position should update`()

@Test
fun `given game over state when restart then should reset to initial state`()

// Should 模式
@Test
fun `should clear completed lines correctly`()

@Test
fun `should generate next piece after placement`()
```

#### 3. 测试数据命名
```kotlin
// 测试常量
companion object {
    private const val TEST_PLAYER_ID = "test_player_123"
    private const val TEST_SCORE = 1000
    private val TEST_GAME_STATE = TetrisGameState.initial()
}

// 测试工厂方法
fun createTestTetrisPiece(
    type: TetrisPieceType = TetrisPieceType.I,
    x: Int = 4,
    y: Int = 0
): TetrisPiece
```

### 测试组织规范

#### 1. 测试类结构
```kotlin
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TetrisGameManagerTest {
    
    // 测试对象和依赖
    private lateinit var gameManager: TetrisGameManager
    private val mockRepository = mockk<GameRepository>()
    
    // 测试规则
    @get:Rule
    val testCoroutineRule = TestCoroutineRule()
    
    // 设置和清理
    @BeforeEach
    fun setUp() {
        // 初始化测试环境
    }
    
    @AfterEach
    fun tearDown() {
        // 清理测试环境
    }
    
    // 嵌套测试组织
    @Nested
    @DisplayName("游戏初始化测试")
    inner class GameInitializationTests {
        // 相关测试方法
    }
    
    @Nested
    @DisplayName("方块移动测试")
    inner class PieceMovementTests {
        // 相关测试方法
    }
}
```

#### 2. 测试方法结构
```kotlin
@Test
fun `should handle piece rotation correctly`() {
    // Given - 准备测试数据
    val initialPiece = createTestTetrisPiece(
        type = TetrisPieceType.T,
        rotation = 0
    )
    val gameState = createTestGameState(currentPiece = initialPiece)
    
    // When - 执行测试操作
    val result = gameManager.rotatePiece(gameState, clockwise = true)
    
    // Then - 验证测试结果
    result.shouldBeInstanceOf<Result.Success<TetrisGameState>>()
    val newState = result.data
    newState.currentPiece?.rotation shouldBe 1
    newState.currentPiece?.type shouldBe TetrisPieceType.T
}
```

### Mock和Stub规范

#### 1. Mock对象创建
```kotlin
class TetrisViewModelTest {
    
    // 使用 @MockK 注解
    @MockK
    private lateinit var tetrisEngine: TetrisEngine
    
    @MockK
    private lateinit var gameRepository: GameRepository
    
    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        
        // 设置默认行为
        coEvery { tetrisEngine.initializeGame(any()) } returns 
            Result.Success(TetrisGameState.initial())
    }
}
```

#### 2. Mock行为定义
```kotlin
// 成功场景
coEvery { repository.saveGame(any()) } returns Result.Success(Unit)

// 失败场景
coEvery { repository.saveGame(any()) } returns 
    Result.Error(GameException.StorageException("Save failed"))

// 复杂行为
coEvery { engine.processAction(any(), any()) } answers {
    val action = firstArg<TetrisAction>()
    val state = secondArg<TetrisGameState>()
    // 根据参数返回不同结果
}
```

### 断言规范

#### 1. 基础断言
```kotlin
// Kotest断言风格
result shouldBe expected
result shouldNotBe null
result.size shouldBe 5
result shouldContain expectedItem

// 异常断言
shouldThrow<IllegalArgumentException> {
    gameManager.movePiece(invalidDirection)
}
```

#### 2. 复杂对象断言
```kotlin
// 对象属性断言
gameState.apply {
    score shouldBe 1000
    level shouldBe 5
    status shouldBe TetrisStatus.PLAYING
    currentPiece shouldNotBe null
}

// 集合断言
clearedLines shouldHaveSize 2
clearedLines shouldContainExactly listOf(18, 19)
```

#### 3. Flow断言
```kotlin
@Test
fun `game state flow should emit correct sequence`() = runTest {
    viewModel.gameState.test {
        // 验证初始状态
        awaitItem() shouldBe TetrisGameState.initial()
        
        // 触发状态变化
        viewModel.startGame()
        
        // 验证新状态
        val playingState = awaitItem()
        playingState.status shouldBe TetrisStatus.PLAYING
        playingState.currentPiece shouldNotBe null
        
        // 确保没有更多发射
        expectNoEvents()
    }
}
```

## 测试数据管理

### 测试数据工厂

#### 1. 基础数据工厂
```kotlin
object TestDataFactory {
    
    fun createTetrisPiece(
        type: TetrisPieceType = TetrisPieceType.I,
        x: Int = 4,
        y: Int = 0,
        rotation: Int = 0
    ): TetrisPiece = TetrisPiece(
        type = type,
        x = x,
        y = y,
        rotation = rotation,
        shape = type.getShape(rotation)
    )
    
    fun createTetrisGameState(
        status: TetrisStatus = TetrisStatus.READY,
        score: Int = 0,
        level: Int = 1,
        currentPiece: TetrisPiece? = null
    ): TetrisGameState = TetrisGameState(
        status = status,
        score = score,
        level = level,
        lines = 0,
        currentPiece = currentPiece,
        nextPiece = createTetrisPiece(),
        board = TetrisBoard.empty(),
        holdPiece = null
    )
}
```

#### 2. 构建器模式
```kotlin
class TetrisGameStateBuilder {
    private var status = TetrisStatus.READY
    private var score = 0
    private var level = 1
    private var currentPiece: TetrisPiece? = null
    
    fun withStatus(status: TetrisStatus) = apply { this.status = status }
    fun withScore(score: Int) = apply { this.score = score }
    fun withLevel(level: Int) = apply { this.level = level }
    fun withCurrentPiece(piece: TetrisPiece) = apply { this.currentPiece = piece }
    
    fun build(): TetrisGameState = TetrisGameState(
        status = status,
        score = score,
        level = level,
        currentPiece = currentPiece,
        // ... 其他属性
    )
}

// 使用示例
val gameState = TetrisGameStateBuilder()
    .withStatus(TetrisStatus.PLAYING)
    .withScore(1500)
    .withLevel(3)
    .build()
```

### Fixture管理

#### 1. 测试场景Fixture
```kotlin
object TetrisTestFixtures {
    
    // 游戏开始场景
    val gameStartScenario = TetrisTestScenario(
        name = "Game Start",
        initialState = TestDataFactory.createTetrisGameState(),
        expectedActions = listOf(TetrisAction.Start),
        expectedFinalState = TestDataFactory.createTetrisGameState(
            status = TetrisStatus.PLAYING
        )
    )
    
    // 行消除场景
    val lineClearScenario = TetrisTestScenario(
        name = "Line Clear",
        initialState = createGameStateWithFullLines(),
        expectedActions = listOf(TetrisAction.Tick),
        expectedFinalState = createGameStateAfterLineClear()
    )
}
```

## 性能测试规范

### 内存测试
```kotlin
@Test
fun `game should not leak memory during long play session`() {
    val initialMemory = Runtime.getRuntime().totalMemory()
    
    repeat(1000) {
        gameManager.tick()
        if (it % 100 == 0) {
            System.gc()
            val currentMemory = Runtime.getRuntime().totalMemory()
            (currentMemory - initialMemory) shouldBeLessThan 50_000_000 // 50MB
        }
    }
}
```

### 响应时间测试
```kotlin
@Test
fun `piece movement should respond within acceptable time`() {
    val startTime = System.nanoTime()
    
    gameManager.movePiece(Direction.LEFT)
    
    val endTime = System.nanoTime()
    val durationMs = (endTime - startTime) / 1_000_000
    
    durationMs shouldBeLessThan 16 // 60 FPS = 16ms per frame
}
```

## 持续集成规范

### CI/CD测试流水线
```yaml
# 测试阶段配置
test:
  stage: test
  script:
    # 单元测试
    - ./gradlew testDebugUnitTest
    # 集成测试
    - ./gradlew testDebugIntegrationTest
    # UI测试
    - ./gradlew connectedDebugAndroidTest
    # 覆盖率报告
    - ./gradlew jacocoTestReport
  artifacts:
    reports:
      junit: app/build/test-results/*/TEST-*.xml
      coverage: app/build/reports/jacoco/test/jacocoTestReport.xml
```

### 质量门禁
- **单元测试覆盖率**: > 80%
- **集成测试覆盖率**: > 60%
- **UI测试覆盖率**: > 40%
- **测试通过率**: 100%
- **性能回归**: < 5%

## 总结

本测试架构和规范文档建立了完整的测试体系，包括：

1. **清晰的分层架构**: 单元、集成、UI三层测试策略
2. **统一的命名规范**: 提高测试代码的可读性
3. **标准化的组织方式**: 便于维护和扩展
4. **完善的数据管理**: 工厂模式和构建器模式
5. **性能测试保障**: 确保应用性能不回归
6. **CI/CD集成**: 自动化测试流水线

这个架构将为Questicle项目提供坚实的测试基础，确保代码质量和长期可维护性。
