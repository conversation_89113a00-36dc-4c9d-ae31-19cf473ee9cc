# Hilt Navigation Compose 重复依赖清理

## 问题描述

在版本目录 `gradle/libs.versions.toml` 中发现了 `hilt-navigation-compose` 的重复定义：

1. `androidx-hilt-navigation-compose` (第101行)
2. `hilt-navigation-compose` (第104行) - 标记为"导航别名"

这种重复定义可能导致依赖管理混乱和潜在的版本冲突。

## 使用情况分析

通过检查项目中的使用情况：

```bash
find . -name "*.gradle.kts" -exec grep -H "hilt.*navigation" {} \;
```

发现以下文件使用了该依赖：
- `./app/build.gradle.kts`: `implementation(libs.hilt.navigation.compose)`
- `./feature/settings/impl/build.gradle.kts`: `implementation(libs.hilt.navigation.compose)`

项目中使用的是 `libs.hilt.navigation.compose`，对应版本目录中的 `hilt-navigation-compose` 定义。

## 解决方案

### 修复前
```toml
# Compose 导航和集成
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "composeActivity" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "composeNavigation" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "composeHiltNavigation" }

# 导航别名 (为了兼容现有代码)
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "composeHiltNavigation" }
```

### 修复后
```toml
# Compose 导航和集成
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "composeActivity" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "composeNavigation" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "composeHiltNavigation" }
```

## 修复内容

1. **移除重复定义**: 删除了 `androidx-hilt-navigation-compose` 定义
2. **保留实际使用的定义**: 保留了 `hilt-navigation-compose` 定义，因为项目中使用的是 `libs.hilt.navigation.compose`
3. **清理注释**: 移除了"导航别名"的注释，因为现在只有一个定义

## 验证结果

### 构建测试
运行构建测试验证修复：
```bash
./gradlew :feature:settings:impl:compileDemoDebugKotlin --warning-mode all
```

✅ **构建成功**: 1分33秒完成，无依赖冲突错误  
✅ **依赖解析正常**: 所有相关模块正常编译  
✅ **无重复定义**: 版本目录中只有一个 `hilt-navigation-compose` 定义  

### 依赖检查
```bash
grep -n "hilt-navigation-compose" gradle/libs.versions.toml
```

结果显示只有一个匹配项：
```
101:hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "composeHiltNavigation" }
```

## 最佳实践

### 1. 避免重复定义
- 每个依赖在版本目录中只应有一个定义
- 如果需要别名，应该通过不同的命名约定来区分用途

### 2. 命名一致性
- 使用清晰、一致的命名约定
- 避免使用"别名"注释，而是使用明确的命名

### 3. 定期清理
- 定期检查版本目录中的重复定义
- 移除不再使用的依赖定义

## 影响范围

### 直接影响
- 清理了版本目录中的重复定义
- 简化了依赖管理

### 间接影响
- 减少了潜在的版本冲突风险
- 提高了版本目录的可读性和维护性

## 后续建议

1. **定期审查**: 定期检查版本目录中是否有新的重复定义
2. **命名规范**: 建立清晰的依赖命名规范
3. **文档维护**: 更新相关的开发文档，说明依赖命名约定

## 总结

通过移除 `hilt-navigation-compose` 的重复定义，项目现在具有：

- **清晰的依赖管理**: 每个依赖只有一个定义
- **简化的版本目录**: 移除了不必要的重复和别名
- **稳定的构建**: 无依赖冲突的构建环境
- **更好的维护性**: 简化的依赖管理便于长期维护

这个修复是项目依赖标准化工作的重要组成部分，有助于保持项目的整洁性和可维护性。
