# 🎨 Questicle UI/UX 设计规范与标准 2025

## 📋 文档信息
- **文档标题**: Questicle UI/UX 设计规范与标准
- **文档版本**: v2.0.0
- **创建日期**: 2025-06-26
- **文档状态**: ✅ 制定中
- **作者**: Augment Agent
- **审核人**: 设计团队
- **批准人**: 产品负责人

## 📖 变更历史
| 版本 | 日期 | 作者 | 变更内容 |
|------|------|------|----------|
| v2.0.0 | 2025-06-26 | Augment Agent | 基于2025年最新设计趋势的全面重构 |
| v1.0.0 | 2025-06-21 | Augment Agent | 初始版本 |

---

## 🎯 设计理念与原则

### 核心设计理念
**"简约智能，沉浸体验"** - 融合2025年最新设计趋势，打造世界级游戏应用

### 设计原则 (SMART原则)
1. **Simple** - 简约至上，减少认知负担
2. **Modern** - 现代感设计，融合最新趋势
3. **Accessible** - 无障碍设计，包容所有用户
4. **Responsive** - 响应式布局，适配所有设备
5. **Thoughtful** - 贴心设计，关注用户情感

### 2025年设计趋势融合
- **🌙 低光模式设计** - 减少视觉疲劳的柔和界面
- **🎮 3D沉浸式元素** - 增强游戏体验的立体感
- **🔤 动态字体系统** - 智能调整的鸿蒙字体
- **📱 Bento网格布局** - 模块化的界面组织
- **🎨 情感化配色** - 基于心理学的色彩运用
- **⚡ 微交互动效** - 精致的反馈动画
- **🤖 AI驱动个性化** - 智能适应用户偏好

---

## 🎨 视觉设计系统

### 色彩系统

#### 主色调 (Primary Colors) - 深度蓝色系
```kotlin
// 主品牌色 - 深邃科技蓝
val Primary = Color(0xFF1565C0)           // 主色
val PrimaryVariant = Color(0xFF0D47A1)    // 主色变体
val OnPrimary = Color(0xFFFFFFFF)         // 主色上的文字

// 主色容器
val PrimaryContainer = Color(0xFFE3F2FD)  // 主色容器
val OnPrimaryContainer = Color(0xFF0D47A1) // 主色容器上的文字
```

#### 辅助色调 (Secondary Colors) - 活力橙色系
```kotlin
// 辅助色 - 活力橙
val Secondary = Color(0xFFFF7043)         // 辅助色
val SecondaryVariant = Color(0xFFE64A19)  // 辅助色变体
val OnSecondary = Color(0xFFFFFFFF)       // 辅助色上的文字

// 辅助色容器
val SecondaryContainer = Color(0xFFFFE0B2) // 辅助色容器
val OnSecondaryContainer = Color(0xFFE64A19) // 辅助色容器上的文字
```

#### 第三色调 (Tertiary Colors) - 智能紫色系
```kotlin
// 第三色 - 智能紫
val Tertiary = Color(0xFF7B1FA2)          // 第三色
val TertiaryVariant = Color(0xFF4A148C)   // 第三色变体
val OnTertiary = Color(0xFFFFFFFF)        // 第三色上的文字

// 第三色容器
val TertiaryContainer = Color(0xFFF3E5F5) // 第三色容器
val OnTertiaryContainer = Color(0xFF4A148C) // 第三色容器上的文字
```

#### 功能色彩 (Functional Colors)
```kotlin
// 成功色 - 自然绿
val Success = Color(0xFF2E7D32)
val OnSuccess = Color(0xFFFFFFFF)
val SuccessContainer = Color(0xFFE8F5E8)
val OnSuccessContainer = Color(0xFF1B5E20)

// 警告色 - 阳光黄
val Warning = Color(0xFFF57C00)
val OnWarning = Color(0xFFFFFFFF)
val WarningContainer = Color(0xFFFFF3E0)
val OnWarningContainer = Color(0xFFE65100)

// 错误色 - 温和红
val Error = Color(0xFFD32F2F)
val OnError = Color(0xFFFFFFFF)
val ErrorContainer = Color(0xFFFFEBEE)
val OnErrorContainer = Color(0xFFB71C1C)

// 信息色 - 清新蓝
val Info = Color(0xFF1976D2)
val OnInfo = Color(0xFFFFFFFF)
val InfoContainer = Color(0xFFE3F2FD)
val OnInfoContainer = Color(0xFF0D47A1)
```

#### 游戏专用色彩 (Game-Specific Colors)
```kotlin
// 俄罗斯方块标准色彩 (符合国际标准)
object TetrisColors {
    val IPiece = Color(0xFF00FFFF)    // I型 - 天蓝色
    val JPiece = Color(0xFF0000FF)    // J型 - 深蓝色
    val LPiece = Color(0xFFFFA500)    // L型 - 橙色
    val OPiece = Color(0xFFFFFF00)    // O型 - 黄色
    val SPiece = Color(0xFF00FF00)    // S型 - 绿色
    val TPiece = Color(0xFF800080)    // T型 - 紫色
    val ZPiece = Color(0xFFFF0000)    // Z型 - 红色
    
    // 游戏界面色彩
    val GameBoard = Color(0xFF1A1A1A)      // 游戏板背景
    val GridLine = Color(0xFF333333)       // 网格线
    val Ghost = Color(0x4DFFFFFF)          // 幽灵方块
    val Preview = Color(0xFF2A2A2A)        // 预览区背景
}
```

#### 低光模式色彩 (Low Light Mode) - 2025年趋势
```kotlin
// 低光模式专用色彩 - 减少视觉疲劳
object LowLightColors {
    val Background = Color(0xFF0A0A0A)     // 极深背景
    val Surface = Color(0xFF1A1A1A)        // 表面色
    val Primary = Color(0xFF4FC3F7)        // 柔和主色
    val Secondary = Color(0xFFFFB74D)      // 柔和辅助色
    val OnSurface = Color(0xFFE0E0E0)      // 柔和文字色
}
```

### 字体系统 - 鸿蒙字体

#### 字体家族定义
```kotlin
// 鸿蒙字体家族 - 2025年最新版本
val HarmonyOSFontFamily = FontFamily(
    Font(R.font.harmonyos_sans_thin, FontWeight.Thin),        // 100
    Font(R.font.harmonyos_sans_light, FontWeight.Light),      // 300
    Font(R.font.harmonyos_sans_regular, FontWeight.Normal),   // 400
    Font(R.font.harmonyos_sans_medium, FontWeight.Medium),    // 500
    Font(R.font.harmonyos_sans_bold, FontWeight.Bold),        // 700
    Font(R.font.harmonyos_sans_black, FontWeight.Black)       // 900
)

// 游戏专用等宽字体 - 用于分数显示
val GameMonoFontFamily = FontFamily(
    Font(R.font.jetbrains_mono_regular, FontWeight.Normal),
    Font(R.font.jetbrains_mono_bold, FontWeight.Bold)
)
```

#### Material 3 字体层级 - 鸿蒙字体适配
```kotlin
val QuesticleTypography = Typography(
    // 显示级别 - 用于大标题和品牌展示
    displayLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Black,
        fontSize = 57.sp,
        lineHeight = 64.sp,
        letterSpacing = (-0.25).sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    displayMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 45.sp,
        lineHeight = 52.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    displaySmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 36.sp,
        lineHeight = 44.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    
    // 标题级别 - 用于页面标题和重要信息
    headlineLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    headlineMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    headlineSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    
    // 标题级别 - 用于卡片标题和组件标题
    titleLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    titleMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    titleSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    
    // 正文级别 - 用于主要内容
    bodyLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    bodyMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    bodySmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    
    // 标签级别 - 用于按钮和小标签
    labelLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    labelMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    ),
    labelSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
)
```

#### 游戏专用字体样式
```kotlin
// 游戏数据显示专用字体
object GameTypography {
    // 分数显示 - 大号等宽字体
    val ScoreDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 2.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
    
    // 等级显示 - 中号等宽字体
    val LevelDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 1.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
    
    // 统计数据 - 小号等宽字体
    val StatDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
    
    // 游戏提示文字 - 鸿蒙字体
    val GameHint = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp,
        platformStyle = PlatformTextStyle(includeFontPadding = false)
    )
}
```

---

## 📐 布局与间距系统

### 间距系统 (8dp基准)
```kotlin
object Spacing {
    val None = 0.dp
    val ExtraSmall = 4.dp      // 1/2 基准
    val Small = 8.dp           // 1x 基准
    val Medium = 16.dp         // 2x 基准
    val Large = 24.dp          // 3x 基准
    val ExtraLarge = 32.dp     // 4x 基准
    val Huge = 48.dp           // 6x 基准
    val Massive = 64.dp        // 8x 基准
}
```

### Bento网格布局系统 - 2025年趋势
```kotlin
// Bento网格 - 模块化布局
@Composable
fun BentoGrid(
    modifier: Modifier = Modifier,
    content: @Composable BentoGridScope.() -> Unit
) {
    // 实现Bento风格的网格布局
    // 支持不规则大小的卡片组合
}

// 网格项目大小定义
enum class BentoSize {
    Small,      // 1x1
    Medium,     // 2x1 或 1x2
    Large,      // 2x2
    Wide,       // 3x1
    Tall        // 1x3
}
```

---

## 🎮 交互设计规范

### 手势交互标准
```kotlin
// 俄罗斯方块手势定义
object TetrisGestures {
    val SwipeLeft = "向左滑动 - 左移方块"
    val SwipeRight = "向右滑动 - 右移方块"
    val SwipeDown = "向下滑动 - 软降"
    val Tap = "点击 - 旋转方块"
    val DoubleTap = "双击 - 硬降"
    val LongPress = "长按 - 暂存方块"
}
```

### 触觉反馈标准
```kotlin
// 触觉反馈强度定义
enum class HapticIntensity {
    Light,      // 轻微反馈 - 移动操作
    Medium,     // 中等反馈 - 旋转操作
    Strong      // 强烈反馈 - 消除行、游戏结束
}
```

---

## 🎭 动效设计规范

### 动画时长标准
```kotlin
object AnimationDuration {
    val Instant = 0.milliseconds
    val Quick = 150.milliseconds      // 快速反馈
    val Normal = 300.milliseconds     // 标准动画
    val Slow = 500.milliseconds       // 慢速动画
    val Dramatic = 800.milliseconds   // 戏剧性动画
}
```

### 缓动函数
```kotlin
object Easing {
    val Standard = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
    val Decelerate = CubicBezierEasing(0.0f, 0.0f, 0.2f, 1.0f)
    val Accelerate = CubicBezierEasing(0.4f, 0.0f, 1.0f, 1.0f)
    val Bounce = CubicBezierEasing(0.68f, -0.55f, 0.265f, 1.55f)
}
```

---

## 📱 响应式设计标准

### 断点定义
```kotlin
object Breakpoints {
    val Compact = 0.dp..599.dp        // 手机竖屏
    val Medium = 600.dp..839.dp       // 手机横屏/小平板
    val Expanded = 840.dp..1199.dp    // 平板
    val Large = 1200.dp..1599.dp      // 桌面
    val ExtraLarge = 1600.dp..Int.MAX_VALUE.dp // 大屏桌面
}
```

---

## ♿ 无障碍设计标准

### 对比度要求
- **正常文字**: 最小对比度 4.5:1
- **大文字**: 最小对比度 3:1
- **图标**: 最小对比度 3:1
- **焦点指示器**: 最小对比度 3:1

### 触摸目标大小
- **最小触摸目标**: 48dp x 48dp
- **推荐触摸目标**: 56dp x 56dp
- **游戏控制按钮**: 64dp x 64dp

---

## 🎨 主题系统架构

### 主题模式
```kotlin
enum class ThemeMode {
    LIGHT,          // 浅色模式
    DARK,           // 深色模式
    LOW_LIGHT,      // 低光模式 (2025年新趋势)
    SYSTEM,         // 跟随系统
    AUTO            // 智能切换
}
```

### 动态配色支持
```kotlin
// Material You 动态配色
@Composable
fun QuesticleTheme(
    themeMode: ThemeMode = ThemeMode.SYSTEM,
    dynamicColor: Boolean = true,
    lowLightMode: Boolean = false,
    gameTheme: GameTheme? = null,
    content: @Composable () -> Unit
) {
    // 主题实现逻辑
}
```

---

## 📊 设计质量标准

### 性能指标
- **首屏渲染时间**: < 500ms
- **动画帧率**: 60fps
- **内存使用**: < 100MB
- **包体积增量**: < 5MB

### 用户体验指标
- **学习成本**: < 3分钟上手
- **操作效率**: 核心操作 < 2步
- **错误率**: < 5%
- **满意度**: > 4.5/5.0

---

## 🔧 实施指南

### 开发工具配置
1. **Android Studio**: 启用Compose预览
2. **设计工具**: Figma + Material Theme Builder
3. **字体工具**: 鸿蒙字体管理器
4. **颜色工具**: Material Color Utilities

### 代码规范
1. **组件命名**: 使用Questicle前缀
2. **文件组织**: 按功能模块分组
3. **注释规范**: 中英文双语注释
4. **测试覆盖**: UI组件100%覆盖

---

## 📈 持续优化

### 数据驱动设计
- **用户行为分析**: 热力图、点击流
- **性能监控**: 渲染时间、内存使用
- **A/B测试**: 设计方案对比
- **用户反馈**: 定期收集和分析

### 趋势跟踪
- **设计趋势**: 季度更新设计规范
- **技术发展**: 跟进Compose新特性
- **用户需求**: 持续用户研究
- **竞品分析**: 定期竞品设计分析

---

## 🧩 组件设计规范

### 按钮组件 (QuesticleButton)
```kotlin
@Composable
fun QuesticleButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    style: ButtonStyle = ButtonStyle.Primary,
    size: ButtonSize = ButtonSize.Medium,
    icon: ImageVector? = null,
    enabled: Boolean = true,
    loading: Boolean = false
) {
    // 按钮实现
}

enum class ButtonStyle {
    Primary,        // 主要按钮 - 品牌色
    Secondary,      // 次要按钮 - 辅助色
    Tertiary,       // 第三按钮 - 边框样式
    Ghost,          // 幽灵按钮 - 透明背景
    Danger,         // 危险按钮 - 错误色
    Success         // 成功按钮 - 成功色
}

enum class ButtonSize {
    Small,          // 32dp 高度
    Medium,         // 40dp 高度
    Large,          // 48dp 高度
    ExtraLarge      // 56dp 高度
}
```

### 卡片组件 (QuesticleCard)
```kotlin
@Composable
fun QuesticleCard(
    modifier: Modifier = Modifier,
    style: CardStyle = CardStyle.Elevated,
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    // 卡片实现
}

enum class CardStyle {
    Elevated,       // 阴影卡片
    Filled,         // 填充卡片
    Outlined,       // 边框卡片
    Glass           // 毛玻璃效果 (2025年趋势)
}
```

### 游戏信息卡片 (GameInfoCard)
```kotlin
@Composable
fun GameInfoCard(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    icon: ImageVector? = null,
    trend: TrendDirection? = null,
    subtitle: String? = null,
    onClick: (() -> Unit)? = null
) {
    // 游戏信息卡片实现
}

enum class TrendDirection {
    Up,             // 上升趋势 - 绿色箭头
    Down,           // 下降趋势 - 红色箭头
    Stable          // 稳定趋势 - 水平线
}
```

### 3D方块组件 (TetrisBlock3D) - 2025年趋势
```kotlin
@Composable
fun TetrisBlock3D(
    pieceType: TetrisPieceType,
    modifier: Modifier = Modifier,
    depth: Dp = 4.dp,
    shadowIntensity: Float = 0.3f,
    animationEnabled: Boolean = true
) {
    // 3D方块渲染实现
    // 使用Canvas和阴影效果创建立体感
}
```

---

## 🎨 2025年设计趋势实施

### 1. 低光模式 (Low Light Mode)
```kotlin
// 低光模式主题实现
@Composable
fun LowLightTheme(
    content: @Composable () -> Unit
) {
    val lowLightColorScheme = darkColorScheme(
        primary = Color(0xFF4FC3F7),           // 柔和蓝色
        secondary = Color(0xFFFFB74D),         // 柔和橙色
        background = Color(0xFF0A0A0A),        // 极深背景
        surface = Color(0xFF1A1A1A),           // 深色表面
        onSurface = Color(0xFFE0E0E0)          // 柔和文字
    )

    MaterialTheme(
        colorScheme = lowLightColorScheme,
        typography = QuesticleTypography.copy(
            // 降低字体对比度
            bodyLarge = QuesticleTypography.bodyLarge.copy(
                color = Color(0xFFE0E0E0)
            )
        ),
        content = content
    )
}
```

### 2. 毛玻璃效果 (Glassmorphism)
```kotlin
@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    blurRadius: Dp = 16.dp,
    alpha: Float = 0.1f,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.5f)
                    )
                ),
                shape = RoundedCornerShape(16.dp)
            )
            .blur(blurRadius),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        border = BorderStroke(
            width = 1.dp,
            color = Color.White.copy(alpha = 0.2f)
        )
    ) {
        content()
    }
}
```

### 3. 动态字体系统
```kotlin
@Composable
fun DynamicText(
    text: String,
    importance: TextImportance = TextImportance.Normal,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.bodyLarge
) {
    val animatedFontSize by animateFloatAsState(
        targetValue = when (importance) {
            TextImportance.Critical -> 1.5f
            TextImportance.High -> 1.2f
            TextImportance.Normal -> 1.0f
            TextImportance.Low -> 0.9f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "font_size_animation"
    )

    Text(
        text = text,
        style = style.copy(
            fontSize = style.fontSize * animatedFontSize
        ),
        modifier = modifier
    )
}

enum class TextImportance {
    Critical,       // 关键信息 - 1.5x
    High,           // 重要信息 - 1.2x
    Normal,         // 普通信息 - 1.0x
    Low             // 次要信息 - 0.9x
}
```

### 4. 微交互动效
```kotlin
// 按钮点击微交互
@Composable
fun MicroInteractionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "button_scale"
    )

    Button(
        onClick = {
            onClick()
            // 触觉反馈
        },
        modifier = modifier
            .scale(scale)
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    }
                )
            }
    ) {
        content()
    }
}
```

---

## 🎮 游戏专用设计规范

### 俄罗斯方块界面布局
```kotlin
// 移动端竖屏布局
@Composable
fun TetrisMobilePortraitLayout(
    gameState: TetrisGameState,
    onAction: (TetrisAction) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 顶部信息栏 - Bento网格风格
        BentoInfoGrid(
            score = gameState.score,
            level = gameState.level,
            lines = gameState.lines,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )

        // 游戏区域
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 左侧预览区
            Column(
                modifier = Modifier.width(80.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NextPiecePreview(piece = gameState.nextPiece)
                HoldPiecePreview(piece = gameState.holdPiece)
            }

            // 中央游戏板
            TetrisBoard3D(
                gameState = gameState,
                modifier = Modifier.weight(1f)
            )

            // 右侧统计区
            StatisticsPanel(
                statistics = gameState.statistics,
                modifier = Modifier.width(80.dp)
            )
        }

        // 底部控制区
        TetrisControlPanel(
            onAction = onAction,
            enabled = gameState.status == TetrisStatus.PLAYING,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
    }
}
```

### 游戏状态指示器
```kotlin
@Composable
fun GameStatusIndicator(
    status: TetrisStatus,
    modifier: Modifier = Modifier
) {
    val statusColor = when (status) {
        TetrisStatus.READY -> MaterialTheme.colorScheme.primary
        TetrisStatus.PLAYING -> Color(0xFF4CAF50)
        TetrisStatus.PAUSED -> Color(0xFFFF9800)
        TetrisStatus.GAME_OVER -> Color(0xFFF44336)
    }

    val statusText = when (status) {
        TetrisStatus.READY -> "准备就绪"
        TetrisStatus.PLAYING -> "游戏中"
        TetrisStatus.PAUSED -> "已暂停"
        TetrisStatus.GAME_OVER -> "游戏结束"
    }

    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = statusColor.copy(alpha = 0.1f)
        ),
        border = BorderStroke(1.dp, statusColor)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(statusColor, CircleShape)
            )
            Text(
                text = statusText,
                style = MaterialTheme.typography.labelSmall,
                color = statusColor
            )
        }
    }
}
```

---

## 📐 网格系统与布局

### Bento网格实现
```kotlin
@Composable
fun BentoInfoGrid(
    score: Int,
    level: Int,
    lines: Int,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item(span = { GridItemSpan(2) }) {
            // 分数卡片 - 占据2列
            GameInfoCard(
                title = "分数",
                value = score.toString(),
                icon = Icons.Default.Star,
                style = CardStyle.Elevated
            )
        }

        item {
            // 等级卡片 - 占据1列
            GameInfoCard(
                title = "等级",
                value = level.toString(),
                icon = Icons.Default.TrendingUp,
                style = CardStyle.Filled
            )
        }

        item {
            // 行数卡片 - 占据1列
            GameInfoCard(
                title = "行数",
                value = lines.toString(),
                icon = Icons.Default.ViewHeadline,
                style = CardStyle.Outlined
            )
        }

        item(span = { GridItemSpan(2) }) {
            // 时间卡片 - 占据2列
            GameTimeCard(
                modifier = Modifier.height(60.dp)
            )
        }
    }
}
```

---

## 🎨 主题切换动画

### 主题过渡动效
```kotlin
@Composable
fun AnimatedThemeTransition(
    targetTheme: ThemeMode,
    content: @Composable () -> Unit
) {
    val transition = updateTransition(
        targetState = targetTheme,
        label = "theme_transition"
    )

    val backgroundColor by transition.animateColor(
        transitionSpec = {
            tween(
                durationMillis = 500,
                easing = FastOutSlowInEasing
            )
        },
        label = "background_color"
    ) { theme ->
        when (theme) {
            ThemeMode.LIGHT -> Color(0xFFFFFBFE)
            ThemeMode.DARK -> Color(0xFF1C1B1F)
            ThemeMode.LOW_LIGHT -> Color(0xFF0A0A0A)
            else -> Color(0xFFFFFBFE)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        content()
    }
}
```

---

## 📊 设计系统度量

### 设计令牌 (Design Tokens)
```kotlin
object DesignTokens {
    // 圆角系统
    object CornerRadius {
        val None = 0.dp
        val Small = 4.dp
        val Medium = 8.dp
        val Large = 12.dp
        val ExtraLarge = 16.dp
        val Circle = 50.dp
    }

    // 阴影系统
    object Elevation {
        val Level0 = 0.dp
        val Level1 = 1.dp
        val Level2 = 3.dp
        val Level3 = 6.dp
        val Level4 = 8.dp
        val Level5 = 12.dp
    }

    // 边框系统
    object BorderWidth {
        val None = 0.dp
        val Thin = 1.dp
        val Medium = 2.dp
        val Thick = 4.dp
    }

    // 透明度系统
    object Alpha {
        val Invisible = 0.0f
        val Disabled = 0.38f
        val Medium = 0.6f
        val High = 0.87f
        val Opaque = 1.0f
    }
}
```

---

## 🔍 质量保证

### 设计审查清单
- [ ] **色彩对比度**: 所有文字对比度 ≥ 4.5:1
- [ ] **触摸目标**: 所有可点击元素 ≥ 48dp
- [ ] **字体大小**: 最小字体 ≥ 12sp
- [ ] **动画性能**: 所有动画保持60fps
- [ ] **响应式设计**: 适配所有屏幕尺寸
- [ ] **无障碍支持**: 支持TalkBack和其他辅助功能
- [ ] **主题一致性**: 深色/浅色模式完整支持
- [ ] **国际化**: 支持多语言布局

### 自动化测试
```kotlin
// UI组件测试示例
@Test
fun testButtonAccessibility() {
    composeTestRule.setContent {
        QuesticleButton(
            text = "测试按钮",
            onClick = { }
        )
    }

    composeTestRule
        .onNodeWithText("测试按钮")
        .assertIsDisplayed()
        .assertHasClickAction()
        .assertHeightIsAtLeast(48.dp)
}
```

---

*本文档将持续更新，确保与最新设计趋势和技术发展保持同步。*
