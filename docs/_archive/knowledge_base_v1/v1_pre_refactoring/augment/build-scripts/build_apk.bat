@echo off
setlocal enabledelayedexpansion

REM Questicle APK自动化打包脚本 (Windows版本)
REM 版本: v1.0
REM 更新: 2025-06-20

title Questicle APK Builder

REM 项目信息
set PROJECT_NAME=Questicle
set BUILD_DIR=app\build\outputs\apk
set BUNDLE_DIR=app\build\outputs\bundle

REM 颜色定义 (Windows 10+)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

REM 打印带颜色的消息
:print_message
echo %~2
goto :eof

REM 打印标题
:print_title
echo.
echo ==================================
echo %~1
echo ==================================
echo.
goto :eof

REM 检查环境
:check_environment
call :print_title "🔍 检查构建环境"

REM 检查是否在项目根目录
if not exist "gradlew.bat" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java
    pause
    exit /b 1
) else (
    echo ✅ Java已安装
)

REM 检查Android SDK
if defined ANDROID_HOME (
    echo ✅ Android SDK: %ANDROID_HOME%
) else (
    echo ⚠️  警告: ANDROID_HOME未设置
)

echo ✅ 环境检查完成
goto :eof

REM 清理构建
:clean_build
call :print_title "🧹 清理构建缓存"

echo 正在清理...
call gradlew.bat clean --quiet
if %errorlevel% neq 0 (
    echo ❌ 清理失败
    pause
    exit /b 1
)

echo ✅ 清理完成
goto :eof

REM 构建Debug版本
:build_debug
call :print_title "📱 构建Debug版本"

echo 正在构建Debug APK...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ Debug APK构建失败
    pause
    exit /b 1
)

if exist "%BUILD_DIR%\debug\app-debug.apk" (
    echo ✅ Debug APK构建成功
    echo 📍 位置: %BUILD_DIR%\debug\app-debug.apk
) else (
    echo ❌ Debug APK文件未找到
    pause
    exit /b 1
)
goto :eof

REM 构建Release版本
:build_release
call :print_title "🎯 构建Release版本"

echo 正在构建Release APK...
call gradlew.bat assembleRelease
if %errorlevel% neq 0 (
    echo ❌ Release APK构建失败
    pause
    exit /b 1
)

if exist "%BUILD_DIR%\release\app-release.apk" (
    echo ✅ Release APK构建成功
    echo 📍 位置: %BUILD_DIR%\release\app-release.apk
) else (
    echo ❌ Release APK文件未找到
    pause
    exit /b 1
)
goto :eof

REM 构建AAB包
:build_bundle
call :print_title "📦 构建AAB包"

echo 正在构建Release AAB...
call gradlew.bat bundleRelease
if %errorlevel% neq 0 (
    echo ❌ Release AAB构建失败
    pause
    exit /b 1
)

if exist "%BUNDLE_DIR%\release\app-release.aab" (
    echo ✅ Release AAB构建成功
    echo 📍 位置: %BUNDLE_DIR%\release\app-release.aab
) else (
    echo ❌ Release AAB文件未找到
    pause
    exit /b 1
)
goto :eof

REM 运行测试
:run_tests
call :print_title "🧪 运行测试"

echo 正在运行单元测试...
call gradlew.bat test --continue

echo 正在运行代码检查...
call gradlew.bat lint --continue

echo ✅ 测试完成
goto :eof

REM 生成构建报告
:generate_report
call :print_title "📊 生成构建报告"

set REPORT_FILE=build_report_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set REPORT_FILE=%REPORT_FILE: =0%

(
echo Questicle APK构建报告
echo =====================
echo 构建时间: %date% %time%
echo 构建机器: %COMPUTERNAME%
echo 操作系统: %OS%
echo.

if exist "%BUILD_DIR%\debug\app-debug.apk" (
    echo Debug APK:
    echo   文件: %BUILD_DIR%\debug\app-debug.apk
    echo   修改时间: 
    forfiles /p "%BUILD_DIR%\debug" /m "app-debug.apk" /c "cmd /c echo @fdate @ftime" 2>nul
    echo.
)

if exist "%BUILD_DIR%\release\app-release.apk" (
    echo Release APK:
    echo   文件: %BUILD_DIR%\release\app-release.apk
    echo   修改时间: 
    forfiles /p "%BUILD_DIR%\release" /m "app-release.apk" /c "cmd /c echo @fdate @ftime" 2>nul
    echo.
)

if exist "%BUNDLE_DIR%\release\app-release.aab" (
    echo Release AAB:
    echo   文件: %BUNDLE_DIR%\release\app-release.aab
    echo   修改时间: 
    forfiles /p "%BUNDLE_DIR%\release" /m "app-release.aab" /c "cmd /c echo @fdate @ftime" 2>nul
    echo.
)
) > "%REPORT_FILE%"

echo ✅ 构建报告已生成: %REPORT_FILE%
goto :eof

REM 打开输出目录
:open_output_directory
call :print_title "📂 打开输出目录"
explorer "%BUILD_DIR%"
goto :eof

REM 显示帮助信息
:show_help
echo Questicle APK自动化打包脚本 (Windows版本)
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   1 - 构建Debug版本
echo   2 - 构建Release版本
echo   3 - 构建AAB包
echo   4 - 运行测试
echo   5 - 清理构建
echo   6 - 构建所有版本
echo   0 - 退出
echo.
goto :eof

REM 主菜单
:main_menu
cls
call :print_title "🚀 %PROJECT_NAME% APK自动化打包"

echo 请选择操作:
echo.
echo 1. 构建Debug版本
echo 2. 构建Release版本
echo 3. 构建AAB包
echo 4. 运行测试
echo 5. 清理构建
echo 6. 构建所有版本
echo 7. 显示帮助
echo 0. 退出
echo.

set /p choice=请输入选项 (0-7): 

if "%choice%"=="1" goto option_debug
if "%choice%"=="2" goto option_release
if "%choice%"=="3" goto option_bundle
if "%choice%"=="4" goto option_test
if "%choice%"=="5" goto option_clean
if "%choice%"=="6" goto option_all
if "%choice%"=="7" goto option_help
if "%choice%"=="0" goto exit_script

echo 无效选项，请重新选择
pause
goto main_menu

:option_debug
call :check_environment
call :build_debug
call :generate_report
call :open_output_directory
pause
goto main_menu

:option_release
call :check_environment
call :build_release
call :generate_report
call :open_output_directory
pause
goto main_menu

:option_bundle
call :check_environment
call :build_bundle
call :generate_report
call :open_output_directory
pause
goto main_menu

:option_test
call :check_environment
call :run_tests
pause
goto main_menu

:option_clean
call :check_environment
call :clean_build
pause
goto main_menu

:option_all
call :check_environment
call :clean_build
call :run_tests
call :build_debug
call :build_release
call :build_bundle
call :generate_report
call :print_title "🎉 构建完成"
echo 所有APK已成功构建！
call :open_output_directory
pause
goto main_menu

:option_help
call :show_help
pause
goto main_menu

:exit_script
echo 感谢使用 Questicle APK 构建工具！
pause
exit /b 0

REM 程序入口点
if "%~1"=="" (
    goto main_menu
) else (
    call :check_environment
    if "%~1"=="debug" call :build_debug
    if "%~1"=="release" call :build_release
    if "%~1"=="bundle" call :build_bundle
    if "%~1"=="test" call :run_tests
    if "%~1"=="clean" call :clean_build
    if "%~1"=="all" (
        call :clean_build
        call :run_tests
        call :build_debug
        call :build_release
        call :build_bundle
    )
    call :generate_report
    call :open_output_directory
)
