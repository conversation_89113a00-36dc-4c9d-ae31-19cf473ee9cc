# 🛠️ Questicle APK构建脚本

这个目录包含了Questicle项目的自动化构建脚本，帮助您快速打包APK文件。

## 📁 文件说明

| 文件 | 平台 | 描述 |
|------|------|------|
| `build_apk.sh` | macOS/Linux | Shell脚本，支持命令行参数 |
| `build_apk.bat` | Windows | 批处理脚本，支持交互式菜单 |
| `README.md` | 通用 | 使用说明文档 |

## 🚀 快速开始

### macOS/Linux 用户

1. **赋予执行权限**
   ```bash
   chmod +x docs/augment/build-scripts/build_apk.sh
   ```

2. **运行脚本**
   ```bash
   # 在项目根目录运行
   ./docs/augment/build-scripts/build_apk.sh
   ```

3. **查看帮助**
   ```bash
   ./docs/augment/build-scripts/build_apk.sh --help
   ```

### Windows 用户

1. **双击运行**
   - 双击 `build_apk.bat` 文件
   - 按照菜单提示操作

2. **命令行运行**
   ```cmd
   # 在项目根目录运行
   docs\augment\build-scripts\build_apk.bat
   ```

## 📋 功能特性

### Shell脚本 (build_apk.sh)

#### 支持的命令行参数
```bash
./build_apk.sh [选项]

选项:
  -h, --help     显示帮助信息
  -d, --debug    仅构建Debug版本
  -r, --release  仅构建Release版本
  -b, --bundle   仅构建AAB包
  -t, --test     运行测试
  -c, --clean    清理构建
  -a, --all      构建所有版本（默认）
```

#### 使用示例
```bash
# 构建所有版本
./build_apk.sh

# 仅构建Debug版本
./build_apk.sh --debug

# 构建Release版本并运行测试
./build_apk.sh --release --test

# 清理后构建所有版本
./build_apk.sh --clean --all
```

### 批处理脚本 (build_apk.bat)

#### 交互式菜单
```
1. 构建Debug版本
2. 构建Release版本
3. 构建AAB包
4. 运行测试
5. 清理构建
6. 构建所有版本
7. 显示帮助
0. 退出
```

#### 命令行参数
```cmd
build_apk.bat [选项]

选项:
  debug    - 构建Debug版本
  release  - 构建Release版本
  bundle   - 构建AAB包
  test     - 运行测试
  clean    - 清理构建
  all      - 构建所有版本
```

## 🔧 脚本功能

### 环境检查
- ✅ 检查Java版本
- ✅ 检查Android SDK
- ✅ 验证项目目录
- ✅ 检查Gradle环境

### 构建功能
- 📱 构建Debug APK
- 🎯 构建Release APK
- 📦 构建AAB包 (Google Play)
- 🧹 清理构建缓存
- 🧪 运行单元测试和代码检查

### 验证功能
- 🔍 APK文件验证
- 📊 包信息提取
- 🔐 签名验证 (Release版本)
- 📈 构建报告生成

### 便利功能
- 📂 自动打开输出目录
- 📄 生成详细构建报告
- 🎨 彩色输出 (Shell脚本)
- ⏱️ 构建时间统计

## 📍 输出位置

### APK文件
```
app/build/outputs/apk/
├── debug/
│   └── app-debug.apk
└── release/
    └── app-release.apk
```

### AAB文件
```
app/build/outputs/bundle/
└── release/
    └── app-release.aab
```

### 构建报告
```
build_report_YYYYMMDD_HHMMSS.txt
```

## 🚨 故障排除

### 常见问题

#### 1. 权限错误 (macOS/Linux)
```bash
# 解决方案
chmod +x docs/augment/build-scripts/build_apk.sh
```

#### 2. Java未找到
```bash
# 检查Java安装
java -version

# 设置JAVA_HOME (如果需要)
export JAVA_HOME=/path/to/java
```

#### 3. Android SDK未找到
```bash
# 设置ANDROID_HOME
export ANDROID_HOME=/path/to/android-sdk
```

#### 4. 内存不足
```bash
# 增加Gradle内存
export GRADLE_OPTS="-Xmx8192m -XX:MaxMetaspaceSize=1024m"
```

#### 5. 构建失败
```bash
# 清理并重试
./gradlew clean
./build_apk.sh --clean --all
```

### 调试模式

#### Shell脚本调试
```bash
# 启用详细输出
bash -x docs/augment/build-scripts/build_apk.sh
```

#### 查看详细构建日志
```bash
# 手动运行Gradle命令查看详细信息
./gradlew assembleDebug --info --stacktrace
```

## 📝 自定义配置

### 修改脚本变量

在脚本开头可以修改以下变量：

```bash
# Shell脚本 (build_apk.sh)
PROJECT_NAME="Questicle"
BUILD_DIR="app/build/outputs/apk"
BUNDLE_DIR="app/build/outputs/bundle"
```

```batch
REM 批处理脚本 (build_apk.bat)
set PROJECT_NAME=Questicle
set BUILD_DIR=app\build\outputs\apk
set BUNDLE_DIR=app\build\outputs\bundle
```

### 添加自定义构建步骤

可以在脚本中添加自定义的构建步骤，例如：

```bash
# 添加代码格式化
./gradlew ktlintFormat

# 添加安全扫描
./gradlew detekt

# 添加性能测试
./gradlew connectedAndroidTest
```

## 🔗 相关文档

- [完整APK打包手册](../apk-packaging-manual.md)
- [快速参考卡片](../apk-quick-reference.md)
- [项目架构说明](../../backup/项目结构说明.md)

## 📞 技术支持

如果遇到问题：

1. 查看 [故障排除](#-故障排除) 部分
2. 参考 [完整打包手册](../apk-packaging-manual.md)
3. 提交 GitHub Issue

## 📄 许可证

这些脚本遵循项目的开源许可证。

---

**💡 提示**: 首次运行脚本时，Gradle可能需要下载依赖，请耐心等待。后续构建会利用缓存显著加速。
