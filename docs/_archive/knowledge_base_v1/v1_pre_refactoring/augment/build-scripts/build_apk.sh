#!/bin/bash

# Questicle APK自动化打包脚本
# 版本: v1.0
# 更新: 2025-06-20

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="Questicle"
BUILD_DIR="app/build/outputs/apk"
BUNDLE_DIR="app/build/outputs/bundle"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "=================================="
    print_message $BLUE "$1"
    print_message $BLUE "=================================="
    echo
}

# 检查环境
check_environment() {
    print_title "🔍 检查构建环境"
    
    # 检查是否在项目根目录
    if [ ! -f "gradlew" ]; then
        print_message $RED "❌ 错误: 请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查Java版本
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        print_message $GREEN "✅ Java版本: $JAVA_VERSION"
    else
        print_message $RED "❌ 错误: 未找到Java"
        exit 1
    fi
    
    # 检查Android SDK
    if [ -n "$ANDROID_HOME" ]; then
        print_message $GREEN "✅ Android SDK: $ANDROID_HOME"
    else
        print_message $YELLOW "⚠️  警告: ANDROID_HOME未设置"
    fi
    
    print_message $GREEN "✅ 环境检查完成"
}

# 清理构建
clean_build() {
    print_title "🧹 清理构建缓存"
    
    print_message $YELLOW "正在清理..."
    ./gradlew clean --quiet
    
    print_message $GREEN "✅ 清理完成"
}

# 构建Debug版本
build_debug() {
    print_title "📱 构建Debug版本"

    print_message $YELLOW "正在构建Demo Debug APK..."
    ./gradlew assembleDemoDebug

    if [ -f "$BUILD_DIR/demo/debug/app-demo-debug.apk" ]; then
        local size=$(du -h "$BUILD_DIR/demo/debug/app-demo-debug.apk" | cut -f1)
        print_message $GREEN "✅ Demo Debug APK构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUILD_DIR/demo/debug/app-demo-debug.apk"
    else
        print_message $RED "❌ Demo Debug APK构建失败"
        exit 1
    fi

    print_message $YELLOW "正在构建Prod Debug APK..."
    ./gradlew assembleProdDebug

    if [ -f "$BUILD_DIR/prod/debug/app-prod-debug.apk" ]; then
        local size=$(du -h "$BUILD_DIR/prod/debug/app-prod-debug.apk" | cut -f1)
        print_message $GREEN "✅ Prod Debug APK构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUILD_DIR/prod/debug/app-prod-debug.apk"
    else
        print_message $RED "❌ Prod Debug APK构建失败"
        exit 1
    fi
}

# 构建Release版本
build_release() {
    print_title "🎯 构建Release版本"

    print_message $YELLOW "正在构建Demo Release APK..."
    ./gradlew assembleDemoRelease

    if [ -f "$BUILD_DIR/demo/release/app-demo-release.apk" ]; then
        local size=$(du -h "$BUILD_DIR/demo/release/app-demo-release.apk" | cut -f1)
        print_message $GREEN "✅ Demo Release APK构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUILD_DIR/demo/release/app-demo-release.apk"
    else
        print_message $RED "❌ Demo Release APK构建失败"
        exit 1
    fi

    print_message $YELLOW "正在构建Prod Release APK..."
    ./gradlew assembleProdRelease

    if [ -f "$BUILD_DIR/prod/release/app-prod-release.apk" ]; then
        local size=$(du -h "$BUILD_DIR/prod/release/app-prod-release.apk" | cut -f1)
        print_message $GREEN "✅ Prod Release APK构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUILD_DIR/prod/release/app-prod-release.apk"
    else
        print_message $RED "❌ Prod Release APK构建失败"
        exit 1
    fi
}

# 构建AAB包
build_bundle() {
    print_title "📦 构建AAB包"

    print_message $YELLOW "正在构建Demo Release AAB..."
    ./gradlew bundleDemoRelease

    if [ -f "$BUNDLE_DIR/demoRelease/app-demo-release.aab" ]; then
        local size=$(du -h "$BUNDLE_DIR/demoRelease/app-demo-release.aab" | cut -f1)
        print_message $GREEN "✅ Demo Release AAB构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUNDLE_DIR/demoRelease/app-demo-release.aab"
    else
        print_message $RED "❌ Demo Release AAB构建失败"
        exit 1
    fi

    print_message $YELLOW "正在构建Prod Release AAB..."
    ./gradlew bundleProdRelease

    if [ -f "$BUNDLE_DIR/prodRelease/app-prod-release.aab" ]; then
        local size=$(du -h "$BUNDLE_DIR/prodRelease/app-prod-release.aab" | cut -f1)
        print_message $GREEN "✅ Prod Release AAB构建成功 (大小: $size)"
        print_message $BLUE "📍 位置: $BUNDLE_DIR/prodRelease/app-prod-release.aab"
    else
        print_message $RED "❌ Prod Release AAB构建失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    print_title "🧪 运行测试"
    
    print_message $YELLOW "正在运行单元测试..."
    ./gradlew test --continue
    
    print_message $YELLOW "正在运行代码检查..."
    ./gradlew lint --continue
    
    print_message $GREEN "✅ 测试完成"
}

# 验证APK
verify_apk() {
    local apk_path=$1
    local apk_name=$2
    
    if [ -f "$apk_path" ]; then
        print_message $YELLOW "正在验证 $apk_name..."
        
        # 检查APK信息
        if command -v aapt &> /dev/null; then
            local package_name=$(aapt dump badging "$apk_path" | grep package | awk '{print $2}' | sed "s/name='\(.*\)'/\1/")
            local version_name=$(aapt dump badging "$apk_path" | grep versionName | awk '{print $4}' | sed "s/versionName='\(.*\)'/\1/")
            print_message $GREEN "📦 包名: $package_name"
            print_message $GREEN "🏷️  版本: $version_name"
        fi
        
        # 检查签名（仅Release版本）
        if [[ "$apk_name" == *"release"* ]]; then
            if command -v jarsigner &> /dev/null; then
                if jarsigner -verify "$apk_path" &> /dev/null; then
                    print_message $GREEN "✅ APK签名验证通过"
                else
                    print_message $RED "❌ APK签名验证失败"
                fi
            fi
        fi
    fi
}

# 生成构建报告
generate_report() {
    print_title "📊 生成构建报告"
    
    local report_file="build_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Questicle APK构建报告"
        echo "====================="
        echo "构建时间: $(date)"
        echo "构建机器: $(hostname)"
        echo "操作系统: $(uname -s)"
        echo
        
        if [ -f "$BUILD_DIR/debug/app-debug.apk" ]; then
            echo "Debug APK:"
            echo "  文件: $BUILD_DIR/debug/app-debug.apk"
            echo "  大小: $(du -h "$BUILD_DIR/debug/app-debug.apk" | cut -f1)"
            echo "  修改时间: $(stat -c %y "$BUILD_DIR/debug/app-debug.apk" 2>/dev/null || stat -f %Sm "$BUILD_DIR/debug/app-debug.apk")"
            echo
        fi
        
        if [ -f "$BUILD_DIR/release/app-release.apk" ]; then
            echo "Release APK:"
            echo "  文件: $BUILD_DIR/release/app-release.apk"
            echo "  大小: $(du -h "$BUILD_DIR/release/app-release.apk" | cut -f1)"
            echo "  修改时间: $(stat -c %y "$BUILD_DIR/release/app-release.apk" 2>/dev/null || stat -f %Sm "$BUILD_DIR/release/app-release.apk")"
            echo
        fi
        
        if [ -f "$BUNDLE_DIR/release/app-release.aab" ]; then
            echo "Release AAB:"
            echo "  文件: $BUNDLE_DIR/release/app-release.aab"
            echo "  大小: $(du -h "$BUNDLE_DIR/release/app-release.aab" | cut -f1)"
            echo "  修改时间: $(stat -c %y "$BUNDLE_DIR/release/app-release.aab" 2>/dev/null || stat -f %Sm "$BUNDLE_DIR/release/app-release.aab")"
            echo
        fi
        
    } > "$report_file"
    
    print_message $GREEN "✅ 构建报告已生成: $report_file"
}

# 打开输出目录
open_output_directory() {
    print_title "📂 打开输出目录"
    
    if command -v open &> /dev/null; then
        # macOS
        open "$BUILD_DIR"
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "$BUILD_DIR"
    elif command -v explorer &> /dev/null; then
        # Windows
        explorer "$BUILD_DIR"
    else
        print_message $YELLOW "请手动打开目录: $BUILD_DIR"
    fi
}

# 显示帮助信息
show_help() {
    echo "Questicle APK自动化打包脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -d, --debug    仅构建Debug版本"
    echo "  -r, --release  仅构建Release版本"
    echo "  -b, --bundle   仅构建AAB包"
    echo "  -t, --test     运行测试"
    echo "  -c, --clean    清理构建"
    echo "  -a, --all      构建所有版本（默认）"
    echo
    echo "示例:"
    echo "  $0              # 构建所有版本"
    echo "  $0 -d           # 仅构建Debug版本"
    echo "  $0 -r -t        # 构建Release版本并运行测试"
}

# 主函数
main() {
    local build_debug=false
    local build_release=false
    local build_bundle=false
    local run_test=false
    local clean_first=false
    local build_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--debug)
                build_debug=true
                build_all=false
                shift
                ;;
            -r|--release)
                build_release=true
                build_all=false
                shift
                ;;
            -b|--bundle)
                build_bundle=true
                build_all=false
                shift
                ;;
            -t|--test)
                run_test=true
                shift
                ;;
            -c|--clean)
                clean_first=true
                shift
                ;;
            -a|--all)
                build_all=true
                shift
                ;;
            *)
                print_message $RED "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示欢迎信息
    print_title "🚀 $PROJECT_NAME APK自动化打包"
    
    # 检查环境
    check_environment
    
    # 清理构建（如果需要）
    if [ "$clean_first" = true ]; then
        clean_build
    fi
    
    # 运行测试（如果需要）
    if [ "$run_test" = true ]; then
        run_tests
    fi
    
    # 构建APK
    if [ "$build_all" = true ]; then
        build_debug
        build_release
        build_bundle
    else
        if [ "$build_debug" = true ]; then
            build_debug
        fi
        if [ "$build_release" = true ]; then
            build_release
        fi
        if [ "$build_bundle" = true ]; then
            build_bundle
        fi
    fi
    
    # 验证APK
    if [ -f "$BUILD_DIR/debug/app-debug.apk" ]; then
        verify_apk "$BUILD_DIR/debug/app-debug.apk" "debug"
    fi
    if [ -f "$BUILD_DIR/release/app-release.apk" ]; then
        verify_apk "$BUILD_DIR/release/app-release.apk" "release"
    fi
    
    # 生成报告
    generate_report
    
    # 显示完成信息
    print_title "🎉 构建完成"
    print_message $GREEN "所有APK已成功构建！"
    
    # 打开输出目录
    open_output_directory
}

# 运行主函数
main "$@"
