# Kotest 版本冲突解决方案

## 问题描述

在项目构建过程中遇到了以下错误：
```
There are multiple dependencies io.kotest:kotest-runner-junit5 but with different version
```

这个错误表明项目中存在多个不同版本的 Kotest 依赖，导致依赖冲突。

## 根本原因分析

### 1. **重复依赖定义**
在 `gradle/libs.versions.toml` 中发现了重复的 kotest 依赖定义：
```toml
kotest-runner = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-runner-junit5 = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-assertions = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
kotest-assertions-core = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
```

### 2. **不一致的引用**
在不同的依赖包中使用了不同的引用名称：
- `testing` bundle 使用了 `kotest-runner` 和 `kotest-assertions`
- `unit-testing` bundle 使用了 `kotest-assertions-core`

### 3. **版本管理问题**
虽然都引用了相同的版本变量 `kotest = "5.9.1"`，但重复的定义可能导致 Gradle 解析时的混乱。

## 解决方案

### 第一步：统一版本管理

确认使用最新稳定版本 **Kotest 5.9.1**（截至2024年6月的最新稳定版本）。

### 第二步：清理重复定义

**修改前：**
```toml
kotest-runner = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-runner-junit5 = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-assertions = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
kotest-assertions-core = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
```

**修改后：**
```toml
# Kotest 测试框架 - 统一版本管理
kotest-runner-junit5 = { group = "io.kotest", name = "kotest-runner-junit5", version.ref = "kotest" }
kotest-assertions-core = { group = "io.kotest", name = "kotest-assertions-core", version.ref = "kotest" }
kotest-property = { group = "io.kotest", name = "kotest-property", version.ref = "kotest" }
```

### 第三步：统一依赖包引用

**修改前：**
```toml
testing = [
    "junit5-api",
    "junit5-engine", 
    "junit5-params",
    "mockk",
    "turbine",
    "kotest-runner",           # 旧引用
    "kotest-assertions",       # 旧引用
    "kotlinx-coroutines-test"
]
```

**修改后：**
```toml
testing = [
    "junit5-api",
    "junit5-engine",
    "junit5-params", 
    "mockk",
    "turbine",
    "kotest-runner-junit5",    # 统一引用
    "kotest-assertions-core",  # 统一引用
    "kotlinx-coroutines-test"
]
```

### 第四步：验证模块引用

检查所有模块中的 kotest 引用，确保使用正确的引用名称：
- ✅ `libs.kotest.assertions.core` - 正确
- ✅ `libs.kotest.runner.junit5` - 正确

## 验证结果

### 1. **依赖报告验证**
运行依赖检查命令：
```bash
./gradlew :core:testing:dependencies --configuration api
```

结果显示所有 kotest 依赖都统一使用版本 5.9.1：
```
+--- io.kotest:kotest-assertions-core:5.9.1 (n)
+--- io.kotest:kotest-runner-junit5:5.9.1 (n)
```

### 2. **构建验证**
运行构建测试：
```bash
./gradlew :core:testing:compileDemoDebugKotlin --warning-mode all
```

✅ **构建成功** - 2分5秒完成，无版本冲突错误

### 3. **无警告验证**
构建过程中没有出现任何 kotest 版本冲突的警告或错误。

## 最佳实践总结

### 1. **版本目录管理**
- 为每个依赖使用唯一的标识符
- 避免重复定义相同的依赖
- 使用清晰、一致的命名约定

### 2. **依赖包设计**
- 在依赖包中使用一致的引用名称
- 定期检查和清理不使用的依赖定义
- 为不同用途创建专门的依赖包

### 3. **版本管理策略**
- 定期更新到最新稳定版本
- 使用版本目录集中管理所有版本
- 避免在模块中硬编码版本号

### 4. **验证流程**
- 使用 `dependencies` 任务检查依赖树
- 运行构建测试验证无冲突
- 定期审查版本目录的一致性

## 后续维护建议

1. **定期更新**：关注 Kotest 的新版本发布，及时更新到最新稳定版本
2. **依赖审计**：定期运行依赖检查，确保没有新的版本冲突
3. **文档维护**：更新开发文档，说明正确的 kotest 依赖使用方式
4. **团队培训**：确保团队成员了解版本目录的正确使用方法

## 相关文件

- `gradle/libs.versions.toml` - 版本目录主文件
- `core/testing/build.gradle.kts` - 测试模块配置
- `docs/augment/build-standardization-report.md` - 构建标准化报告

通过这次修复，项目现在具有了统一、清晰的 Kotest 依赖管理，为后续的测试开发提供了稳定的基础。
