# 🎨 Questicle UI/UX 设计规格说明书 2025

## 文档信息
- **文档标题**: Questicle UI/UX 设计规格说明书
- **文档版本**: v1.0.0
- **创建日期**: 2025-06-21
- **文档状态**: ✅ 制定中
- **作者**: Augment Agent
- **审核人**: 设计团队
- **批准人**: 产品负责人

## 📖 变更历史
| 版本 | 日期 | 作者 | 变更内容 |
|------|------|------|----------|
| v1.0.0 | 2025-06-21 | Augment Agent | 初始版本，基于2025年最新设计趋势 |

## 🎯 设计理念

### 核心设计原则
1. **简约至上** - 清晰、直观的界面设计
2. **游戏优先** - 以游戏体验为中心的设计
3. **现代感** - 融合2025年最新设计趋势
4. **无障碍** - 确保所有用户都能轻松使用
5. **性能导向** - 优化用户交互响应速度

### 设计目标
- **学习成本**: < 5分钟上手
- **操作效率**: 核心操作 < 3步完成
- **视觉愉悦**: 现代化、美观的界面
- **情感连接**: 营造沉浸式游戏体验

## 🎨 视觉设计系统

### 色彩系统

#### 主色调 (Primary Colors)
```kotlin
// 主品牌色 - 深蓝色系
val Primary = Color(0xFF1976D2)
val PrimaryVariant = Color(0xFF0D47A1)
val OnPrimary = Color(0xFFFFFFFF)

// 次要色 - 青色系
val Secondary = Color(0xFF00BCD4)
val SecondaryVariant = Color(0xFF0097A7)
val OnSecondary = Color(0xFF000000)
```

#### 游戏色彩 (Game Colors)
```kotlin
// 俄罗斯方块颜色
val TetrisPieces = mapOf(
    "I" to Color(0xFF00F5FF), // 青色 - I型
    "O" to Color(0xFFFFD700), // 金色 - O型
    "T" to Color(0xFF9C27B0), // 紫色 - T型
    "S" to Color(0xFF4CAF50), // 绿色 - S型
    "Z" to Color(0xFFF44336), // 红色 - Z型
    "J" to Color(0xFF2196F3), // 蓝色 - J型
    "L" to Color(0xFFFF9800)  // 橙色 - L型
)
```

#### 状态色彩 (Status Colors)
```kotlin
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)
```

### 字体系统

#### 字体层级
```kotlin
// 标题字体
val HeadlineLarge = TextStyle(
    fontSize = 32.sp,
    fontWeight = FontWeight.Bold,
    lineHeight = 40.sp
)

val HeadlineMedium = TextStyle(
    fontSize = 28.sp,
    fontWeight = FontWeight.Bold,
    lineHeight = 36.sp
)

// 正文字体
val BodyLarge = TextStyle(
    fontSize = 16.sp,
    fontWeight = FontWeight.Normal,
    lineHeight = 24.sp
)

val BodyMedium = TextStyle(
    fontSize = 14.sp,
    fontWeight = FontWeight.Normal,
    lineHeight = 20.sp
)

// 游戏数据字体
val GameScore = TextStyle(
    fontSize = 24.sp,
    fontWeight = FontWeight.Bold,
    fontFamily = FontFamily.Monospace
)
```

### 间距系统

#### 标准间距
```kotlin
object Spacing {
    val XSmall = 4.dp
    val Small = 8.dp
    val Medium = 16.dp
    val Large = 24.dp
    val XLarge = 32.dp
    val XXLarge = 48.dp
}
```

### 圆角系统

#### 圆角规范
```kotlin
object CornerRadius {
    val Small = 4.dp
    val Medium = 8.dp
    val Large = 16.dp
    val XLarge = 24.dp
    val Round = 50.dp
}
```

## 🏗️ 组件设计规范

### 按钮设计

#### 主要按钮 (Primary Button)
- **用途**: 主要操作（开始游戏、确认等）
- **样式**: 填充背景，圆角，阴影效果
- **尺寸**: 最小48dp高度，确保触控友好
- **状态**: Normal, Pressed, Disabled

#### 次要按钮 (Secondary Button)
- **用途**: 次要操作（设置、取消等）
- **样式**: 边框样式，透明背景
- **交互**: 悬停效果，点击反馈

#### 游戏控制按钮
- **特殊设计**: 大尺寸，易于游戏中操作
- **触觉反馈**: 支持振动反馈
- **视觉反馈**: 明显的按下效果

### 卡片设计

#### 标准卡片
```kotlin
@Composable
fun StandardCard(
    modifier: Modifier = Modifier,
    elevation: Dp = 4.dp,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = elevation),
        shape = RoundedCornerShape(CornerRadius.Medium),
        content = content
    )
}
```

#### 游戏信息卡片
- **内容**: 分数、等级、时间等游戏数据
- **布局**: 清晰的信息层级
- **动画**: 数据变化时的过渡动画

### 输入组件

#### 文本输入框
- **样式**: Material 3 OutlinedTextField
- **验证**: 实时输入验证和错误提示
- **无障碍**: 完整的标签和描述

#### 开关和选择器
- **一致性**: 统一的开关样式
- **状态**: 清晰的开启/关闭状态
- **动画**: 平滑的状态切换动画

## 🎮 游戏界面设计

### 俄罗斯方块游戏界面

#### 布局结构
```
┌─────────────────────────────────┐
│           游戏信息栏              │
│    分数 | 等级 | 行数 | 时间      │
├─────────────────────────────────┤
│  预览  │                │ 暂存  │
│  方块  │    游戏主区域    │ 方块  │
│       │                │      │
├─────────────────────────────────┤
│           控制按钮区              │
│    ← ↓ → ⟲ ⬇ ⏸ ⏹           │
└─────────────────────────────────┘
```

#### 游戏板设计
- **网格**: 10x20标准俄罗斯方块网格
- **边框**: 清晰的边界线
- **背景**: 深色背景，突出方块颜色
- **网格线**: 淡色辅助线，帮助定位

#### 方块设计
- **3D效果**: 轻微的立体感和阴影
- **颜色**: 鲜明的对比色，易于区分
- **动画**: 平滑的移动和旋转动画
- **幽灵方块**: 半透明预览落点

### 主页界面设计

#### 用户信息区
- **头像**: 圆形头像，支持自定义
- **等级**: 清晰的等级显示和进度条
- **统计**: 关键游戏数据展示

#### 游戏选择区
- **卡片布局**: 网格式游戏选择卡片
- **预览**: 游戏截图或图标
- **状态**: 新游戏标识、推荐标签

## 📱 响应式设计

### 屏幕适配

#### 手机竖屏 (Portrait)
- **主要布局**: 垂直堆叠
- **游戏区**: 占据主要屏幕空间
- **控制区**: 底部固定

#### 手机横屏 (Landscape)
- **主要布局**: 水平分布
- **游戏区**: 居中显示
- **信息区**: 左右分布

#### 平板适配
- **更大间距**: 利用额外空间
- **多列布局**: 更丰富的信息展示
- **手势支持**: 更多手势操作

### 字体缩放
- **系统字体**: 支持系统字体大小设置
- **最小尺寸**: 确保可读性
- **最大尺寸**: 避免布局破坏

## 🎭 动画设计

### 页面转场动画
```kotlin
// 淡入淡出转场
val fadeTransition = fadeIn(
    animationSpec = tween(300)
) + fadeOut(
    animationSpec = tween(300)
)

// 滑动转场
val slideTransition = slideInHorizontally(
    initialOffsetX = { it },
    animationSpec = tween(300)
) + slideOutHorizontally(
    targetOffsetX = { -it },
    animationSpec = tween(300)
)
```

### 游戏动画
- **方块下落**: 平滑的重力动画
- **行消除**: 闪烁和消失动画
- **得分**: 数字跳动和飞入动画
- **等级提升**: 庆祝动画效果

### 微交互动画
- **按钮点击**: 缩放和波纹效果
- **卡片悬停**: 轻微上升和阴影变化
- **加载状态**: 优雅的加载指示器

## 🔊 音效设计

### 音效分类
- **操作音效**: 按钮点击、方块移动
- **游戏音效**: 行消除、等级提升
- **背景音乐**: 轻松的游戏背景音
- **反馈音效**: 成功、失败、警告

### 音效控制
- **音量调节**: 独立的音效和音乐音量
- **静音模式**: 快速静音功能
- **情境感知**: 根据游戏状态调整音效

## ♿ 无障碍设计

### 视觉无障碍
- **对比度**: 符合WCAG 2.1 AA标准
- **色盲友好**: 不仅依赖颜色传达信息
- **字体大小**: 支持系统字体缩放

### 操作无障碍
- **触控目标**: 最小48dp触控区域
- **语音描述**: 完整的内容描述
- **键盘导航**: 支持外接键盘操作

### 认知无障碍
- **简单导航**: 清晰的信息架构
- **一致性**: 统一的交互模式
- **错误处理**: 友好的错误提示

## 🎯 用户体验流程

### 首次使用流程
1. **欢迎页面** - 简洁的产品介绍
2. **快速教程** - 核心操作指导
3. **游客模式** - 无需注册即可体验
4. **首次游戏** - 引导式游戏体验

### 日常使用流程
1. **快速启动** - 直接进入游戏
2. **继续游戏** - 恢复上次进度
3. **查看统计** - 个人游戏数据
4. **设置调整** - 个性化配置

## 📊 设计验证

### 可用性测试
- **任务完成率**: > 95%
- **错误率**: < 5%
- **满意度**: > 4.0/5.0
- **学习时间**: < 5分钟

### 性能指标
- **界面响应**: < 100ms
- **动画流畅**: 60fps
- **内存使用**: < 200MB
- **电池消耗**: < 10%/小时

## 🔄 迭代计划

### 短期优化 (1个月)
- [ ] 完善动画效果
- [ ] 优化触控体验
- [ ] 增强视觉反馈

### 中期优化 (3个月)
- [ ] 个性化主题
- [ ] 高级动画效果
- [ ] 社交功能界面

### 长期规划 (6个月)
- [ ] AR/VR界面探索
- [ ] AI辅助界面
- [ ] 跨平台一致性

## 🛠️ 实施指南

### 开发者实施清单

#### UI组件实施
- [ ] 创建统一的设计系统组件库
- [ ] 实现所有标准组件的@Preview注解
- [ ] 建立组件使用文档和示例
- [ ] 实施组件的无障碍支持

#### 主题系统实施
```kotlin
// 主题实施示例
@Composable
fun QuesticleTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        darkColorScheme(
            primary = Primary,
            secondary = Secondary,
            // ... 其他颜色定义
        )
    } else {
        lightColorScheme(
            primary = Primary,
            secondary = Secondary,
            // ... 其他颜色定义
        )
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = QuesticleTypography,
        content = content
    )
}
```

#### 动画实施
```kotlin
// 游戏动画实施示例
@Composable
fun AnimatedTetrisPiece(
    piece: TetrisPiece,
    isDropping: Boolean
) {
    val animatedY by animateFloatAsState(
        targetValue = if (isDropping) piece.y.toFloat() else piece.y.toFloat(),
        animationSpec = tween(
            durationMillis = if (isDropping) 100 else 300,
            easing = if (isDropping) LinearEasing else FastOutSlowInEasing
        )
    )

    // 渲染逻辑...
}
```

### 质量保证清单

#### 设计一致性检查
- [ ] 颜色使用符合设计系统
- [ ] 字体大小和层级正确
- [ ] 间距使用标准值
- [ ] 圆角使用统一规范

#### 无障碍检查
- [ ] 所有交互元素有适当的内容描述
- [ ] 颜色对比度符合标准
- [ ] 触控目标大小符合要求
- [ ] 支持屏幕阅读器

#### 性能检查
- [ ] 动画帧率稳定在60fps
- [ ] 界面响应时间 < 100ms
- [ ] 内存使用合理
- [ ] 电池消耗优化

## 📋 设计资源

### 设计文件
- **Figma设计稿**: [链接待添加]
- **图标库**: Material Icons + 自定义游戏图标
- **插画资源**: 游戏相关插画和图形
- **动画原型**: 交互动画演示

### 开发资源
- **组件库**: 完整的Compose组件实现
- **主题文件**: 颜色、字体、间距定义
- **动画库**: 常用动画效果封装
- **工具函数**: UI相关的工具函数

## 🎯 成功指标

### 用户体验指标
- **首次使用成功率**: > 95%
- **任务完成时间**: 比竞品快20%
- **用户满意度**: > 4.5/5.0
- **用户留存率**: 7日留存 > 60%

### 技术指标
- **界面加载时间**: < 1秒
- **动画流畅度**: 60fps稳定
- **崩溃率**: < 0.1%
- **内存使用**: < 150MB

### 业务指标
- **游戏完成率**: > 80%
- **平均游戏时长**: > 10分钟
- **功能使用率**: 核心功能 > 90%
- **用户反馈评分**: > 4.0/5.0

---

*文档版本: v1.0.0*
*最后更新: 2025-06-21*
*下次评审: 2025-09-21*
