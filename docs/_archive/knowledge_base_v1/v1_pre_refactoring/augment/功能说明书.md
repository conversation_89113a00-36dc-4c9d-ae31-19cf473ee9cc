# Questicle 游戏平台 - 功能说明书

## 文档信息
- **版本**: 1.0.0
- **创建日期**: 2025-01-20
- **最后更新**: 2025-01-20
- **目的**: 作为重构前的功能基线，确保重构后功能的完整性和准确性

## 1. 项目概述

### 1.1 项目简介
Questicle是一个基于Android平台的多游戏娱乐应用，采用现代化的架构设计，支持多种类型的游戏，目前主要实现了俄罗斯方块游戏。

### 1.2 技术架构
- **架构模式**: Clean Architecture + MVVM
- **UI框架**: Jetpack Compose
- **依赖注入**: Dagger Hilt
- **数据库**: Room
- **网络**: Retrofit + OkHttp
- **导航**: Navigation Compose
- **状态管理**: StateFlow + Compose State

### 1.3 模块结构
```
questicle/
├── app/                    # 主应用模块
├── core/                   # 核心功能模块
│   ├── common/            # 通用工具和扩展
│   ├── domain/            # 业务领域模型
│   ├── data/              # 数据层实现
│   ├── database/          # 数据库相关
│   ├── datastore/         # 数据存储
│   ├── network/           # 网络层
│   ├── designsystem/      # 设计系统
│   └── testing/           # 测试工具
└── feature/               # 功能模块
    ├── home/              # 主页功能
    │   ├── api/           # 主页API接口
    │   └── impl/          # 主页实现
    ├── settings/          # 设置功能
    │   ├── api/           # 设置API接口
    │   └── impl/          # 设置实现
    └── tetris/            # 俄罗斯方块游戏
        ├── api/           # 游戏API接口
        └── impl/          # 游戏实现
```

## 2. 核心功能模块

### 2.1 用户系统 (User System)

#### 2.1.1 用户模型
- **User**: 用户基本信息
  - id: 用户唯一标识
  - username: 用户名
  - email: 邮箱（可选）
  - displayName: 显示名称
  - avatarUrl: 头像URL（可选）
  - level: 用户等级
  - experience: 经验值
  - coins: 金币数量
  - gems: 宝石数量
  - preferences: 用户偏好设置
  - stats: 用户统计数据
  - achievements: 成就集合
  - friends: 好友列表
  - createdAt: 创建时间
  - lastLoginAt: 最后登录时间
  - isActive: 是否活跃

#### 2.1.2 用户统计 (UserStats)
- totalGames: 总游戏次数
- totalScore: 总分数
- totalPlayTime: 总游戏时长（秒）
- gamesWon: 获胜次数
- gamesLost: 失败次数
- currentStreak: 当前连胜
- bestStreak: 最佳连胜
- favoriteGameType: 最喜欢的游戏类型
- gameStats: 各游戏类型的详细统计

#### 2.1.3 用户偏好 (UserPreferences)
- theme: 主题设置（亮色/暗色/跟随系统）
- language: 语言设置
- soundEnabled: 音效开关
- musicEnabled: 音乐开关
- vibrationEnabled: 震动开关
- notificationsEnabled: 通知开关
- autoSaveEnabled: 自动保存开关
- difficulty: 默认难度
- privacySettings: 隐私设置

#### 2.1.4 等级系统 (UserLevelSystem)
- 基于经验值的等级计算
- 20个等级，每个等级有不同的经验值要求
- 提供等级计算、经验值查询、进度计算等功能

### 2.2 游戏系统 (Game System)

#### 2.2.1 游戏模型
- **Game**: 游戏实例
  - id: 游戏唯一标识
  - type: 游戏类型
  - status: 游戏状态
  - playerId: 玩家ID
  - score: 分数
  - level: 关卡
  - startTime: 开始时间
  - endTime: 结束时间
  - duration: 游戏时长
  - metadata: 元数据

#### 2.2.2 游戏类型 (GameType)
- TETRIS: 俄罗斯方块
- PUZZLE: 拼图游戏
- CARD: 卡牌游戏
- STRATEGY: 策略游戏
- ACTION: 动作游戏

#### 2.2.3 游戏状态 (GameStatus)
- READY: 准备开始
- PLAYING: 游戏中
- PAUSED: 暂停
- COMPLETED: 完成
- FAILED: 失败
- ABANDONED: 放弃

#### 2.2.4 难度等级 (GameDifficulty)
- EASY: 简单（1.0倍分数）
- MEDIUM: 中等（1.5倍分数）
- HARD: 困难（2.0倍分数）
- EXPERT: 专家（3.0倍分数）
- MASTER: 大师（5.0倍分数）

### 2.3 数据存储系统

#### 2.3.1 数据库 (Room Database)
- **UserEntity**: 用户数据表
- **GameEntity**: 游戏数据表
- **GameSessionEntity**: 游戏会话表
- **AchievementEntity**: 成就数据表

#### 2.3.2 数据存储 (DataStore)
- 用户偏好设置存储
- 应用配置存储
- 临时数据缓存

#### 2.3.3 网络层 (Network)
- Retrofit配置
- API接口定义
- 网络错误处理
- 请求/响应拦截器

## 3. 功能模块详述

### 3.1 主页模块 (Home Feature)

#### 3.1.1 主要功能
- 用户信息展示
- 游戏选择网格
- 快速统计卡片
- 最近游戏记录
- 成就预览

#### 3.1.2 API接口 (HomeApi)
```kotlin
interface HomeApi {
    @Composable
    fun HomeScreen(
        modifier: Modifier = Modifier,
        onNavigateToGame: (GameType) -> Unit = {},
        onNavigateToSettings: () -> Unit = {},
        onNavigateToProfile: () -> Unit = {}
    )
    
    @Composable
    fun GameSelectionGrid(
        modifier: Modifier = Modifier,
        onGameSelected: (GameType) -> Unit = {}
    )
    
    @Composable
    fun UserProfileCard(
        user: User,
        modifier: Modifier = Modifier,
        onClick: () -> Unit = {}
    )
    
    fun getHomeController(): HomeController
}
```

#### 3.1.3 控制器接口 (HomeController)
```kotlin
interface HomeController {
    val currentUser: Flow<User?>
    val availableGames: Flow<List<GameInfo>>
    val recentGames: Flow<List<RecentGameInfo>>
    val userStats: Flow<UserStatsInfo>
    
    suspend fun initialize()
    suspend fun refresh()
    suspend fun getGameStats(gameType: GameType): GameStatsInfo?
}
```

#### 3.1.4 数据模型
- **GameInfo**: 游戏信息
  - type: 游戏类型
  - name: 游戏名称
  - description: 游戏描述
  - isAvailable: 是否可用
  - bestScore: 最佳分数
  - isNew: 是否为新游戏

- **RecentGameInfo**: 最近游戏信息
  - gameId: 游戏ID
  - gameType: 游戏类型
  - score: 分数
  - level: 关卡
  - playedAt: 游戏时间
  - duration: 游戏时长

- **UserStatsInfo**: 用户统计信息
  - totalGames: 总游戏次数
  - totalScore: 总分数
  - totalPlayTime: 总游戏时长
  - achievements: 成就数量
  - currentStreak: 当前连胜
  - favoriteGame: 最喜欢的游戏

#### 3.1.5 UI组件
- **HomeScreen**: 主页面
- **UserProfileCard**: 用户资料卡片
- **GameSelectionGrid**: 游戏选择网格
- **SimpleGameCard**: 简单游戏卡片
- **QuickStatsCard**: 快速统计卡片
- **RecentGameCard**: 最近游戏卡片
- **AchievementsPreviewCard**: 成就预览卡片

### 3.2 设置模块 (Settings Feature)

#### 3.2.1 主要功能
- 用户偏好设置
- 音频设置
- 显示设置
- 游戏设置
- 隐私设置
- 关于信息

#### 3.2.2 API接口 (SettingsApi)
```kotlin
interface SettingsApi {
    @Composable
    fun SettingsScreen(
        modifier: Modifier = Modifier,
        onNavigateBack: () -> Unit = {}
    )
    
    fun getSettingsController(): SettingsController
}
```

#### 3.2.3 控制器接口 (SettingsController)
```kotlin
interface SettingsController {
    val userPreferences: Flow<UserPreferences>
    
    suspend fun updateTheme(theme: Theme)
    suspend fun updateSoundEnabled(enabled: Boolean)
    suspend fun updateMusicEnabled(enabled: Boolean)
    suspend fun updateVibrationEnabled(enabled: Boolean)
    suspend fun updateNotificationsEnabled(enabled: Boolean)
    suspend fun updateAutoSaveEnabled(enabled: Boolean)
    suspend fun updateDifficulty(difficulty: GameDifficulty)
    suspend fun updateLanguage(language: String)
    suspend fun resetToDefaults()
    suspend fun exportUserData(): String
    suspend fun importUserData(data: String): Boolean
}
```

#### 3.2.4 UI组件
- **SettingsScreen**: 设置主页面
- **AudioSettingsSection**: 音频设置区域
- **DisplaySettingsSection**: 显示设置区域
- **GameSettingsSection**: 游戏设置区域
- **PrivacySettingsSection**: 隐私设置区域
- **AboutSection**: 关于信息区域

### 3.3 俄罗斯方块模块 (Tetris Feature)

#### 3.3.1 主要功能
- 经典俄罗斯方块游戏
- 方块移动、旋转、下落
- 行消除和得分
- 等级系统
- 暂停/继续
- 游戏保存/加载
- 幽灵方块显示
- Hold功能

#### 3.3.2 API接口 (TetrisApi)
```kotlin
interface TetrisApi {
    @Composable
    fun TetrisGameScreen(
        modifier: Modifier = Modifier,
        onNavigateBack: () -> Unit = {},
        onGameComplete: (score: Int) -> Unit = {}
    )
    
    @Composable
    fun TetrisMenuScreen(
        modifier: Modifier = Modifier,
        onStartGame: () -> Unit = {},
        onNavigateBack: () -> Unit = {}
    )
    
    fun getTetrisController(): TetrisController
}
```

#### 3.3.3 控制器接口 (TetrisController)
```kotlin
interface TetrisController {
    val gameState: StateFlow<TetrisGameState>
    
    suspend fun startNewGame(playerId: String)
    suspend fun pauseGame()
    suspend fun resumeGame()
    suspend fun processAction(action: TetrisAction)
    suspend fun saveGame()
    suspend fun loadGame(gameId: String)
}
```

#### 3.3.4 游戏数据模型
- **TetrisGameState**: 游戏状态
  - board: 游戏板
  - currentPiece: 当前方块
  - nextPiece: 下一个方块
  - holdPiece: 保留方块
  - score: 分数
  - level: 等级
  - lines: 消除行数
  - status: 游戏状态
  - canHold: 是否可以保留
  - combo: 连击数
  - lastDropTime: 最后下落时间
  - statistics: 游戏统计

- **TetrisPiece**: 俄罗斯方块
  - type: 方块类型
  - x, y: 位置坐标
  - rotation: 旋转状态
  - shape: 方块形状

- **TetrisBoard**: 游戏板
  - width, height: 尺寸
  - cells: 单元格数组
  - 提供方块放置、行检查、行清除等功能

- **TetrisAction**: 游戏动作
  - Move: 移动（左/右/下）
  - Rotate: 旋转
  - Drop: 下落（软下落/硬下落）
  - Hold: 保留方块
  - Pause: 暂停

#### 3.3.5 游戏引擎 (TetrisEngine)
- 游戏逻辑处理
- 动作验证
- 分数计算
- 等级计算
- 行消除检测
- 游戏结束判断
- 统计数据收集

#### 3.3.6 UI组件
- **TetrisGameScreen**: 游戏主界面
- **TetrisBoard**: 游戏板显示
- **TetrisControls**: 游戏控制按钮
- **TetrisGameInfo**: 游戏信息显示
- **NextPiecePreview**: 下一个方块预览
- **HoldPieceDisplay**: 保留方块显示
- **ScoreDisplay**: 分数显示

## 4. 设计系统 (Design System)

### 4.1 主题系统
- **QuesticleTheme**: 应用主题
- 支持亮色/暗色主题
- Material Design 3规范
- 自定义颜色方案
- 动态颜色支持

### 4.2 组件库
- 基础组件扩展
- 自定义Compose组件
- 统一的视觉风格
- 可复用的UI元素

### 4.3 图标和资源
- Material Icons
- 自定义图标
- 多分辨率资源
- 矢量图形支持

## 5. 测试架构

### 5.1 测试策略
- 单元测试：业务逻辑测试
- 集成测试：模块间交互测试
- UI测试：用户界面测试
- 端到端测试：完整流程测试

### 5.2 测试工具
- JUnit: 单元测试框架
- Kotest: Kotlin测试库
- Compose Testing: UI测试
- Hilt Testing: 依赖注入测试

## 6. 构建和配置

### 6.1 构建系统
- Gradle Kotlin DSL
- 版本目录管理
- 自定义构建插件
- 多变体支持

### 6.2 代码质量
- Kotlin编码规范
- 静态代码分析
- 代码覆盖率检查
- 持续集成支持

## 7. 性能和优化

### 7.1 性能考虑
- Compose性能优化
- 内存管理
- 网络请求优化
- 数据库查询优化

### 7.2 用户体验
- 流畅的动画
- 响应式设计
- 无障碍支持
- 多语言支持

---

**注意**: 本文档记录了重构前的完整功能状态，作为验证重构工作完整性和准确性的基线。任何重构后的功能变更都应该与此文档进行对比验证。
