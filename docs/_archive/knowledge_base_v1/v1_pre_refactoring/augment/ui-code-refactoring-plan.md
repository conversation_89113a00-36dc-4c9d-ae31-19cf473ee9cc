# UI代码重构实施计划

> 基于2025年设计趋势的代码重构详细方案

## 📋 重构概览

**重构目标**: 将现有UI代码升级到2025年设计标准  
**重构范围**: 主题系统、组件库、布局系统、动画系统  
**预计时间**: 4-6周  
**风险等级**: 中等

---

## 🎯 重构优先级

### 🔥 第一阶段：基础设施重构 (Week 1-2)
1. **统一设计令牌系统**
2. **主题系统现代化**
3. **组件库标准化**

### 🚀 第二阶段：体验增强 (Week 3-4)
1. **Bento网格布局实施**
2. **微交互动画升级**
3. **响应式设计完善**

### 🎯 第三阶段：高级功能 (Week 5-6)
1. **3D交互元素**
2. **AI界面准备**
3. **性能优化**

---

## 🛠️ 具体重构方案

### 1. 设计令牌系统重构

#### 当前问题
```kotlin
// 问题：硬编码值散布在各处
RoundedCornerShape(12.dp)
Modifier.padding(16.dp)
Color(0xFF1E88E5)
```

#### 重构方案
```kotlin
// 新建：ui/design2025/tokens/DesignTokens.kt
object DesignTokens {
    object Spacing {
        val xs = 4.dp
        val sm = 8.dp
        val md = 16.dp
        val lg = 24.dp
        val xl = 32.dp
    }
    
    object CornerRadius {
        val small = 8.dp
        val medium = 12.dp
        val large = 16.dp
        val extraLarge = 24.dp
    }
    
    object Colors {
        val primary = Color(0xFF1E88E5)
        val secondary = Color(0xFF43A047)
        val accent = Color(0xFFFB8C00)
    }
}

// 使用示例
RoundedCornerShape(DesignTokens.CornerRadius.medium)
Modifier.padding(DesignTokens.Spacing.md)
```

#### 重构步骤
1. **创建设计令牌文件**
   ```bash
   mkdir -p app/src/main/java/com/yu/questicle/ui/design2025/tokens
   ```

2. **替换硬编码值**
   - 搜索所有 `.dp` 使用
   - 搜索所有 `Color(0x...)` 使用
   - 逐个替换为设计令牌

3. **验证替换**
   ```kotlin
   // 添加编译时检查
   @Deprecated("Use DesignTokens.Spacing instead")
   val hardcodedPadding = 16.dp
   ```

### 2. 主题系统现代化

#### 当前状态分析
```kotlin
// 现有主题系统
@Composable
fun QuesticleTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false,
    lowLightMode: Boolean = false
)
```

#### 重构目标
```kotlin
// 增强的主题系统
@Composable
fun QuesticleTheme2025(
    themeMode: ThemeMode = ThemeMode.SYSTEM,
    colorScheme: ColorSchemeType = ColorSchemeType.DYNAMIC,
    gameTheme: GameTheme? = null,
    accessibility: AccessibilityConfig = AccessibilityConfig.Default,
    content: @Composable () -> Unit
) {
    val effectiveColorScheme = when (colorScheme) {
        ColorSchemeType.DYNAMIC -> {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val context = LocalContext.current
                when (themeMode) {
                    ThemeMode.LIGHT -> dynamicLightColorScheme(context)
                    ThemeMode.DARK -> dynamicDarkColorScheme(context)
                    ThemeMode.SYSTEM -> {
                        if (isSystemInDarkTheme()) {
                            dynamicDarkColorScheme(context)
                        } else {
                            dynamicLightColorScheme(context)
                        }
                    }
                }
            } else {
                // 降级到静态主题
                getStaticColorScheme(themeMode)
            }
        }
        ColorSchemeType.GAME_SPECIFIC -> {
            gameTheme?.colorScheme ?: getStaticColorScheme(themeMode)
        }
        ColorSchemeType.STATIC -> getStaticColorScheme(themeMode)
    }
    
    MaterialTheme(
        colorScheme = effectiveColorScheme,
        typography = getAccessibleTypography(accessibility),
        shapes = DesignTokens.Shapes,
        content = {
            CompositionLocalProvider(
                LocalGameTheme provides gameTheme,
                LocalAccessibilityConfig provides accessibility,
                content = content
            )
        }
    )
}

enum class ThemeMode { LIGHT, DARK, SYSTEM }
enum class ColorSchemeType { DYNAMIC, GAME_SPECIFIC, STATIC }

data class GameTheme(
    val colorScheme: ColorScheme,
    val name: String,
    val preview: @Composable () -> Unit
)

data class AccessibilityConfig(
    val highContrast: Boolean = false,
    val largeText: Boolean = false,
    val reduceMotion: Boolean = false
) {
    companion object {
        val Default = AccessibilityConfig()
    }
}
```

### 3. 组件库标准化

#### 重构UnifiedControlButton
```kotlin
// 当前实现的问题：样式分散，缺乏一致性

// 重构后的统一按钮系统
@Composable
fun QButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    text: String? = null,
    icon: ImageVector? = null,
    style: QButtonStyle = QButtonStyle.Primary,
    size: QButtonSize = QButtonSize.Medium,
    state: QButtonState = QButtonState.Enabled,
    hapticFeedback: Boolean = true
) {
    val buttonConfig = remember(style, size) {
        QButtonConfig.from(style, size)
    }
    
    val haptic = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    // 动画状态
    val scale by animateFloatAsState(
        targetValue = if (isPressed) buttonConfig.pressedScale else 1f,
        animationSpec = buttonConfig.scaleAnimation
    )
    
    val elevation by animateDpAsState(
        targetValue = if (isPressed) buttonConfig.pressedElevation else buttonConfig.defaultElevation,
        animationSpec = buttonConfig.elevationAnimation
    )
    
    Surface(
        onClick = {
            if (hapticFeedback) {
                haptic.performHapticFeedback(buttonConfig.hapticType)
            }
            onClick()
        },
        modifier = modifier
            .scale(scale)
            .size(buttonConfig.size),
        enabled = state == QButtonState.Enabled,
        shape = buttonConfig.shape,
        color = buttonConfig.getBackgroundColor(state),
        shadowElevation = elevation,
        tonalElevation = elevation / 2,
        interactionSource = interactionSource
    ) {
        QButtonContent(
            text = text,
            icon = icon,
            config = buttonConfig,
            state = state
        )
    }
}

// 配置数据类
data class QButtonConfig(
    val size: DpSize,
    val shape: Shape,
    val defaultElevation: Dp,
    val pressedElevation: Dp,
    val pressedScale: Float,
    val scaleAnimation: AnimationSpec<Float>,
    val elevationAnimation: AnimationSpec<Dp>,
    val hapticType: HapticFeedbackType,
    val colors: QButtonColors
) {
    companion object {
        fun from(style: QButtonStyle, size: QButtonSize): QButtonConfig {
            return when (style) {
                QButtonStyle.Primary -> primaryConfig(size)
                QButtonStyle.Secondary -> secondaryConfig(size)
                QButtonStyle.Glass -> glassConfig(size)
                QButtonStyle.Neomorphic -> neomorphicConfig(size)
            }
        }
        
        private fun primaryConfig(size: QButtonSize) = QButtonConfig(
            size = size.toDpSize(),
            shape = RoundedCornerShape(DesignTokens.CornerRadius.medium),
            defaultElevation = DesignTokens.Elevation.level2,
            pressedElevation = DesignTokens.Elevation.level1,
            pressedScale = 0.95f,
            scaleAnimation = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
            elevationAnimation = tween(150),
            hapticType = HapticFeedbackType.LongPress,
            colors = QButtonColors.Primary
        )
        
        // 其他样式配置...
    }
}

enum class QButtonStyle {
    Primary,
    Secondary, 
    Glass,
    Neomorphic
}

enum class QButtonSize {
    Small,
    Medium,
    Large;
    
    fun toDpSize(): DpSize = when (this) {
        Small -> DpSize(width = 80.dp, height = 36.dp)
        Medium -> DpSize(width = 120.dp, height = 48.dp)
        Large -> DpSize(width = 160.dp, height = 56.dp)
    }
}

enum class QButtonState {
    Enabled,
    Disabled,
    Loading
}
```

### 4. Bento网格布局实施

#### 创建新的布局系统
```kotlin
// 新建：ui/design2025/layout/BentoGrid.kt

@Composable
fun BentoGrid(
    modifier: Modifier = Modifier,
    columns: Int = 2,
    spacing: Dp = DesignTokens.Spacing.md,
    content: @Composable BentoGridScope.() -> Unit
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 响应式列数
    val responsiveColumns = remember(screenWidth, columns) {
        when {
            screenWidth < 600.dp -> 1
            screenWidth < 840.dp -> minOf(columns, 2)
            else -> columns
        }
    }
    
    LazyVerticalStaggeredGrid(
        columns = StaggeredGridCells.Fixed(responsiveColumns),
        modifier = modifier,
        verticalItemSpacing = spacing,
        horizontalArrangement = Arrangement.spacedBy(spacing),
        contentPadding = PaddingValues(spacing)
    ) {
        val scope = BentoGridScopeImpl()
        content(scope)
        
        scope.items.forEach { item ->
            item(span = item.span) {
                BentoCard(
                    item = item,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

// 在TetrisGameScreen中应用
@Composable
fun TetrisGameScreen2025(
    gameState: TetrisGameState,
    uiState: TetrisUiState,
    eventHandler: TetrisEventHandler
) {
    QuesticleTheme2025 {
        BentoGrid(columns = 3) {
            // 游戏板 - 占据主要空间
            largeItem(span = StaggeredGridItemSpan.FullLine) {
                TetrisBoard(
                    gameState = gameState,
                    modifier = Modifier.aspectRatio(0.5f)
                )
            }
            
            // 分数卡片
            smallItem {
                ScoreCard(score = uiState.score)
            }
            
            // 等级卡片
            smallItem {
                LevelCard(level = uiState.level)
            }
            
            // 下一个方块
            mediumItem {
                NextPieceCard(nextPiece = gameState.nextPiece)
            }
            
            // 控制面板
            wideItem {
                TetrisControlPanel2025(eventHandler = eventHandler)
            }
        }
    }
}
```

---

## 📅 实施时间表

### Week 1: 基础重构
- [ ] 创建设计令牌系统
- [ ] 重构主题系统
- [ ] 更新颜色管理

### Week 2: 组件标准化
- [ ] 重构按钮组件
- [ ] 重构卡片组件
- [ ] 创建统一动画系统

### Week 3: 布局系统
- [ ] 实施Bento网格
- [ ] 更新游戏界面布局
- [ ] 响应式设计测试

### Week 4: 微交互
- [ ] 增强按钮动画
- [ ] 添加转场效果
- [ ] 触觉反馈优化

### Week 5: 高级功能
- [ ] 3D交互元素
- [ ] 渐进式模糊
- [ ] AI界面准备

### Week 6: 测试和优化
- [ ] 性能测试
- [ ] 无障碍测试
- [ ] 用户体验测试

---

## 🧪 测试策略

### 单元测试
```kotlin
@Test
fun testDesignTokens() {
    assertEquals(16.dp, DesignTokens.Spacing.md)
    assertEquals(12.dp, DesignTokens.CornerRadius.medium)
}

@Test
fun testQButtonConfig() {
    val config = QButtonConfig.from(QButtonStyle.Primary, QButtonSize.Medium)
    assertEquals(DpSize(120.dp, 48.dp), config.size)
}
```

### UI测试
```kotlin
@Test
fun testBentoGridLayout() {
    composeTestRule.setContent {
        BentoGrid {
            smallItem { Text("Item 1") }
            largeItem { Text("Item 2") }
        }
    }
    
    composeTestRule.onNodeWithText("Item 1").assertIsDisplayed()
    composeTestRule.onNodeWithText("Item 2").assertIsDisplayed()
}
```

---

## 🚨 风险管理

### 高风险项
1. **向后兼容性** - 渐进式迁移，保持旧API
2. **性能影响** - 基准测试，性能监控
3. **用户适应** - A/B测试，用户反馈

### 缓解策略
1. **功能开关** - 允许用户选择新旧界面
2. **渐进发布** - 分阶段推出新功能
3. **回滚计划** - 快速回滚机制

---

**重构负责人**: UI/UX团队  
**技术审核**: 架构师  
**用户测试**: 产品团队  
**发布计划**: 分阶段发布
