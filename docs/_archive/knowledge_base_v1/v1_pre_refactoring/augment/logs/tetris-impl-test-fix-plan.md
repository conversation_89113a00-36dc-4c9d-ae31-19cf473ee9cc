# Feature:Tetris:Impl 测试修复计划

## 修复状态总结

### ✅ 已完成
- HoldManagerTest.kt - 修复了null参数问题
- TetrisInputHandlerTest.kt - 修复了JUnit 4到JUnit 5迁移问题
- TetrisGameScreenTest.kt - 完全重写，修复了API调用和数据模型问题
- TetrisEngineImplTest.kt - 修复了JUnit 4迁移和导入问题
- TetrisGameIntegrationTest.kt - 修复了JUnit 4遗留问题
- TetrisEngineImplComprehensiveTest.kt - 修复了导入、数据模型和断言问题

### ❌ 需要修复的文件

#### 1. 控制器测试
- **TetrisControllerImplTest.kt** - 大量错误，需要完全重写

#### 2. 引擎测试  
- **TetrisEngineImplTest.kt** - 缺少导入和JUnit 5迁移
- **TetrisEngineImplComprehensiveTest.kt** - 缺少导入和数据模型问题

#### 3. 输入处理测试
- **TetrisInputHandlerTest.kt** - JUnit 5注解问题

#### 4. 集成测试
- **TetrisGameIntegrationTest.kt** - JUnit 4 Rule问题

#### 5. UI测试
- **TetrisGameScreenTest.kt** - 缺少导入和方法调用问题
- **TetrisBoardTest.kt** - 数据模型属性问题

## 主要问题分类

### 1. 导入问题
```kotlin
// 需要添加的导入
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.feature.tetris.api.TetrisEngine
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.BeforeEach
import kotlinx.coroutines.test.runTest
```

### 2. JUnit 4到JUnit 5迁移
```kotlin
// 需要替换
@get:Rule -> 删除，使用JUnit 5扩展
@Before -> @BeforeEach  
@Nested -> 保留但确保导入
@DisplayName -> 保留但确保导入
testRule.runTest -> runTest
```

### 3. 数据模型属性更新
```kotlin
// TetrisGameState属性映射
currentPiece -> currentPiece (保持)
holdPiece -> holdPiece (保持) 
canHold -> canHold (保持)
board -> board (保持)
nextPiece -> nextPiece (保持)
filledRows -> 不存在，需要删除相关测试
```

### 4. 方法调用更新
```kotlin
// TetrisController方法映射
initializeGame() -> 不存在，需要查找替代
startGame() -> 不存在，需要查找替代  
pauseGame() -> pauseGame() (保持)
resumeGame() -> 不存在，需要查找替代
endGame() -> 不存在，需要查找替代
moveLeft() -> 不存在，需要查找替代
moveRight() -> 不存在，需要查找替代
rotate() -> 不存在，需要查找替代
hardDrop() -> 不存在，需要查找替代
hold() -> 不存在，需要查找替代
```

## 修复优先级

### 高优先级 (立即修复)
1. **HoldManagerTest.kt** ✅ - 已完成
2. **TetrisInputHandlerTest.kt** - 只需要JUnit 5注解修复
3. **TetrisEngineImplTest.kt** - 相对简单的导入和注解问题

### 中优先级 (需要API研究)
4. **TetrisEngineImplComprehensiveTest.kt** - 需要研究实际API
5. **TetrisGameIntegrationTest.kt** - 需要研究集成测试架构

### 低优先级 (需要重写)
6. **TetrisControllerImplTest.kt** - 可能需要完全重写
7. **TetrisGameScreenTest.kt** - 需要研究UI测试架构  
8. **TetrisBoardTest.kt** - 需要研究UI组件测试

## 下一步行动

1. **立即开始**: 修复TetrisInputHandlerTest.kt的JUnit 5注解问题
2. **研究实际API**: 查找TetrisController和TetrisEngine的实际方法签名
3. **逐步修复**: 从简单到复杂，确保每个文件都能编译通过
4. **测试验证**: 修复后运行测试确保功能正确

## 预期时间

- 高优先级: 30分钟
- 中优先级: 1小时  
- 低优先级: 2小时
- 总计: 3.5小时
