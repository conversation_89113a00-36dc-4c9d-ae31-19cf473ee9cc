# 🏗️ 阶段7：详细设计方案 - 2.3、2.4、2.5模块

## 文档信息
- **阶段**: 详细设计 (2.3-2.5)
- **日期**: 2025-06-20
- **版本**: 1.0.0
- **负责人**: Augment Agent
- **状态**: 进行中

## 1. 整体架构设计

### 1.1 模块依赖关系
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│ TetrisGameScreen │ StatisticsScreen │ SettingsScreen       │
│ TetrisController │ ChartsRenderer   │ ThemeManager         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│ TetrisGameUseCase │ StatisticsUseCase │ SettingsUseCase    │
│ AudioManager      │ DataAnalyzer      │ ThemeController    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
├─────────────────────────────────────────────────────────────┤
│ TetrisEngine      │ StatisticsEngine  │ SettingsValidator  │
│ InputHandler      │ TrendCalculator   │ LocaleManager      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│ TetrisRepository  │ StatisticsRepository │ SettingsRepository│
│ AudioRepository   │ MetricsCollector     │ PreferencesManager│
└─────────────────────────────────────────────────────────────┘
```

## 2. 俄罗斯方块游戏增强设计 (REQ-TETRIS-001 to REQ-TETRIS-020)

### 2.1 触摸控制系统设计

#### 2.1.1 输入处理架构
```kotlin
// 触摸输入处理器
interface TetrisInputHandler {
    fun handleTouchDown(x: Float, y: Float): TetrisAction?
    fun handleTouchMove(x: Float, y: Float): TetrisAction?
    fun handleTouchUp(x: Float, y: Float): TetrisAction?
    fun handleGesture(gesture: TetrisGesture): TetrisAction?
}

// 游戏动作定义
sealed class TetrisAction {
    object MoveLeft : TetrisAction()
    object MoveRight : TetrisAction()
    object SoftDrop : TetrisAction()
    object HardDrop : TetrisAction()
    object RotateClockwise : TetrisAction()
    object RotateCounterClockwise : TetrisAction()
    object Hold : TetrisAction()
    object Pause : TetrisAction()
}

// 手势识别
enum class TetrisGesture {
    SWIPE_LEFT, SWIPE_RIGHT, SWIPE_DOWN, SWIPE_UP,
    TAP, DOUBLE_TAP, LONG_PRESS
}
```

#### 2.1.2 控制区域设计
```kotlin
// 控制区域配置
data class ControlLayout(
    val gameArea: Rect,           // 游戏区域
    val leftMoveArea: Rect,       // 左移区域
    val rightMoveArea: Rect,      // 右移区域
    val rotateArea: Rect,         // 旋转区域
    val dropArea: Rect,           // 下落区域
    val holdArea: Rect,           // Hold区域
    val pauseArea: Rect           // 暂停区域
)

// 控制灵敏度配置
data class ControlSensitivity(
    val moveRepeatDelay: Long = 150L,     // 移动重复延迟
    val softDropSpeed: Long = 50L,        // 软下落速度
    val gestureThreshold: Float = 50f,    // 手势识别阈值
    val tapTimeout: Long = 200L           // 点击超时
)
```

### 2.2 Hold功能设计

#### 2.2.1 Hold系统实现
```kotlin
// Hold管理器
class HoldManager {
    private var heldPiece: TetrominoType? = null
    private var canHold: Boolean = true
    
    fun holdCurrentPiece(currentPiece: TetrominoType): HoldResult {
        if (!canHold) return HoldResult.CannotHold
        
        val previousHeld = heldPiece
        heldPiece = currentPiece
        canHold = false
        
        return if (previousHeld != null) {
            HoldResult.SwapPiece(previousHeld)
        } else {
            HoldResult.HoldPiece
        }
    }
    
    fun enableHold() {
        canHold = true
    }
}

sealed class HoldResult {
    object CannotHold : HoldResult()
    object HoldPiece : HoldResult()
    data class SwapPiece(val piece: TetrominoType) : HoldResult()
}
```

### 2.3 幽灵方块设计

#### 2.3.1 幽灵方块计算
```kotlin
// 幽灵方块计算器
class GhostPieceCalculator {
    fun calculateGhostPosition(
        currentPiece: Tetromino,
        gameBoard: GameBoard
    ): Position {
        var ghostPosition = currentPiece.position
        
        // 向下移动直到碰撞
        while (gameBoard.canPlacePiece(currentPiece.copy(position = ghostPosition))) {
            ghostPosition = ghostPosition.copy(y = ghostPosition.y + 1)
        }
        
        // 返回最后一个有效位置
        return ghostPosition.copy(y = ghostPosition.y - 1)
    }
}

// 幽灵方块渲染数据
data class GhostPiece(
    val type: TetrominoType,
    val position: Position,
    val rotation: Rotation,
    val alpha: Float = 0.3f  // 透明度
)
```

### 2.4 音效系统设计

#### 2.4.1 音效管理架构
```kotlin
// 音效管理器
interface AudioManager {
    suspend fun playSound(sound: GameSound, volume: Float = 1.0f)
    suspend fun playMusic(music: GameMusic, loop: Boolean = true)
    suspend fun stopMusic()
    suspend fun setMasterVolume(volume: Float)
    suspend fun setSoundEffectsVolume(volume: Float)
    suspend fun setMusicVolume(volume: Float)
}

// 游戏音效定义
enum class GameSound {
    PIECE_MOVE,           // 方块移动
    PIECE_ROTATE,         // 方块旋转
    PIECE_DROP,           // 方块下落
    PIECE_LOCK,           // 方块锁定
    LINE_CLEAR_SINGLE,    // 单行消除
    LINE_CLEAR_DOUBLE,    // 双行消除
    LINE_CLEAR_TRIPLE,    // 三行消除
    LINE_CLEAR_TETRIS,    // 四行消除
    LEVEL_UP,             // 升级
    GAME_OVER,            // 游戏结束
    PAUSE,                // 暂停
    MENU_SELECT           // 菜单选择
}

enum class GameMusic {
    MAIN_THEME,           // 主题音乐
    GAME_MUSIC_1,         // 游戏音乐1
    GAME_MUSIC_2,         // 游戏音乐2
    MENU_MUSIC            // 菜单音乐
}
```

#### 2.4.2 音效同步机制
```kotlin
// 音效事件系统
class AudioEventManager {
    private val eventQueue = mutableListOf<AudioEvent>()
    
    fun queueAudioEvent(event: AudioEvent) {
        eventQueue.add(event)
    }
    
    suspend fun processAudioEvents() {
        eventQueue.forEach { event ->
            when (event) {
                is AudioEvent.PlaySound -> audioManager.playSound(event.sound, event.volume)
                is AudioEvent.PlayMusic -> audioManager.playMusic(event.music, event.loop)
                is AudioEvent.StopMusic -> audioManager.stopMusic()
            }
        }
        eventQueue.clear()
    }
}

sealed class AudioEvent {
    data class PlaySound(val sound: GameSound, val volume: Float = 1.0f) : AudioEvent()
    data class PlayMusic(val music: GameMusic, val loop: Boolean = true) : AudioEvent()
    object StopMusic : AudioEvent()
}
```

## 3. 数据统计系统设计 (REQ-STATS-001 to REQ-STATS-010)

### 3.1 统计数据模型设计

#### 3.1.1 详细统计数据结构
```kotlin
// 游戏统计数据
data class DetailedGameStats(
    val userId: String,
    val gameType: GameType,
    
    // 基础统计 (REQ-STATS-001-005)
    val totalGames: Long = 0,
    val totalPlayTime: Long = 0,        // 秒
    val totalScore: Long = 0,
    val gamesWon: Long = 0,
    val gamesLost: Long = 0,
    val currentWinStreak: Int = 0,
    val bestWinStreak: Int = 0,
    
    // 俄罗斯方块专项统计
    val totalLinesCleared: Long = 0,
    val singleLineClears: Long = 0,
    val doubleLineClears: Long = 0,
    val tripleLineClears: Long = 0,
    val tetrisClears: Long = 0,
    val tSpinSingles: Long = 0,
    val tSpinDoubles: Long = 0,
    val tSpinTriples: Long = 0,
    val maxCombo: Int = 0,
    val totalPiecesPlaced: Long = 0,
    
    // 时间统计
    val averageGameDuration: Double = 0.0,
    val shortestGame: Long = 0,
    val longestGame: Long = 0,
    
    // 性能统计
    val averagePiecesPerMinute: Double = 0.0,
    val averageLinesPerMinute: Double = 0.0,
    val efficiency: Double = 0.0,       // 分数/时间比率
    
    val lastUpdated: Long = System.currentTimeMillis()
)
```

#### 3.1.2 时间维度统计
```kotlin
// 时间维度统计
data class TimeBasedStats(
    val userId: String,
    val gameType: GameType,
    val timeFrame: TimeFrame,
    val startTime: Long,
    val endTime: Long,
    val stats: DetailedGameStats
)

enum class TimeFrame {
    DAILY, WEEKLY, MONTHLY, YEARLY, ALL_TIME
}

// 趋势数据点
data class TrendDataPoint(
    val timestamp: Long,
    val value: Double,
    val label: String
)

// 趋势分析结果
data class TrendAnalysis(
    val metric: StatisticMetric,
    val timeFrame: TimeFrame,
    val dataPoints: List<TrendDataPoint>,
    val trend: TrendDirection,
    val changePercentage: Double,
    val summary: String
)

enum class TrendDirection {
    INCREASING, DECREASING, STABLE, VOLATILE
}

enum class StatisticMetric {
    TOTAL_SCORE, AVERAGE_SCORE, GAMES_PLAYED, WIN_RATE,
    PLAY_TIME, EFFICIENCY, LINES_CLEARED, PIECES_PER_MINUTE
}
```

### 3.2 统计计算引擎设计

#### 3.2.1 统计计算器
```kotlin
// 统计计算引擎
class StatisticsEngine {
    
    suspend fun calculateDetailedStats(
        userId: String,
        gameType: GameType,
        timeFrame: TimeFrame
    ): DetailedGameStats {
        val sessions = getGameSessions(userId, gameType, timeFrame)
        return calculateStatsFromSessions(sessions)
    }
    
    suspend fun calculateTrendAnalysis(
        userId: String,
        metric: StatisticMetric,
        timeFrame: TimeFrame,
        dataPoints: Int = 30
    ): TrendAnalysis {
        val historicalData = getHistoricalData(userId, metric, timeFrame, dataPoints)
        return analyzeTrend(historicalData, metric, timeFrame)
    }
    
    suspend fun generatePerformanceReport(
        userId: String,
        gameType: GameType
    ): PerformanceReport {
        val stats = calculateDetailedStats(userId, gameType, TimeFrame.ALL_TIME)
        val trends = calculateAllTrends(userId, gameType)
        return PerformanceReport(stats, trends, generateInsights(stats, trends))
    }
}

// 性能报告
data class PerformanceReport(
    val overallStats: DetailedGameStats,
    val trends: Map<StatisticMetric, TrendAnalysis>,
    val insights: List<PerformanceInsight>,
    val recommendations: List<String>,
    val generatedAt: Long = System.currentTimeMillis()
)

data class PerformanceInsight(
    val type: InsightType,
    val title: String,
    val description: String,
    val impact: InsightImpact,
    val actionable: Boolean = false,
    val action: String? = null
)

enum class InsightType {
    IMPROVEMENT, ACHIEVEMENT, PATTERN, WARNING, RECOMMENDATION
}

enum class InsightImpact {
    HIGH, MEDIUM, LOW
}
```

### 3.3 数据可视化设计

#### 3.3.1 图表组件设计
```kotlin
// 图表数据模型
sealed class ChartData {
    data class LineChart(
        val datasets: List<LineDataset>,
        val xAxisLabels: List<String>,
        val title: String,
        val xAxisTitle: String,
        val yAxisTitle: String
    ) : ChartData()
    
    data class BarChart(
        val datasets: List<BarDataset>,
        val categories: List<String>,
        val title: String
    ) : ChartData()
    
    data class PieChart(
        val slices: List<PieSlice>,
        val title: String
    ) : ChartData()
}

data class LineDataset(
    val label: String,
    val data: List<Double>,
    val color: Color,
    val strokeWidth: Float = 2f
)

data class BarDataset(
    val label: String,
    val data: List<Double>,
    val colors: List<Color>
)

data class PieSlice(
    val label: String,
    val value: Double,
    val color: Color
)
```

## 4. 设置管理系统设计 (REQ-SETTINGS-001 to REQ-SETTINGS-015)

### 4.1 设置数据模型设计

#### 4.1.1 完整设置模型
```kotlin
// 应用设置
data class AppSettings(
    // 基础设置 (REQ-SETTINGS-001-005)
    val theme: AppTheme = AppTheme.SYSTEM,
    val soundEffectsVolume: Float = 1.0f,
    val musicVolume: Float = 0.8f,
    val vibrationEnabled: Boolean = true,
    val language: String = "system",
    
    // 游戏设置 (REQ-SETTINGS-006-010)
    val defaultDifficulty: GameDifficulty = GameDifficulty.MEDIUM,
    val autoSaveEnabled: Boolean = true,
    val controlSensitivity: Float = 1.0f,
    val controlLayout: ControlLayoutType = ControlLayoutType.DEFAULT,
    
    // 隐私和安全 (REQ-SETTINGS-011-015)
    val dataCollectionEnabled: Boolean = true,
    val analyticsEnabled: Boolean = true,
    val crashReportingEnabled: Boolean = true,
    val notificationsEnabled: Boolean = true,
    val notificationTypes: Set<NotificationType> = NotificationType.values().toSet(),
    val autoBackupEnabled: Boolean = true,
    val backupFrequency: BackupFrequency = BackupFrequency.DAILY,
    
    val lastModified: Long = System.currentTimeMillis()
)

enum class AppTheme {
    LIGHT, DARK, SYSTEM
}

enum class ControlLayoutType {
    DEFAULT, COMPACT, EXTENDED, CUSTOM
}

enum class NotificationType {
    GAME_REMINDERS, ACHIEVEMENTS, UPDATES, SOCIAL
}

enum class BackupFrequency {
    MANUAL, DAILY, WEEKLY, MONTHLY
}
```

#### 4.1.2 设置验证器
```kotlin
// 设置验证器
class SettingsValidator {
    
    fun validateSettings(settings: AppSettings): SettingsValidationResult {
        val errors = mutableListOf<SettingsError>()
        
        // 音量范围验证
        if (settings.soundEffectsVolume !in 0f..1f) {
            errors.add(SettingsError.InvalidSoundVolume)
        }
        if (settings.musicVolume !in 0f..1f) {
            errors.add(SettingsError.InvalidMusicVolume)
        }
        
        // 控制灵敏度验证
        if (settings.controlSensitivity !in 0.1f..3.0f) {
            errors.add(SettingsError.InvalidControlSensitivity)
        }
        
        // 语言代码验证
        if (!isValidLanguageCode(settings.language)) {
            errors.add(SettingsError.InvalidLanguageCode)
        }
        
        return if (errors.isEmpty()) {
            SettingsValidationResult.Valid
        } else {
            SettingsValidationResult.Invalid(errors)
        }
    }
    
    private fun isValidLanguageCode(language: String): Boolean {
        return language == "system" || Locale.getAvailableLocales()
            .any { it.language == language }
    }
}

sealed class SettingsValidationResult {
    object Valid : SettingsValidationResult()
    data class Invalid(val errors: List<SettingsError>) : SettingsValidationResult()
}

enum class SettingsError {
    InvalidSoundVolume,
    InvalidMusicVolume,
    InvalidControlSensitivity,
    InvalidLanguageCode,
    InvalidBackupFrequency
}
```

### 4.2 主题系统设计

#### 4.2.1 主题管理器
```kotlin
// 主题管理器
class ThemeManager {
    private val _currentTheme = MutableStateFlow(AppTheme.SYSTEM)
    val currentTheme: StateFlow<AppTheme> = _currentTheme.asStateFlow()
    
    private val _isDarkMode = MutableStateFlow(false)
    val isDarkMode: StateFlow<Boolean> = _isDarkMode.asStateFlow()
    
    fun setTheme(theme: AppTheme) {
        _currentTheme.value = theme
        updateDarkMode(theme)
    }
    
    private fun updateDarkMode(theme: AppTheme) {
        _isDarkMode.value = when (theme) {
            AppTheme.LIGHT -> false
            AppTheme.DARK -> true
            AppTheme.SYSTEM -> isSystemInDarkMode()
        }
    }
    
    private fun isSystemInDarkMode(): Boolean {
        return (context.resources.configuration.uiMode and 
                Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES
    }
}

// 主题颜色定义
object QuesticleTheme {
    val LightColorScheme = lightColorScheme(
        primary = Color(0xFF667EEA),
        secondary = Color(0xFF764BA2),
        background = Color(0xFFFFFBFE),
        surface = Color(0xFFFFFBFE),
        // ... 更多颜色定义
    )
    
    val DarkColorScheme = darkColorScheme(
        primary = Color(0xFF8B9EFF),
        secondary = Color(0xFF9B7BC8),
        background = Color(0xFF1A1A2E),
        surface = Color(0xFF16213E),
        // ... 更多颜色定义
    )
}
```

### 4.3 数据备份恢复设计

#### 4.3.1 备份管理器
```kotlin
// 备份管理器
class BackupManager {
    
    suspend fun createBackup(): BackupResult {
        return try {
            val backupData = collectBackupData()
            val backupFile = createBackupFile(backupData)
            BackupResult.Success(backupFile)
        } catch (e: Exception) {
            BackupResult.Error(e.toQuesticleException())
        }
    }
    
    suspend fun restoreBackup(backupFile: BackupFile): RestoreResult {
        return try {
            val backupData = parseBackupFile(backupFile)
            validateBackupData(backupData)
            applyBackupData(backupData)
            RestoreResult.Success
        } catch (e: Exception) {
            RestoreResult.Error(e.toQuesticleException())
        }
    }
    
    private suspend fun collectBackupData(): BackupData {
        return BackupData(
            userProfile = userRepository.getCurrentUser().first(),
            gameStats = statsRepository.getAllStats(),
            settings = settingsRepository.getSettings(),
            gameHistory = gameRepository.getAllGames(),
            version = BuildConfig.VERSION_NAME,
            timestamp = System.currentTimeMillis()
        )
    }
}

// 备份数据模型
data class BackupData(
    val userProfile: User?,
    val gameStats: List<DetailedGameStats>,
    val settings: AppSettings,
    val gameHistory: List<Game>,
    val version: String,
    val timestamp: Long
)

data class BackupFile(
    val name: String,
    val size: Long,
    val createdAt: Long,
    val version: String,
    val uri: Uri
)

sealed class BackupResult {
    data class Success(val backupFile: BackupFile) : BackupResult()
    data class Error(val exception: QuesticleException) : BackupResult()
}

sealed class RestoreResult {
    object Success : RestoreResult()
    data class Error(val exception: QuesticleException) : RestoreResult()
}
```

---

**设计结论**: 通过模块化设计和清晰的接口定义，三个模块可以独立开发和测试，同时保持良好的集成性。重点关注性能优化和用户体验，确保游戏的流畅性和设置的易用性。

**下一阶段**: 进入实现阶段，按优先级逐步实现各个功能模块。
