# 🏗️ 阶段2：详细设计方案

## 文档信息
- **阶段**: 详细设计
- **日期**: 2025-06-20
- **版本**: 1.0.0
- **负责人**: Augment Agent
- **状态**: 进行中

## 1. 架构设计

### 1.1 整体架构优化

#### 当前架构问题
1. **用户系统不完整** - UserRepository实现过于简化
2. **游戏系统分散** - 缺少统一的游戏管理层
3. **状态管理不一致** - 各模块状态管理方式不统一
4. **数据持久化不完整** - 缺少完整的本地存储实现

#### 目标架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
├─────────────────────────────────────────────────────────────┤
│  HomeScreen  │  UserProfileScreen  │  GameListScreen       │
│  TetrisScreen │  SettingsScreen    │  LeaderboardScreen    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│  UserManager  │  GameManager  │  SessionManager            │
│  StatsManager │  AchievementManager │  PreferencesManager  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
├─────────────────────────────────────────────────────────────┤
│  UserRepository │ GameRepository │ StatsRepository          │
│  GameEngine     │ ScoreCalculator │ LevelCalculator        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│  LocalDataSource │ RemoteDataSource │ CacheManager         │
│  DatabaseDao     │ NetworkApi       │ FileStorage          │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 模块依赖关系

#### 核心模块重构
1. **core:user** - 新增用户管理专用模块
2. **core:game** - 新增游戏管理专用模块
3. **core:stats** - 新增统计分析专用模块
4. **core:session** - 新增会话管理专用模块

## 2. 用户管理系统设计 (REQ-USER-001 to REQ-USER-015)

### 2.1 用户认证与注册系统

#### 2.1.1 用户认证流程
```kotlin
// 认证状态管理
sealed class AuthState {
    object Loading : AuthState()
    object Unauthenticated : AuthState()
    data class Authenticated(val user: User) : AuthState()
    data class Error(val message: String) : AuthState()
}

// 认证用例
interface AuthUseCase {
    suspend fun loginWithCredentials(username: String, password: String): Result<User>
    suspend fun loginWithEmail(email: String, password: String): Result<User>
    suspend fun registerUser(username: String, email: String, password: String): Result<User>
    suspend fun resetPassword(email: String): Result<Unit>
    suspend fun loginAsGuest(): Result<User>
    suspend fun logout(): Result<Unit>
}
```

#### 2.1.2 用户验证规则 (REQ-USER-005)
```kotlin
object UserValidation {
    fun validateUsername(username: String): ValidationResult
    fun validateEmail(email: String): ValidationResult
    fun validatePassword(password: String): ValidationResult
    fun validateDisplayName(displayName: String): ValidationResult
}

sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Invalid(val errors: List<String>) : ValidationResult()
}
```

### 2.2 用户资料管理 (REQ-USER-006 to REQ-USER-010)

#### 2.2.1 用户资料界面设计
```kotlin
// 用户资料ViewModel
@HiltViewModel
class UserProfileViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val imageUploadUseCase: ImageUploadUseCase,
    private val userStatsUseCase: UserStatsUseCase
) : ViewModel() {
    
    val userProfile: StateFlow<User?>
    val userStats: StateFlow<UserStats>
    val isLoading: StateFlow<Boolean>
    val errorMessage: StateFlow<String?>
    
    fun updateDisplayName(newName: String)
    fun uploadAvatar(imageUri: Uri)
    fun updatePreferences(preferences: UserPreferences)
    fun refreshStats()
}
```

#### 2.2.2 头像上传功能 (REQ-USER-007)
```kotlin
interface ImageUploadUseCase {
    suspend fun uploadAvatar(userId: String, imageUri: Uri): Result<String>
    suspend fun deleteAvatar(userId: String): Result<Unit>
    fun getAvatarUrl(userId: String): String?
}
```

### 2.3 等级经验系统 (REQ-USER-011 to REQ-USER-015)

#### 2.3.1 等级计算系统
```kotlin
// 等级系统配置
object LevelSystem {
    const val MAX_LEVEL = 20
    const val BASE_EXPERIENCE = 100L
    const val EXPERIENCE_MULTIPLIER = 1.5
    
    fun calculateRequiredExperience(level: Int): Long
    fun calculateLevel(totalExperience: Long): Int
    fun calculateProgress(currentExp: Long, level: Int): Float
    fun getNextLevelExperience(level: Int): Long
}

// 经验值计算
interface ExperienceCalculator {
    fun calculateGameExperience(gameType: GameType, score: Int, duration: Long): Long
    fun calculateAchievementExperience(achievementId: String): Long
    fun calculateDailyBonusExperience(): Long
}
```

#### 2.3.2 升级反馈系统 (REQ-USER-015)
```kotlin
// 升级事件
sealed class LevelUpEvent {
    data class LevelUp(val oldLevel: Int, val newLevel: Int, val rewards: List<Reward>) : LevelUpEvent()
    data class ExperienceGained(val amount: Long, val source: String) : LevelUpEvent()
}

// 奖励系统
data class Reward(
    val type: RewardType,
    val amount: Long,
    val description: String
)

enum class RewardType {
    COINS, GEMS, ACHIEVEMENT, UNLOCK
}
```

## 3. 游戏系统设计 (REQ-GAME-001 to REQ-GAME-015)

### 3.1 游戏列表与选择 (REQ-GAME-001 to REQ-GAME-005)

#### 3.1.1 游戏注册系统
```kotlin
// 游戏注册表
@Singleton
class GameRegistry @Inject constructor() {
    private val games = mutableMapOf<GameType, GameInfo>()
    
    fun registerGame(gameInfo: GameInfo)
    fun getAvailableGames(): List<GameInfo>
    fun getGameInfo(gameType: GameType): GameInfo?
    fun isGameSupported(gameType: GameType): Boolean
}

// 游戏信息
data class GameInfo(
    val type: GameType,
    val name: String,
    val description: String,
    val previewImageRes: Int,
    val isAvailable: Boolean = true,
    val minLevel: Int = 1,
    val features: List<GameFeature> = emptyList()
)
```

#### 3.1.2 游戏启动管理
```kotlin
// 游戏启动器
interface GameLauncher {
    suspend fun launchGame(gameType: GameType, playerId: String): Result<GameSession>
    suspend fun resumeGame(gameId: String): Result<GameSession>
    suspend fun quickStart(gameType: GameType): Result<GameSession>
}

// 游戏会话管理器
@Singleton
class GameSessionManager @Inject constructor(
    private val gameRepository: GameRepository,
    private val userRepository: UserRepository
) {
    fun getCurrentSession(): Flow<GameSession?>
    suspend fun startSession(gameType: GameType, playerId: String): Result<GameSession>
    suspend fun endSession(sessionId: String, finalScore: Int): Result<Unit>
    suspend fun pauseSession(sessionId: String): Result<Unit>
    suspend fun resumeSession(sessionId: String): Result<Unit>
}
```

### 3.2 游戏会话管理 (REQ-GAME-006 to REQ-GAME-010)

#### 3.2.1 会话记录系统
```kotlin
// 增强的游戏会话
data class EnhancedGameSession(
    val id: String,
    val gameId: String,
    val playerId: String,
    val gameType: GameType,
    val startTime: Long,
    val endTime: Long?,
    val status: SessionStatus,
    val score: Int,
    val level: Int,
    val duration: Long,
    val actions: List<GameAction>,
    val achievements: List<String>,
    val statistics: Map<String, Any>,
    val metadata: Map<String, String>
)

enum class SessionStatus {
    ACTIVE, PAUSED, COMPLETED, ABANDONED
}
```

#### 3.2.2 游戏结果展示 (REQ-GAME-009)
```kotlin
// 游戏结果
data class GameResult(
    val session: EnhancedGameSession,
    val finalScore: Int,
    val personalBest: Boolean,
    val experienceGained: Long,
    val coinsEarned: Long,
    val achievementsUnlocked: List<Achievement>,
    val levelUp: LevelUpEvent?,
    val statistics: GameStatistics,
    val ranking: LeaderboardPosition?
)

// 结果展示ViewModel
@HiltViewModel
class GameResultViewModel @Inject constructor(
    private val gameResultUseCase: GameResultUseCase,
    private val leaderboardUseCase: LeaderboardUseCase
) : ViewModel() {
    
    fun processGameResult(sessionId: String): Flow<GameResult>
    fun shareResult(result: GameResult): Result<Unit>
    fun playAgain(gameType: GameType): Result<Unit>
}
```

### 3.3 分数排名系统 (REQ-GAME-011 to REQ-GAME-015)

#### 3.3.1 分数计算系统
```kotlin
// 分数计算器接口
interface ScoreCalculator {
    fun calculateBaseScore(gameType: GameType, actions: List<GameAction>): Int
    fun applyDifficultyMultiplier(baseScore: Int, difficulty: GameDifficulty): Int
    fun applyComboBonus(score: Int, combo: Int): Int
    fun applyTimeBonus(score: Int, duration: Long, targetTime: Long): Int
}

// 俄罗斯方块分数计算
class TetrisScoreCalculator : ScoreCalculator {
    companion object {
        const val SINGLE_LINE_SCORE = 100
        const val DOUBLE_LINE_SCORE = 300
        const val TRIPLE_LINE_SCORE = 500
        const val TETRIS_SCORE = 800
        const val T_SPIN_MULTIPLIER = 1.5f
    }
    
    override fun calculateBaseScore(gameType: GameType, actions: List<GameAction>): Int
    fun calculateTSpinBonus(lines: Int): Int
    fun calculateSoftDropBonus(cells: Int): Int
    fun calculateHardDropBonus(cells: Int): Int
}
```

#### 3.3.2 排行榜系统 (REQ-GAME-014)
```kotlin
// 排行榜类型
enum class LeaderboardType {
    GLOBAL_ALL_TIME,
    GLOBAL_WEEKLY,
    GLOBAL_DAILY,
    FRIENDS_ALL_TIME,
    PERSONAL_BEST
}

// 排行榜条目
data class LeaderboardEntry(
    val rank: Int,
    val user: User,
    val score: Int,
    val gameType: GameType,
    val achievedAt: Long,
    val isCurrentUser: Boolean = false
)

// 排行榜管理器
interface LeaderboardManager {
    suspend fun getLeaderboard(
        gameType: GameType,
        type: LeaderboardType,
        limit: Int = 100
    ): Result<List<LeaderboardEntry>>
    
    suspend fun getUserRank(
        userId: String,
        gameType: GameType,
        type: LeaderboardType
    ): Result<LeaderboardEntry?>
    
    suspend fun submitScore(
        userId: String,
        gameType: GameType,
        score: Int
    ): Result<LeaderboardEntry>
}
```

#### 3.3.3 成就系统 (REQ-GAME-015)
```kotlin
// 成就定义
data class Achievement(
    val id: String,
    val name: String,
    val description: String,
    val iconRes: Int,
    val category: AchievementCategory,
    val rarity: AchievementRarity,
    val requirements: List<AchievementRequirement>,
    val rewards: List<Reward>,
    val isSecret: Boolean = false
)

enum class AchievementCategory {
    SCORE, GAMES_PLAYED, TIME_PLAYED, SPECIAL_MOVES, PROGRESSION
}

enum class AchievementRarity {
    COMMON, RARE, EPIC, LEGENDARY
}

// 成就检查器
interface AchievementChecker {
    suspend fun checkAchievements(
        userId: String,
        gameSession: EnhancedGameSession
    ): List<Achievement>
    
    suspend fun checkProgressAchievements(
        userId: String,
        userStats: UserStats
    ): List<Achievement>
}
```

## 4. 数据持久化设计

### 4.1 数据库架构
```kotlin
// 用户相关表
@Entity(tableName = "users")
data class UserEntity(...)

@Entity(tableName = "user_sessions")
data class UserSessionEntity(...)

@Entity(tableName = "user_achievements")
data class UserAchievementEntity(...)

// 游戏相关表
@Entity(tableName = "games")
data class GameEntity(...)

@Entity(tableName = "game_sessions")
data class GameSessionEntity(...)

@Entity(tableName = "game_statistics")
data class GameStatisticsEntity(...)

@Entity(tableName = "leaderboard_entries")
data class LeaderboardEntryEntity(...)
```

### 4.2 缓存策略
```kotlin
// 缓存管理器
@Singleton
class CacheManager @Inject constructor() {
    suspend fun cacheUserData(user: User, ttl: Duration = Duration.ofHours(1))
    suspend fun cacheGameData(game: Game, ttl: Duration = Duration.ofMinutes(30))
    suspend fun cacheLeaderboard(gameType: GameType, entries: List<LeaderboardEntry>)
    suspend fun invalidateUserCache(userId: String)
    suspend fun invalidateGameCache(gameId: String)
}
```

## 5. 性能优化设计

### 5.1 内存管理
- 使用对象池管理游戏对象
- 实现智能缓存策略
- 优化图片加载和缓存

### 5.2 数据库优化
- 添加适当的索引
- 实现分页加载
- 使用事务优化批量操作

### 5.3 网络优化
- 实现请求去重
- 添加重试机制
- 使用压缩传输

## 6. 测试策略

### 6.1 单元测试覆盖
- UserRepository: 100%
- GameEngine: 95%
- ScoreCalculator: 100%
- AchievementChecker: 90%

### 6.2 集成测试
- 用户注册登录流程
- 游戏会话完整流程
- 分数计算和排行榜更新

### 6.3 UI测试
- 用户资料管理界面
- 游戏列表和启动
- 排行榜显示

---

**设计结论**: 通过模块化设计和清晰的职责分离，可以实现需求说明书中定义的所有功能，同时保持代码的可维护性和可扩展性。

**下一阶段**: 进入实现阶段，按优先级逐步实现各个模块。
