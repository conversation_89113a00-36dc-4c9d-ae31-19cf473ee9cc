# 🎨 Questicle UI/UX 设计规范概览 2025

## 📋 文档信息
- **文档标题**: Questicle UI/UX 设计规范概览
- **文档版本**: v1.0.0
- **创建日期**: 2025-06-26
- **文档状态**: ✅ 已完成
- **作者**: Augment Agent

## 🎯 设计规范体系概览

本设计规范体系基于2025年最新设计趋势，结合鸿蒙字体系统，为Questicle项目提供完整的UI/UX设计指导。

### 📚 文档结构
```
docs/augment/
├── ui-ux-design-standards-2025.md      # 核心设计规范
├── ui-ux-implementation-plan-2025.md   # 实施计划
├── harmonyos-font-implementation.md     # 鸿蒙字体实施
├── design-trends-2025-analysis.md      # 设计趋势分析
└── ui-ux-design-overview-2025.md       # 本概览文档
```

---

## 🌟 核心设计理念

### 设计愿景
**"简约智能，沉浸体验"** - 打造世界级的现代化游戏应用

### 设计原则 (SMART)
- **S**imple - 简约至上，减少认知负担
- **M**odern - 现代感设计，融合最新趋势  
- **A**ccessible - 无障碍设计，包容所有用户
- **R**esponsive - 响应式布局，适配所有设备
- **T**houghtful - 贴心设计，关注用户情感

---

## 🎨 视觉设计系统

### 色彩系统
```kotlin
// 主色调 - 深度蓝色系
val Primary = Color(0xFF1565C0)           // 深邃科技蓝
val PrimaryContainer = Color(0xFFE3F2FD)  // 主色容器

// 辅助色调 - 活力橙色系  
val Secondary = Color(0xFFFF7043)         // 活力橙
val SecondaryContainer = Color(0xFFFFE0B2) // 辅助色容器

// 第三色调 - 智能紫色系
val Tertiary = Color(0xFF7B1FA2)          // 智能紫
val TertiaryContainer = Color(0xFFF3E5F5) // 第三色容器

// 功能色彩
val Success = Color(0xFF2E7D32)           // 成功色
val Warning = Color(0xFFF57C00)           // 警告色
val Error = Color(0xFFD32F2F)             // 错误色
val Info = Color(0xFF1976D2)              // 信息色

// 游戏专用色彩 (俄罗斯方块标准)
val TetrisColors = mapOf(
    "I" to Color(0xFF00FFFF),  // I型 - 天蓝色
    "J" to Color(0xFF0000FF),  // J型 - 深蓝色
    "L" to Color(0xFFFFA500),  // L型 - 橙色
    "O" to Color(0xFFFFFF00),  // O型 - 黄色
    "S" to Color(0xFF00FF00),  // S型 - 绿色
    "T" to Color(0xFF800080),  // T型 - 紫色
    "Z" to Color(0xFFFF0000)   // Z型 - 红色
)

// 低光模式色彩 (2025年趋势)
val LowLightColors = mapOf(
    "background" to Color(0xFF0A0A0A),     // 极深背景
    "surface" to Color(0xFF1A1A1A),        // 深色表面
    "primary" to Color(0xFF4FC3F7),        // 柔和主色
    "onSurface" to Color(0xFFE0E0E0)       // 柔和文字
)
```

### 字体系统 - 鸿蒙字体
```kotlin
// 鸿蒙字体家族
val HarmonyOSFontFamily = FontFamily(
    Font(R.font.harmonyos_sans_thin, FontWeight.Thin),        // 100
    Font(R.font.harmonyos_sans_light, FontWeight.Light),      // 300
    Font(R.font.harmonyos_sans_regular, FontWeight.Normal),   // 400
    Font(R.font.harmonyos_sans_medium, FontWeight.Medium),    // 500
    Font(R.font.harmonyos_sans_bold, FontWeight.Bold),        // 700
    Font(R.font.harmonyos_sans_black, FontWeight.Black)       // 900
)

// 字体层级 (Material 3 + 鸿蒙字体)
val Typography = Typography(
    displayLarge = TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 57.sp),
    headlineLarge = TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 32.sp),
    titleLarge = TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 22.sp),
    bodyLarge = TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 16.sp),
    labelLarge = TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 14.sp)
)

// 游戏专用字体
val GameTypography = mapOf(
    "score" to TextStyle(fontFamily = GameMonoFontFamily, fontSize = 32.sp),
    "level" to TextStyle(fontFamily = GameMonoFontFamily, fontSize = 24.sp),
    "hint" to TextStyle(fontFamily = HarmonyOSFontFamily, fontSize = 14.sp)
)
```

---

## 🏗️ 布局与组件系统

### 间距系统 (8dp基准)
```kotlin
object Spacing {
    val None = 0.dp
    val ExtraSmall = 4.dp      // 1/2 基准
    val Small = 8.dp           // 1x 基准
    val Medium = 16.dp         // 2x 基准
    val Large = 24.dp          // 3x 基准
    val ExtraLarge = 32.dp     // 4x 基准
    val Huge = 48.dp           // 6x 基准
}
```

### Bento网格布局 (2025年趋势)
```kotlin
// Bento网格 - 模块化布局
enum class BentoSize(val span: Int) {
    Small(1),    // 1x1
    Medium(2),   // 2x1
    Large(4),    // 2x2
    Wide(3),     // 3x1
    Tall(2)      // 1x2
}

@Composable
fun BentoGrid(content: @Composable BentoGridScope.() -> Unit)
```

### 核心组件
```kotlin
// 按钮组件
@Composable
fun QuesticleButton(
    text: String,
    onClick: () -> Unit,
    style: ButtonStyle = ButtonStyle.Primary,
    size: ButtonSize = ButtonSize.Medium
)

// 卡片组件
@Composable
fun QuesticleCard(
    style: CardStyle = CardStyle.Elevated,
    content: @Composable ColumnScope.() -> Unit
)

// 游戏信息卡片
@Composable
fun GameInfoCard(
    title: String,
    value: String,
    icon: ImageVector? = null,
    trend: TrendDirection? = null
)
```

---

## 🎮 2025年设计趋势应用

### 1. 低光模式设计 🌙
- **目标**: 减少视觉疲劳，保护用户视力
- **特点**: 极低对比度、柔和色彩、减少蓝光
- **应用**: 夜间游戏模式、长时间使用场景

### 2. 3D沉浸式元素 🎮
- **目标**: 增强游戏体验的立体感
- **特点**: 深度阴影、立体方块、层次分明
- **应用**: 俄罗斯方块3D渲染、卡片立体效果

### 3. 动态字体系统 🔤
- **目标**: 智能调整字体以适应内容重要性
- **特点**: 响应式字体大小、权重动画、上下文感知
- **应用**: 分数显示、重要提示、用户反馈

### 4. Bento网格布局 📱
- **目标**: 现代化的模块化界面组织
- **特点**: 不规则网格、灵活组合、视觉层次
- **应用**: 游戏信息面板、设置界面、统计展示

### 5. 情感化配色 🌈
- **目标**: 基于心理学的色彩运用
- **特点**: 情境感知、动态调整、个性化体验
- **应用**: 游戏状态指示、用户情绪响应

### 6. 微交互动效 ⚡
- **目标**: 精致的细节动效提升体验
- **特点**: 细腻反馈、平滑过渡、操作确认
- **应用**: 按钮点击、状态变化、加载动画

### 7. 毛玻璃效果 🔮
- **目标**: 现代化的半透明设计语言
- **特点**: 半透明背景、模糊效果、层次感
- **应用**: 控制面板、弹窗、导航栏

---

## 🎯 游戏界面设计规范

### 俄罗斯方块界面布局
```
┌─────────────────────────────────────┐
│  Bento信息网格 (分数/等级/行数)        │
├─────────┬─────────────────┬─────────┤
│ 下一个  │                 │ 统计    │
│ 方块    │   3D游戏板       │ 数据    │
│ 预览    │                 │        │
├─────────┼─────────────────┼─────────┤
│ 暂存    │                 │ 成就    │
│ 方块    │                 │ 进度    │
└─────────┴─────────────────┴─────────┘
│         毛玻璃控制面板              │
└─────────────────────────────────────┘
```

### 交互设计规范
```kotlin
// 手势定义
object TetrisGestures {
    val SwipeLeft = "向左滑动 - 左移方块"
    val SwipeRight = "向右滑动 - 右移方块"
    val SwipeDown = "向下滑动 - 软降"
    val Tap = "点击 - 旋转方块"
    val DoubleTap = "双击 - 硬降"
    val LongPress = "长按 - 暂存方块"
}

// 触觉反馈
enum class HapticIntensity {
    Light,      // 轻微反馈 - 移动操作
    Medium,     // 中等反馈 - 旋转操作
    Strong      // 强烈反馈 - 消除行、游戏结束
}
```

---

## 📱 响应式设计标准

### 断点定义
```kotlin
object Breakpoints {
    val Compact = 0.dp..599.dp        // 手机竖屏
    val Medium = 600.dp..839.dp       // 手机横屏/小平板
    val Expanded = 840.dp..1199.dp    // 平板
    val Large = 1200.dp..1599.dp      // 桌面
    val ExtraLarge = 1600.dp..Int.MAX_VALUE.dp // 大屏桌面
}
```

### 自适应布局
- **Compact**: 单列布局，垂直堆叠
- **Medium**: 双列布局，侧边栏显示
- **Expanded**: 三列布局，完整信息展示
- **Large+**: 多列布局，大屏优化

---

## ♿ 无障碍设计标准

### 对比度要求
- **正常文字**: 最小对比度 4.5:1
- **大文字**: 最小对比度 3:1
- **图标**: 最小对比度 3:1
- **焦点指示器**: 最小对比度 3:1

### 触摸目标大小
- **最小触摸目标**: 48dp x 48dp
- **推荐触摸目标**: 56dp x 56dp
- **游戏控制按钮**: 64dp x 64dp

### 辅助功能支持
- **TalkBack**: 完整语音描述
- **键盘导航**: 全功能键盘操作
- **语音控制**: 基础语音命令
- **大字体**: 支持系统字体缩放

---

## 🚀 实施路线图

### 第一阶段 (第1-2周): 基础设施
- [x] 鸿蒙字体系统集成
- [x] 2025年色彩系统建立
- [x] 设计令牌定义
- [x] 基础组件库创建

### 第二阶段 (第3-4周): 趋势实施
- [ ] 低光模式实现
- [ ] 3D效果集成
- [ ] Bento网格布局
- [ ] 微交互动效

### 第三阶段 (第5-6周): 游戏界面
- [ ] 俄罗斯方块界面重构
- [ ] 交互体验优化
- [ ] 游戏数据可视化
- [ ] 成就系统界面

### 第四阶段 (第7-8周): 优化完善
- [ ] 无障碍设计实施
- [ ] 性能优化
- [ ] 全面测试
- [ ] 文档完善

---

## 📊 成功指标

### 用户体验指标
| 指标 | 当前值 | 目标值 | 提升幅度 |
|------|--------|--------|----------|
| 用户体验评分 | 82/100 | 95/100 | +16% |
| 无障碍评分 | 70/100 | 95/100 | +36% |
| 视觉设计评分 | 85/100 | 98/100 | +15% |
| 性能表现 | 90/100 | 98/100 | +9% |

### 技术指标
- **首屏渲染时间**: < 500ms
- **动画帧率**: 60fps稳定
- **内存使用**: < 100MB
- **包体积增量**: < 15MB

---

## 🔧 开发工具与资源

### 设计工具
- **Figma**: UI设计和原型制作
- **Material Theme Builder**: 色彩系统生成
- **Android Studio**: Compose预览和开发

### 字体资源
- **鸿蒙字体**: HarmonyOS Sans (6个字重)
- **等宽字体**: JetBrains Mono (游戏数据显示)

### 代码规范
- **组件命名**: Questicle前缀
- **文件组织**: 功能模块分组
- **注释规范**: 中英文双语
- **测试覆盖**: 100%组件测试

---

## 📚 相关文档

1. **[UI/UX设计规范详细版](ui-ux-design-standards-2025.md)** - 完整的设计规范文档
2. **[实施计划](ui-ux-implementation-plan-2025.md)** - 详细的实施步骤和时间安排
3. **[鸿蒙字体实施方案](harmonyos-font-implementation.md)** - 字体系统的具体实施
4. **[2025年设计趋势分析](design-trends-2025-analysis.md)** - 设计趋势的深入分析

---

## 🎯 总结

本设计规范体系将为Questicle项目带来：

✅ **现代化视觉体验** - 基于2025年最新设计趋势  
✅ **优秀的中文显示** - 鸿蒙字体系统优化  
✅ **无障碍友好设计** - 包容性用户体验  
✅ **高性能表现** - 流畅的动画和交互  
✅ **可维护的代码** - 规范化的组件系统  

通过系统性的设计规范实施，Questicle将成为行业领先的现代化游戏应用，为用户提供世界级的游戏体验。

---

*设计规范将持续迭代优化，确保与最新技术和用户需求保持同步。*
