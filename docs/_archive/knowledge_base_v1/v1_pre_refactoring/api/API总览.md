# 🔌 Questicle API 总览文档

## 文档信息

- **API 名称**: Questicle Internal APIs
- **API 版本**: v2.0.0
- **文档版本**: v2.0.0
- **创建日期**: 2025-06-20
- **最后更新**: 2025-06-20
- **文档状态**: ✅ Approved
- **维护人**: Augment Agent

## 📖 变更历史

| 版本   | 日期       | 作者          | 变更内容                               |
| ------ | ---------- | ------------- | -------------------------------------- |
| v1.0.0 | 2025-01-01 | 开发团队      | 初始 API 设计                          |
| v2.0.0 | 2025-06-20 | Augment Agent | 基于实际实现更新，完整 API 文档        |
| v2.1.0 | 2025-06-21 | Augment Agent | 代码检查后更新，添加使用示例和最佳实践 |

## 🎯 API 概述

### 简介

Questicle 应用采用模块化架构，各模块通过定义良好的 API 接口进行通信。本文档描述了所有内部 API 接口的设计和使用方法。

### API 设计原则

1. **接口隔离**: 每个模块只暴露必要的接口
2. **依赖倒置**: 依赖抽象而非具体实现
3. **单一职责**: 每个接口职责明确
4. **可测试性**: 接口易于 Mock 和测试
5. **向后兼容**: API 变更保持向后兼容

### 模块 API 架构

```mermaid
graph TD
    subgraph "Feature APIs"
        TetrisAPI[TetrisApi]
        HomeAPI[HomeApi]
        SettingsAPI[SettingsApi]
    end

    subgraph "Core APIs"
        DomainAPI[Domain Interfaces]
        DataAPI[Repository Interfaces]
        EngineAPI[Game Engine Interfaces]
    end

    subgraph "Implementation"
        TetrisImpl[TetrisApiImpl]
        HomeImpl[HomeApiImpl]
        SettingsImpl[SettingsApiImpl]
    end

    TetrisAPI --> TetrisImpl
    HomeAPI --> HomeImpl
    SettingsAPI --> SettingsImpl

    TetrisImpl --> DomainAPI
    HomeImpl --> DomainAPI
    SettingsImpl --> DomainAPI

    DomainAPI --> DataAPI
    DomainAPI --> EngineAPI
```

## 🎮 游戏引擎 API

### TetrisEngine Interface

#### 基础信息

- **包名**: `com.yu.questicle.core.domain.engine`
- **接口**: `TetrisEngine`
- **实现**: `TetrisEngineImpl`

#### 核心方法

```kotlin
interface TetrisEngine : GameEngine<TetrisGameState, TetrisAction> {

    /**
     * 处理游戏动作
     * @param action 游戏动作
     * @param currentState 当前游戏状态
     * @return 新的游戏状态
     */
    suspend fun processAction(
        action: TetrisAction,
        currentState: TetrisGameState
    ): Result<TetrisGameState>

    /**
     * 生成下一个方块
     * @return 新生成的方块
     */
    fun generateNextPiece(): TetrisPiece

    /**
     * 移动方块
     * @param direction 移动方向
     * @param gameState 当前游戏状态
     * @return 更新后的游戏状态
     */
    suspend fun movePiece(
        direction: Direction,
        gameState: TetrisGameState
    ): Result<TetrisGameState>

    /**
     * 旋转方块
     * @param clockwise 是否顺时针旋转
     * @param gameState 当前游戏状态
     * @return 更新后的游戏状态
     */
    suspend fun rotatePiece(
        clockwise: Boolean,
        gameState: TetrisGameState
    ): Result<TetrisGameState>

    /**
     * 下落方块
     * @param hard 是否硬下落
     * @param gameState 当前游戏状态
     * @return 更新后的游戏状态
     */
    suspend fun dropPiece(
        hard: Boolean,
        gameState: TetrisGameState
    ): Result<TetrisGameState>

    // 状态流
    val gameState: StateFlow<TetrisGameState>
    val gameEvents: Flow<TetrisEvent>
}
```

#### 使用示例

```kotlin
class TetrisController @Inject constructor(
    private val tetrisEngine: TetrisEngine
) {
    suspend fun handleUserInput(input: UserInput) {
        val currentState = tetrisEngine.gameState.value

        val result = when (input) {
            is UserInput.Move -> tetrisEngine.movePiece(input.direction, currentState)
            is UserInput.Rotate -> tetrisEngine.rotatePiece(input.clockwise, currentState)
            is UserInput.Drop -> tetrisEngine.dropPiece(input.hard, currentState)
        }

        result.onSuccess { newState ->
            // 处理成功结果
        }.onError { error ->
            // 处理错误
        }
    }
}
```

### GameEngine Base Interface

```kotlin
interface GameEngine<State, Action> {
    /**
     * 开始游戏
     */
    suspend fun startGame(): Result<State>

    /**
     * 暂停游戏
     */
    suspend fun pauseGame(): Result<State>

    /**
     * 恢复游戏
     */
    suspend fun resumeGame(): Result<State>

    /**
     * 结束游戏
     */
    suspend fun endGame(): Result<GameResult>

    /**
     * 重置游戏
     */
    suspend fun resetGame(): Result<State>

    /**
     * 处理游戏动作
     */
    suspend fun processAction(action: Action, currentState: State): Result<State>

    /**
     * 获取游戏统计
     */
    suspend fun getGameStats(): Result<GameStats>
}
```

## 👤 用户管理 API

### UserRepository Interface

#### 基础信息

- **包名**: `com.yu.questicle.core.domain.repository`
- **接口**: `UserRepository`
- **实现**: `UserRepositoryImpl`

#### 核心方法

```kotlin
interface UserRepository {

    /**
     * 创建游客用户
     * @return 游客用户信息
     */
    suspend fun createGuestUser(): Result<User>

    /**
     * 用户注册
     * @param username 用户名
     * @param email 邮箱
     * @param password 密码
     * @return 注册结果
     */
    suspend fun registerUser(
        username: String,
        email: String,
        password: String
    ): Result<User>

    /**
     * 用户登录
     * @param email 邮箱
     * @param password 密码
     * @return 登录结果
     */
    suspend fun loginUser(email: String, password: String): Result<User>

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    suspend fun getUser(userId: String): Result<User>

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新结果
     */
    suspend fun updateUser(user: User): Result<User>

    /**
     * 获取当前用户
     * @return 当前用户信息
     */
    suspend fun getCurrentUser(): Result<User>

    /**
     * 用户登出
     */
    suspend fun logout(): Result<Unit>
}
```

### AuthUseCase

```kotlin
class AuthUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    /**
     * 游客登录
     */
    suspend fun loginAsGuest(): Result<User> {
        return userRepository.createGuestUser()
    }

    /**
     * 用户注册
     */
    suspend fun registerUser(
        username: String,
        email: String,
        password: String
    ): Result<User> {
        // 输入验证
        val validation = UserValidation.validateRegistration(username, email, password)
        if (!validation.isValid) {
            return Result.Error(ValidationException(validation.errors))
        }

        return userRepository.registerUser(username, email, password)
    }

    /**
     * 用户登录
     */
    suspend fun loginUser(email: String, password: String): Result<User> {
        return userRepository.loginUser(email, password)
    }
}
```

## 🎮 功能模块 API

### TetrisApi Interface

#### 基础信息

- **包名**: `com.yu.questicle.feature.tetris.api`
- **接口**: `TetrisApi`
- **实现**: `TetrisApiImpl`

#### 核心方法

```kotlin
interface TetrisApi {

    /**
     * 俄罗斯方块游戏主界面
     * @param modifier Compose修饰符
     * @param onNavigateBack 返回回调
     * @param onGameComplete 游戏完成回调
     */
    @Composable
    fun TetrisGameScreen(
        modifier: Modifier = Modifier,
        onNavigateBack: () -> Unit = {},
        onGameComplete: (score: Int) -> Unit = {}
    )

    /**
     * 俄罗斯方块游戏预览组件
     * @param modifier Compose修饰符
     * @param onClick 点击回调
     */
    @Composable
    fun TetrisGamePreview(
        modifier: Modifier = Modifier,
        onClick: () -> Unit = {}
    )

    /**
     * 获取游戏控制器
     * @return 游戏控制器实例
     */
    fun getTetrisController(): TetrisController
}
```

### TetrisController Interface

```kotlin
interface TetrisController {

    /**
     * 开始新游戏
     */
    suspend fun startNewGame(): Result<TetrisGameState>

    /**
     * 暂停游戏
     */
    suspend fun pauseGame(): Result<TetrisGameState>

    /**
     * 恢复游戏
     */
    suspend fun resumeGame(): Result<TetrisGameState>

    /**
     * 处理用户输入
     * @param input 用户输入
     */
    suspend fun handleInput(input: TetrisInput): Result<TetrisGameState>

    /**
     * 获取当前游戏状态
     */
    fun getCurrentState(): StateFlow<TetrisGameState>

    /**
     * 获取游戏事件流
     */
    fun getGameEvents(): Flow<TetrisEvent>

    /**
     * 保存游戏状态
     */
    suspend fun saveGame(): Result<Unit>

    /**
     * 加载游戏状态
     */
    suspend fun loadGame(): Result<TetrisGameState>
}
```

### HomeApi Interface

```kotlin
interface HomeApi {

    /**
     * 主页界面
     */
    @Composable
    fun HomeScreen(
        modifier: Modifier = Modifier,
        onNavigateToGame: (gameType: String) -> Unit = {},
        onNavigateToSettings: () -> Unit = {}
    )

    /**
     * 获取主页控制器
     */
    fun getHomeController(): HomeController
}
```

### SettingsApi Interface

```kotlin
interface SettingsApi {

    /**
     * 设置界面
     */
    @Composable
    fun SettingsScreen(
        modifier: Modifier = Modifier,
        onNavigateBack: () -> Unit = {}
    )

    /**
     * 获取设置控制器
     */
    fun getSettingsController(): SettingsController
}
```

## 📊 数据模型 API

### 核心数据模型

#### User Model

```kotlin
@Serializable
data class User(
    val id: String,
    val username: String,
    val email: String?,
    val displayName: String,
    val avatarUrl: String?,
    val level: Int,
    val experience: Long,
    val coins: Long,
    val gems: Long,
    val preferences: UserPreferences,
    val stats: UserStats,
    val achievements: Set<String>,
    val friends: Set<String>,
    val createdAt: Long,
    val lastLoginAt: Long,
    val isActive: Boolean
)
```

#### TetrisGameState Model

```kotlin
@Serializable
data class TetrisGameState(
    val id: String,
    val board: TetrisBoard,
    val currentPiece: TetrisPiece?,
    val nextPiece: TetrisPiece?,
    val holdPiece: TetrisPiece?,
    val score: Int,
    val level: Int,
    val lines: Int,
    val status: TetrisStatus,
    val dropInterval: Long,
    val lastDropTime: Long,
    val canHold: Boolean,
    val combo: Int
)
```

#### TetrisPiece Model

```kotlin
@Serializable
data class TetrisPiece(
    val type: PieceType,
    val shape: Array<IntArray>,
    val position: Position,
    val rotation: Int,
    val color: Int
)
```

## 🔄 Result 类型 API

### Result<T> 设计

```kotlin
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: QuesticleException) : Result<Nothing>()

    inline fun <R> map(transform: (T) -> R): Result<R>
    inline fun <R> flatMap(transform: (T) -> Result<R>): Result<R>
    inline fun onSuccess(action: (T) -> Unit): Result<T>
    inline fun onError(action: (QuesticleException) -> Unit): Result<T>

    fun getOrNull(): T?
    fun getOrThrow(): T
    fun getOrDefault(defaultValue: T): T
}
```

### 使用示例

```kotlin
// 链式调用
val result = userRepository.getUser(userId)
    .map { user -> user.copy(lastLoginAt = System.currentTimeMillis()) }
    .flatMap { updatedUser -> userRepository.updateUser(updatedUser) }
    .onSuccess { user -> logger.info("User updated: ${user.id}") }
    .onError { error -> logger.error("Failed to update user", error) }

// 模式匹配
when (result) {
    is Result.Success -> handleSuccess(result.data)
    is Result.Error -> handleError(result.exception)
}
```

## 🎵 音效系统 API

### AudioManager Interface

```kotlin
interface AudioManager {

    /**
     * 播放音效
     * @param sound 音效类型
     * @param volume 音量 (0.0-1.0)
     */
    suspend fun playSound(sound: GameSound, volume: Float = 1.0f): Result<Unit>

    /**
     * 播放音乐
     * @param music 音乐类型
     * @param loop 是否循环
     */
    suspend fun playMusic(music: GameMusic, loop: Boolean = true): Result<Unit>

    /**
     * 停止音乐
     */
    suspend fun stopMusic(): Result<Unit>

    /**
     * 设置音效音量
     * @param volume 音量 (0.0-1.0)
     */
    suspend fun setSoundEffectsVolume(volume: Float): Result<Unit>

    /**
     * 设置音乐音量
     * @param volume 音量 (0.0-1.0)
     */
    suspend fun setMusicVolume(volume: Float): Result<Unit>

    /**
     * 设置静音状态
     * @param muted 是否静音
     */
    suspend fun setMuted(muted: Boolean): Result<Unit>

    // 状态流
    val isMuted: StateFlow<Boolean>
    val soundEffectsVolume: StateFlow<Float>
    val musicVolume: StateFlow<Float>
    val currentMusic: StateFlow<GameMusic?>
}
```

## 📈 统计系统 API

### StatisticsRepository Interface

```kotlin
interface StatisticsRepository {

    /**
     * 记录游戏统计
     * @param gameStats 游戏统计数据
     */
    suspend fun recordGameStats(gameStats: GameStats): Result<Unit>

    /**
     * 获取用户统计
     * @param userId 用户ID
     * @param timeRange 时间范围
     */
    suspend fun getUserStats(
        userId: String,
        timeRange: TimeRange
    ): Result<DetailedGameStats>

    /**
     * 获取排行榜
     * @param gameType 游戏类型
     * @param limit 数量限制
     */
    suspend fun getLeaderboard(
        gameType: GameType,
        limit: Int = 10
    ): Result<List<LeaderboardEntry>>

    /**
     * 获取成就进度
     * @param userId 用户ID
     */
    suspend fun getAchievementProgress(userId: String): Result<List<Achievement>>
}
```

## 🔧 错误处理 API

### QuesticleException 体系

```kotlin
abstract class QuesticleException(
    message: String,
    cause: Throwable? = null,
    val errorCode: String,
    val errorType: ErrorType,
    val severity: ErrorSeverity,
    val userMessage: String? = null,
    val context: Map<String, Any?> = emptyMap(),
    val retryable: Boolean = false
) : Exception(message, cause)

// 具体异常类型
class ValidationException(errors: List<ValidationError>) : QuesticleException(...)
class NetworkException(message: String, cause: Throwable?) : QuesticleException(...)
class DatabaseException(message: String, cause: Throwable?) : QuesticleException(...)
class GameException(message: String, cause: Throwable?) : QuesticleException(...)
```

## 📚 API 使用指南

### 最佳实践

1. **错误处理**: 始终使用 Result 类型处理可能失败的操作
2. **异步操作**: 使用 suspend 函数和协程
3. **状态管理**: 使用 StateFlow 和 Flow 进行响应式编程
4. **依赖注入**: 通过 Hilt 注入依赖
5. **测试友好**: 依赖接口而非具体实现

### 示例：完整的游戏流程

```kotlin
// 1. 初始化游戏管理器
@HiltViewModel
class GameViewModel @Inject constructor(
    private val tetrisGameManager: TetrisGameManager,
    private val userRepository: UserRepository
) : ViewModel() {

    // 2. 开始新游戏
    suspend fun startNewGame() {
        val currentUser = userRepository.getCurrentUser().first()
        currentUser?.let { user ->
            tetrisGameManager.startNewGame(user.id)
        }
    }

    // 3. 处理用户输入
    suspend fun handleUserInput(action: TetrisAction) {
        when (action) {
            is TetrisAction.Move -> tetrisGameManager.moveLeft()
            is TetrisAction.Rotate -> tetrisGameManager.rotate()
            is TetrisAction.Drop -> tetrisGameManager.hardDrop()
            is TetrisAction.Hold -> tetrisGameManager.hold()
        }
    }

    // 4. 观察游戏状态
    val gameState = tetrisGameManager.gameState.asStateFlow()
    val gameStats = tetrisGameManager.gameStats.asStateFlow()
}
```

### 错误处理示例

```kotlin
// 统一错误处理模式
suspend fun processGameAction(action: TetrisAction): Result<TetrisGameState> {
    return try {
        tetrisEngine.processAction(action, currentState)
    } catch (e: Exception) {
        when (e) {
            is ValidationException -> Result.Error(e)
            is GameException -> Result.Error(e)
            else -> Result.Error(e.toQuesticleException())
        }
    }
}

// 在UI中处理结果
gameViewModel.processAction(action).collect { result ->
    when (result) {
        is Result.Success -> updateUI(result.data)
        is Result.Error -> showError(result.exception.getUserFriendlyMessage())
        is Result.Loading -> showLoading()
    }
}
```

### 响应式编程示例

```kotlin
// 使用Flow进行状态管理
class TetrisGameController {
    private val _gameState = MutableStateFlow(TetrisGameState.initial())
    val gameState: StateFlow<TetrisGameState> = _gameState.asStateFlow()

    // 组合多个数据流
    val gameInfo = combine(
        gameState,
        userRepository.getCurrentUser(),
        settingsRepository.getGameSettings()
    ) { state, user, settings ->
        GameInfo(
            state = state,
            player = user,
            settings = settings
        )
    }
}

// 在Compose中使用
@Composable
fun TetrisGameScreen(controller: TetrisGameController) {
    val gameState by controller.gameState.collectAsState()
    val gameInfo by controller.gameInfo.collectAsState()

    TetrisBoard(gameState = gameState)
    GameStats(stats = gameInfo.stats)
}
```

```kotlin
class GameViewModel @Inject constructor(
    private val tetrisApi: TetrisApi,
    private val userRepository: UserRepository,
    private val statisticsRepository: StatisticsRepository
) : ViewModel() {

    private val tetrisController = tetrisApi.getTetrisController()

    fun startGame() {
        viewModelScope.launch {
            // 1. 获取当前用户
            val user = userRepository.getCurrentUser().getOrNull() ?: return@launch

            // 2. 开始新游戏
            tetrisController.startNewGame()
                .onSuccess { gameState ->
                    // 3. 监听游戏事件
                    tetrisController.getGameEvents()
                        .collect { event ->
                            handleGameEvent(event, user)
                        }
                }
                .onError { error ->
                    handleError(error)
                }
        }
    }

    private suspend fun handleGameEvent(event: TetrisEvent, user: User) {
        when (event) {
            is TetrisEvent.GameOver -> {
                // 记录游戏统计
                val gameStats = createGameStats(event.finalState, user)
                statisticsRepository.recordGameStats(gameStats)
            }
            is TetrisEvent.LineCleared -> {
                // 播放音效
                audioManager.playSound(GameSound.LINE_CLEAR_SINGLE)
            }
        }
    }
}
```

---

_文档版本: v2.0.0_  
_最后更新: 2025-06-20_  
_下次评审: 2025-09-20_
