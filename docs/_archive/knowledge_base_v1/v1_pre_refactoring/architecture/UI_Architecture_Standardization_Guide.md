# Questicle UI架构标准化实施指南

## 📋 标准概览

**架构模式**: **MVI (Model-View-Intent) + Clean Architecture**  
**状态管理**: **统一的StateFlow + 事件驱动**  
**导航模式**: **统一的SideEffect处理**  
**适用范围**: **所有Feature模块的UI层**

---

## 🏗️ 标准架构模板

### 1. **ViewModel标准模板**

```kotlin
/**
 * 标准ViewModel模板
 * 所有Feature的ViewModel都应遵循此模板
 */
@HiltViewModel
class FeatureViewModel @Inject constructor(
    private val controller: FeatureController,
    private val qLogger: QLogger = logger()
) : ViewModel() {

    // 1. UI状态管理 (唯一数据源)
    private val _uiState = MutableStateFlow(FeatureUiState.initial())
    val uiState: StateFlow<FeatureUiState> = _uiState.asStateFlow()

    // 2. 副作用管理 (导航、反馈等)
    private val _sideEffects = MutableSharedFlow<FeatureSideEffect>()
    val sideEffects: SharedFlow<FeatureSideEffect> = _sideEffects.asSharedFlow()

    init {
        qLogger.i("${this::class.simpleName} 初始化")
        observeControllerState()
    }

    // 3. 事件处理 (唯一入口)
    fun handleEvent(event: FeatureEvent) {
        qLogger.d("处理事件: $event")
        viewModelScope.launch {
            when (event) {
                is FeatureEvent.Initialize -> handleInitialize()
                is FeatureEvent.Refresh -> handleRefresh()
                is FeatureEvent.Retry -> handleRetry()
                is FeatureEvent.Navigation -> handleNavigation(event)
                is FeatureEvent.Business -> handleBusinessEvent(event)
            }
        }
    }

    // 4. 控制器状态观察
    private fun observeControllerState() {
        viewModelScope.launch {
            combine(
                controller.data,
                controller.isLoading,
                controller.error
            ) { data, loading, error ->
                _uiState.update { currentState ->
                    currentState.copy(
                        data = data,
                        isLoading = loading,
                        error = error?.let { UiError.fromException(it) }
                    )
                }
            }.collect()
        }
    }

    // 5. 具体事件处理方法
    private suspend fun handleInitialize() {
        controller.initialize()
    }

    private suspend fun handleRefresh() {
        _uiState.update { it.copy(isRefreshing = true) }
        controller.refresh()
        _uiState.update { it.copy(isRefreshing = false) }
    }

    private suspend fun handleRetry() {
        _uiState.update { it.copy(error = null) }
        controller.retry()
    }

    private suspend fun handleNavigation(event: FeatureEvent.Navigation) {
        when (event) {
            is FeatureEvent.NavigateBack -> {
                _sideEffects.emit(FeatureSideEffect.NavigateBack)
            }
            // 其他导航事件
        }
    }

    private suspend fun handleBusinessEvent(event: FeatureEvent.Business) {
        // 具体业务逻辑处理
    }

    override fun onCleared() {
        super.onCleared()
        qLogger.i("${this::class.simpleName} 清理")
    }
}
```

### 2. **UI状态标准模板**

```kotlin
/**
 * 标准UI状态模板
 * 包含通用的UI状态字段和便利方法
 */
@Stable
data class FeatureUiState(
    // 核心数据
    val data: FeatureData? = null,
    
    // 加载状态
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val loadingMessage: String? = null,
    
    // 错误状态
    val error: UiError? = null,
    
    // 网络状态
    val isOffline: Boolean = false,
    
    // 权限状态
    val permissions: Set<String> = emptySet()
) {
    // 便利属性
    val isContentReady: Boolean 
        get() = !isLoading && error == null && data != null
    
    val hasError: Boolean 
        get() = error != null
    
    val isEmpty: Boolean 
        get() = !isLoading && data == null
    
    val canRefresh: Boolean 
        get() = !isLoading && !isRefreshing
    
    // 功能检查
    fun hasPermission(permission: String): Boolean = 
        permissions.contains(permission)
    
    companion object {
        fun initial() = FeatureUiState()
    }
}

/**
 * 统一的UI错误模型
 */
@Stable
data class UiError(
    val id: String = UUID.randomUUID().toString(),
    val message: String,
    val type: ErrorType = ErrorType.GENERAL,
    val canRetry: Boolean = true,
    val retryAction: (() -> Unit)? = null
) {
    companion object {
        fun fromException(exception: Throwable): UiError {
            return when (exception) {
                is NetworkException -> UiError(
                    message = "网络连接异常，请检查网络设置",
                    type = ErrorType.NETWORK,
                    canRetry = true
                )
                is ValidationException -> UiError(
                    message = exception.message ?: "输入数据不合法",
                    type = ErrorType.VALIDATION,
                    canRetry = false
                )
                else -> UiError(
                    message = exception.message ?: "操作失败，请重试",
                    type = ErrorType.GENERAL,
                    canRetry = true
                )
            }
        }
    }
}

/**
 * 错误类型枚举
 */
enum class ErrorType {
    NETWORK,        // 网络错误
    VALIDATION,     // 验证错误
    AUTHENTICATION, // 认证错误
    PERMISSION,     // 权限错误
    GENERAL         // 通用错误
}
```

### 3. **事件模型标准模板**

```kotlin
/**
 * 标准事件模型
 * 定义所有可能的UI事件类型
 */
sealed interface FeatureEvent {
    
    // 生命周期事件
    data object Initialize : FeatureEvent
    data object Refresh : FeatureEvent
    data object Retry : FeatureEvent
    data object Pause : FeatureEvent
    data object Resume : FeatureEvent
    
    // 导航事件
    sealed interface Navigation : FeatureEvent {
        data object NavigateBack : Navigation
        data object NavigateToSettings : Navigation
        data class NavigateToDetail(val id: String) : Navigation
    }
    
    // 业务事件
    sealed interface Business : FeatureEvent {
        data class LoadData(val id: String) : Business
        data class UpdateData(val data: Any) : Business
        data class DeleteData(val id: String) : Business
    }
    
    // 用户交互事件
    sealed interface UserAction : FeatureEvent {
        data class ButtonClick(val buttonId: String) : UserAction
        data class TextInput(val text: String) : UserAction
        data class SelectItem(val itemId: String) : UserAction
    }
    
    // 错误处理事件
    sealed interface ErrorHandling : FeatureEvent {
        data class DismissError(val errorId: String) : ErrorHandling
        data object RetryLastAction : ErrorHandling
        data object ClearAllErrors : ErrorHandling
    }
}

/**
 * 标准副作用模型
 * 定义所有可能的副作用类型
 */
sealed interface FeatureSideEffect {
    
    // 导航副作用
    sealed interface Navigation : FeatureSideEffect {
        data object NavigateBack : Navigation
        data class NavigateToScreen(val route: String) : Navigation
        data class NavigateToScreenWithArgs(
            val route: String, 
            val args: Map<String, Any>
        ) : Navigation
    }
    
    // 用户反馈副作用
    sealed interface UserFeedback : FeatureSideEffect {
        data class ShowSnackbar(
            val message: String,
            val actionLabel: String? = null,
            val action: (() -> Unit)? = null
        ) : UserFeedback
        
        data class ShowToast(val message: String) : UserFeedback
        data class ShowDialog(val dialogData: DialogData) : UserFeedback
    }
    
    // 系统副作用
    sealed interface System : FeatureSideEffect {
        data object HapticFeedback : System
        data class PlaySound(val soundType: SoundType) : System
        data class RequestPermission(val permission: String) : System
    }
}
```

### 4. **Composable标准模板**

```kotlin
/**
 * 标准Screen组件模板
 * 所有Feature的Screen都应遵循此模板
 */
@Composable
fun FeatureScreen(
    modifier: Modifier = Modifier,
    viewModel: FeatureViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    // 副作用处理
    LaunchedEffect(Unit) {
        viewModel.sideEffects.collect { sideEffect ->
            when (sideEffect) {
                is FeatureSideEffect.Navigation.NavigateBack -> {
                    onNavigateBack()
                }
                is FeatureSideEffect.UserFeedback.ShowToast -> {
                    Toast.makeText(context, sideEffect.message, Toast.LENGTH_SHORT).show()
                }
                is FeatureSideEffect.System.HapticFeedback -> {
                    // 触觉反馈
                }
                // 其他副作用处理
            }
        }
    }
    
    // 生命周期事件
    LaunchedEffect(Unit) {
        viewModel.handleEvent(FeatureEvent.Initialize)
    }
    
    // UI内容
    FeatureContent(
        uiState = uiState,
        onEvent = viewModel::handleEvent,
        modifier = modifier
    )
}

/**
 * 内容组件模板
 * 分离UI逻辑和状态处理
 */
@Composable
private fun FeatureContent(
    uiState: FeatureUiState,
    onEvent: (FeatureEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                title = { Text("Feature") },
                navigationIcon = {
                    IconButton(
                        onClick = { 
                            onEvent(FeatureEvent.Navigation.NavigateBack) 
                        }
                    ) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingContent(
                        message = uiState.loadingMessage
                    )
                }
                
                uiState.hasError -> {
                    ErrorContent(
                        error = uiState.error!!,
                        onRetry = { 
                            onEvent(FeatureEvent.Retry) 
                        },
                        onDismiss = { errorId ->
                            onEvent(FeatureEvent.ErrorHandling.DismissError(errorId))
                        }
                    )
                }
                
                uiState.isEmpty -> {
                    EmptyContent(
                        onRefresh = { 
                            onEvent(FeatureEvent.Refresh) 
                        }
                    )
                }
                
                uiState.isContentReady -> {
                    DataContent(
                        data = uiState.data!!,
                        onEvent = onEvent
                    )
                }
            }
            
            // 刷新指示器
            if (uiState.isRefreshing) {
                RefreshIndicator()
            }
        }
    }
}
```

---

## 🔧 实施步骤

### 步骤1: 创建标准模板文件

```bash
# 创建标准模板目录
mkdir -p core/ui/src/main/kotlin/com/yu/questicle/core/ui/template

# 创建模板文件
touch core/ui/src/main/kotlin/com/yu/questicle/core/ui/template/StandardViewModel.kt
touch core/ui/src/main/kotlin/com/yu/questicle/core/ui/template/StandardUiState.kt
touch core/ui/src/main/kotlin/com/yu/questicle/core/ui/template/StandardEvent.kt
touch core/ui/src/main/kotlin/com/yu/questicle/core/ui/template/StandardScreen.kt
```

### 步骤2: 迁移优先级

1. **优先级1**: User模块 (最复杂，收益最大)
2. **优先级2**: Home模块 (已有部分基础)
3. **优先级3**: Tetris模块 (最简单)
4. **优先级4**: Settings模块 (最小)

### 步骤3: 具体迁移步骤

#### User模块迁移示例：

```kotlin
// 旧代码 (UserViewModel.kt)
@HiltViewModel
class UserViewModel @Inject constructor(
    val userController: UserControllerImpl
) : ViewModel() {
    private val _navigationEvents = MutableSharedFlow<UserNavigationEvent>()
    val navigationEvents: SharedFlow<UserNavigationEvent> = _navigationEvents.asSharedFlow()
    
    val currentUser = userController.currentUser
    val isLoading = userController.isLoading
    // ...
}

// 新代码 (UserViewModel.kt)
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userController: UserController,  // 使用接口而非实现
    private val qLogger: QLogger = logger()
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(UserUiState.initial())
    val uiState: StateFlow<UserUiState> = _uiState.asStateFlow()
    
    private val _sideEffects = MutableSharedFlow<UserSideEffect>()
    val sideEffects: SharedFlow<UserSideEffect> = _sideEffects.asSharedFlow()
    
    fun handleEvent(event: UserEvent) {
        // 统一事件处理
    }
    
    // ...
}
```

---

## 🧪 测试标准

### 1. **ViewModel测试模板**

```kotlin
@ExtendWith(MockKExtension::class)
class FeatureViewModelTest {
    
    @MockK
    private lateinit var controller: FeatureController
    
    private lateinit var viewModel: FeatureViewModel
    
    @BeforeEach
    fun setup() {
        MockKAnnotations.init(this)
        every { controller.data } returns flowOf(mockData)
        every { controller.isLoading } returns flowOf(false)
        every { controller.error } returns flowOf(null)
        
        viewModel = FeatureViewModel(controller)
    }
    
    @Test
    fun `初始状态应该正确`() = runTest {
        // Given - 初始条件已在setup中设置
        
        // When - 收集初始状态
        val initialState = viewModel.uiState.value
        
        // Then - 验证初始状态
        assertThat(initialState.isLoading).isFalse()
        assertThat(initialState.error).isNull()
        assertThat(initialState.data).isNotNull()
    }
    
    @Test
    fun `处理刷新事件应该更新状态`() = runTest {
        // Given
        coEvery { controller.refresh() } just Runs
        
        // When
        viewModel.handleEvent(FeatureEvent.Refresh)
        advanceUntilIdle()
        
        // Then
        coVerify { controller.refresh() }
    }
}
```

### 2. **Composable测试模板**

```kotlin
@HiltAndroidTest
class FeatureScreenTest {
    
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @get:Rule
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()
    
    @Test
    fun `加载状态应该显示加载指示器`() {
        // Given
        val uiState = FeatureUiState(isLoading = true)
        
        // When
        composeTestRule.setContent {
            FeatureContent(
                uiState = uiState,
                onEvent = {}
            )
        }
        
        // Then
        composeTestRule.onNodeWithTag("loading_indicator").assertIsDisplayed()
    }
}
```

---

## 📊 质量检查清单

### Code Review检查项目：

- [ ] **ViewModel结构**
  - [ ] 使用标准模板结构
  - [ ] 只有一个UiState StateFlow
  - [ ] 只有一个SideEffect SharedFlow
  - [ ] 统一的handleEvent方法
  - [ ] 正确的依赖注入

- [ ] **状态管理**
  - [ ] UI状态包含所有必要字段
  - [ ] 状态更新使用update方法
  - [ ] 没有直接修改状态
  - [ ] 正确的初始状态

- [ ] **事件处理**
  - [ ] 所有事件通过handleEvent处理
  - [ ] 事件类型正确分类
  - [ ] 副作用正确发送

- [ ] **Composable结构**
  - [ ] 使用标准Screen模板
  - [ ] 正确的副作用处理
  - [ ] 状态收集使用collectAsStateWithLifecycle
  - [ ] 分离逻辑和UI组件

---

## 🚀 迁移时间表

| 阶段 | 模块 | 预估时间 | 负责人 | 状态 |
|------|------|----------|---------|------|
| 1 | 模板创建 | 2天 | 架构师 | 📋 计划中 |
| 2 | User模块迁移 | 2天 | 开发者A | 📋 计划中 |
| 3 | Home模块迁移 | 1.5天 | 开发者B | 📋 计划中 |
| 4 | Tetris模块迁移 | 1天 | 开发者C | 📋 计划中 |
| 5 | Settings模块迁移 | 1天 | 开发者D | 📋 计划中 |
| 6 | 质量验证 | 1.5天 | 全团队 | 📋 计划中 |

**总计**: 9天 (约1.8个工作周)

---

**文档版本**: 1.0  
**创建时间**: 2025-01-19  
**维护者**: 架构团队 