# 🏗️ Questicle 系统架构设计文档

## 文档信息
- **文档标题**: Questicle 系统架构设计文档
- **文档版本**: v2.0.0
- **创建日期**: 2025-06-20
- **最后更新**: 2025-06-20
- **文档状态**: ✅ Approved
- **作者**: Augment Agent
- **审核人**: 架构团队
- **批准人**: 技术负责人

## 📖 变更历史
| 版本 | 日期 | 作者 | 变更内容 |
|------|------|------|----------|
| v1.0.0 | 2025-01-01 | 架构团队 | 初始架构设计 |
| v2.0.0 | 2025-06-20 | Augment Agent | 基于实际实现更新，整合完整架构 |

## 🎯 架构概述

### 设计原则
1. **Clean Architecture**: 分层架构，依赖倒置
2. **模块化设计**: 高内聚，低耦合
3. **可测试性**: 易于单元测试和集成测试
4. **可扩展性**: 支持功能扩展和技术演进
5. **性能优化**: 高效的数据处理和UI渲染

### 架构目标
- **可维护性**: 代码结构清晰，易于维护
- **可扩展性**: 支持新功能和新游戏类型
- **可测试性**: 完整的测试覆盖
- **高性能**: 60FPS游戏体验
- **高质量**: 零崩溃，低内存使用

## 🏛️ 整体架构

### Clean Architecture分层

```mermaid
graph TB
    subgraph "Presentation Layer"
        UI[UI Components]
        VM[ViewModels]
        Screen[Screens]
    end
    
    subgraph "Application Layer"
        UC[Use Cases]
        Service[Application Services]
        Manager[Managers]
    end
    
    subgraph "Domain Layer"
        Entity[Entities]
        Repository[Repository Interfaces]
        Engine[Game Engines]
    end
    
    subgraph "Data Layer"
        RepoImpl[Repository Implementations]
        DataSource[Data Sources]
        Database[Local Database]
        Network[Remote API]
    end
    
    UI --> VM
    VM --> UC
    UC --> Repository
    Repository --> RepoImpl
    RepoImpl --> DataSource
    DataSource --> Database
    DataSource --> Network
```

### 模块架构图

```mermaid
graph TD
    App[app] --> FeatureHome[feature:home:impl]
    App --> FeatureTetris[feature:tetris:impl]
    App --> FeatureSettings[feature:settings:impl]
    App --> CoreDesign[core:designsystem]
    App --> CoreCommon[core:common]
    
    FeatureHome --> FeatureHomeAPI[feature:home:api]
    FeatureTetris --> FeatureTetrisAPI[feature:tetris:api]
    FeatureSettings --> FeatureSettingsAPI[feature:settings:api]
    
    FeatureHome --> CoreDomain[core:domain]
    FeatureTetris --> CoreDomain
    FeatureSettings --> CoreDomain
    
    FeatureHome --> CoreData[core:data]
    FeatureTetris --> CoreData
    FeatureSettings --> CoreData
    
    CoreData --> CoreDatabase[core:database]
    CoreData --> CoreNetwork[core:network]
    CoreData --> CoreDatastore[core:datastore]
    
    CoreDomain --> CoreCommon
    CoreData --> CoreCommon
    
    subgraph "Testing"
        CoreTesting[core:testing]
    end
    
    subgraph "Build Logic"
        BuildLogic[build-logic:convention]
    end
```

## 📦 模块设计

### 核心模块 (Core Modules)

#### core:common
**职责**: 通用工具和基础设施
```kotlin
core:common/
├── src/main/kotlin/
│   └── com/yu/questicle/core/common/
│       ├── result/           # Result类型和错误处理
│       ├── exception/        # 统一异常体系
│       ├── logging/          # 日志系统
│       ├── util/            # 工具类
│       └── extension/       # Kotlin扩展
```

**关键组件**:
- `Result<T>`: 统一的结果类型
- `QuesticleException`: 异常体系基类
- `QLogger`: 统一日志接口

#### core:domain
**职责**: 业务领域模型和接口
```kotlin
core:domain/
├── src/main/kotlin/
│   └── com/yu/questicle/core/domain/
│       ├── model/           # 领域模型
│       ├── repository/      # 仓库接口
│       ├── usecase/         # 用例接口
│       └── engine/          # 游戏引擎接口
```

**关键组件**:
- `User`: 用户模型
- `TetrisGameState`: 游戏状态模型
- `TetrisEngine`: 游戏引擎接口
- `UserRepository`: 用户仓库接口

#### core:data
**职责**: 数据访问层实现
```kotlin
core:data/
├── src/main/kotlin/
│   └── com/yu/questicle/core/data/
│       ├── repository/      # 仓库实现
│       ├── source/          # 数据源
│       ├── mapper/          # 数据映射
│       └── di/              # 依赖注入
```

**关键组件**:
- `UserRepositoryImpl`: 用户仓库实现
- `GameRepositoryImpl`: 游戏仓库实现
- `LocalDataSource`: 本地数据源
- `RemoteDataSource`: 远程数据源

### 功能模块 (Feature Modules)

#### feature:tetris
**职责**: 俄罗斯方块游戏功能

```kotlin
feature:tetris/
├── api/                     # 对外接口
│   └── src/main/kotlin/
│       └── com/yu/questicle/feature/tetris/api/
│           ├── TetrisApi.kt
│           └── TetrisController.kt
└── impl/                    # 具体实现
    └── src/main/kotlin/
        └── com/yu/questicle/feature/tetris/impl/
            ├── engine/      # 游戏引擎实现
            ├── controller/  # 游戏控制器
            ├── ui/          # UI组件
            ├── input/       # 输入处理
            └── audio/       # 音效处理
```

**关键组件**:
- `TetrisEngineImpl`: 游戏引擎实现
- `TetrisControllerImpl`: 游戏控制器
- `TetrisInputHandler`: 输入处理器
- `HoldManager`: Hold功能管理
- `GhostPieceCalculator`: 幽灵方块计算

#### feature:home
**职责**: 主页功能
```kotlin
feature:home/
├── api/
│   └── HomeApi.kt
└── impl/
    ├── HomeController.kt
    ├── HomeScreen.kt
    └── components/
```

#### feature:settings
**职责**: 设置功能
```kotlin
feature:settings/
├── api/
│   └── SettingsApi.kt
└── impl/
    ├── SettingsController.kt
    ├── SettingsScreen.kt
    └── validation/
```

## 🔄 数据流架构

### 游戏数据流

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant VM as ViewModel
    participant UC as UseCase
    participant Engine as TetrisEngine
    participant Repo as Repository
    participant DB as Database
    
    UI->>VM: User Input (Move, Rotate)
    VM->>UC: Execute Game Action
    UC->>Engine: Process Action
    Engine->>Engine: Update Game State
    Engine->>UC: Return New State
    UC->>Repo: Save Game State
    Repo->>DB: Persist Data
    UC->>VM: Return Result
    VM->>UI: Update UI State
```

### 用户数据流

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant VM as ViewModel
    participant UC as AuthUseCase
    participant Repo as UserRepository
    participant Local as LocalDataSource
    participant Remote as RemoteDataSource
    
    UI->>VM: Login Request
    VM->>UC: Authenticate User
    UC->>Repo: Get User Data
    Repo->>Local: Check Local Cache
    alt Cache Miss
        Repo->>Remote: Fetch from Server
        Remote->>Repo: Return User Data
        Repo->>Local: Update Cache
    end
    Repo->>UC: Return User
    UC->>VM: Authentication Result
    VM->>UI: Update UI
```

## 🎮 游戏引擎架构

### TetrisEngine设计

```kotlin
interface TetrisEngine : GameEngine<TetrisGameState, TetrisAction> {
    // 核心游戏逻辑
    suspend fun processAction(action: TetrisAction, currentState: TetrisGameState): Result<TetrisGameState>
    
    // 方块生成
    fun generateNextPiece(): TetrisPiece
    
    // 游戏操作
    suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    
    // 状态管理
    val gameState: StateFlow<TetrisGameState>
    val gameEvents: Flow<TetrisEvent>
}
```

### 游戏状态管理

```kotlin
@Serializable
data class TetrisGameState(
    val id: String,
    val board: TetrisBoard,
    val currentPiece: TetrisPiece?,
    val nextPiece: TetrisPiece?,
    val holdPiece: TetrisPiece?,
    val score: Int,
    val level: Int,
    val lines: Int,
    val status: TetrisStatus,
    val dropInterval: Long,
    val lastDropTime: Long,
    val canHold: Boolean,
    val combo: Int
)
```

## 🔌 依赖注入架构

### Hilt模块设计

```kotlin
// 应用级模块
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): QuesticleDatabase
    
    @Provides
    @Singleton
    fun provideUserRepository(impl: UserRepositoryImpl): UserRepository
}

// 游戏模块
@Module
@InstallIn(SingletonComponent::class)
object GameModule {
    @Provides
    @Singleton
    fun provideTetrisEngine(impl: TetrisEngineImpl): TetrisEngine
    
    @Provides
    fun provideTetrisController(engine: TetrisEngine): TetrisController
}
```

### 依赖关系图

```mermaid
graph LR
    subgraph "Singleton Scope"
        Database[QuesticleDatabase]
        UserRepo[UserRepository]
        TetrisEngine[TetrisEngine]
        AudioManager[AudioManager]
    end
    
    subgraph "ViewModel Scope"
        TetrisVM[TetrisViewModel]
        HomeVM[HomeViewModel]
        SettingsVM[SettingsViewModel]
    end
    
    subgraph "Activity Scope"
        TetrisController[TetrisController]
    end
    
    TetrisVM --> TetrisEngine
    TetrisVM --> UserRepo
    TetrisController --> TetrisEngine
    TetrisController --> AudioManager
    UserRepo --> Database
```

## 🧪 测试架构

### 测试策略

```mermaid
pyramid
    title Testing Pyramid
    
    "E2E Tests" : 10
    "Integration Tests" : 30
    "Unit Tests" : 60
```

### 测试模块结构

```kotlin
core:testing/
├── src/main/kotlin/
│   └── com/yu/questicle/core/testing/
│       ├── rule/            # 测试规则
│       ├── fake/            # 假实现
│       ├── util/            # 测试工具
│       └── data/            # 测试数据
```

**关键组件**:
- `FakeUserRepository`: 用户仓库假实现
- `FakeTetrisEngine`: 游戏引擎假实现
- `TestDataFactory`: 测试数据工厂
- `CoroutineTestRule`: 协程测试规则

## 🔐 安全架构

### 数据安全

```mermaid
graph TD
    UserData[User Data] --> Encryption[AES-256 Encryption]
    Encryption --> LocalStorage[Encrypted Local Storage]
    
    NetworkData[Network Data] --> TLS[TLS 1.3]
    TLS --> HTTPS[HTTPS Communication]
    
    Authentication[User Authentication] --> JWT[JWT Tokens]
    JWT --> SecureStorage[Secure Token Storage]
```

### 权限管理

```kotlin
// 权限检查
interface PermissionManager {
    suspend fun checkPermission(permission: Permission): Boolean
    suspend fun requestPermission(permission: Permission): PermissionResult
}

// 数据访问控制
interface DataAccessControl {
    suspend fun canAccessUserData(userId: String): Boolean
    suspend fun canModifyGameData(gameId: String): Boolean
}
```

## 📊 性能架构

### 性能优化策略

1. **内存管理**
   - 对象池复用
   - 及时释放资源
   - 避免内存泄漏

2. **计算优化**
   - 算法优化
   - 缓存机制
   - 懒加载

3. **UI性能**
   - Compose优化
   - 减少重组
   - 异步加载

### 缓存架构

```kotlin
interface CacheManager {
    suspend fun <T> get(key: String, type: Class<T>): T?
    suspend fun <T> put(key: String, value: T, ttl: Duration)
    suspend fun invalidate(key: String)
    suspend fun clear()
}

// 多级缓存
class MultiLevelCache : CacheManager {
    private val memoryCache: MemoryCache
    private val diskCache: DiskCache
    private val networkCache: NetworkCache
}
```

## 🔄 扩展性设计

### 插件架构

```kotlin
interface GamePlugin {
    val name: String
    val version: String
    
    suspend fun initialize(context: GameContext)
    suspend fun onGameEvent(event: GameEvent)
    suspend fun cleanup()
}

interface PluginManager {
    suspend fun loadPlugin(plugin: GamePlugin)
    suspend fun unloadPlugin(pluginName: String)
    suspend fun getLoadedPlugins(): List<GamePlugin>
}
```

### 多游戏支持

```kotlin
interface GameRegistry {
    suspend fun registerGame(gameInfo: GameInfo)
    suspend fun getAvailableGames(): List<GameInfo>
    suspend fun createGameEngine(gameType: GameType): GameEngine<*, *>
}
```

## 📚 技术决策记录 (ADR)

### ADR-001: 选择Clean Architecture
**状态**: 已接受  
**决策**: 采用Clean Architecture作为整体架构模式  
**理由**: 
- 清晰的分层结构
- 易于测试
- 技术无关性
- 可维护性高

### ADR-002: 选择Jetpack Compose
**状态**: 已接受  
**决策**: 使用Jetpack Compose作为UI框架  
**理由**:
- 声明式UI
- 更好的性能
- 现代化开发体验
- Google官方推荐

### ADR-003: 选择Hilt进行依赖注入
**状态**: 已接受  
**决策**: 使用Hilt作为依赖注入框架  
**理由**:
- Android官方推荐
- 编译时检查
- 与Android生命周期集成
- 简化配置

## 📈 监控和度量

### 性能监控

```kotlin
interface PerformanceMonitor {
    fun trackMethodExecution(methodName: String, duration: Long)
    fun trackMemoryUsage(component: String, memoryMB: Int)
    fun trackFrameRate(fps: Int)
    fun trackCrash(exception: Throwable)
}
```

### 业务指标

```kotlin
interface AnalyticsManager {
    fun trackGameStart(gameType: String)
    fun trackGameEnd(gameType: String, score: Int, duration: Long)
    fun trackUserAction(action: String, properties: Map<String, Any>)
    fun trackError(error: String, context: Map<String, Any>)
}
```

---

*文档版本: v2.0.0*  
*最后更新: 2025-06-20*  
*下次评审: 2025-09-20*
