# Questicle Project – Deep-Dive Code & Architecture Analysis (July 2025)

> **Scope** – This document records an in-depth assessment of the current codebase, identifies key technical debts, and proposes a phased optimisation / refactoring blueprint. It complements the high-level overview file `docs/project/comprehensive_project_overview_2025-07.md` and SHOULD be updated after each major milestone.

---

## A.  Current Code & Architecture Diagnostics

### 1  Module Landscape
* Multi-module Gradle (8.3) with Kotlin 2.1-K2, Jetpack Compose 1.7, Hilt DI.
* Clean-Architecture separation: `core.*` (pure Kotlin), `feature.*` (UI + DI), `app` (navigation + root graph), convention plugins in `build-logic/`.
* Cross-cutting concerns (logging, error, performance) live in `core/common`.

### 2  Tetris Domain Core
* `TetrisEngineImpl` (logic) → `TetrisControllerImpl` (loop + persistence) → `TetrisGameManager` (UI façade).
* Object-pool & caching implemented (`TetrisPiecePool`, `ConcurrentHashMap`).
* Missing features: **Ghost Piece**, **Game-pad / keyboard mapper**.

### 3  Infrastructure
* **QLogger** asynchronous structured logger (JSON), old GameLogger removed.
* Persistence: Room + DataStore via `UnifiedGameStorageRepository`.
* Error handling via `GameResult`, `BusinessException`.

### 4  UI & Design System
* MD3 theme, HarmonyOS typography, colours unified in `UnifiedColorSystem.kt`.
* Responsive layouts (phone / landscape / tablet). Remaining hard-coded colours (~11) in XML.

### 5  Quality & Tooling
* Unit coverage 71 % (core 85 %); UI-tests 40+ cases (phone only).
* No Baseline-Profile / Macrobenchmark – cold start 1.8 s (> target 1.5 s).
* Detekt / Ktlint green, but 3 AutoMirrored icon deprecations.

---

## B.  Key Risks & Technical Debts

| ID | Issue | Impact |
|----|-------|--------|
| B1 | Manager / Controller responsibility overlap | Complexity & duplicated state listeners |
| B2 | Domain logic invoked in UI layer | Breaks Clean Architecture, testability risk |
| B3 | No Baseline-Profile | Cold-start 0 .3 s above SLA |
| B4 | Excess `LogContext` map allocations | GC pressure on low-end devices |
| B5 | Residual unused resources (colour, dimen) | APK +0.8 MB, maintenance overhead |
| B6 | I18n & A11y gaps (es/ar, RTL, TalkBack) | Market reach & compliance risk |
| B7 | Custom CoroutineScope leaks (Controller) | Memory leak potential |

---

## C.  Optimisation / Refactor Blueprint

### ① Unify Game Flow Layers  (High)
* Remove duplicate listeners in `TetrisGameManager`; ViewModels subscribe directly to `TetrisControllerImpl`.
* Move stats updates into Engine; expose via Flow.

### ② Baseline-Profile + Macrobenchmark (High)
* Create `baselineprofile` module; record Splash→Main→Game route.
* CI task `:baselineprofile:recordReleaseBaselineProfile`.
* Gate with startup median ≤ 1500 ms.

### ③ Resource & Design-System Cleanup (Med-High)
* Grep residual hex colours; migrate to `UnifiedColorSystem`.
* Enable `UnusedResources` lint.

### ④ Coroutine Scope Governance (Med)
* Inject `@ApplicationScope` or `ViewModelScope` instead of new `SupervisorJob`.
* Ensure cancellation in `onCleared`.

### ⑤ Logger Context Optimisation (Med)
* Introduce DSL builder + ThreadLocal cache to reuse `LogContextBuilder`.

### ⑥ I18n & A11y Expansion (Med)
* Add `values-es`, `values-ar`; validate RTL.
* Compose semantics & Accessibility-Scanner tests.

### ⑦ Feature Gaps – Ghost Piece & Game-pad  (High)
* Ghost Piece: add semi-transparent draw pass in `TetrisBoard`.
* `InputActionMapper` for physical keys / controllers.

### ⑧ Test Matrix Reinforcement (Med-Low)
* Add Pixel C device, a11y UI-tests.
* Target unit coverage ≥ 85 %, overall ≥ 75 %.

### ⑨ CI + Security  (Low)
* Integrate Play Integrity API; automate licenses report.

---

## D.  Recommended Timeline & Deliverables

| Week | Focus | Expected Output |
|------|-------|-----------------|
| 1-2  | ① Layer unification + ④ Scope fix | Controller/Manager merged, memory leak test pass |
| 3   | ② Baseline-Profile | Startup ≤ 1.5 s report, CI gate |
| 4   | ③ Resource cleanup | APK -0.8 MB, lint all-green |
| 5-6 | ⑦ Ghost Piece & Input-Mapper | Feature PR + tests |
| parallel | ⑤ Logger & ⑥ I18n/A11y | GC ↓ 15 %, es & ar locales done |
| ongoing | ⑧ Tests | Coverage dashboards |

---

_Last updated – 2025-07-07_ 