# Tetris Engine Testing and Benchmarking Guide

## Overview

This guide provides instructions for testing and benchmarking the Tetris engine. It covers unit testing, integration testing, performance testing, and benchmarking.

## Testing

### Unit Testing

Unit tests verify that individual components work correctly in isolation. They use mock dependencies to isolate the component being tested.

#### Running Unit Tests

```bash
# Run all unit tests
./gradlew :feature:tetris:impl:unitTest

# Run a specific test class
./gradlew :feature:tetris:impl:test --tests "com.yu.questicle.feature.tetris.impl.engine.TetrisEngineImplTest"

# Run a specific test method
./gradlew :feature:tetris:impl:test --tests "com.yu.questicle.feature.tetris.impl.engine.TetrisEngineImplTest.test_initialize_game"
```

#### Writing Unit Tests

1. Create a test class in the `src/test/kotlin` directory
2. Use the `MockComponentFactory` to create mock dependencies
3. Use the `TestDataProvider` to create test data
4. Write test methods that verify the behavior of the component

Example:

```kotlin
@Test
fun `test initialize game`() = runTest {
    // Arrange
    val engine = TetrisEngineImpl(
        gameStateRepository = MockComponentFactory.createMockGameStateRepository(),
        gameLogicProcessor = MockComponentFactory.createMockGameLogicProcessor(),
        collisionDetector = MockComponentFactory.createMockCollisionDetector(),
        statisticsCalculator = MockComponentFactory.createMockStatisticsCalculator(),
        performanceManager = MockComponentFactory.createMockPerformanceManager(),
        cacheManager = MockComponentFactory.createMockCacheManager()
    )
    
    // Act
    val result = engine.initializeGame("player1")
    
    // Assert
    assertTrue(result.isSuccess)
    val gameState = result.getOrThrow()
    assertEquals(TetrisStatus.READY, gameState.status)
}
```

### Integration Testing

Integration tests verify that components work correctly together. They use real implementations of dependencies.

#### Running Integration Tests

```bash
# Run all integration tests
./gradlew :feature:tetris:impl:integrationTest

# Run a specific integration test class
./gradlew :feature:tetris:impl:test --tests "com.yu.questicle.feature.tetris.impl.engine.TetrisComponentIntegrationTest"
```

#### Writing Integration Tests

1. Create a test class in the `src/test/kotlin` directory
2. Add the `@Tag("integration")` annotation
3. Use real implementations of dependencies or carefully configured mocks
4. Write test methods that verify the behavior of multiple components working together

Example:

```kotlin
@Tag("integration")
class TetrisComponentIntegrationTest {
    @Test
    fun `test game initialization and piece movement`() = runTest {
        // Arrange
        val engine = TetrisEngineImpl(
            gameStateRepository = MockComponentFactory.createMockGameStateRepository(),
            gameLogicProcessor = TetrisGameLogicProcessorImpl(
                collisionDetector = TetrisCollisionDetectorImpl()
            ),
            collisionDetector = TetrisCollisionDetectorImpl(),
            statisticsCalculator = MockComponentFactory.createMockStatisticsCalculator(),
            performanceManager = MockComponentFactory.createMockPerformanceManager(),
            cacheManager = MockComponentFactory.createMockCacheManager()
        )
        
        // Act
        val initResult = engine.initializeGame("player1")
        val gameState = initResult.getOrThrow()
        val moveResult = engine.movePiece(Direction.RIGHT, gameState)
        
        // Assert
        assertTrue(moveResult.isSuccess)
        val movedState = moveResult.getOrThrow()
        assertEquals(gameState.currentPiece?.x?.plus(1), movedState.currentPiece?.x)
    }
}
```

### Performance Testing

Performance tests verify that the engine meets performance requirements. They measure the execution time of operations and memory usage.

#### Running Performance Tests

```bash
# Run all performance tests
./gradlew :feature:tetris:impl:performanceTest

# Run a specific performance test class
./gradlew :feature:tetris:impl:test --tests "com.yu.questicle.feature.tetris.impl.engine.TetrisEnginePerformanceTest"
```

#### Writing Performance Tests

1. Create a test class in the `src/test/kotlin` directory
2. Add the `@Tag("performance")` annotation
3. Use the `measureTimeMillis` function to measure execution time
4. Write test methods that verify performance requirements

Example:

```kotlin
@Tag("performance")
class TetrisEnginePerformanceTest {
    @Test
    fun `measure performance of piece movement`() = runTest {
        // Arrange
        val engine = // create engine
        val gameState = // create game state
        
        // Warmup
        repeat(100) {
            engine.movePiece(Direction.RIGHT, gameState).getOrThrow()
        }
        
        // Act
        val iterations = 10000
        val time = measureTimeMillis {
            repeat(iterations) {
                engine.movePiece(Direction.RIGHT, gameState).getOrThrow()
            }
        }
        
        // Assert
        val averageTime = time.toDouble() / iterations
        println("Average time per move: $averageTime ms")
        assert(averageTime < 0.5) { "Move operation should take less than 0.5ms" }
    }
}
```

## Benchmarking

### JMH Benchmarks

JMH (Java Microbenchmark Harness) is used for accurate microbenchmarking. It handles warmup, measurement, and statistical analysis.

#### Running JMH Benchmarks

```bash
# Run all JMH benchmarks
./gradlew :feature:tetris:impl:jmh

# Run a specific benchmark class
./gradlew :feature:tetris:impl:jmh -Pjmh.include=TetrisEngineBenchmark

# Run a specific benchmark method
./gradlew :feature:tetris:impl:jmh -Pjmh.include=TetrisEngineBenchmark.benchmarkPieceMovement
```

#### Writing JMH Benchmarks

1. Create a benchmark class in the `src/jmh/kotlin` directory
2. Add JMH annotations to configure the benchmark
3. Write benchmark methods that measure the performance of operations

Example:

```kotlin
@State(Scope.Benchmark)
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MICROSECONDS)
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
@Fork(2)
class TetrisEngineBenchmark {
    // Test data
    private lateinit var engine: TetrisEngineImpl
    private lateinit var gameState: TetrisGameState
    
    @Setup
    fun setUp() {
        // Initialize test data
    }
    
    @Benchmark
    fun benchmarkPieceMovement(blackhole: Blackhole) {
        val result = engine.movePiece(Direction.RIGHT, gameState)
        blackhole.consume(result)
    }
}
```

### Benchmark Reporting

Benchmark results can be generated in various formats and analyzed using the provided tools.

#### Generating Reports

```bash
# Run benchmarks and generate reports
./gradlew :feature:tetris:impl:jmh -Pjmh.resultFormat=JSON,CSV,HTML
```

#### Comparing Results

The `JmhResultsComparator` can be used to compare benchmark results between runs:

```kotlin
val comparator = JmhResultsComparator()
comparator.compareResults(
    baselineResults = baselineResults,
    currentResults = currentResults,
    outputFile = File("benchmark-comparison.html"),
    thresholdPercent = 5.0
)
```

## Best Practices

### Testing

1. **Test Coverage**: Aim for >95% test coverage
2. **Isolation**: Ensure tests are isolated and don't depend on each other
3. **Determinism**: Make tests deterministic to avoid flakiness
4. **Readability**: Write clear test names and assertions
5. **Performance**: Keep tests fast to encourage frequent testing

### Benchmarking

1. **Warmup**: Always include warmup iterations to avoid cold start effects
2. **Statistical Significance**: Run enough iterations to get statistically significant results
3. **Environment**: Run benchmarks in a stable environment with minimal background activity
4. **Blackhole**: Use the JMH `Blackhole` to prevent dead code elimination
5. **Comparison**: Compare benchmark results against a baseline to detect regressions

## Troubleshooting

### Common Issues

1. **Flaky Tests**: If tests are flaky, check for non-deterministic behavior or race conditions
2. **Slow Tests**: If tests are slow, check for unnecessary operations or inefficient algorithms
3. **Memory Leaks**: If memory usage increases over time, check for objects that aren't being garbage collected
4. **Benchmark Variability**: If benchmark results are highly variable, increase the number of iterations or forks

### Getting Help

If you encounter issues with testing or benchmarking, please:

1. Check the documentation in the `docs/07_quality` directory
2. Look for similar issues in the issue tracker
3. Ask for help in the #tetris-engine Slack channel