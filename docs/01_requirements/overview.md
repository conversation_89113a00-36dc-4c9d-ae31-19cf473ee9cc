# 🎯 Questicle 项目概览

> **项目愿景** - 打造现代化、高质量的俄罗斯方块游戏应用

[![Project Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)](./overview.md)
[![Version](https://img.shields.io/badge/Version-1.0.0-blue.svg)](./overview.md)
[![Last Updated](https://img.shields.io/badge/Last%20Updated-2025--07--16-green.svg)](./overview.md)

---

## 🎮 项目简介

Questicle 是一个现代化的俄罗斯方块游戏，采用最新的 Android 开发技术栈构建。项目不仅提供优秀的游戏体验，更是现代 Android 开发最佳实践的展示。

### 核心价值主张
- **经典体验** + **现代技术** = **卓越品质**
- 为玩家提供流畅的游戏体验
- 为开发者提供学习和参考的标杆项目
- 为团队提供可维护、可扩展的代码架构

---

## ✨ 核心特性

### 🎯 游戏特性
- **经典玩法**: 完整的俄罗斯方块游戏逻辑
- **高级技巧**: 支持 SRS 旋转系统和 T-Spin
- **智能预览**: 下一个方块预览和幽灵方块
- **暂存功能**: Hold 功能支持策略性游戏
- **公平算法**: 7-bag 随机算法确保公平性

### 🛠️ 技术特性
- **现代化UI**: 基于 Jetpack Compose 的流畅界面
- **清洁架构**: 遵循 Clean Architecture 原则
- **模块化设计**: api/impl 分离的模块架构
- **高性能构建**: 3秒内完成构建（配置缓存优化）
- **完善测试**: 98.2% 测试通过率
- **自动化CI/CD**: 完整的质量门禁流程

---

## 🛠️ 技术栈

### 核心技术
| 技术领域 | 技术选型 | 版本 | 说明 |
|---------|---------|------|------|
| **开发语言** | Kotlin | 2.0.0 | 100% Kotlin 代码 |
| **UI框架** | Jetpack Compose | 1.6 | 现代化声明式UI |
| **架构模式** | Clean Architecture + MVVM | - | 分层架构设计 |
| **依赖注入** | Hilt | - | Google 推荐的DI框架 |
| **异步处理** | Coroutines + Flow | - | Kotlin 原生异步方案 |
| **数据存储** | Room | - | SQLite 抽象层 |
| **构建工具** | Gradle | 8.14.3 | 高性能构建系统 |

### 测试技术
| 测试类型 | 技术选型 | 说明 |
|---------|---------|------|
| **单元测试** | JUnit 5 + MockK | 现代化测试框架 |
| **集成测试** | Kotest | BDD 风格测试 |
| **UI测试** | Compose Test | Compose 专用测试 |
| **性能测试** | Turbine | Flow 测试工具 |

---

## 📊 项目指标

### 代码质量指标
| 指标 | 当前值 | 目标值 | 状态 |
|------|--------|--------|------|
| **代码行数** | ~15,000 行 | - | 📊 |
| **模块数量** | 8个核心模块 | - | 📊 |
| **构建时间** | < 3秒 | < 5秒 | ✅ 超标 |
| **测试覆盖率** | > 85% | > 80% | ✅ 达标 |
| **测试通过率** | 98.2% | > 95% | ✅ 超标 |
| **代码质量评分** | 85/100 | > 80 | ✅ 达标 |

### 性能指标
| 指标 | 当前值 | 目标值 | 状态 |
|------|--------|--------|------|
| **应用启动时间** | < 2秒 | < 3秒 | ✅ 优秀 |
| **游戏帧率** | 60 FPS | > 30 FPS | ✅ 流畅 |
| **内存占用** | < 100MB | < 150MB | ✅ 优化 |
| **APK大小** | < 20MB | < 30MB | ✅ 精简 |

---

## 🎯 项目目标

### 短期目标 (3个月)
- ✅ 完成核心游戏功能开发
- ✅ 建立完善的测试体系
- ✅ 实现高性能构建系统
- ✅ 建立标准化文档体系

### 中期目标 (6个月)
- 🔄 添加多人游戏模式
- 🔄 实现云端数据同步
- 🔄 支持自定义主题
- 🔄 添加成就系统

### 长期目标 (1年)
- 📋 跨平台支持 (iOS)
- 📋 AI 对战模式
- 📋 社区功能
- 📋 电竞模式

---

## 👥 目标用户

### 主要用户群体
1. **休闲游戏玩家**
   - 寻求经典俄罗斯方块体验
   - 重视游戏流畅性和稳定性
   - 偏好简洁直观的界面

2. **俄罗斯方块爱好者**
   - 熟悉高级技巧 (T-Spin, SRS)
   - 追求竞技性和挑战性
   - 关注游戏的专业性

### 次要用户群体
1. **技术爱好者**
   - 对现代 Android 开发技术感兴趣
   - 希望学习 Clean Architecture 实践
   - 关注代码质量和架构设计

2. **开发者社区**
   - 寻找高质量的开源项目参考
   - 希望贡献代码或提出改进建议
   - 学习最佳实践和设计模式

---

## 🚀 快速开始

### 环境要求
- **Android Studio**: Iguana (2023.2.1) 或更新版本
- **JDK**: 21 或更高版本
- **Android SDK**: API 34
- **Git**: 用于版本控制

### 5分钟快速体验
```bash
# 1. 克隆项目
git clone <repository-url>
cd questicle

# 2. 构建项目
./gradlew build --configuration-cache

# 3. 运行应用
./gradlew installDemoDebug
```

### 深入了解
- 📖 [用户快速指南](../07_user/guides/quick-start.md)
- 🛠️ [开发环境搭建](../08_development/setup/environment.md)
- 🏛️ [系统架构概览](../02_architecture/overview.md)

---

## 📚 相关文档

### 需求和规划
- 📋 [产品需求文档 (PRD)](specifications/prd.md)
- 📄 [软件需求规格 (SRS)](specifications/srs.md)

### 技术文档
- 🏛️ [系统架构设计](../02_architecture/overview.md)
- 🎨 [设计系统规范](../03_design/system/design-system.md)
- 🔌 [API接口文档](../04_api/internal/modules.md)

### 开发指南
- 🛠️ [环境搭建指南](../08_development/setup/environment.md)
- 📏 [开发规范标准](../08_development/standards/coding.md)
- 🔧 [开发工具配置](../08_development/tools/)

---

## 🤝 参与贡献

我们欢迎各种形式的贡献！

### 贡献方式
- 🐛 **报告问题**: 发现 Bug 或提出改进建议
- 💡 **功能建议**: 提出新功能想法
- 💻 **代码贡献**: 提交代码改进或新功能
- 📝 **文档改进**: 完善或更新文档内容

### 参与指南
- [新成员入职指南](../08_development/setup/onboarding.md)
- [开发规范标准](../08_development/standards/coding.md)
- [文档贡献指南](../CONTRIBUTING.md)

---

## 📞 联系我们

- **项目仓库**: [GitHub Repository](https://github.com/your-org/questicle)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/questicle/issues)
- **讨论交流**: [GitHub Discussions](https://github.com/your-org/questicle/discussions)

---

*Questicle 项目致力于成为现代 Android 开发的标杆项目，为玩家提供优秀体验，为开发者提供学习参考。*

**最后更新**: 2025年7月16日 | **文档版本**: 1.0.0