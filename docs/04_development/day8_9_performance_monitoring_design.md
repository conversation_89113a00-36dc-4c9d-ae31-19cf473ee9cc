# 🏗️ Day 8-9: 性能监控系统详细设计

> **Questicle 项目第二周代码质量提升 - 性能监控系统详细设计文档**

[![Status](https://img.shields.io/badge/Status-Design%20Phase-blue.svg)](./day8_9_performance_monitoring_design.md)
[![Phase](https://img.shields.io/badge/Phase-2%20Code%20Quality-blue.svg)](./day8_9_performance_monitoring_design.md)
[![Priority](https://img.shields.io/badge/Priority-P1-orange.svg)](./day8_9_performance_monitoring_design.md)

## 📋 设计概览

| 项目 | 详情 |
|------|------|
| **设计时间** | 2025年7月20日 |
| **设计阶段** | Phase 2 - 代码质量提升 (P1) |
| **设计范围** | Day 8-9: 性能监控系统详细设计 |
| **架构模式** | 分层架构 + 观察者模式 + 策略模式 |
| **技术栈** | <PERSON><PERSON><PERSON>, Corout<PERSON>, Choreographer, Supabase |

## 🏗️ 系统架构设计

### 整体架构

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[PerformanceMonitorFacade] --> B[PerformanceMonitor]
    end
    
    subgraph "业务层 (Business Layer)"
        B --> C[FrameRateMonitor]
        B --> D[MemoryMonitor]
        B --> E[CPUMonitor]
        B --> F[NetworkMonitor]
        B --> G[PerformanceAnalyzer]
        B --> H[PerformanceAlerter]
    end
    
    subgraph "数据层 (Data Layer)"
        C --> I[PerformanceDataCollector]
        D --> I
        E --> I
        F --> I
        I --> J[PerformanceRepository]
        J --> K[LocalDataSource]
        J --> L[RemoteDataSource]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        K --> M[Room Database]
        L --> N[SupabaseClient]
        G --> O[AnalysisEngine]
        H --> P[AlertEngine]
    end
```

### 分层职责

#### 表现层 (Presentation Layer)
- **PerformanceMonitorFacade**: 统一的性能监控接口
- **职责**: 提供简化的API，隐藏内部复杂性
- **模式**: 门面模式 (Facade Pattern)

#### 业务层 (Business Layer)
- **PerformanceMonitor**: 核心监控控制器
- **各种Monitor**: 具体的性能监控器
- **PerformanceAnalyzer**: 性能数据分析器
- **PerformanceAlerter**: 性能告警器
- **职责**: 实现核心业务逻辑
- **模式**: 策略模式 + 观察者模式

#### 数据层 (Data Layer)
- **PerformanceDataCollector**: 数据收集器
- **PerformanceRepository**: 数据仓库
- **DataSource**: 数据源抽象
- **职责**: 数据收集、存储、同步
- **模式**: 仓库模式 (Repository Pattern)

#### 基础设施层 (Infrastructure Layer)
- **Room Database**: 本地数据库
- **SupabaseClient**: 云端数据客户端
- **AnalysisEngine**: 分析引擎
- **AlertEngine**: 告警引擎
- **职责**: 提供基础技术服务

## 📊 核心组件设计

### 1. PerformanceMonitor 设计

```kotlin
@Singleton
class PerformanceMonitor @Inject constructor(
    private val frameRateMonitor: FrameRateMonitor,
    private val memoryMonitor: MemoryMonitor,
    private val cpuMonitor: CPUMonitor,
    private val networkMonitor: NetworkMonitor,
    private val dataCollector: PerformanceDataCollector,
    private val analyzer: PerformanceAnalyzer,
    private val alerter: PerformanceAlerter
) {
    // 监控状态管理
    private val _monitoringState = MutableStateFlow(MonitoringState.STOPPED)
    val monitoringState: StateFlow<MonitoringState> = _monitoringState.asStateFlow()
    
    // 性能数据流
    private val _performanceData = MutableSharedFlow<PerformanceSnapshot>()
    val performanceData: SharedFlow<PerformanceSnapshot> = _performanceData.asSharedFlow()
    
    // 配置管理
    private var config = PerformanceMonitorConfig()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
}
```

#### 核心方法设计

```kotlin
interface PerformanceMonitorInterface {
    suspend fun startMonitoring(config: PerformanceMonitorConfig = PerformanceMonitorConfig())
    suspend fun stopMonitoring()
    suspend fun pauseMonitoring()
    suspend fun resumeMonitoring()
    
    fun getPerformanceSnapshot(): PerformanceSnapshot
    fun getPerformanceHistory(timeRange: TimeRange): List<PerformanceSnapshot>
    
    fun setConfig(config: PerformanceMonitorConfig)
    fun addPerformanceListener(listener: PerformanceListener)
    fun removePerformanceListener(listener: PerformanceListener)
}
```

### 2. FrameRateMonitor 设计

```kotlin
@Singleton
class FrameRateMonitor @Inject constructor(
    private val context: Context
) : BaseMonitor<FrameRateMetric>() {
    
    // Choreographer 实例
    private val choreographer = Choreographer.getInstance()
    
    // 帧率计算
    private var frameCount = 0
    private var lastFrameTime = 0L
    private val frameTimeHistory = CircularBuffer<Long>(capacity = 60)
    
    // 帧率回调
    private val frameCallback = object : Choreographer.FrameCallback {
        override fun doFrame(frameTimeNanos: Long) {
            recordFrame(frameTimeNanos)
            if (isMonitoring) {
                choreographer.postFrameCallback(this)
            }
        }
    }
}
```

#### 帧率计算算法

```kotlin
private fun calculateFrameRate(): FrameRateMetric {
    val currentTime = System.currentTimeMillis()
    val timeWindow = 1000L // 1秒窗口
    
    // 计算1秒内的帧数
    val framesInWindow = frameTimeHistory.count { 
        currentTime - it / 1_000_000 <= timeWindow 
    }
    
    // 计算平均帧时间
    val avgFrameTime = if (frameTimeHistory.isNotEmpty()) {
        frameTimeHistory.average() / 1_000_000.0 // 转换为毫秒
    } else 0.0
    
    // 计算丢帧率
    val expectedFrames = 60 // 假设目标60fps
    val droppedFrameRate = if (expectedFrames > 0) {
        maxOf(0.0, (expectedFrames - framesInWindow) / expectedFrames.toDouble())
    } else 0.0
    
    return FrameRateMetric(
        timestamp = currentTime,
        fps = framesInWindow,
        avgFrameTime = avgFrameTime,
        droppedFrameRate = droppedFrameRate,
        isSmooth = framesInWindow >= 55 // 55fps以上认为流畅
    )
}
```

### 3. MemoryMonitor 设计

```kotlin
@Singleton
class MemoryMonitor @Inject constructor(
    private val context: Context
) : BaseMonitor<MemoryMetric>() {
    
    // 内存信息获取
    private val runtime = Runtime.getRuntime()
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    
    // GC监控
    private var lastGcCount = 0L
    private val gcHistory = CircularBuffer<GcEvent>(capacity = 100)
    
    override suspend fun collectMetric(): MemoryMetric {
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        // 堆内存信息
        val totalHeap = runtime.totalMemory()
        val freeHeap = runtime.freeMemory()
        val usedHeap = totalHeap - freeHeap
        val maxHeap = runtime.maxMemory()
        
        // 系统内存信息
        val availableMemory = memoryInfo.availMem
        val totalMemory = memoryInfo.totalMem
        val usedMemory = totalMemory - availableMemory
        
        // GC信息
        val currentGcCount = getGcCount()
        val gcRate = calculateGcRate(currentGcCount)
        
        return MemoryMetric(
            timestamp = System.currentTimeMillis(),
            usedHeap = usedHeap,
            totalHeap = totalHeap,
            maxHeap = maxHeap,
            heapUtilization = usedHeap.toDouble() / maxHeap,
            usedMemory = usedMemory,
            totalMemory = totalMemory,
            availableMemory = availableMemory,
            memoryPressure = memoryInfo.lowMemory,
            gcCount = currentGcCount,
            gcRate = gcRate
        )
    }
}
```

### 4. CPUMonitor 设计

```kotlin
@Singleton
class CPUMonitor @Inject constructor() : BaseMonitor<CPUMetric>() {
    
    // CPU信息读取
    private var lastCpuTime = 0L
    private var lastIdleTime = 0L
    private val cpuHistory = CircularBuffer<Double>(capacity = 60)
    
    override suspend fun collectMetric(): CPUMetric = withContext(Dispatchers.IO) {
        val cpuUsage = calculateCpuUsage()
        val threadCount = getThreadCount()
        val loadAverage = getLoadAverage()
        
        CPUMetric(
            timestamp = System.currentTimeMillis(),
            cpuUsage = cpuUsage,
            threadCount = threadCount,
            loadAverage = loadAverage,
            isHighUsage = cpuUsage > 80.0
        )
    }
    
    private fun calculateCpuUsage(): Double {
        return try {
            val statFile = File("/proc/stat")
            val firstLine = statFile.readLines().first()
            val cpuTimes = firstLine.split("\\s+".toRegex())
                .drop(1)
                .take(7)
                .map { it.toLong() }
            
            val idleTime = cpuTimes[3]
            val totalTime = cpuTimes.sum()
            
            if (lastCpuTime != 0L) {
                val totalDelta = totalTime - lastCpuTime
                val idleDelta = idleTime - lastIdleTime
                val usage = if (totalDelta > 0) {
                    (1.0 - idleDelta.toDouble() / totalDelta) * 100.0
                } else 0.0
                
                lastCpuTime = totalTime
                lastIdleTime = idleTime
                
                usage.coerceIn(0.0, 100.0)
            } else {
                lastCpuTime = totalTime
                lastIdleTime = idleTime
                0.0
            }
        } catch (e: Exception) {
            0.0
        }
    }
}
```

## 📊 数据模型设计

### 核心数据模型

```kotlin
// 基础性能指标
abstract class PerformanceMetric {
    abstract val timestamp: Long
    abstract val metricType: MetricType
    abstract fun toMap(): Map<String, Any?>
}

// 帧率指标
data class FrameRateMetric(
    override val timestamp: Long,
    val fps: Int,
    val avgFrameTime: Double,
    val droppedFrameRate: Double,
    val isSmooth: Boolean
) : PerformanceMetric() {
    override val metricType = MetricType.FRAME_RATE
}

// 内存指标
data class MemoryMetric(
    override val timestamp: Long,
    val usedHeap: Long,
    val totalHeap: Long,
    val maxHeap: Long,
    val heapUtilization: Double,
    val usedMemory: Long,
    val totalMemory: Long,
    val availableMemory: Long,
    val memoryPressure: Boolean,
    val gcCount: Long,
    val gcRate: Double
) : PerformanceMetric() {
    override val metricType = MetricType.MEMORY
}

// CPU指标
data class CPUMetric(
    override val timestamp: Long,
    val cpuUsage: Double,
    val threadCount: Int,
    val loadAverage: Double,
    val isHighUsage: Boolean
) : PerformanceMetric() {
    override val metricType = MetricType.CPU
}

// 性能快照
data class PerformanceSnapshot(
    val timestamp: Long,
    val frameRate: FrameRateMetric?,
    val memory: MemoryMetric?,
    val cpu: CPUMetric?,
    val network: NetworkMetric?,
    val overall: OverallPerformance
) {
    fun toSupabaseRecord(): SupabasePerformanceRecord {
        return SupabasePerformanceRecord(
            id = UUID.randomUUID().toString(),
            timestamp = timestamp,
            metric_type = "SNAPSHOT",
            metric_value = overall.score,
            metadata = mapOf(
                "frameRate" to frameRate?.toMap(),
                "memory" to memory?.toMap(),
                "cpu" to cpu?.toMap(),
                "network" to network?.toMap()
            ).filterValues { it != null }
        )
    }
}
```

### 配置模型

```kotlin
data class PerformanceMonitorConfig(
    val enableFrameRateMonitoring: Boolean = true,
    val enableMemoryMonitoring: Boolean = true,
    val enableCpuMonitoring: Boolean = true,
    val enableNetworkMonitoring: Boolean = false,
    
    // 采样配置
    val frameRateSamplingInterval: Long = 1000L, // 1秒
    val memorySamplingInterval: Long = 5000L,    // 5秒
    val cpuSamplingInterval: Long = 10000L,      // 10秒
    val networkSamplingInterval: Long = 30000L,  // 30秒
    
    // 阈值配置
    val frameRateThreshold: Int = 55,            // 55fps
    val memoryThreshold: Double = 0.8,           // 80%堆内存
    val cpuThreshold: Double = 80.0,             // 80%CPU
    
    // 存储配置
    val enableLocalStorage: Boolean = true,
    val enableRemoteStorage: Boolean = true,
    val maxLocalRecords: Int = 1000,
    val uploadBatchSize: Int = 50,
    
    // 告警配置
    val enableAlerts: Boolean = true,
    val alertCooldown: Long = 60000L             // 1分钟冷却
)
```

## 🔄 数据流设计

### 数据收集流程

```mermaid
sequenceDiagram
    participant PM as PerformanceMonitor
    participant FRM as FrameRateMonitor
    participant MM as MemoryMonitor
    participant CM as CPUMonitor
    participant DC as DataCollector
    participant R as Repository
    
    PM->>FRM: startMonitoring()
    PM->>MM: startMonitoring()
    PM->>CM: startMonitoring()
    
    loop 监控循环
        FRM->>DC: emit(FrameRateMetric)
        MM->>DC: emit(MemoryMetric)
        CM->>DC: emit(CPUMetric)
        DC->>DC: aggregateMetrics()
        DC->>PM: emit(PerformanceSnapshot)
        PM->>R: saveSnapshot()
    end
```

### 数据存储策略

```kotlin
class PerformanceRepository @Inject constructor(
    private val localDataSource: LocalPerformanceDataSource,
    private val remoteDataSource: RemotePerformanceDataSource
) {
    
    suspend fun savePerformanceSnapshot(snapshot: PerformanceSnapshot) {
        // 本地存储
        localDataSource.insertSnapshot(snapshot)
        
        // 批量上传到云端
        if (shouldUploadToRemote()) {
            val pendingSnapshots = localDataSource.getPendingSnapshots()
            remoteDataSource.uploadSnapshots(pendingSnapshots)
            localDataSource.markAsUploaded(pendingSnapshots.map { it.id })
        }
    }
    
    private suspend fun shouldUploadToRemote(): Boolean {
        val pendingCount = localDataSource.getPendingCount()
        val lastUploadTime = localDataSource.getLastUploadTime()
        val timeSinceLastUpload = System.currentTimeMillis() - lastUploadTime
        
        return pendingCount >= config.uploadBatchSize || 
               timeSinceLastUpload >= config.uploadInterval
    }
}
```

## 🚨 告警系统设计

### 告警策略

```kotlin
class PerformanceAlerter @Inject constructor(
    private val alertEngine: AlertEngine,
    private val notificationManager: NotificationManager
) {
    
    suspend fun checkPerformanceAlerts(snapshot: PerformanceSnapshot) {
        val alerts = mutableListOf<PerformanceAlert>()
        
        // 帧率告警
        snapshot.frameRate?.let { frameRate ->
            if (frameRate.fps < config.frameRateThreshold) {
                alerts.add(PerformanceAlert.LowFrameRate(frameRate.fps))
            }
        }
        
        // 内存告警
        snapshot.memory?.let { memory ->
            if (memory.heapUtilization > config.memoryThreshold) {
                alerts.add(PerformanceAlert.HighMemoryUsage(memory.heapUtilization))
            }
        }
        
        // CPU告警
        snapshot.cpu?.let { cpu ->
            if (cpu.cpuUsage > config.cpuThreshold) {
                alerts.add(PerformanceAlert.HighCpuUsage(cpu.cpuUsage))
            }
        }
        
        // 发送告警
        alerts.forEach { alert ->
            if (alertEngine.shouldTriggerAlert(alert)) {
                notificationManager.sendAlert(alert)
            }
        }
    }
}
```

## 📱 Supabase 集成设计

### 数据库表设计

```sql
-- 性能监控数据表
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp BIGINT NOT NULL,
    user_id TEXT,
    session_id TEXT,
    app_version TEXT NOT NULL,
    metric_type TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 性能告警表
CREATE TABLE performance_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp BIGINT NOT NULL,
    alert_type TEXT NOT NULL,
    severity TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### API 接口设计

```kotlin
interface SupabasePerformanceApi {
    suspend fun uploadPerformanceMetrics(metrics: List<SupabasePerformanceRecord>): Result<Unit>
    suspend fun getPerformanceStatistics(timeRange: TimeRange): Result<PerformanceStatistics>
    suspend fun getPerformanceTrends(timeRange: TimeRange): Result<List<PerformanceTrend>>
    suspend fun uploadPerformanceAlert(alert: SupabasePerformanceAlert): Result<Unit>
}
```

---

**文档版本**: v1.0  
**设计完成时间**: 2025年7月20日  
**设计状态**: ✅ 完成  
**下一步**: 编码实现阶段
