# 🧪 测试框架最佳实践指南

> **Questicle 项目测试框架优化后的最佳实践指南**

[![Status](https://img.shields.io/badge/Status-Best%20Practices-green.svg)](./day10_test_framework_best_practices.md)
[![Phase](https://img.shields.io/badge/Phase-2%20Code%20Quality-blue.svg)](./day10_test_framework_best_practices.md)
[![Priority](https://img.shields.io/badge/Priority-P1-orange.svg)](./day10_test_framework_best_practices.md)

## 📋 概览

本文档提供了 Questicle 项目测试框架优化后的最佳实践指南，帮助开发者高效地编写和维护测试代码。

## 🏗️ 测试架构概览

### 分层测试策略

```
┌─────────────────────────────────────────┐
│              端到端测试 (10%)              │  ← 完整业务流程
├─────────────────────────────────────────┤
│              集成测试 (20%)               │  ← 组件间集成
├─────────────────────────────────────────┤
│              单元测试 (70%)               │  ← 单个类/方法
└─────────────────────────────────────────┘
```

### 测试分类

- **单元测试**: `@Tag("unit")` - 快速执行，完全隔离
- **集成测试**: `@Tag("integration")` - 组件间交互
- **性能测试**: `@Tag("performance")` - 性能监控相关
- **内存测试**: `@Tag("memory")` - 内存使用测试

## 🧪 编写测试的最佳实践

### 1. 使用 MockDataFactory

```kotlin
class MyTestClass {
    @Inject
    lateinit var mockDataFactory: MockDataFactory
    
    @Test
    fun `should create performance snapshot correctly`() = runTest {
        // Given - 使用工厂创建测试数据
        val snapshot = mockDataFactory.createPerformanceSnapshot {
            withFrameRate(60)
            withMemory(0.7)
            withCpu(45.0)
        }
        
        // When & Then
        assertThat(snapshot.frameRate!!.fps).isEqualTo(60)
        assertThat(snapshot.memory!!.heapUtilization).isEqualTo(0.7)
    }
}
```

### 2. 嵌套测试结构

```kotlin
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Execution(ExecutionMode.CONCURRENT)
class PerformanceMonitoringTestSuite {
    
    @Nested
    @DisplayName("帧率监控测试")
    inner class FrameRateMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控帧率")
        fun `should monitor frame rate correctly`() = runTest {
            // 测试实现
        }
        
        @ParameterizedTest
        @ValueSource(ints = [30, 45, 60, 90, 120])
        @DisplayName("应该正确检测不同帧率")
        fun `should detect different frame rates`(targetFps: Int) = runTest {
            // 参数化测试实现
        }
    }
}
```

### 3. 参数化测试

```kotlin
@ParameterizedTest
@EnumSource(ErrorSeverity::class)
@DisplayName("应该根据严重程度正确处理异常")
fun `should handle exceptions based on severity`(severity: ErrorSeverity) = runTest {
    // Given
    val exception = mockDataFactory.createQuesticleException {
        withSeverity(severity)
    }
    
    // When
    val result = exceptionHandler.handleException(exception)
    
    // Then
    val expectedAction = when (severity) {
        ErrorSeverity.LOW -> ExceptionAction.LOG_ONLY
        ErrorSeverity.MEDIUM -> ExceptionAction.LOG_AND_CONTINUE
        ErrorSeverity.HIGH -> ExceptionAction.LOG_AND_RECOVER
        ErrorSeverity.CRITICAL -> ExceptionAction.LOG_AND_TERMINATE
    }
    
    assertThat(result.action).isEqualTo(expectedAction)
}
```

### 4. 协程测试

```kotlin
@Test
fun `should handle async operations correctly`() = runTest {
    // Given
    val config = mockDataFactory.createAdvancedPerformanceConfig()
    
    // When
    performanceMonitor.startAdvancedMonitoring(config)
    delay(100) // 等待异步操作
    val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
    
    // Then
    assertThat(snapshot).isNotNull()
}
```

## 🏭 测试数据工厂使用指南

### 基本用法

```kotlin
// 创建默认性能快照
val snapshot = mockDataFactory.createPerformanceSnapshot()

// 创建自定义性能快照
val customSnapshot = mockDataFactory.createPerformanceSnapshot {
    withTimestamp(System.currentTimeMillis())
    withFrameRate(60)
    withMemory(0.8)
    withCpu(70.0)
}
```

### 建造者模式

```kotlin
// 创建复杂的异常对象
val exception = mockDataFactory.createQuesticleException(
    type = ExceptionType.NETWORK
) {
    withMessage("Network timeout occurred")
    withErrorCode("NETWORK_TIMEOUT")
    withSeverity(ErrorSeverity.HIGH)
    withContext(mapOf(
        "url" to "https://api.example.com",
        "timeout" to 5000
    ))
}
```

### 流数据创建

```kotlin
// 创建性能快照流
val snapshotFlow = mockDataFactory.createPerformanceSnapshotFlow(count = 10) {
    withFrameRate(Random.nextInt(30, 120))
}

// 在测试中使用
snapshotFlow.take(5).toList().forEach { snapshot ->
    assertThat(snapshot.frameRate!!.fps).isGreaterThan(30)
}
```

## 🔧 测试配置和执行

### 运行不同类型的测试

```bash
# 运行所有单元测试
./gradlew unitTest

# 运行集成测试
./gradlew integrationTest

# 运行性能测试
./gradlew performanceTest

# 运行内存测试
./gradlew memoryTest

# 运行所有测试（并行）
./gradlew test --parallel --max-workers=4
```

### 测试配置选项

```bash
# 显示测试输出
./gradlew test -PshowTestOutput

# 运行特定测试类
./gradlew test --tests="*PerformanceMonitoringTestSuite"

# 运行特定测试方法
./gradlew test --tests="*.should monitor frame rate correctly"
```

## 📊 测试覆盖率

### 生成覆盖率报告

```bash
# 生成测试覆盖率报告
./gradlew jacocoTestReport

# 验证覆盖率阈值
./gradlew jacocoTestCoverageVerification
```

### 覆盖率目标

- **整体覆盖率**: > 85%
- **新功能覆盖率**: > 90%
- **分支覆盖率**: > 80%
- **核心业务逻辑**: > 95%

## 🚀 性能优化建议

### 1. 测试执行优化

```kotlin
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // 减少实例创建
@Execution(ExecutionMode.CONCURRENT) // 启用并行执行
class OptimizedTestSuite {
    
    @BeforeAll
    fun setupOnce() {
        // 一次性设置，减少重复初始化
    }
    
    @Test
    fun fastTest() {
        // 快速测试，避免耗时操作
    }
}
```

### 2. Mock 优化

```kotlin
// 使用 relaxed mock 减少配置
val mockService = mockk<SomeService>(relaxed = true)

// 批量配置 mock
every { mockService.method1() } returns "result1"
every { mockService.method2() } returns "result2"

// 验证调用
verify(exactly = 1) { mockService.method1() }
```

### 3. 测试数据优化

```kotlin
// 使用伴生对象缓存测试数据
companion object {
    private val cachedUser = mockDataFactory.createUser()
    private val cachedConfig = mockDataFactory.createAdvancedPerformanceConfig()
}

@Test
fun testWithCachedData() {
    // 使用缓存的测试数据，避免重复创建
    val result = service.processUser(cachedUser)
    assertThat(result).isNotNull()
}
```

## 🐛 调试和故障排除

### 1. 测试失败调试

```kotlin
@Test
fun debuggableTest() = runTest {
    // 添加详细的断言消息
    assertThat(result.status)
        .withFailMessage("Expected status to be SUCCESS but was ${result.status}")
        .isEqualTo(Status.SUCCESS)
    
    // 使用 println 进行调试（临时）
    println("Debug: result = $result")
}
```

### 2. 并行测试问题

```kotlin
// 避免共享状态
@Test
fun isolatedTest() = runTest {
    // 每个测试创建独立的数据
    val localData = mockDataFactory.createUser()
    // 测试逻辑
}

// 使用 @Isolated 注解避免并行执行
@Test
@Isolated
fun sequentialTest() {
    // 需要顺序执行的测试
}
```

### 3. 内存泄漏检测

```kotlin
@Test
@Tag("memory")
fun memoryLeakTest() = runTest {
    val initialMemory = Runtime.getRuntime().totalMemory()
    
    // 执行可能导致内存泄漏的操作
    repeat(1000) {
        service.processLargeData()
    }
    
    System.gc() // 强制垃圾回收
    val finalMemory = Runtime.getRuntime().totalMemory()
    
    assertThat(finalMemory - initialMemory)
        .isLessThan(50 * 1024 * 1024) // 50MB 阈值
}
```

## 📝 测试文档和命名

### 测试命名规范

```kotlin
// 使用描述性的测试名称
@Test
fun `should return user when valid id is provided`() { }

@Test
fun `should throw exception when user not found`() { }

@Test
fun `should update user profile successfully`() { }
```

### 测试文档

```kotlin
/**
 * 性能监控系统测试套件
 * 
 * 测试范围：
 * - 帧率监控功能
 * - 内存监控功能
 * - CPU监控功能
 * - 性能数据收集和存储
 */
@DisplayName("性能监控系统测试")
class PerformanceMonitoringTestSuite {
    // 测试实现
}
```

## 🔄 持续集成最佳实践

### CI/CD 配置

```yaml
test:
  stage: test
  script:
    - ./gradlew test --parallel --max-workers=4
    - ./gradlew jacocoTestReport
  artifacts:
    reports:
      junit: "**/build/test-results/*/TEST-*.xml"
      coverage: "**/build/reports/jacoco/test/jacocoTestReport.xml"
  coverage: '/Total.*?([0-9]{1,3})%/'
```

### 质量门禁

- 所有测试必须通过
- 代码覆盖率 > 85%
- 没有严重的代码质量问题
- 性能测试不能回归

## 📚 参考资源

### 官方文档
- [JUnit 5 用户指南](https://junit.org/junit5/docs/current/user-guide/)
- [Mockk 文档](https://mockk.io/)
- [Kotest 文档](https://kotest.io/)

### 项目相关
- [测试框架优化设计文档](./day10_test_framework_optimization_design.md)
- [测试框架优化需求分析](./day10_test_framework_optimization_requirements.md)

---

**文档版本**: v1.0  
**最后更新**: 2025年7月20日  
**维护者**: Questicle 开发团队
