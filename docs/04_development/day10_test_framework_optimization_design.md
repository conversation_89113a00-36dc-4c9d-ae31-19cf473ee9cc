# 🏗️ Day 10: 测试框架优化详细设计

> **Questicle 项目第二周代码质量提升 - 测试框架优化详细设计文档**

[![Status](https://img.shields.io/badge/Status-Design%20Phase-blue.svg)](./day10_test_framework_optimization_design.md)
[![Phase](https://img.shields.io/badge/Phase-2%20Code%20Quality-blue.svg)](./day10_test_framework_optimization_design.md)
[![Priority](https://img.shields.io/badge/Priority-P1-orange.svg)](./day10_test_framework_optimization_design.md)

## 📋 设计概览

| 项目 | 详情 |
|------|------|
| **设计时间** | 2025年7月20日 |
| **设计阶段** | Phase 2 - 代码质量提升 (P1) |
| **设计范围** | Day 10: 测试框架优化详细设计 |
| **架构模式** | 分层测试架构 + 工厂模式 + 建造者模式 |
| **技术栈** | JUnit 5, Mockk, Testcontainers, Kotest, Jacoco |

## 🏗️ 测试架构设计

### 整体测试架构

```mermaid
graph TB
    subgraph "测试执行层 (Test Execution Layer)"
        A[TestRunner] --> B[ParallelTestExecutor]
        B --> C[TestSuiteManager]
    end
    
    subgraph "测试框架层 (Test Framework Layer)"
        C --> D[UnitTestSuite]
        C --> E[IntegrationTestSuite]
        C --> F[E2ETestSuite]
        C --> G[PerformanceTestSuite]
        C --> H[ExceptionTestSuite]
    end
    
    subgraph "测试工具层 (Test Tools Layer)"
        D --> I[MockDataFactory]
        E --> J[TestContainerManager]
        F --> K[TestEnvironmentManager]
        G --> L[PerformanceTestUtils]
        H --> M[ExceptionTestUtils]
    end
    
    subgraph "测试基础设施层 (Test Infrastructure Layer)"
        I --> N[TestDatabase]
        J --> O[TestContainers]
        K --> P[TestConfiguration]
        L --> Q[MetricsCollector]
        M --> R[MockServices]
    end
```

### 分层职责

#### 测试执行层 (Test Execution Layer)
- **TestRunner**: 测试运行器，支持并行执行
- **ParallelTestExecutor**: 并行测试执行器
- **TestSuiteManager**: 测试套件管理器
- **职责**: 控制测试执行流程和并行策略

#### 测试框架层 (Test Framework Layer)
- **各种TestSuite**: 不同类型的测试套件
- **职责**: 组织和管理不同类型的测试
- **模式**: 策略模式 + 模板方法模式

#### 测试工具层 (Test Tools Layer)
- **各种TestUtils**: 测试工具类
- **MockDataFactory**: 测试数据工厂
- **职责**: 提供测试辅助工具和数据
- **模式**: 工厂模式 + 建造者模式

#### 测试基础设施层 (Test Infrastructure Layer)
- **TestDatabase**: 测试数据库
- **TestContainers**: 容器化测试环境
- **职责**: 提供测试基础设施支持

## 📊 核心组件设计

### 1. ParallelTestExecutor 设计

```kotlin
@Component
class ParallelTestExecutor {
    
    private val executorService = ForkJoinPool.commonPool()
    private val testResultCollector = TestResultCollector()
    
    fun executeTestSuites(
        testSuites: List<TestSuite>,
        config: ParallelTestConfig
    ): TestExecutionResult {
        
        val futures = testSuites.map { testSuite ->
            executorService.submit {
                executeTestSuite(testSuite, config)
            }
        }
        
        val results = futures.map { it.get() }
        return testResultCollector.aggregate(results)
    }
    
    private fun executeTestSuite(
        testSuite: TestSuite,
        config: ParallelTestConfig
    ): TestSuiteResult {
        // 执行测试套件
        return testSuite.execute(config)
    }
}
```

#### 并行执行策略

```kotlin
data class ParallelTestConfig(
    val maxParallelism: Int = Runtime.getRuntime().availableProcessors(),
    val testTimeout: Duration = Duration.ofMinutes(5),
    val enableTestSharding: Boolean = true,
    val shardSize: Int = 10,
    val isolationLevel: TestIsolationLevel = TestIsolationLevel.METHOD
)

enum class TestIsolationLevel {
    METHOD,     // 方法级隔离
    CLASS,      // 类级隔离
    SUITE       // 套件级隔离
}
```

### 2. MockDataFactory 设计

```kotlin
@Singleton
class MockDataFactory {
    
    private val faker = Faker()
    private val objectMapper = ObjectMapper()
    
    // 性能监控测试数据
    fun createPerformanceSnapshot(
        customizer: PerformanceSnapshotBuilder.() -> Unit = {}
    ): PerformanceSnapshot {
        return PerformanceSnapshotBuilder()
            .apply(customizer)
            .build()
    }
    
    // 异常处理测试数据
    fun createQuesticleException(
        type: ExceptionType = ExceptionType.BUSINESS_LOGIC,
        customizer: ExceptionBuilder.() -> Unit = {}
    ): QuesticleException {
        return ExceptionBuilder(type)
            .apply(customizer)
            .build()
    }
    
    // 用户数据
    fun createUser(
        customizer: UserBuilder.() -> Unit = {}
    ): User {
        return UserBuilder()
            .withId(faker.random.nextUuid())
            .withName(faker.name.fullName())
            .withEmail(faker.internet.emailAddress())
            .apply(customizer)
            .build()
    }
    
    // 游戏数据
    fun createGameSession(
        customizer: GameSessionBuilder.() -> Unit = {}
    ): GameSession {
        return GameSessionBuilder()
            .withId(faker.random.nextUuid())
            .withStartTime(Instant.now())
            .apply(customizer)
            .build()
    }
}
```

#### 建造者模式实现

```kotlin
class PerformanceSnapshotBuilder {
    private var timestamp: Long = System.currentTimeMillis()
    private var frameRate: FrameRateMetric? = null
    private var memory: MemoryMetric? = null
    private var cpu: CPUMetric? = null
    
    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    
    fun withFrameRate(fps: Int, avgFrameTime: Double = 16.67) = apply {
        this.frameRate = FrameRateMetric(
            timestamp = timestamp,
            fps = fps,
            avgFrameTime = avgFrameTime,
            droppedFrameRate = if (fps < 55) 0.1 else 0.0,
            isSmooth = fps >= 55
        )
    }
    
    fun withMemory(heapUtilization: Double) = apply {
        this.memory = MemoryMetric(
            timestamp = timestamp,
            usedHeap = (heapUtilization * 1024 * 1024 * 100).toLong(),
            totalHeap = 1024 * 1024 * 100,
            maxHeap = 1024 * 1024 * 200,
            heapUtilization = heapUtilization,
            usedMemory = (heapUtilization * 1024 * 1024 * 500).toLong(),
            totalMemory = 1024 * 1024 * 1000,
            availableMemory = 1024 * 1024 * (1000 - 500 * heapUtilization).toLong(),
            memoryPressure = heapUtilization > 0.9,
            gcCount = 10,
            gcRate = if (heapUtilization > 0.8) 5.0 else 2.0
        )
    }
    
    fun withCpu(cpuUsage: Double) = apply {
        this.cpu = CPUMetric(
            timestamp = timestamp,
            cpuUsage = cpuUsage,
            threadCount = 20,
            loadAverage = cpuUsage / 100.0 * 4.0,
            isHighUsage = cpuUsage > 80.0
        )
    }
    
    fun build(): PerformanceSnapshot {
        val overall = calculateOverallPerformance()
        return PerformanceSnapshot(
            timestamp = timestamp,
            frameRate = frameRate,
            memory = memory,
            cpu = cpu,
            network = null,
            overall = overall
        )
    }
    
    private fun calculateOverallPerformance(): OverallPerformance {
        var score = 100.0
        val issues = mutableListOf<String>()
        
        frameRate?.let { if (it.fps < 30) { score -= 30; issues.add("Low FPS") } }
        memory?.let { if (it.heapUtilization > 0.9) { score -= 25; issues.add("High Memory") } }
        cpu?.let { if (it.cpuUsage > 80) { score -= 20; issues.add("High CPU") } }
        
        return OverallPerformance(
            score = score.coerceAtLeast(0.0),
            grade = when {
                score >= 90 -> PerformanceGrade.EXCELLENT
                score >= 75 -> PerformanceGrade.GOOD
                score >= 60 -> PerformanceGrade.FAIR
                score >= 40 -> PerformanceGrade.POOR
                else -> PerformanceGrade.CRITICAL
            },
            issues = issues
        )
    }
}
```

### 3. TestContainerManager 设计

```kotlin
@Singleton
class TestContainerManager {
    
    private val containers = mutableMapOf<String, GenericContainer<*>>()
    
    fun startSupabaseContainer(): SupabaseTestContainer {
        return containers.computeIfAbsent("supabase") {
            SupabaseTestContainer().apply {
                start()
            }
        } as SupabaseTestContainer
    }
    
    fun startRedisContainer(): RedisTestContainer {
        return containers.computeIfAbsent("redis") {
            RedisTestContainer().apply {
                start()
            }
        } as RedisTestContainer
    }
    
    fun stopAllContainers() {
        containers.values.forEach { it.stop() }
        containers.clear()
    }
}

class SupabaseTestContainer : GenericContainer<SupabaseTestContainer>("supabase/postgres:latest") {
    
    init {
        withExposedPorts(5432)
        withEnv("POSTGRES_DB", "test_db")
        withEnv("POSTGRES_USER", "test_user")
        withEnv("POSTGRES_PASSWORD", "test_password")
        waitingFor(Wait.forListeningPort())
    }
    
    fun getJdbcUrl(): String {
        return "jdbc:postgresql://${host}:${getMappedPort(5432)}/test_db"
    }
    
    fun getUsername(): String = "test_user"
    fun getPassword(): String = "test_password"
}
```

## 🧪 测试套件设计

### 1. PerformanceTestSuite 设计

```kotlin
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PerformanceMonitoringTestSuite {
    
    @Inject
    lateinit var performanceMonitor: PerformanceMonitor
    
    @Inject
    lateinit var mockDataFactory: MockDataFactory
    
    @Inject
    lateinit var performanceRepository: PerformanceRepository
    
    @BeforeAll
    fun setup() {
        // 初始化测试环境
    }
    
    @Nested
    @DisplayName("帧率监控测试")
    inner class FrameRateMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控帧率")
        fun `should monitor frame rate correctly`() = runTest {
            // Given
            val config = AdvancedPerformanceConfig(
                enableFrameRateMonitoring = true,
                frameRateSamplingInterval = 100L
            )
            
            // When
            performanceMonitor.startAdvancedMonitoring(config)
            delay(500) // 等待采样
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            assertThat(snapshot).isNotNull()
            assertThat(snapshot!!.frameRate).isNotNull()
            assertThat(snapshot.frameRate!!.fps).isGreaterThan(0)
        }
        
        @ParameterizedTest
        @ValueSource(ints = [30, 45, 60, 90, 120])
        @DisplayName("应该正确检测不同帧率")
        fun `should detect different frame rates`(targetFps: Int) = runTest {
            // 测试不同帧率的检测
        }
    }
    
    @Nested
    @DisplayName("内存监控测试")
    inner class MemoryMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控内存使用")
        fun `should monitor memory usage correctly`() = runTest {
            // 内存监控测试
        }
        
        @Test
        @DisplayName("应该检测内存压力")
        fun `should detect memory pressure`() = runTest {
            // 内存压力检测测试
        }
    }
    
    @Nested
    @DisplayName("CPU监控测试")
    inner class CpuMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控CPU使用率")
        fun `should monitor cpu usage correctly`() = runTest {
            // CPU监控测试
        }
    }
    
    @Nested
    @DisplayName("数据同步测试")
    inner class DataSyncTests {
        
        @Test
        @DisplayName("应该正确同步性能数据到Supabase")
        fun `should sync performance data to supabase correctly`() = runTest {
            // 数据同步测试
        }
    }
}
```

### 2. ExceptionHandlingTestSuite 设计

```kotlin
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExceptionHandlingTestSuite {
    
    @Inject
    lateinit var exceptionHandler: QuesticleExceptionHandler
    
    @Inject
    lateinit var recoveryManager: ExceptionRecoveryManager
    
    @Inject
    lateinit var crashReportingManager: CrashReportingManager
    
    @Inject
    lateinit var mockDataFactory: MockDataFactory
    
    @Nested
    @DisplayName("异常处理测试")
    inner class ExceptionHandlingTests {
        
        @Test
        @DisplayName("应该正确处理业务逻辑异常")
        fun `should handle business logic exceptions correctly`() = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException(
                type = ExceptionType.BUSINESS_LOGIC
            ) {
                withMessage("Test business logic error")
                withErrorCode("BUSINESS_ERROR")
                withSeverity(ErrorSeverity.MEDIUM)
            }
            
            // When
            val result = exceptionHandler.handleException(exception)
            
            // Then
            assertThat(result.action).isEqualTo(ExceptionAction.LOG_AND_CONTINUE)
            assertThat(result.shouldReport).isTrue()
        }
        
        @ParameterizedTest
        @EnumSource(ErrorSeverity::class)
        @DisplayName("应该根据严重程度正确处理异常")
        fun `should handle exceptions based on severity`(severity: ErrorSeverity) = runTest {
            // 测试不同严重程度的异常处理
        }
    }
    
    @Nested
    @DisplayName("异常恢复测试")
    inner class ExceptionRecoveryTests {
        
        @Test
        @DisplayName("应该正确恢复网络异常")
        fun `should recover from network exceptions correctly`() = runTest {
            // 网络异常恢复测试
        }
        
        @Test
        @DisplayName("应该正确恢复数据库异常")
        fun `should recover from database exceptions correctly`() = runTest {
            // 数据库异常恢复测试
        }
    }
    
    @Nested
    @DisplayName("崩溃报告测试")
    inner class CrashReportingTests {
        
        @Test
        @DisplayName("应该正确报告崩溃到Supabase")
        fun `should report crashes to supabase correctly`() = runTest {
            // 崩溃报告测试
        }
    }
}
```

## ⚙️ Gradle 配置优化

### 测试配置优化

```kotlin
// build.gradle.kts
tasks.test {
    useJUnitPlatform()
    
    // 并行执行配置
    maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).coerceAtLeast(1)
    
    // JVM 参数优化
    jvmArgs = listOf(
        "-Xmx2g",
        "-XX:+UseG1GC",
        "-XX:MaxGCPauseMillis=100",
        "-Djunit.jupiter.execution.parallel.enabled=true",
        "-Djunit.jupiter.execution.parallel.mode.default=concurrent"
    )
    
    // 测试分类
    systemProperty("junit.jupiter.testinstance.lifecycle.default", "per_class")
    
    // 测试报告
    reports {
        junitXml.required.set(true)
        html.required.set(true)
    }
    
    // 测试过滤
    filter {
        includeTestsMatching("*Test")
        includeTestsMatching("*Tests")
        includeTestsMatching("*TestSuite")
    }
}

// 测试套件配置
testing {
    suites {
        val test by getting(JvmTestSuite::class) {
            useJUnitJupiter()
        }
        
        val integrationTest by registering(JvmTestSuite::class) {
            useJUnitJupiter()
            dependencies {
                implementation(project())
                implementation("org.testcontainers:junit-jupiter")
                implementation("org.testcontainers:postgresql")
            }
            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
        
        val performanceTest by registering(JvmTestSuite::class) {
            useJUnitJupiter()
            dependencies {
                implementation(project())
                implementation("org.junit.jupiter:junit-jupiter-params")
            }
        }
    }
}
```

### 覆盖率配置

```kotlin
// Jacoco 配置
jacoco {
    toolVersion = "0.8.8"
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)
    
    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }
    
    executionData.setFrom(fileTree(layout.buildDirectory.dir("jacoco")).include("**/*.exec"))
    
    finalizedBy(tasks.jacocoTestCoverageVerification)
}

tasks.jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = "0.85".toBigDecimal()
            }
        }
        
        rule {
            element = "CLASS"
            limit {
                counter = "BRANCH"
                value = "COVEREDRATIO"
                minimum = "0.80".toBigDecimal()
            }
            excludes = listOf(
                "*.test.*",
                "*.*Test*",
                "*.*Mock*"
            )
        }
    }
}
```

## 📊 测试报告设计

### 自定义测试报告

```kotlin
class CustomTestReportGenerator {
    
    fun generateReport(testResults: TestExecutionResult): TestReport {
        return TestReport(
            summary = generateSummary(testResults),
            coverageReport = generateCoverageReport(),
            performanceMetrics = generatePerformanceMetrics(testResults),
            failureAnalysis = generateFailureAnalysis(testResults),
            recommendations = generateRecommendations(testResults)
        )
    }
    
    private fun generateSummary(results: TestExecutionResult): TestSummary {
        return TestSummary(
            totalTests = results.totalTests,
            passedTests = results.passedTests,
            failedTests = results.failedTests,
            skippedTests = results.skippedTests,
            executionTime = results.executionTime,
            successRate = results.successRate
        )
    }
    
    private fun generatePerformanceMetrics(results: TestExecutionResult): TestPerformanceMetrics {
        return TestPerformanceMetrics(
            averageTestTime = results.averageTestTime,
            slowestTests = results.slowestTests,
            parallelEfficiency = results.parallelEfficiency,
            memoryUsage = results.memoryUsage
        )
    }
}
```

---

**文档版本**: v1.0  
**设计完成时间**: 2025年7月20日  
**设计状态**: ✅ 完成  
**下一步**: 编码实现阶段
