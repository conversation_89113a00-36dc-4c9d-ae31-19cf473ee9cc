# Tetris Engine Component Usage Guide

## Overview

This guide provides examples and best practices for using the refactored Tetris Engine components. It covers how to extend and customize each component, common usage patterns, and troubleshooting tips.

## Table of Contents

1. [TetrisEngineImpl](#tetrisengineimpl)
2. [TetrisGameLogicProcessor](#tetrisgamelogicprocessor)
3. [TetrisCollisionDetector](#tetriscollisiondetector)
4. [TetrisStatisticsCalculator](#tetrisstatisticscalculator)
5. [TetrisPerformanceManager](#tetrisperformancemanager)
6. [TetrisCacheManager](#tetriscachemanager)
7. [TetrisObjectPoolManager](#tetrisobjectpoolmanager)
8. [TetrisMemoryMonitor](#tetrismemorymonitor)
9. [BitPackedTetrisBoard](#bitpackedtetrisboard)
10. [Troubleshooting](#troubleshooting)

## TetrisEngineImpl

The `TetrisEngineImpl` class is the main entry point for the Tetris Engine. It orchestrates the interactions between all other components.### Usage
 Example

```kotlin
// Create dependencies
val boardFactory = TetrisBoardFactory()
val boardAdapter = TetrisBoardAdapter(boardFactory)
val collisionDetector = TetrisCollisionDetectorImpl(boardAdapter)
val statisticsCalculator = TetrisStatisticsCalculatorImpl()
val objectPoolManager = TetrisObjectPoolManager()
val memoryMonitor = TetrisMemoryMonitor()
val gameLogicProcessor = TetrisGameLogicProcessorImpl(
    collisionDetector,
    statisticsCalculator,
    objectPoolManager
)
val gameStateValidator = GameStateValidatorImpl()
val performanceManager = TetrisPerformanceManagerImpl()
val cacheManager = TetrisCacheManagerImpl()

// Create the engine
val tetrisEngine = TetrisEngineImpl(
    gameRepository = gameRepository,
    tetrisGameStateRepository = tetrisGameStateRepository,
    playerSessionManager = playerSessionManager,
    performanceMonitor = performanceMonitor,
    gameLogicProcessor = gameLogicProcessor,
    collisionDetector = collisionDetector,
    statisticsCalculator = statisticsCalculator,
    performanceManager = performanceManager,
    cacheManager = cacheManager,
    gameStateValidator = gameStateValidator,
    objectPoolManager = objectPoolManager,
    memoryMonitor = memoryMonitor,
    defaultDispatcher = Dispatchers.Default
)

// Initialize the game
val initResult = tetrisEngine.initializeGame("player1")
val gameState = (initResult as Result.Success).data

// Use the engine
tetrisEngine.movePiece(Direction.RIGHT, gameState)
tetrisEngine.rotatePiece(true, gameState)
tetrisEngine.dropPiece(false, gameState)
```

### Best Practices

1. **Use Dependency Injection**: Inject all dependencies into the `TetrisEngineImpl` constructor. This makes it easier to test and extend the engine.

2. **Maintain API Compatibility**: When extending the engine, maintain compatibility with the `TetrisEngine` interface to ensure interoperability with existing code.

3. **Handle Errors Properly**: Always check the result of engine operations and handle errors appropriately.

4. **Use Immutable Game State**: Use the `ImmutableGameState` class for game state to ensure thread safety and efficient memory usage.

5. **Monitor Performance**: Use the performance monitoring tools to identify bottlenecks and optimize performance.

## TetrisGameLogicProcessor

The `TetrisGameLogicProcessor` handles core game mechanics and rule processing.

### Usage Example

```kotlin
// Create the game logic processor
val gameLogicProcessor = TetrisGameLogicProcessorImpl(
    collisionDetector,
    statisticsCalculator,
    objectPoolManager
)

// Process a move
val piece = TetrisPiece(TetrisPieceType.I, 3, 0, 0)
val board = BitPackedTetrisBoard.empty(10, 20)
val moveResult = gameLogicProcessor.processMove(piece, Direction.RIGHT, board)

// Handle the result
when (moveResult) {
    is GameMoveResult.Success -> {
        val newPiece = moveResult.newPiece
        // Update game state with new piece
    }
    is GameMoveResult.Blocked -> {
        // Handle blocked move
    }
    is GameMoveResult.Landed -> {
        // Handle landed piece
    }
}

// Process a rotation
val rotationResult = gameLogicProcessor.processRotation(piece, true, board)

// Handle the result
when (rotationResult) {
    is GameRotationResult.Success -> {
        val rotatedPiece = rotationResult.rotatedPiece
        // Update game state with rotated piece
    }
    is GameRotationResult.WallKick -> {
        val kickedPiece = rotationResult.kickedPiece
        // Update game state with kicked piece
    }
    is GameRotationResult.Blocked -> {
        // Handle blocked rotation
    }
}

// Process line clear
val lineClearResult = gameLogicProcessor.processLineClear(board)

// Handle the result
when (lineClearResult) {
    is LineClearResult.Success -> {
        val clearedBoard = lineClearResult.clearedBoard
        val linesCleared = lineClearResult.linesCleared
        val scoreIncrease = lineClearResult.scoreIncrease
        // Update game state with cleared board and score
    }
    is LineClearResult.NoLines -> {
        // No lines cleared
    }
}
```

### Best Practices

1. **Separate Game Logic**: Keep game logic separate from UI and other concerns. The game logic processor should only handle game mechanics.

2. **Use Result Types**: Use result types like `GameMoveResult` and `GameRotationResult` to communicate the outcome of operations.

3. **Validate Game State**: Always validate the game state before processing operations to ensure consistency.

4. **Handle Edge Cases**: Handle edge cases like wall kicks, T-spins, and perfect clears correctly.

5. **Optimize Hot Paths**: Optimize hot paths like collision detection and line clearing for performance.

## TetrisCollisionDetector

The `TetrisCollisionDetector` handles collision detection and validation.

### Usage Example

```kotlin
// Create the collision detector
val collisionDetector = TetrisCollisionDetectorImpl(boardAdapter)

// Check if a position is valid
val piece = TetrisPiece(TetrisPieceType.I, 3, 0, 0)
val board = BitPackedTetrisBoard.empty(10, 20)
val isValid = collisionDetector.isValidPosition(piece, board)

// Find landing position
val landingPiece = collisionDetector.findLandingPosition(piece, board)

// Detect collisions
val collisionInfo = collisionDetector.detectCollisions(piece, board)
if (collisionInfo.hasCollision) {
    collisionInfo.collisionPoints.forEach { point ->
        when (point.type) {
            CollisionType.BOUNDARY -> {
                // Handle boundary collision
            }
            CollisionType.BLOCK -> {
                // Handle block collision
            }
        }
    }
}
```

### Best Practices

1. **Use Spatial Optimization**: Use spatial optimization techniques like early boundary checks and spatial partitioning for efficient collision detection.

2. **Cache Collision Results**: Cache collision results for repeated queries to improve performance.

3. **Use Binary Search**: Use binary search for finding landing positions to minimize collision checks.

4. **Optimize for Common Cases**: Optimize for common cases like piece movement and rotation.

5. **Use Bit-Packed Board**: Use the `BitPackedTetrisBoard` for efficient board representation and collision detection.

## TetrisStatisticsCalculator

The `TetrisStatisticsCalculator` handles score calculation and game statistics.

### Usage Example

```kotlin
// Create the statistics calculator
val statisticsCalculator = TetrisStatisticsCalculatorImpl()

// Calculate line clear score
val score = statisticsCalculator.calculateLineClearScore(
    linesCleared = 4,
    level = 5,
    combo = 2
)

// Calculate level
val level = statisticsCalculator.calculateLevel(lines = 50)

// Calculate drop interval
val dropInterval = statisticsCalculator.calculateDropInterval(level = 5)

// Calculate combo multiplier
val comboMultiplier = statisticsCalculator.calculateComboMultiplier(combo = 3)

// Update game statistics
val updatedStats = statisticsCalculator.updateGameStatistics(
    statistics = gameState.statistics,
    linesCleared = 4,
    pieceType = TetrisPieceType.I,
    isTSpin = false,
    isPerfectClear = false,
    isBackToBack = true
)
```

### Best Practices

1. **Support Different Scoring Rules**: Support different scoring rules like classic, modern, and custom.

2. **Handle Special Cases**: Handle special cases like T-spins, perfect clears, and back-to-back clears correctly.

3. **Use Immutable Statistics**: Use the `ImmutableGameStatistics` class for efficient statistics updates.

4. **Cache Score Calculations**: Cache score calculations for repeated queries to improve performance.

5. **Provide Detailed Statistics**: Provide detailed statistics for analysis and display.

## TetrisPerformanceManager

The `TetrisPerformanceManager` handles performance optimization and monitoring.

### Usage Example

```kotlin
// Create the performance manager
val performanceManager = TetrisPerformanceManagerImpl(cacheManager)

// Optimize for device
val config = performanceManager.optimizeForDevice(
    DeviceSpecs(
        cpuCores = 4,
        memoryMB = 2048,
        isLowPowerMode = false
    )
)

// Track operation
performanceManager.trackOperation("movePiece", durationNanos = 1_000_000)

// Record memory usage
performanceManager.recordMemoryUsage("TetrisEngine", memoryUsageMB = 10)

// Get performance metrics
val metrics = performanceManager.getPerformanceMetrics()

// Check if optimization should be used
val shouldUseOptimization = performanceManager.shouldUseOptimization(
    OptimizationType.CACHE_COLLISION_RESULTS
)
```

### Best Practices

1. **Optimize for Device**: Optimize performance settings based on device capabilities.

2. **Track Critical Operations**: Track execution time of critical operations to identify bottlenecks.

3. **Monitor Memory Usage**: Monitor memory usage to identify memory leaks and excessive allocations.

4. **Adjust Settings Dynamically**: Adjust performance settings dynamically based on performance metrics.

5. **Use Performance Profiles**: Use different performance profiles for different devices and scenarios.

## TetrisCacheManager

The `TetrisCacheManager` handles caching for performance optimization.

### Usage Example

```kotlin
// Create the cache manager
val cacheManager = TetrisCacheManagerImpl()

// Configure for performance
cacheManager.configureForPerformance(
    PerformanceConfig.HIGH_PERFORMANCE
)

// Get cached value
val cachedValue = cacheManager.getCached(
    key = CacheKey("collision", piece.hashCode(), board.hashCode())
) {
    // Compute value if not in cache
    collisionDetector.isValidPosition(piece, board)
}

// Invalidate cache
cacheManager.invalidateCache("collision:*")

// Get cache statistics
val statistics = cacheManager.getCacheStatistics()

// Clear all caches
cacheManager.clearAllCaches()
```

### Best Practices

1. **Use Multi-Level Caching**: Use multi-level caching for different types of data and access patterns.

2. **Use Bloom Filters**: Use Bloom filters for fast negative lookups.

3. **Configure Cache Sizes**: Configure cache sizes based on device capabilities and usage patterns.

4. **Invalidate Caches**: Invalidate caches when the underlying data changes.

5. **Monitor Cache Performance**: Monitor cache hit rates and adjust cache sizes accordingly.

## TetrisObjectPoolManager

The `TetrisObjectPoolManager` handles object pooling for memory optimization.

### Usage Example

```kotlin
// Create the object pool manager
val objectPoolManager = TetrisObjectPoolManager()

// Acquire a piece from the pool
val piece = objectPoolManager.acquirePiece(
    type = TetrisPieceType.I,
    x = 3,
    y = 0,
    rotation = 0
)

// Use the piece
val moveResult = objectPoolManager.acquireMoveResultSuccess(piece)

// Release objects back to the pool
objectPoolManager.releaseMoveResultSuccess(moveResult)
objectPoolManager.releasePiece(piece)

// Get memory statistics
val statistics = objectPoolManager.getMemoryStatistics()

// Clear all pools
objectPoolManager.clearAllPools()
```

### Best Practices

1. **Always Release Objects**: Always release objects back to the pool when done with them.

2. **Use Object Pools for Hot Paths**: Use object pools for frequently created objects in hot paths.

3. **Configure Pool Sizes**: Configure pool sizes based on device capabilities and usage patterns.

4. **Monitor Pool Efficiency**: Monitor pool efficiency and adjust pool sizes accordingly.

5. **Reset Objects**: Reset objects to their initial state when releasing them back to the pool.

## TetrisMemoryMonitor

The `TetrisMemoryMonitor` handles memory usage tracking and optimization.

### Usage Example

```kotlin
// Create the memory monitor
val memoryMonitor = TetrisMemoryMonitor()

// Record allocation
memoryMonitor.recordAllocation("TetrisPiece", sizeBytes = 64)

// Record release
memoryMonitor.recordRelease("TetrisPiece")

// Take snapshot
memoryMonitor.takeSnapshot("After initialization")

// Record GC event
memoryMonitor.recordGCEvent(durationMs = 100, collectedBytes = 1024 * 1024)

// Get memory statistics
val statistics = memoryMonitor.getMemoryStatistics()

// Get memory snapshots
val snapshots = memoryMonitor.getMemorySnapshots()

// Get GC events
val gcEvents = memoryMonitor.getGCEvents()

// Reset
memoryMonitor.reset()
```

### Best Practices

1. **Track Allocations and Releases**: Track allocations and releases to identify memory leaks.

2. **Take Memory Snapshots**: Take memory snapshots at key points to track memory usage over time.

3. **Monitor GC Events**: Monitor garbage collection events to identify GC pressure.

4. **Analyze Memory Usage**: Analyze memory usage to identify optimization opportunities.

5. **Reset Periodically**: Reset the memory monitor periodically to avoid memory leaks in the monitor itself.

## BitPackedTetrisBoard

The `BitPackedTetrisBoard` provides an optimized representation of the Tetris game board.

### Usage Example

```kotlin
// Create an empty board
val board = BitPackedTetrisBoard.empty(10, 20)

// Set cell types
val updatedBoard = board
    .setCellType(1, 1, TetrisCellType.I)
    .setCellType(2, 2, TetrisCellType.J)
    .setCellType(3, 3, TetrisCellType.L)

// Check if a row is filled
val isRowFilled = updatedBoard.isRowFilled(1)

// Check if a row is empty
val isRowEmpty = updatedBoard.isRowEmpty(0)

// Clear a row
val clearedBoard = updatedBoard.clearRow(1)

// Clear multiple rows
val multiClearedBoard = updatedBoard.clearRows(listOf(1, 2))

// Place a piece
val piece = TetrisPiece(TetrisPieceType.T, 3, 0, 0)
val boardWithPiece = updatedBoard.placePiece(piece)

// Check if a piece can be placed
val canPlace = boardWithPiece.canPlacePiece(piece)

// Clear completed lines
val (newBoard, clearedLines) = boardWithPiece.clearCompletedLines()
```

### Best Practices

1. **Use Bit-Packed Representation**: Use the bit-packed representation for efficient memory usage.

2. **Avoid Unnecessary Copies**: Avoid creating unnecessary copies of the board.

3. **Use Immutable Operations**: Use immutable operations to ensure thread safety.

4. **Optimize Board Operations**: Optimize board operations like line clearing and piece placement.

5. **Use Board Adapter**: Use the `TetrisBoardAdapter` to convert between board representations.

## Troubleshooting

### Common Issues and Solutions

#### Issue: High Memory Usage

**Symptoms:**
- Increasing memory usage over time
- Frequent garbage collection pauses
- Out of memory errors

**Solutions:**
1. Check for object leaks using `memoryMonitor.getMemoryStatistics()`
2. Ensure objects are being released back to the pool
3. Reduce cache and pool sizes
4. Use the `ImmutableGameState` and `BitPackedTetrisBoard` classes
5. Take memory snapshots before and after operations to identify memory growth

#### Issue: Slow Performance

**Symptoms:**
- Laggy gameplay
- High CPU usage
- Slow response to user input

**Solutions:**
1. Identify hot path operations using `componentPerformanceMonitor.getHotPathOperations()`
2. Check cache hit rates using `componentCache.getCacheStatistics()`
3. Minimize component interactions in hot paths
4. Use optimized communication methods
5. Configure the performance manager for your device

#### Issue: Incorrect Game Behavior

**Symptoms:**
- Pieces not moving or rotating correctly
- Line clears not working properly
- Score not calculated correctly

**Solutions:**
1. Validate game state using `gameStateValidator.validateGameState()`
2. Check collision detection using `collisionDetector.detectCollisions()`
3. Verify game logic processing using `gameLogicProcessor` methods
4. Test with simple board configurations
5. Use the `TetrisEngineRegressionTest` to verify behavior

#### Issue: Integration Problems

**Symptoms:**
- Errors when integrating with other components
- Incompatible interfaces
- Missing dependencies

**Solutions:**
1. Ensure all required dependencies are provided
2. Check interface compatibility
3. Use the `TetrisEngineApiCompatibilityTest` to verify API compatibility
4. Follow the migration guide in the documentation
5. Use dependency injection to manage component dependencies

### Debugging Tips

1. **Use Memory Snapshots**: Take memory snapshots before and after operations to track memory usage.

2. **Monitor Performance**: Use the `ComponentPerformanceMonitor` to identify bottlenecks.

3. **Check Cache Hit Rates**: Monitor cache hit rates to ensure caching is effective.

4. **Validate Game State**: Use the `GameStateValidator` to ensure game state consistency.

5. **Run Regression Tests**: Use the `TetrisEngineRegressionTest` to verify behavior.

## Conclusion

By following the best practices and examples in this guide, you can effectively use and extend the Tetris Engine components. Remember to monitor performance and memory usage regularly, and adjust configuration options as needed.

For more information, refer to the [Tetris Engine API Documentation](../../05_api/specifications/tetris-engine-api.md) and [Tetris Engine Refactored Architecture](../../02_architecture/modules/tetris-engine-refactored.md).