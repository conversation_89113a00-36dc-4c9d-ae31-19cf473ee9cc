
# 🚀 Questicle 新成员入zhí指南 (Onboarding Guide)

> **欢迎加入Questicle！** 这份指南将帮助你在 **1小时内** 完成从环境搭建到提交第一个代码贡献的全过程。

[![Guide Status](https://img.shields.io/badge/Guide%20Status-Polished-brightgreen.svg)](https://github.com/dragon-entropy/questicle)
[![Last Updated](https://img.shields.io/badge/Last%20Updated-2025--01--28-green.svg)](https://github.com/dragon-entropy/questicle)

---

## 🎯 新成员画像：选择你的路径

我们为你设计了三条路径，请根据你的时间选择：

| 时间投入 | 你的目标 | 你将获得 ✅ |
|:---|:---|:---|
| **5分钟** | **我只想让项目跑起来！** | 成功在模拟器或设备上运行Questicle应用。 |
| **30分钟** | **我想了解代码并做点小修改。** | 理解核心代码结构，并完成一次本地的UI或文本修改。 |
| **1小时** | **我准备好贡献代码了！** | 领取一个真实的入门任务，并提交你的第一个Pull Request。 |

---

## 📍 路径一：5分钟 - 让项目跑起来

> **目标**：克隆项目并在本地成功运行。

### 1. 克隆代码库 (1分钟)
```bash
git clone https://github.com/dragon-entropy/questicle.git
cd questicle
```

### 2. 构建项目 (3分钟)
用Android Studio打开项目。它会自动使用Gradle进行构建。首次构建会下载依赖，可能需要几分钟。
你也可以在终端运行：
```bash
# 这会构建用于快速测试的demo-debug版本
./gradlew assembleDemoDebug
```

### 3. 运行应用 (1分钟)
在Android Studio中，选择一个模拟器或连接你的Android设备，然后点击 "Run 'app'" (▶️)。
或者在终端运行：
```bash
# 这会将应用安装到连接的设备/模拟器上
./gradlew installDemoDebug
```

**✅ 成就解锁**：你已经在本地成功运行Questicle！现在可以玩一下游戏，直观地感受这个项目。

---

## 📍 路径二：30分钟 - 理解代码并做点小修改

> **目标**：了解项目核心结构，并进行一次安全的本地修改。

### 1. 我加入了一个什么项目？(5分钟)

Questicle是一个使用最前沿技术构建的现代化俄罗斯方块游戏。

*   **项目愿景**：不仅为玩家提供乐趣，更作为团队学习和实践现代Android开发的“活样板”。
*   **核心技术栈**：
    | 技术领域 | 我们为什么选择它？ |
    |:---|:---|
    | `Kotlin` + `Coroutine` | 官方推荐，提供强大而简洁的异步编程能力。 |
    | `Jetpack Compose` | 用于构建原生UI的现代化声明式框架，开发效率高。 |
    | `Clean Architecture` | 分离关注点，使代码更清晰、可测试、易维护。 |
    | `Hilt` | 官方推荐的依赖注入框架，简化依赖管理。 |
    | `JUnit5` + `Mockk` | 强大且流行的测试组合，保证代码质量。 |

### 2. 代码的核心在哪？(10分钟)

为了快速找到方向，请熟悉以下目录结构：
```
questicle/
├── 📁 feature/               # ⭐️ 功能模块 (你的主要工作区)
│   ├── 📁 tetris/             # 俄罗斯方块游戏的核心逻辑和UI
│   └── 📁 user/               # 用户资料、设置等
├── 📁 core/                   # 🔩 核心模块 (提供公共能力)
│   ├── 📁 designsystem/       # 设计系统 (颜色, 字体, 公共组件)
│   ├── 📁 domain/             # 领域模型和业务规则
│   └── 📁 data/               # 数据层 (Repository, 数据源)
└── 📁 app/                    # 🚀 应用主模块 (组装所有功能)
```
**建议**：先从 `feature/tetris/impl/src` 开始探索，这里是游戏的主要实现。

### 3. 来做个小修改！(15分钟)
这是一个安全的、不会破坏任何东西的练习。

1.  **找到目标文件**：
    *   `feature/tetris/impl/src/.../tetris/impl/ui/TetrisGameScreen.kt`
    *   或者更简单的: `feature/home/<USER>/src/.../home/<USER>/ui/HomeScreen.kt`
2.  **找到一个文本**：使用搜索功能 (Cmd/Ctrl + F) 查找 `"开始游戏"`。
3.  **修改它**：把它改成 `"开始挑战"`。
4.  **重新运行应用**：再次点击 "Run 'app'" (▶️)。

**✅ 成就解锁**：你已经成功修改了代码并立即看到了效果！这证明你的开发环境完全没有问题。

---

## 📍 路径三：1小时 - 提交你的第一个贡献

> **目标**：走完完整的代码贡献流程，提交你的第一个Pull Request。

### 1. 领取你的第一个任务 (5分钟)

我们为新成员准备了专门的入门任务。
*   **前往任务墙**: [**点击这里查看 "Good First Issues"**](https://github.com/dragon-entropy/questicle/issues?q=is%3Aissue+is%3Aopen+label%3A%22good+first+issue%22)
*   **选择一个**: 找一个你感兴趣的，通常是修改文档、修复小Bug或优化UI文本。
*   **声明任务**: 在Issue下留言，例如“I'll take this one!”，并@你的导师（如果没有，请@项目负责人）。

### 2. 遵循Git工作流程 (15分钟)

我们使用功能分支工作流。详情请参考 [**Git工作流程指南**](../development/workflows/git-workflow.md)。

```bash
# 1. 确保你的main分支是最新
git checkout main
git pull origin main

# 2. 从main分支创建你的功能分支 (分支名关联Issue编号)
git checkout -b feature/issue-123-fix-button-text

# 3. 开始编码...
# (在这里完成你的代码修改)

# 4. 提交你的修改 (遵循提交信息规范)
git add .
git commit -m "feat(ui): Update start button text to 'Start Challenge'

Fixes #123"

# 5. 推送到远程仓库
git push -u origin feature/issue-123-fix-button-text
```

### 3. 创建你的Pull Request (PR) (10分钟)

1.  在`git push`之后，终端会给你一个链接，点击它可以直接创建PR。或者访问项目的GitHub页面，你会看到一个黄色的提示条。
2.  **标题**：使用和Commit Message一致的清晰标题。
3.  **描述**：
    *   **它解决了什么问题？** (Link to the issue, e.g., `Closes #123`)
    *   **你做了什么改变？** (简要描述你的修改)
    *   **如何测试？** (提供测试步骤)
    *   **(可选)截图**：如果是UI修改，请附上修改前后的截图。
4.  **指定Reviewer**：在右侧面板选择你的导师或团队成员进行代码审查。
5.  点击 "Create Pull Request"。

### 4. 代码审查与合并 (30分钟)

*   你的Reviewer会检查你的代码，并可能提出修改建议。
*   根据反馈进行修改，并再次`git push`。PR会自动更新。
*   当你的PR被批准 (Approve) 后，它将被合并到`main`分支。

**✅ 恭喜！你已经成功向Questicle贡献了你的第一行代码！**

---

## 🤔 我遇到了问题，该找谁？

> **目标**：快速找到能帮你解决问题的人。

| 问题类型 | 我应该联系谁？ | 沟通渠道 |
|:---|:---|:---|
| **环境搭建问题** | 你的入职伙伴 / 团队导师 | Slack `#dev-environment` |
| **代码逻辑不懂** | 功能模块的负责人 / 导师 | Slack `#feature-tetris` |
| **PR审查反馈** | 你的PR的Reviewer | GitHub PR评论区 |
| **工具或流程问题** | 项目负责人 / 技术负责人 | Slack `#development-process` |
| **任何其他问题** | 你的团队导师 | 直接私聊 |

---

## 📚 深入学习资源

当你准备好深入了解项目时，请阅读以下核心文档：

1.  [**系统架构概览**](../../architecture/system-architecture.md) - 理解项目的设计哲学。
2.  [**开发工作流程**](../development/workflows/git-workflow.md) - 掌握我们的协作方式。
3.  [**编码与UI规范**](../development/standards/coding-standards.md) - 编写高质量、统一风格的代码。

---

**最后更新**: 2025-01-29
**维护者**: AI Assistant
**版本**: v2.2.0 