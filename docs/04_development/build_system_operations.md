# 🛠️ 构建系统操作手册

> **Questicle 项目构建系统日常操作指南**

[![Operations](https://img.shields.io/badge/Operations-Build%20System-blue.svg)](./build_system_operations.md)
[![Tools](https://img.shields.io/badge/Tools-Automation-green.svg)](./build_system_operations.md)
[![Scripts](https://img.shields.io/badge/Scripts-Validated-orange.svg)](./build_system_operations.md)

## 📋 操作概览

本手册提供了Questicle项目构建系统的完整操作指南，包括日常使用、故障排除和维护操作。

## 🚀 快速开始

### 基本构建命令

```bash
# 标准构建
./gradlew build

# 快速构建（开发模式）
./gradlew assembleDebug

# 清理构建
./gradlew clean build

# 带性能分析的构建
./gradlew build --profile --scan
```

### 配置缓存使用

```bash
# 启用配置缓存构建
./gradlew build --configuration-cache

# 查看配置缓存状态
./gradlew help --configuration-cache

# 清理配置缓存
./gradlew --stop
rm -rf .gradle/configuration-cache
```

## 🔧 核心工具使用

### 1. 系统信息检查

**命令**: `./gradlew systemInfo`

**功能**: 显示当前系统信息和推荐的构建配置

**输出示例**:
```
SystemInfo:
├── OS: Mac OS X 14.0
├── Architecture: x86_64
├── CPU Cores: 8
├── Memory: 16.0 GB
├── CI Environment: false
├── High Performance: true
├── Development Machine: true
└── Recommended Parallel Tasks: 6

构建配置:
├── 并行任务数: 6
├── 内存设置: 8g
├── 编译器参数: -Xopt-in=kotlin.RequiresOptIn, -Xjvm-default=all
└── 缓存配置: 构建缓存=true, 配置缓存=true
```

**使用场景**:
- 新环境配置验证
- 性能问题诊断
- 构建参数调优

### 2. 构建性能测试

**命令**: `./gradlew buildPerformanceTest`

**功能**: 测试当前构建系统的性能表现

**输出示例**:
```
🚀 开始构建性能测试...
✅ 构建性能测试完成
⏱️ 测试耗时: 1250ms
```

**使用场景**:
- 性能回归检测
- 优化效果验证
- 基准测试建立

### 3. 优化建议生成

**命令**: `./gradlew optimizationSuggestions`

**功能**: 基于当前系统状态提供优化建议

**输出示例**:
```
💡 优化建议:
1. 可以考虑增加并行任务数以充分利用CPU资源
2. 建议启用远程构建缓存以提升团队构建效率
```

**使用场景**:
- 定期性能优化
- 新团队成员指导
- 构建环境调优

## 📊 依赖管理操作

### 1. 依赖锁定管理

**生成依赖锁定文件**:
```bash
./gradlew generateDependencyLocks
```

**验证依赖锁定**:
```bash
./gradlew verifyDependencyLocks
```

**更新依赖锁定**:
```bash
./gradlew updateDependencyLocks
```

**输出示例**:
```
🔒 开始生成依赖锁定文件...
✅ 已锁定配置: compileClasspath
✅ 已锁定配置: runtimeClasspath
✅ 已锁定配置: testCompileClasspath
🎉 依赖锁定文件生成完成
```

### 2. 依赖冲突分析

**命令**: `./gradlew analyzeDependencyConflicts`

**功能**: 检测和分析项目中的依赖冲突

**输出示例**:
```
🔍 分析依赖冲突...
✅ 未发现依赖冲突
```

**冲突处理**:
```bash
# 查看特定依赖的冲突详情
./gradlew dependencyInsight --dependency kotlin-stdlib

# 强制使用特定版本
./gradlew dependencies --configuration compileClasspath
```

### 3. 依赖报告生成

**详细依赖分析**:
```bash
./gradlew dependencyInsights
```

**依赖树分析**:
```bash
./gradlew dependencyTreeAnalysis
```

**安全扫描**:
```bash
./gradlew securityScanDependencies
```

## 🔍 验证和诊断脚本

### 1. Gradle现代化验证

**脚本**: `./scripts/gradle-modernization-validator.sh`

**功能**: 验证Gradle配置是否符合2025年最佳实践

**检查项目**:
- ✅ Gradle版本检查
- ✅ 配置缓存状态
- ✅ 构建缓存状态
- ✅ 并行构建状态
- ✅ 文件系统监控
- ✅ Kotlin增量编译

**使用方法**:
```bash
chmod +x scripts/gradle-modernization-validator.sh
./scripts/gradle-modernization-validator.sh
```

**输出示例**:
```
🔧 Gradle现代化配置验证
==================================
📋 检查Gradle版本...
✅ Gradle版本正确 (8.14.3)
🔍 检查配置缓存...
✅ 配置缓存已启用
🚀 测试基本构建功能...
✅ 基本构建功能正常
```

### 2. 依赖管理验证

**脚本**: `./scripts/dependency-management-validator.sh`

**功能**: 验证依赖管理系统的完整性和正确性

**检查项目**:
- 📋 依赖锁定目录
- 🔍 版本目录配置
- 🔍 仓库配置
- 🚀 构建逻辑编译
- 🔧 依赖解析测试

**使用方法**:
```bash
chmod +x scripts/dependency-management-validator.sh
./scripts/dependency-management-validator.sh
```

### 3. Agent进度跟踪

**脚本**: `./scripts/agent-progress-tracker.sh`

**功能**: 跟踪重构进度和验证完成状态

**使用方法**:
```bash
./scripts/agent-progress-tracker.sh
```

## 📈 性能监控和分析

### 1. 构建扫描分析

**启用构建扫描**:
```bash
./gradlew build --scan
```

**分析要点**:
- 构建时间分布
- 任务执行时间
- 依赖解析时间
- 缓存命中率

### 2. Kotlin编译报告

**启用编译报告**:
```properties
# gradle.properties
kotlin.build.report.output=file
kotlin.build.report.file.output_dir=build/reports/kotlin-build
```

**查看报告**:
```bash
cat build/reports/kotlin-build/kotlin-build-*.txt
```

### 3. 内存使用监控

**JVM参数监控**:
```bash
# 查看当前JVM设置
./gradlew help -Dorg.gradle.jvmargs="-XX:+PrintGCDetails"

# 内存使用分析
./gradlew build -Dorg.gradle.jvmargs="-XX:+HeapDumpOnOutOfMemoryError"
```

## 🚨 故障排除指南

### 常见问题和解决方案

#### 1. 配置缓存问题

**问题**: 配置缓存失效或报错

**诊断**:
```bash
./gradlew help --configuration-cache --stacktrace
```

**解决方案**:
```bash
# 清理配置缓存
./gradlew --stop
rm -rf .gradle/configuration-cache

# 重新生成
./gradlew help --configuration-cache
```

#### 2. 依赖解析失败

**问题**: 依赖下载失败或版本冲突

**诊断**:
```bash
./gradlew dependencies --configuration compileClasspath
./gradlew analyzeDependencyConflicts
```

**解决方案**:
```bash
# 清理依赖缓存
./gradlew --stop
rm -rf ~/.gradle/caches/modules-2

# 重新解析依赖
./gradlew build --refresh-dependencies
```

#### 3. 构建性能下降

**问题**: 构建时间明显增加

**诊断**:
```bash
./gradlew build --profile --scan
./scripts/gradle-modernization-validator.sh
```

**解决方案**:
```bash
# 检查系统资源
./gradlew systemInfo

# 获取优化建议
./gradlew optimizationSuggestions

# 清理构建缓存
./gradlew clean
```

#### 4. 内存不足错误

**问题**: OutOfMemoryError

**诊断**:
```bash
# 检查当前内存设置
grep "org.gradle.jvmargs" gradle.properties
```

**解决方案**:
```bash
# 增加堆内存
echo "org.gradle.jvmargs=-Xmx8g -XX:+UseG1GC" >> gradle.properties

# 或使用系统推荐设置
./gradlew systemInfo
```

## 🔄 维护操作

### 定期维护任务

#### 每日维护
```bash
# 验证构建系统状态
./scripts/gradle-modernization-validator.sh

# 检查依赖安全更新
./gradlew securityScanDependencies
```

#### 每周维护
```bash
# 更新依赖锁定文件
./gradlew updateDependencyLocks

# 生成依赖报告
./gradlew dependencyInsights

# 清理过期缓存
./gradlew cleanupDependencies
```

#### 每月维护
```bash
# 完整系统验证
./scripts/week1-completion-report.sh

# 性能基准测试
./gradlew buildPerformanceTest

# 依赖更新检查
./gradlew dependencyUpdates
```

### 配置文件维护

#### gradle.properties 更新
```bash
# 备份当前配置
cp gradle.properties gradle.properties.backup

# 应用新配置
# 编辑 gradle.properties

# 验证配置
./scripts/gradle-modernization-validator.sh
```

#### 版本目录更新
```bash
# 检查版本更新
./gradlew dependencyUpdates

# 更新 gradle/libs.versions.toml
# 验证更新
./gradlew build
```

## 📚 最佳实践

### 开发环境配置

1. **本地开发**:
   ```bash
   # 启用所有优化特性
   ./gradlew build --configuration-cache --build-cache
   ```

2. **CI/CD环境**:
   ```bash
   # 使用保守配置
   ./gradlew build --no-daemon --max-workers=4
   ```

### 团队协作

1. **依赖锁定**:
   - 定期更新锁定文件
   - 提交锁定文件到版本控制
   - 团队同步依赖版本

2. **配置同步**:
   - 统一gradle.properties配置
   - 共享构建脚本
   - 定期验证环境一致性

---

**文档版本**: v1.0  
**最后更新**: 2025年7月20日  
**维护者**: Augment Agent  
**状态**: ✅ 可用于生产
