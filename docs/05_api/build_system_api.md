# 📚 构建系统 API 文档

> **Questicle 项目构建系统 API 参考手册**

[![API](https://img.shields.io/badge/API-Build%20System-blue.svg)](./build_system_api.md)
[![Kotlin](https://img.shields.io/badge/Kotlin-2.1.21-purple.svg)](./build_system_api.md)
[![Gradle](https://img.shields.io/badge/Gradle-8.14.3-orange.svg)](./build_system_api.md)

## 📋 API 概览

本文档提供了Questicle项目构建系统所有公开API的详细参考，包括类、方法、属性和使用示例。

## 🏗️ 核心 API

### SystemInfo

**包路径**: `com.yu.questicle.buildlogic.SystemInfo`

**描述**: 系统信息收集器，用于动态检测系统资源并提供优化建议。

#### 构造函数

```kotlin
data class SystemInfo(
    val availableProcessors: Int,    // CPU核心数
    val maxMemory: Long,             // 最大内存（字节）
    val osName: String,              // 操作系统名称
    val osVersion: String,           // 操作系统版本
    val isCI: Boolean = false        // 是否为CI环境
)
```

#### 静态方法

```kotlin
companion object {
    /**
     * 获取当前系统信息
     * @return SystemInfo 当前系统的信息实例
     */
    fun current(): SystemInfo
}
```

#### 计算属性

```kotlin
/**
 * 判断是否为高性能系统
 * 标准: CPU >= 8核心 且 内存 >= 8GB
 */
val isHighPerformanceSystem: Boolean

/**
 * 判断是否为低资源系统
 * 标准: CPU <= 2核心 或 内存 <= 2GB
 */
val isLowResourceSystem: Boolean

/**
 * 判断是否为Mac Intel架构
 */
val isMacIntel: Boolean

/**
 * 判断是否为Mac Apple Silicon架构
 */
val isMacAppleSilicon: Boolean

/**
 * 判断是否为Windows系统
 */
val isWindows: Boolean

/**
 * 判断是否为Linux系统
 */
val isLinux: Boolean

/**
 * 获取系统内存大小（GB）
 */
val memoryGB: Double

/**
 * 判断是否为开发机器
 * 标准: 非CI环境 且 CPU >= 4核心 且 内存 >= 8GB
 */
val isDevelopmentMachine: Boolean

/**
 * 获取推荐的并行任务数
 * 算法: 
 * - CI环境: min(CPU核心数, 4)
 * - 高性能系统: CPU核心数 - 2
 * - 其他: 4
 */
val recommendedParallelTasks: Int

/**
 * 获取推荐的堆内存大小
 * 算法:
 * - CI环境: 4g
 * - 内存 >= 16GB: 8g
 * - 内存 >= 8GB: 6g
 * - 内存 >= 4GB: 4g
 * - 其他: 2g
 */
val recommendedHeapSize: String
```

#### 使用示例

```kotlin
val systemInfo = SystemInfo.current()
println("CPU核心数: ${systemInfo.availableProcessors}")
println("推荐并行任务数: ${systemInfo.recommendedParallelTasks}")
println("推荐堆内存: ${systemInfo.recommendedHeapSize}")

if (systemInfo.isHighPerformanceSystem) {
    println("检测到高性能系统，启用高级优化")
}
```

---

### BuildOptimization

**包路径**: `com.yu.questicle.buildlogic.BuildOptimization`

**描述**: 构建优化配置系统，根据系统资源动态调整构建参数。

#### 公开方法

```kotlin
object BuildOptimization {
    /**
     * 获取最优构建配置
     * @return BuildConfiguration 优化后的构建配置
     */
    fun getOptimalConfiguration(): BuildConfiguration
}
```

#### 数据类

```kotlin
/**
 * 构建配置数据类
 */
data class BuildConfiguration(
    val parallelForks: Int,              // 并行任务数
    val memorySettings: MemorySettings,  // 内存设置
    val cacheSettings: CacheConfiguration, // 缓存配置
    val compilerArgs: List<String>       // 编译器参数
)

/**
 * 内存设置数据类
 */
data class MemorySettings(
    val heapSize: String,        // 堆内存大小
    val metaspaceSize: String,   // 元空间大小
    val codeCache: String,       // 代码缓存大小
    val directMemory: String     // 直接内存大小
) {
    /**
     * 转换为JVM参数列表
     * @return List<String> JVM参数列表
     */
    fun toJvmArgs(): List<String>
}

/**
 * 缓存配置数据类
 */
data class CacheConfiguration(
    val buildCache: Boolean,        // 构建缓存
    val configurationCache: Boolean, // 配置缓存
    val parallelGC: Boolean,        // 并行GC
    val daemonEnabled: Boolean      // 守护进程
)
```

#### 使用示例

```kotlin
val config = BuildOptimization.getOptimalConfiguration()
println("推荐并行任务数: ${config.parallelForks}")
println("推荐堆内存: ${config.memorySettings.heapSize}")
println("JVM参数: ${config.memorySettings.toJvmArgs().joinToString(" ")}")
```

---

### DependencyManagement

**包路径**: `com.yu.questicle.buildlogic.DependencyManagement`

**描述**: 依赖管理系统，统一管理项目依赖，提供版本锁定和解析优化。

#### 公开方法

```kotlin
object DependencyManagement {
    /**
     * 配置依赖解析策略
     * @param project Gradle项目实例
     */
    fun configureDependencyResolution(project: Project)
    
    /**
     * 配置仓库优化
     * @param project Gradle项目实例
     */
    fun configureRepositories(project: Project)
    
    /**
     * 创建依赖锁定任务
     * @param project Gradle项目实例
     */
    fun createDependencyLockingTasks(project: Project)
    
    /**
     * 分析依赖冲突
     * @param project Gradle项目实例
     */
    fun analyzeDependencyConflicts(project: Project)
    
    /**
     * 创建依赖报告
     * @param project Gradle项目实例
     */
    fun createDependencyReports(project: Project)
}
```

#### 创建的Gradle任务

| 任务名 | 描述 | 分组 |
|--------|------|------|
| `generateDependencyLocks` | 生成所有配置的依赖锁定文件 | dependency management |
| `verifyDependencyLocks` | 验证依赖锁定文件的完整性 | dependency management |
| `updateDependencyLocks` | 更新所有依赖锁定文件 | dependency management |
| `analyzeDependencyConflicts` | 分析项目中的依赖冲突 | dependency management |
| `dependencyInsights` | 生成详细的依赖分析报告 | dependency management |
| `dependencyTreeAnalysis` | 分析依赖树结构 | dependency management |

#### 使用示例

```kotlin
// 在插件中使用
class MyPlugin : Plugin<Project> {
    override fun apply(project: Project) {
        DependencyManagement.configureDependencyResolution(project)
        DependencyManagement.configureRepositories(project)
        DependencyManagement.createDependencyLockingTasks(project)
    }
}
```

---

## 🔌 插件 API

### BuildOptimizationPlugin

**包路径**: `com.yu.questicle.buildlogic.BuildOptimizationPlugin`

**插件ID**: `questicle.build.optimization`

**描述**: 构建优化插件，自动应用最优构建配置。

#### 应用方式

```kotlin
// build.gradle.kts
plugins {
    id("questicle.build.optimization")
}
```

#### 创建的Gradle任务

| 任务名 | 描述 | 分组 |
|--------|------|------|
| `systemInfo` | 显示系统信息和构建配置 | build optimization |
| `buildPerformanceTest` | 测试构建性能 | build optimization |
| `optimizationSuggestions` | 提供构建优化建议 | build optimization |

#### 配置示例

```kotlin
// 插件会自动应用优化配置，无需手动配置
// 可通过任务查看应用的配置
tasks.named("systemInfo") {
    doLast {
        println("查看系统信息和优化配置")
    }
}
```

---

### DependencyManagementPlugin

**包路径**: `com.yu.questicle.buildlogic.DependencyManagementPlugin`

**插件ID**: `questicle.dependency.management`

**描述**: 依赖管理插件，自动配置依赖解析策略、仓库优化和依赖锁定。

#### 应用方式

```kotlin
// build.gradle.kts
plugins {
    id("questicle.dependency.management")
}
```

#### 创建的Gradle任务

| 任务名 | 描述 | 分组 |
|--------|------|------|
| `verifyDependencies` | 验证项目依赖的完整性和安全性 | dependency management |
| `cleanupDependencies` | 清理过时和冗余的依赖 | dependency management |
| `securityScanDependencies` | 扫描依赖中的安全漏洞 | dependency management |

#### 配置示例

```kotlin
// 插件会自动配置依赖管理，也可以手动调用
dependencyManagement {
    // 插件会自动应用最佳实践配置
}
```

---

## 🎯 扩展 API

### 自定义优化策略

```kotlin
interface OptimizationStrategy {
    fun optimize(systemInfo: SystemInfo): BuildConfiguration
}

// 实现自定义策略
class CustomOptimizationStrategy : OptimizationStrategy {
    override fun optimize(systemInfo: SystemInfo): BuildConfiguration {
        // 自定义优化逻辑
        return BuildConfiguration(...)
    }
}
```

### 自定义系统检测

```kotlin
interface SystemDetector {
    fun detectCapabilities(): SystemCapabilities
}

// 实现自定义检测
class CustomSystemDetector : SystemDetector {
    override fun detectCapabilities(): SystemCapabilities {
        // 自定义检测逻辑
        return SystemCapabilities(...)
    }
}
```

---

## 📊 配置 API

### gradle.properties 配置项

| 配置项 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `org.gradle.configuration-cache` | Boolean | true | 启用配置缓存 |
| `org.gradle.vfs.watch` | Boolean | true | 启用文件系统监控 |
| `org.gradle.parallel` | Boolean | true | 启用并行构建 |
| `org.gradle.caching` | Boolean | true | 启用构建缓存 |
| `kotlin.incremental` | Boolean | true | 启用Kotlin增量编译 |
| `kotlin.incremental.useClasspathSnapshot` | Boolean | true | 启用类路径快照 |

### 环境变量

| 变量名 | 类型 | 描述 |
|--------|------|------|
| `CI` | String | CI环境标识，存在时认为是CI环境 |
| `GRADLE_OPTS` | String | 额外的Gradle JVM选项 |

---

## 🔍 调试 API

### 日志级别

```kotlin
// 启用详细日志
project.logger.lifecycle("信息级别日志")
project.logger.info("调试信息")
project.logger.debug("详细调试信息")
project.logger.warn("警告信息")
project.logger.error("错误信息")
```

### 性能监控

```kotlin
// 测量执行时间
val startTime = System.currentTimeMillis()
// ... 执行操作
val endTime = System.currentTimeMillis()
project.logger.lifecycle("操作耗时: ${endTime - startTime}ms")
```

---

**文档版本**: v1.0  
**最后更新**: 2025年7月20日  
**维护者**: Augment Agent  
**API版本**: 2025.1
