# Tetris Engine API Documentation

## Overview

This document provides detailed API documentation for the refactored Tetris Engine components. It covers the interfaces, classes, and methods available for interacting with the Tetris game engine.

## Core Interfaces

### TetrisEngine

The main interface for interacting with the Tetris game engine. It provides methods for game initialization, piece movement, rotation, and other game operations.

```kotlin
interface TetrisEngine {
    suspend fun initializeGame(playerId: String): Result<TetrisGameState>
    suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun processAction(action: TetrisAction, currentState: TetrisGameState): Result<TetrisGameState>
    suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun endGame(gameState: TetrisGameState): Result<Game>
    fun observeGameState(): Flow<TetrisGameState>
    fun isValidAction(action: TetrisAction, currentState: TetrisGameState): Boolean
    fun calculateScore(gameState: TetrisGameState): Int
    fun isGameOver(gameState: TetrisGameState): Boolean
    fun getGameStatistics(gameState: TetrisGameState): Map<String, Any>
    suspend fun saveGameState(gameState: TetrisGameState): Result<Unit>
    suspend fun loadGameState(gameId: String): Result<TetrisGameState>
    fun generateNextPiece(): TetrisPiece
    suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState>
    fun calculateDropInterval(level: Int): Long
    fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int
    fun getGhostPiece(gameState: TetrisGameState): TetrisPiece?
    fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean
    fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece>
    fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece>
    suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState>
    fun checkTSpin(gameState: TetrisGameState): Boolean
    fun calculateLevel(lines: Int): Int
}
```

### TetrisGameLogicProcessor

Handles core game mechanics and rule processing.

```kotlin
interface TetrisGameLogicProcessor {
    suspend fun processMove(piece: TetrisPiece, direction: Direction, board: TetrisBoard): GameMoveResult
    suspend fun processRotation(piece: TetrisPiece, clockwise: Boolean, board: TetrisBoard): GameRotationResult
    suspend fun processLineClear(board: TetrisBoard): LineClearResult
    suspend fun validateGameState(gameState: TetrisGameState): ValidationResult
    suspend fun generateNextPiece(gameState: TetrisGameState): TetrisPiece
}
```

### TetrisCollisionDetector

Handles collision detection and validation.

```kotlin
interface TetrisCollisionDetector {
    fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean
    fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece
    fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo
}
```

### TetrisStatisticsCalculator

Handles score calculation and game statistics.

```kotlin
interface TetrisStatisticsCalculator {
    fun calculateLineClearScore(linesCleared: Int, level: Int, combo: Int = 0): Int
    fun calculateLevel(lines: Int): Int
    fun calculateDropInterval(level: Int): Long
    fun calculateComboMultiplier(combo: Int): Float
    fun updateGameStatistics(
        statistics: GameStatistics,
        linesCleared: Int? = null,
        pieceType: TetrisPieceType? = null,
        isTSpin: Boolean = false,
        isPerfectClear: Boolean = false,
        isBackToBack: Boolean = false
    ): GameStatistics
}
```

### TetrisPerformanceManager

Handles performance optimization and monitoring.

```kotlin
interface TetrisPerformanceManager {
    fun optimizeForDevice(deviceSpecs: DeviceSpecs): PerformanceConfig
    fun trackOperation(operation: String, durationNanos: Long)
    fun recordMemoryUsage(component: String, memoryUsageMB: Long)
    fun getPerformanceMetrics(): PerformanceMetrics
    fun shouldUseOptimization(optimization: OptimizationType): Boolean
}
```

### TetrisCacheManager

Handles caching for performance optimization.

```kotlin
interface TetrisCacheManager {
    fun <T> getCached(key: CacheKey, factory: () -> T): T
    fun invalidateCache(pattern: String)
    fun configureForPerformance(config: PerformanceConfig)
    fun getCacheStatistics(): CacheStatistics
    fun clearAllCaches()
}
```

### GameStateValidator

Handles game state validation.

```kotlin
interface GameStateValidator {
    fun validateGameState(gameState: TetrisGameState): ValidationResult
    fun isGameOver(gameState: TetrisGameState): Boolean
}
```

## Data Classes

### TetrisGameState

Represents the state of a Tetris game.

```kotlin
data class TetrisGameState(
    val board: TetrisBoard,
    val currentPiece: TetrisPiece?,
    val nextPiece: TetrisPiece?,
    val holdPiece: TetrisPiece?,
    val score: Int,
    val level: Int,
    val lines: Int,
    val combo: Int,
    val canHold: Boolean,
    val status: TetrisStatus,
    val lastDropTime: Long,
    val statistics: GameStatistics
)
```

### TetrisPiece

Represents a Tetris piece.

```kotlin
data class TetrisPiece(
    val type: TetrisPieceType,
    val x: Int,
    val y: Int,
    val rotation: Int
)
```

### TetrisBoard

Interface for the Tetris game board.

```kotlin
interface TetrisBoard {
    val width: Int
    val height: Int
    val cells: Array<Array<TetrisCellType>>
}
```

### GameStatistics

Represents game statistics.

```kotlin
interface GameStatistics {
    val piecesPlaced: Int
    val linesCleared: Int
    val singles: Int
    val doubles: Int
    val triples: Int
    val tetrises: Int
    val tSpins: Int
    val maxCombo: Int
    val perfectClears: Int
    val backToBackTetrises: Int
    val backToBackTSpins: Int
}
```

### Result Types

```kotlin
sealed class GameMoveResult {
    data class Success(val newPiece: TetrisPiece) : GameMoveResult()
    data class Blocked(val originalPiece: TetrisPiece) : GameMoveResult()
    data class Landed(val landedPiece: TetrisPiece) : GameMoveResult()
}

sealed class GameRotationResult {
    data class Success(val rotatedPiece: TetrisPiece) : GameRotationResult()
    data class WallKick(val kickedPiece: TetrisPiece) : GameRotationResult()
    class Blocked : GameRotationResult()
}

sealed class LineClearResult {
    object NoLines : LineClearResult()
    data class Success(
        val clearedBoard: TetrisBoard,
        val linesCleared: Int,
        val scoreIncrease: Int,
        val isTSpin: Boolean = false,
        val isPerfectClear: Boolean = false,
        val isBackToBack: Boolean = false
    ) : LineClearResult()
}

data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList()
)

data class CollisionInfo(
    val hasCollision: Boolean,
    val collisionPoints: List<CollisionPoint> = emptyList()
)

data class CollisionPoint(
    val x: Int,
    val y: Int,
    val type: CollisionType
)

enum class CollisionType {
    BOUNDARY,
    BLOCK
}
```

## Memory Optimization Components

### TetrisObjectPoolManager

Manages object pools for frequently created objects.

```kotlin
class TetrisObjectPoolManager {
    fun acquirePiece(type: TetrisPieceType, x: Int, y: Int, rotation: Int): TetrisPiece
    fun releasePiece(piece: TetrisPiece)
    fun acquireMoveResultSuccess(newPiece: TetrisPiece): GameMoveResult.Success
    fun releaseMoveResultSuccess(result: GameMoveResult.Success)
    fun acquireMoveResultBlocked(originalPiece: TetrisPiece): GameMoveResult.Blocked
    fun acquireMoveResultLanded(landedPiece: TetrisPiece): GameMoveResult.Landed
    fun acquireRotationResultSuccess(rotatedPiece: TetrisPiece): GameRotationResult.Success
    fun acquireRotationResultWallKick(kickedPiece: TetrisPiece): GameRotationResult.WallKick
    fun acquireRotationResultBlocked(): GameRotationResult.Blocked
    fun getLineClearResultNoLines(): LineClearResult.NoLines
    fun getMemoryStatistics(): Map<String, Any>
    fun clearAllPools()
}
```

### TetrisMemoryMonitor

Monitors memory usage and provides insights for optimization.

```kotlin
class TetrisMemoryMonitor {
    fun recordAllocation(objectType: String, sizeBytes: Int)
    fun recordRelease(objectType: String)
    fun takeSnapshot(label: String)
    fun recordGCEvent(durationMs: Long, collectedBytes: Long)
    fun getMemoryStatistics(): Map<String, Any>
    fun getMemorySnapshots(): List<MemorySnapshot>
    fun getGCEvents(): List<GCEvent>
    fun reset()
}
```

### ImmutableGameState

Immutable implementation of TetrisGameState with structural sharing.

```kotlin
class ImmutableGameState private constructor(
    override val board: TetrisBoard,
    override val currentPiece: TetrisPiece?,
    override val nextPiece: TetrisPiece?,
    override val holdPiece: TetrisPiece?,
    override val score: Int,
    override val level: Int,
    override val lines: Int,
    override val combo: Int,
    override val canHold: Boolean,
    override val status: TetrisStatus,
    override val lastDropTime: Long,
    override val statistics: GameStatistics
) : TetrisGameState {
    fun with(
        board: TetrisBoard = this.board,
        currentPiece: TetrisPiece? = this.currentPiece,
        nextPiece: TetrisPiece? = this.nextPiece,
        holdPiece: TetrisPiece? = this.holdPiece,
        score: Int = this.score,
        level: Int = this.level,
        lines: Int = this.lines,
        combo: Int = this.combo,
        canHold: Boolean = this.canHold,
        status: TetrisStatus = this.status,
        lastDropTime: Long = this.lastDropTime,
        statistics: GameStatistics = this.statistics
    ): ImmutableGameState
    
    fun toStandardGameState(): TetrisGameState
    
    companion object {
        fun from(state: TetrisGameState): ImmutableGameState
        fun initial(): ImmutableGameState
    }
}
```

### ImmutableGameStatistics

Immutable implementation of GameStatistics with structural sharing.

```kotlin
class ImmutableGameStatistics private constructor(
    override val piecesPlaced: Int,
    override val linesCleared: Int,
    override val singles: Int,
    override val doubles: Int,
    override val triples: Int,
    override val tetrises: Int,
    override val tSpins: Int,
    override val maxCombo: Int,
    override val perfectClears: Int,
    override val backToBackTetrises: Int,
    override val backToBackTSpins: Int
) : GameStatistics {
    fun with(
        piecesPlaced: Int = this.piecesPlaced,
        linesCleared: Int = this.linesCleared,
        singles: Int = this.singles,
        doubles: Int = this.doubles,
        triples: Int = this.triples,
        tetrises: Int = this.tetrises,
        tSpins: Int = this.tSpins,
        maxCombo: Int = this.maxCombo,
        perfectClears: Int = this.perfectClears,
        backToBackTetrises: Int = this.backToBackTetrises,
        backToBackTSpins: Int = this.backToBackTSpins
    ): ImmutableGameStatistics
    
    fun withLineClear(
        linesCleared: Int,
        isTSpin: Boolean = false,
        isPerfectClear: Boolean = false,
        isBackToBack: Boolean = false
    ): ImmutableGameStatistics
    
    fun withPiecePlaced(): ImmutableGameStatistics
    
    fun withCombo(combo: Int): ImmutableGameStatistics
    
    fun toStandardStatistics(): GameStatistics
    
    companion object {
        fun from(statistics: GameStatistics): ImmutableGameStatistics
        fun empty(): ImmutableGameStatistics
    }
}
```

## Board Optimization Components

### BitPackedTetrisBoard

Optimized TetrisBoard implementation using bit-packed storage.

```kotlin
class BitPackedTetrisBoard private constructor(
    private val data: LongArray,
    override val width: Int,
    override val height: Int,
    private val bitsPerCell: Int = 3,
    private val cellMask: Long = (1L shl bitsPerCell) - 1
) : TetrisBoard {
    override val cells: Array<Array<TetrisCellType>>
    
    fun setCellType(x: Int, y: Int, cellType: TetrisCellType): BitPackedTetrisBoard
    fun isRowFilled(y: Int): Boolean
    fun isRowEmpty(y: Int): Boolean
    fun clearRow(y: Int): BitPackedTetrisBoard
    fun clearRows(rows: List<Int>): BitPackedTetrisBoard
    fun placePiece(piece: TetrisPiece): BitPackedTetrisBoard
    fun canPlacePiece(piece: TetrisPiece): Boolean
    fun clearCompletedLines(): Pair<BitPackedTetrisBoard, List<Int>>
    
    companion object {
        fun empty(width: Int = 10, height: Int = 20, bitsPerCell: Int = 3): BitPackedTetrisBoard
        fun fromCells(cells: Array<Array<TetrisCellType>>): BitPackedTetrisBoard
    }
}
```

### TetrisBoardAdapter

Adapter for integrating BitPackedTetrisBoard with the existing TetrisBoard interface.

```kotlin
class TetrisBoardAdapter @Inject constructor(
    private val boardFactory: TetrisBoardFactory
) {
    fun toBitPackedBoard(board: TetrisBoard): BitPackedTetrisBoard
    fun placePiece(board: TetrisBoard, piece: TetrisPiece): TetrisBoard
    fun canPlacePiece(board: TetrisBoard, piece: TetrisPiece): Boolean
    fun clearCompletedLines(board: TetrisBoard): Pair<TetrisBoard, List<Int>>
    fun isRowFilled(board: TetrisBoard, y: Int): Boolean
    fun isRowEmpty(board: TetrisBoard, y: Int): Boolean
    fun createEmptyBoard(width: Int = 10, height: Int = 20): TetrisBoard
    fun createBoardFromCells(cells: Array<Array<TetrisCellType>>): TetrisBoard
}
```

### TetrisBoardFactory

Factory for creating optimized TetrisBoard instances.

```kotlin
class TetrisBoardFactory @Inject constructor() {
    fun createEmptyBoard(width: Int = 10, height: Int = 20): TetrisBoard
    fun createBoardFromCells(cells: Array<Array<TetrisCellType>>): TetrisBoard
    fun createTestBoard(pattern: BoardPattern, width: Int = 10, height: Int = 20): TetrisBoard
    
    enum class BoardPattern {
        EMPTY,
        FULL,
        CHECKERBOARD,
        BOTTOM_ROW,
        ALMOST_FULL_ROW
    }
}
```

## Performance Optimization Components

### ComponentCache

Cache for frequently used component operations.

```kotlin
class ComponentCache @Inject constructor() {
    fun <T> getCachedCollisionResult(
        piece: TetrisPiece,
        board: TetrisBoard,
        compute: () -> Boolean
    ): Boolean
    
    fun getCachedLandingPosition(
        piece: TetrisPiece,
        board: TetrisBoard,
        compute: () -> Int
    ): Int
    
    fun getCachedScore(
        linesCleared: Int,
        level: Int,
        combo: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        isBackToBack: Boolean,
        compute: () -> Int
    ): Int
    
    fun getCachedRotationResult(
        piece: TetrisPiece,
        clockwise: Boolean,
        board: TetrisBoard,
        compute: () -> RotationCacheResult
    ): RotationCacheResult
    
    fun invalidateBoardDependentCaches()
    fun getCacheStatistics(): Map<String, Any>
    fun resetCacheStatistics()
    fun clearAllCaches()
    fun configureCacheSizes(
        collisionCacheSize: Int,
        landingPositionCacheSize: Int,
        scoreCacheSize: Int,
        rotationCacheSize: Int
    )
    
    data class RotationCacheResult(
        val success: Boolean,
        val resultPiece: TetrisPiece?,
        val isWallKick: Boolean
    )
}
```

### ComponentPerformanceMonitor

Monitors performance of component interactions.

```kotlin
class ComponentPerformanceMonitor @Inject constructor() {
    fun recordOperationTime(operation: String, timeNanos: Long)
    fun recordComponentInteraction(fromComponent: String, toComponent: String)
    inline fun <T> measureOperation(operation: String, block: () -> T): T
    inline fun <T> measureComponentInteraction(
        operation: String,
        fromComponent: String,
        toComponent: String,
        block: () -> T
    ): T
    fun getOperationMetrics(): Map<String, Map<String, Any>>
    fun getComponentInteractionMetrics(): Map<String, Map<String, Long>>
    fun getPerformanceSummary(): Map<String, Any>
    fun getHotPathOperations(limit: Int = 10): List<Map<String, Any>>
    fun getMostFrequentInteractions(limit: Int = 10): List<Map<String, Any>>
    fun resetMetrics()
}
```

### OptimizedComponentCommunication

Optimizes communication between components.

```kotlin
class OptimizedComponentCommunication @Inject constructor(
    private val collisionDetector: TetrisCollisionDetector,
    private val gameLogicProcessor: TetrisGameLogicProcessor,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val objectPoolManager: TetrisObjectPoolManager
) {
    suspend fun checkAndProcessMove(
        piece: TetrisPiece,
        direction: Direction,
        board: TetrisBoard
    ): Pair<Boolean, TetrisPiece?>
    
    suspend fun checkAndProcessRotation(
        piece: TetrisPiece,
        clockwise: Boolean,
        board: TetrisBoard
    ): Pair<Boolean, TetrisPiece?>
    
    fun findLandingPositionOptimized(
        piece: TetrisPiece,
        board: TetrisBoard
    ): TetrisPiece
    
    fun calculateScoreOptimized(
        linesCleared: Int,
        level: Int,
        combo: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        isBackToBack: Boolean
    ): Int
}
```

## Usage Examples

### Basic Game Loop

```kotlin
// Initialize the game
val initResult = tetrisEngine.initializeGame("player1")
val gameState = (initResult as Result.Success).data

// Start the game
val startedState = tetrisEngine.startGame(gameState).data

// Game loop
var currentState = startedState
while (!tetrisEngine.isGameOver(currentState)) {
    // Process user input
    val userAction = getUserAction()
    
    // Validate action
    if (tetrisEngine.isValidAction(userAction, currentState)) {
        // Process action
        val result = tetrisEngine.processAction(userAction, currentState)
        if (result is Result.Success) {
            currentState = result.data
        }
    }
    
    // Auto drop
    val dropResult = tetrisEngine.autoDrop(currentState)
    if (dropResult is Result.Success) {
        currentState = dropResult.data
    }
    
    // Check for line clears
    val clearResult = tetrisEngine.checkLineClear(currentState)
    if (clearResult is Result.Success) {
        currentState = clearResult.data
    }
    
    // Update UI
    updateUI(currentState)
    
    // Delay based on level
    delay(tetrisEngine.calculateDropInterval(currentState.level))
}

// End the game
tetrisEngine.endGame(currentState)
```

### Using Immutable Game State

```kotlin
// Create an immutable game state
val initialState = ImmutableGameState.initial()

// Update the state
val updatedState = initialState.with(
    score = 100,
    level = 2,
    lines = 10
)

// Update the state with a new piece
val piece = TetrisPiece(TetrisPieceType.I, 3, 0, 0)
val stateWithPiece = updatedState.with(currentPiece = piece)

// Update the state with a new board
val board = BitPackedTetrisBoard.empty(10, 20)
val stateWithBoard = stateWithPiece.with(board = board)
```

### Using Object Pooling

```kotlin
// Acquire a piece from the pool
val piece = objectPoolManager.acquirePiece(TetrisPieceType.I, 3, 0, 0)

// Use the piece
val moveResult = objectPoolManager.acquireMoveResultSuccess(piece)

// Release objects back to the pool
objectPoolManager.releaseMoveResultSuccess(moveResult)
objectPoolManager.releasePiece(piece)
```

### Using Bit-Packed Board

```kotlin
// Create an empty board
val board = BitPackedTetrisBoard.empty(10, 20)

// Set cell types
val updatedBoard = board
    .setCellType(1, 1, TetrisCellType.I)
    .setCellType(2, 2, TetrisCellType.J)
    .setCellType(3, 3, TetrisCellType.L)

// Place a piece
val piece = TetrisPiece(TetrisPieceType.T, 3, 0, 0)
val boardWithPiece = updatedBoard.placePiece(piece)

// Check if a piece can be placed
val canPlace = boardWithPiece.canPlacePiece(piece)

// Clear completed lines
val (clearedBoard, clearedLines) = boardWithPiece.clearCompletedLines()
```

### Using Component Cache

```kotlin
// Get cached collision result
val isValid = componentCache.getCachedCollisionResult(piece, board) {
    // Compute collision result if not in cache
    collisionDetector.isValidPosition(piece, board)
}

// Get cached landing position
val landingY = componentCache.getCachedLandingPosition(piece, board) {
    // Compute landing position if not in cache
    findLandingPosition(piece, board)
}

// Get cached score
val score = componentCache.getCachedScore(
    linesCleared = 4,
    level = 5,
    combo = 2,
    isTSpin = true,
    isPerfectClear = false,
    isBackToBack = true
) {
    // Compute score if not in cache
    calculateScore(4, 5, 2, true, false, true)
}
```

### Using Performance Monitoring

```kotlin
// Measure operation time
val result = componentPerformanceMonitor.measureOperation("movePiece") {
    // Operation to measure
    tetrisEngine.movePiece(Direction.RIGHT, gameState)
}

// Measure component interaction
val interactionResult = componentPerformanceMonitor.measureComponentInteraction(
    operation = "processMove",
    fromComponent = "TetrisEngine",
    toComponent = "GameLogicProcessor"
) {
    // Interaction to measure
    gameLogicProcessor.processMove(piece, direction, board)
}

// Get performance metrics
val metrics = componentPerformanceMonitor.getOperationMetrics()
val hotPaths = componentPerformanceMonitor.getHotPathOperations(5)
val interactions = componentPerformanceMonitor.getComponentInteractionMetrics()
val summary = componentPerformanceMonitor.getPerformanceSummary()
```

## Error Handling

The Tetris engine uses a Result type for error handling:

```kotlin
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: Exception) : Result<Nothing>()
}
```

Common exceptions:

```kotlin
sealed class TetrisEngineException(message: String, cause: Throwable? = null) : Exception(message, cause)

class InvalidGameStateException(message: String) : TetrisEngineException(message)
class CollisionDetectionException(message: String, cause: Throwable) : TetrisEngineException(message, cause)
class PerformanceOptimizationException(message: String) : TetrisEngineException(message)
class CacheException(message: String, cause: Throwable) : TetrisEngineException(message, cause)
```

Example error handling:

```kotlin
val result = tetrisEngine.movePiece(Direction.RIGHT, gameState)
when (result) {
    is Result.Success -> {
        val updatedState = result.data
        // Handle success
    }
    is Result.Error -> {
        val exception = result.exception
        when (exception) {
            is InvalidGameStateException -> {
                // Handle invalid game state
            }
            is CollisionDetectionException -> {
                // Handle collision detection error
            }
            else -> {
                // Handle other errors
            }
        }
    }
}
```