# ADR-001: 全局异常处理设计规范
## 文档信息
- 文档标题: ADR-001: 全局异常处理设计规范
- 文档版本: 1.0.0
- 创建日期: 2025-07-11
- 最后更新: 2025-07-11
- 文档状态: 正式发布
- 作者: Documentation Team

## 📖 变更历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | 2025-07-11 | 初始版本 | Documentation Team |

## 📚 目录



- **状态**: ✅ 已接受
- **决策日期**: 2025-01-20
- **标签**: `ADR` `异常处理` `架构设计`

---

## 1. 背景

Questicle项目需要一个统一、可靠、可观测的全局异常处理系统。当前系统在异常传播、错误恢复和日志监控方面存在不足，影响了应用的稳定性和用户体验。本决策旨在建立一个遵循Clean Architecture和函数式编程思想的现代化异常处理框架。

## 2. 约束条件

- **性能**: 异常处理机制不能显著影响正常执行路径的性能。
- **安全**: 错误信息不能泄露任何敏感数据（如用户ID、服务器路径等）。
- **用户体验**: 必须为用户提供清晰、有帮助的错误提示和恢复路径。
- **一致性**: 全团队必须遵循统一的错误处理模型。

## 3. 考虑的选项

### 选项 A: 传统 `try-catch` 块
- **描述**: 在代码各处使用语言原生的`try-catch`块来捕获和处理异常。
- **优点**: 开发者熟悉，性能开销低。
- **缺点**: 异常传播路径不清晰，容易遗漏处理，与函数式编程风格不符，难以标准化。

### 选项 B: `Result` 类型 + 分层异常处理 (最终选择)
- **描述**: 使用密封类 `Result<T, E>` 来显式地在类型系统中表示成功或失败。结合自定义的异常体系，在不同架构层级进行归一化处理。
- **优点**: 类型安全（编译时检查），强制调用者处理错误，完美契合函数式编程（如 `map`, `flatMap`），流程可控，易于测试和统一监控。
- **缺点**: 存在一定的学习曲线，可能会引入额外的封装代码。

### 选项 C: 混合模式
- **描述**: 在核心业务流程（如支付、登录）中使用 `Result` 类型，在其他非关键部分使用 `try-catch`。
- **优点**: 渐进式引入，风险较低。
- **缺点**: 破坏了系统一致性，增加了认知负担，导致异常处理模型混乱。

## 4. 决策

我们选择 **选项 B: `Result` 类型 + 分层异常处理**。

**决策理由**:
该方案在**类型安全、可维护性、可测试性和一致性**方面提供了无与伦比的优势。虽然存在学习成本，但其带来的长期收益（更少的运行时错误、更清晰的代码逻辑、更强的系统韧性）远超短期投入。这与我们追求高质量、可扩展软件架构的长期目标完全一致。

## 5. 设计方案

### 5.1. 核心异常体系 `QuesticleException`

所有业务和技术异常都应继承自统一的根异常 `QuesticleException`。

```mermaid
classDiagram
    direction LR
    class QuesticleException {
        <<abstract>>
        + code: String
        + message: String
    }
    class BusinessException {
        <<abstract>>
    }
    class TechnicalException {
        <<abstract>>
    }
    class SystemException {
        <<abstract>>
    }
    QuesticleException <|-- BusinessException
    QuesticleException <|-- TechnicalException
    QuesticleException <|-- SystemException

    BusinessException <|-- GameException
    BusinessException <|-- UserException
    BusinessException <|-- ValidationException

    TechnicalException <|-- NetworkException
    TechnicalException <|-- DatabaseException
    TechnicalException <|-- FileSystemException

    class GameException
    class UserException
    class ValidationException
    class NetworkException
    class DatabaseException
    class FileSystemException
```

### 5.2. `Result` 类型实现

提供一个健壮的 `Result` 类，包含丰富的函数式操作符。

```kotlin
// In: core/common/src/main/kotlin/com/yu/questicle/core/common/result/Result.kt
sealed class Result<out T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error(val exception: QuesticleException) : Result<Nothing>()

    // 便捷方法
    fun getOrNull(): T? { ... }
    fun exceptionOrNull(): QuesticleException? { ... }
    
    // 函数式操作
    inline fun <R> map(transform: (T) -> R): Result<R> { ... }
    inline fun <R> flatMap(transform: (T) -> Result<R>): Result<R> { ... }
    inline fun onSuccess(action: (T) -> Unit): Result<T> { ... }
    inline fun onError(action: (QuesticleException) -> Unit): Result<T> { ... }
}
```

### 5.3. 分层处理策略

- **Data 层 (Repository)**: 负责捕获最原始的第三方库异常（如 `IOException`, `SQLException`），并将其**转换**为标准化的 `TechnicalException`，包裹在 `Result.Error` 中返回。
- **Domain 层 (UseCase)**: 负责处理 `TechnicalException` 和 `BusinessException`。可以包含重试、回退等业务逻辑。通常直接将 `Result` 对象透传给上层。
- **Presentation 层 (ViewModel)**: **必须处理**所有 `Result` 类型。根据 `Result.Error` 中的具体异常类型，转换为用户可理解的UI状态（如 `Toast`, `Dialog`, `ErrorMessage`）。**严禁在此层级抛出或传递原始异常**。

## 6. 实施计划

| 阶段 | 关键任务 | 预计时间 | 状态 |
|---|---|---|---|
| **阶段 1: 基础框架** | 1. 实现 `QuesticleException` 体系 <br> 2. 实现 `Result` 类型和扩展函数 <br> 3. 定义异常处理器接口 | 1-2 周 | `TODO` |
| **阶段 2: 分层实现** | 1. 在Data层全面应用转换 <br> 2. 在Domain层集成处理逻辑 <br> 3. 在Presentation层实现UI反馈 | 2-3 周 | `TODO` |
| **阶段 3: 监控与优化** | 1. 对接日志与监控系统 (见 `ADR-002`) <br> 2. 性能基准测试与优化 <br> 3. 编写开发者指南和培训 | 1-2 周 | `TODO` |

## 7. 后果

- **积极**: 显著提升代码质量和系统稳定性；改善用户体验；建立统一、可观测的错误处理标准。
- **负面**: 团队需要时间适应新的函数式错误处理范式。可通过结对编程和严格的代码审查来缓解。

## 8. 相关链接

- **依赖于**: `ADR-002: 日志系统设计规范`
- **参考**:
    - [Kotlin Result 类型官方文档](https://kotlinlang.org/api/latest/jvm/stdlib/kotlin/-result/)
    - [Arrow-Kt 函数式错误处理](https://arrow-kt.io/docs/patterns/error_handling/)
---
*本决策于 2025-01-20 被接受，并作为全项目异常处理的统一标准。*
