# ADR-002: 日志系统设计规范
## 文档信息
- 文档标题: ADR-002: 日志系统设计规范
- 文档版本: 1.0.0
- 创建日期: 2025-07-11
- 最后更新: 2025-07-11
- 文档状态: 正式发布
- 作者: Documentation Team

## 📖 变更历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | 2025-07-11 | 初始版本 | Documentation Team |

## 📚 目录



- **状态**: ✅ 已接受
- **决策日期**: 2025-01-20
- **标签**: `ADR` `日志` `可观测性` `架构设计`

---

## 1. 背景

Questicle项目需要一个现代化、高性能、可扩展的日志系统，以支持开发调试、生产监控和数据分析。Android原生的日志工具功能有限，无法满足结构化、持久化和远程收集的需求。

## 2. 约束条件

- **性能**: 日志记录操作必须是异步的，且不能对主线程和游戏循环产生可感知的性能影响。
- **存储**: 日志应支持压缩、轮转和过期清理，以控制在移动设备上的存储占用。
- **隐私**: 必须自动或手动脱敏所有PII（个人身份信息），符合GDPR等隐私法规。
- **可扩展性**: 系统应支持添加新的日志输出目标（如远程服务器、分析平台）而无需修改现有业务代码。

## 3. 考虑的选项

### 选项 A: Android 原生 `Logcat`
- **描述**: 直接使用 `android.util.Log`。
- **优点**: 无依赖，零成本。
- **缺点**: 功能极其有限，不支持结构化、持久化、远程上报，不适用于生产环境。

### 选项 B: 集成第三方日志库 (如 Timber)
- **描述**: 引入一个成熟的第三方日志门面库。
- **优点**: 功能比原生`Logcat`强大，API友好。
- **缺点**: 仅是一个日志门面，底层仍依赖`Logcat`，无法解决持久化和上报等根本问题，定制化能力有限。

### 选项 C: 自研高性能日志系统 `QLogger` (最终选择)
- **描述**: 基于项目需求，设计并实现一套完整的、包含前端API、后端处理和多目标输出的专用日志系统。
- **优点**: 完全按需定制，可进行极致的性能优化，与项目架构（如异常处理）深度集成，内置隐私保护和结构化能力。
- **缺点**: 初始开发和长期维护成本较高。

## 4. 决策

我们选择 **选项 C: 自研高性能日志系统 (QLogger)**。

**决策理由**:
对于一个商业级应用来说，日志是其可观测性的基石。一个功能完备、性能卓越的日志系统是保障服务质量、快速定位问题和进行数据驱动决策的必要投资。自研系统虽然成本更高，但它提供了无与伦比的**定制化能力、性能控制和架构集成度**，这些是第三方通用库无法比拟的。我们认为这是保障项目长期健康发展的正确选择。

## 5. 设计方案

### 5.1. 整体架构

`QLogger` 采用多层异步架构，确保日志记录对业务代码的影响降至最低。

```mermaid
graph TD
    subgraph "应用层"
        AppCode["业务代码 (Activity, ViewModel)"]
    end

    subgraph "日志门面 (API)"
        QLoggerAPI["QLogger (接口)"]
    end

    subgraph "日志核心"
        direction LR
        LogProcessor["LogProcessor (异步处理器)"] --> LogStorage["LogStorage (多目标输出)"]
    end
    
    subgraph "输出目标"
        direction LR
        Console["Logcat 输出"]
        File["文件存储 (压缩/轮转)"]
        Remote["远程服务器 (数据分析)"]
    end

    AppCode --> |调用| QLoggerAPI
    QLoggerAPI --> |提交日志事件| LogProcessor
    LogStorage --> Console
    LogStorage --> File
    LogStorage --> Remote
```

### 5.2. 核心组件

1.  **`QLogger` (门面接口)**: 为应用层提供统一、简洁的日志记录API。
2.  **`LogProcessor` (异步处理器)**: 运行在专用后台线程，负责从队列中消费日志事件，进行格式化、过滤和分发。
3.  **`LogStorage` (多目标输出)**: 策略模式实现，可将格式化后的日志同时输出到`Logcat`、文件和远程服务器等一个或多个目标。

### 5.3. 结构化日志格式

所有日志都应遵循统一的JSON结构，便于机器解析和查询。

```json
{
  "timestamp": "2025-01-20T10:30:45.123Z",
  "level": "INFO",
  "loggerName": "com.yu.questicle.feature.tetris.GameManager",
  "message": "Game started successfully",
  "context": {
    "userId": "usr_masked_1a2b",
    "gameId": "gid_tetris_classic_xyz"
  },
  "tags": ["game_lifecycle", "user_action"],
  "platform": {
    "os": "Android",
    "version": "14.0",
    "deviceId": "dev_masked_3c4d"
  }
}
```

### 5.4. API 设计

```kotlin
// In: core/common/src/main/kotlin/com/yu/questicle/core/common/logging/QLogger.kt
interface QLogger {
    fun v(message: String, context: Map<String, Any>? = null)
    fun d(message: String, context: Map<String, Any>? = null)
    fun i(message: String, context: Map<String, Any>? = null)
    fun w(message: String, context: Map<String, Any>? = null)
    fun e(error: Throwable, context: Map<String, Any>? = null)

    // 返回一个带有额外上下文的新Logger实例，用于链式调用
    fun withContext(context: Map<String, Any>): QLogger
}
```

## 6. 实施计划

| 阶段 | 关键任务 | 预计时间 | 状态 |
|---|---|---|---|
| **阶段 1: 核心框架** | 1. 实现 `QLogger` 接口和异步处理器 <br> 2. 实现基于 `Logcat` 和文件的 `LogStorage` | 1-2 周 | `TODO` |
| **阶段 2: 高级功能** | 1. 实现结构化日志格式化器 <br> 2. 实现数据脱敏和隐私过滤 <br> 3. 实现日志轮转和压缩 | 2-3 周 | `TODO` |
| **阶段 3: 集成与部署** | 1. 对接 `ADR-001` 的异常处理系统 <br> 2. 实现远程日志上报 <br> 3. 全面替换项目中的旧日志调用 | 1-2 周 | `TODO` |

## 7. 后果

- **积极**: 项目将拥有世界级的可观测性能力，极大提升问题排查和数据分析的效率。
- **负面**: 需要投入研发资源进行开发和维护。团队需要学习和适应新的日志API和规范。

## 8. 相关链接

- **集成于**: `ADR-001: 全局异常处理设计规范`
- **参考**:
    - [Elastic Common Schema (ECS) for structured logging](https://www.elastic.co/guide/en/ecs/current/ecs-logging.html)
    - [High-Performance Logging Libraries (e.g., Log4j2)](https://logging.apache.org/log4j/2.x/performance.html)
---
*本决策于 2025-01-20 被接受，并作为全项目日志记录的统一标准。*
