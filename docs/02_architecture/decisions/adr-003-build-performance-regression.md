# ADR-003: Build Performance Regression and Modernization

- **Status**: Proposed
- **Date**: 2025-07-01
- **Deciders**: AI Assistant

## Context and Problem Statement

The project's historical documentation (`P1_Build_Performance_Analysis_Report.md`) claims that a severe build performance issue (10-20 minute builds) was solved, resulting in build times of approximately 1 minute for a clean build and ~3 seconds for subsequent builds.

However, a recent audit and verification attempt (`2025-07-01`) revealed this is no longer true. A clean build of the `:app` module took **9 minutes and 57 seconds**, indicating a severe performance regression.

This problem is a critical blocker, as it prevents efficient development and makes other verification tasks, such as running the full test suite, prohibitively slow.

## Investigation So Far

1.  **Initial Hypothesis:** The JVM heap size for the Gradle daemon had been reduced from the documented `8g` to `4g`.
2.  **Action Taken:** The `gradle.properties` file was corrected to restore `org.gradle.jvmargs` to `-Xmx8g`.
3.  **Result:** The build time improved from `9m 57s` to `5m 0s`.
4.  **Conclusion:** While memory allocation was a contributing factor, it is not the only cause. A deeper, more fundamental issue remains.

## Decision Drivers

*   **Developer Efficiency:** A 5-minute build cycle is unacceptable for modern development and destroys productivity.
*   **CI/CD Costs:** Long build times lead to expensive and slow CI/CD pipeline runs.
*   **Verification Blocker:** It is impossible to efficiently validate the project's test suite (P2 verification) with the current build times.
*   **Documentation Accuracy:** The "living" system must match the standards set in the documentation.

## Considered Options

### Option 1: Deep Dive with Gradle Profiler

*   **Description:** Use the official `gradle-profiler` tool to run a series of benchmark builds and generate detailed performance reports. This will provide a flame graph of task execution, identify bottlenecks, and give a precise breakdown of where time is being spent (configuration, task execution, etc.).
*   **Pros:** Data-driven, precise, the industry-standard tool for this exact problem.
*   **Cons:** Requires setup and analysis of the generated reports.

### Option 2: Manual Inspection and Incremental Changes

*   **Description:** Manually inspect all `build.gradle.kts` files, update dependencies, look for known anti-patterns, and try various optimizations one by one.
*   **Pros:** Might stumble upon a simple fix.
*   **Cons:** Time-consuming, error-prone, not guaranteed to find the root cause, lacks data to back up decisions.

### Option 3: Upgrade All Dependencies

*   **Description:** Assume the issue is with outdated dependencies. Perform a wholesale upgrade of all libraries, especially the Android Gradle Plugin (AGP) and Kotlin.
*   **Pros:** Might fix the issue if it's a known bug in a previous version. Keeps the project modern.
*   **Cons:** High risk. A "big bang" upgrade can introduce many breaking changes at once, making it difficult to identify which change caused a new problem.

## Decision Outcome

**Chosen Option:** Option 1, "Deep Dive with Gradle Profiler," is the only professional and reliable choice. It replaces guesswork with data, ensuring we find the true root cause and can verify the fix.

### Implementation Plan

1.  **Setup `gradle-profiler`:** Integrate the profiler into the project.
2.  **Define Scenarios:** Create profiling scenarios for:
    *   Clean build & assembly.
    *   Incremental build (a small code change).
    *   Running the test suite.
3.  **Run & Analyze:** Execute the profiler and analyze the generated flame graphs and reports to pinpoint the bottleneck(s).
4.  **Implement Fix:** Apply the targeted fix based on the analysis.
5.  **Re-profile & Verify:** Run the profiler again to confirm that the bottleneck has been eliminated and the build time meets the documented standard (<1 min clean build).
6.  **Update Documentation:** Update this ADR with the final findings and solution. 