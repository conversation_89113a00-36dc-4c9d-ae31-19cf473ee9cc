## 文档信息
- 文档标题: 📐 Tetris Feature Module: Architecture
- 文档版本: 1.0.0
- 创建日期: 2025-07-11
- 最后更新: 2025-07-11
- 文档状态: 正式发布
- 作者: Documentation Team

## 📖 变更历史
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0.0 | 2025-07-11 | 初始版本 | Documentation Team |

## 📚 目录



# 📐 Tetris Feature Module: Architecture

-   **Version**: 1.0
-   **Status**: Active
-   **Author**: Gemini Pro

## 1. Overview

This document details the internal architecture of the **Tetris Feature Module** (`feature/tetris`). This module is a self-contained unit responsible for all aspects of the Tetris game experience, from UI to core game logic.

The architecture is designed with a strong emphasis on **Separation of Concerns**, resulting in a clear, testable, and maintainable codebase.

## 2. Core Architectural Principles

-   **Interface/Implementation Separation**: The module is split into an `api` module and an `impl` module.
    -   `feature/tetris/api`: Defines the public contract (interfaces and data models) that other modules use to interact with the Tetris feature.
    -   `feature/tetris/impl`: Contains the concrete implementation details, which are hidden from the rest of the application.
-   **Unidirectional Data Flow**: The UI observes a state stream from the controller and sends user events back. The data flows in one direction, making state changes predictable.
-   **Layered Architecture**: The implementation itself is divided into three distinct layers, each with a specific responsibility.

## 3. Three-Layer Architecture

The `impl` module follows a clean, three-layered architecture: **Controller**, **Engine**, and **UI**.

```mermaid
graph TD
    subgraph "Other App Modules (e.g., app)"
        A[App Navigation]
    end

    subgraph "Tetris Feature Module"
        direction LR
        
        subgraph "API Layer (`:feature:tetris:api`)"
            B(TetrisApi)
            C(TetrisController Interface)
            D(TetrisGameState)
            B --> C
        end

        subgraph "Implementation Layer (`:feature:tetris:impl`)"
            direction TB
            subgraph "UI Layer"
                U[TetrisGameScreen.kt]
                V[TetrisBoard.kt]
                U --> V
            end
            
            subgraph "Controller Layer"
                F[TetrisControllerImpl]
            end

            subgraph "Engine Layer"
                G[TetrisEngineImpl]
            end
            
            F --> G
            U -- User Events --> F
            F -- GameState Flow --> U
        end
    end

    A -- Injects --> B
    B -- Provides --> F
```

### 3.1. API Layer (`:feature:tetris:api`)

-   **File**: `TetrisApi.kt`
-   **Purpose**: To define the public contract of the module.
-   **Key Components**:
    -   `TetrisApi`: The main Hilt entry point to get access to the feature.
    -   `TetrisController`: An interface defining the high-level actions that can be performed (e.g., `startNewGame`, `processAction`).
    -   `TetrisGameState`, `TetrisAction`, `TetrisEvent`: Immutable data classes and sealed interfaces that define the state, actions, and events of the game. This prevents other modules from needing to know about internal implementation details.

### 3.2. Controller Layer (`TetrisControllerImpl`)

-   **File**: `impl/controller/TetrisControllerImpl.kt`
-   **Purpose**: To act as the orchestrator and bridge between the UI and the core game logic.
-   **Responsibilities**:
    -   **State Management**: Owns and exposes the `gameState` as a `StateFlow<TetrisGameState>`.
    -   **Lifecycle Management**: Manages the main game loop (`gameLoopJob`) using Coroutines. It knows *when* to automatically move the piece down.
    -   **Concurrency**: Uses `CoroutineDispatchers` to ensure that game logic runs on a background thread (`Default`) and state updates are emitted on the `Main` thread.
    -   **Orchestration**: Receives high-level requests (like "start game" or "process move action") and calls the appropriate functions on the `TetrisEngine`.
    -   **External Communication**: Interacts with external dependencies like `GameRepository` to save statistics or `QLogger` for logging.
-   **Key Insight**: The `Controller` does **not** know the rules of Tetris. It only knows how to manage the flow of the game.

### 3.3. Engine Layer (`TetrisEngineImpl`)

-   **File**: `impl/engine/TetrisEngineImpl.kt`
-   **Purpose**: To be the "brains" of the game. It contains all the pure, low-level game rules and logic.
-   **Responsibilities**:
    -   **State Manipulation**: Contains the pure functions that take a `TetrisGameState` and a `TetrisAction` and produce a new `TetrisGameState`.
    -   **Rule Enforcement**: Implements all game rules:
        -   Collision detection (`isValidPosition`)
        -   Piece movement and rotation (including SRS - Super Rotation System)
        -   Line clearing and scoring logic
        -   Game over conditions (`isGameOver`)
    -   **Performance Optimization**: This layer is highly optimized. It uses an object pool (`TetrisPiecePool`) to reduce garbage collection and caches expensive calculations.
-   **Key Insight**: The `Engine` is stateless from a dependency perspective. It could theoretically be moved to a pure Kotlin module. It knows all the rules but has no concept of a "game loop" or threads.

## 4. Data Flow

The data flows in a single, predictable direction, following modern Android architecture patterns.

```mermaid
sequenceDiagram
    participant UI as UI Layer<br>(TetrisGameScreen)
    participant C as Controller<br>(TetrisControllerImpl)
    participant E as Engine<br>(TetrisEngineImpl)
    participant Repo as GameRepository

    UI->>+C: User presses 'Move Left' button<br>(onPieceAction(Move(LEFT)))
    C->>+E: processAction(Move(LEFT), currentState)
    E-->>-C: return Result.Success(newState)
    Note over C: Controller updates its internal<br>StateFlow with newState.
    C-->>UI: Emits new GameState
    Note over UI: Composable recomposes<br>to show moved piece.

    loop Game Loop (every second)
        C->>+C: Game loop fires
        C->>+E: processAction(Drop(soft), currentState)
        E-->>-C: return Result.Success(newState)
        C-->>UI: Emits new GameState
    end

    UI->>+C: User triggers 'End Game'
    C->>E: endGame(currentState)
    C->>+Repo: saveGame(gameRecord)
    Repo-->>-C: Return success
```

## 5. Directory Structure (`impl`)

-   `/animation`: Composable animations.
-   `/audio`: Sound effect management.
-   `/constants`: Hardcoded values and constants for the feature.
-   `/controller`: The high-level `TetrisControllerImpl`.
-   `/di`: Hilt dependency injection module.
-   `/engine`: The core `TetrisEngineImpl`.
-   `/game`: Game state models and logic.
-   `/input`: User input handling and gesture detection.
-   `/manager`: Higher-level managers (e.g., `PlayerSessionManager`).
-   `/ui`: Composable screens (`TetrisGameScreen`) and UI components (`TetrisBoard`).
-   `/utils`: Helper functions and extensions.

This granular structure ensures that each class has a clear and single responsibility, making the module easy to navigate and maintain. 