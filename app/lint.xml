<?xml version="1.0" encoding="UTF-8"?>
<lint>
    <!-- 企业级代码质量标准 - 只忽略必要的检查 -->

    <!-- 构建相关 - 暂时忽略，等待官方修复 -->
    <issue id="AndroidGradlePluginVersion" severity="ignore" />
    <issue id="GradleDependency" severity="ignore" />
    <issue id="UnsafeOptInUsageWarning" severity="ignore" />

    <!-- 图标相关 - 产品设计决策 -->
    <issue id="MonochromeLauncherIcon" severity="ignore" />

    <!-- 启用重要的代码质量检查 -->
    <issue id="ComposableNaming" severity="warning" />
    <issue id="ModifierParameter" severity="warning" />
    <issue id="UnusedResources" severity="warning" />
    <issue id="Typos" severity="warning" />
    <issue id="UseKtx" severity="warning" />
    <issue id="AutoboxingStateCreation" severity="warning" />

    <!-- 启用性能相关检查 -->
    <issue id="ObsoleteSdkInt" severity="error" />
    <issue id="UnnecessaryArrayInit" severity="warning" />

    <!-- 启用国际化检查 -->
    <issue id="DefaultLocale" severity="warning" />
    <issue id="ConstantLocale" severity="warning" />

    <!-- 启用架构检查 -->
    <issue id="ViewConstructor" severity="warning" />
    <issue id="UnusedAttribute" severity="warning" />
</lint>