# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# R8性能优化ProGuard规则
# 专为快速混淆和优化设计

# 基础配置
-ignorewarnings
-dontwarn **
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# 应用核心类
-keep class com.yu.questicle.QuesticleApplication { *; }
-keep class com.yu.questicle.MainActivity { *; }

# ViewModels
-keep class * extends androidx.lifecycle.ViewModel { *; }
-keep class * extends androidx.lifecycle.AndroidViewModel { *; }

# Compose核心
-keep class androidx.compose.runtime.** { *; }
-keep class androidx.compose.ui.** { *; }
-keep class androidx.compose.material3.** { *; }

# Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembers class kotlinx.coroutines.** { volatile <fields>; }

# Room Database
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *

# Hilt
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class * extends dagger.hilt.android.lifecycle.HiltViewModel { *; }

# Kotlin Serialization
-keepattributes *Annotation*
-keep class kotlinx.serialization.** { *; }

# 调试信息
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# R8优化（使用R8兼容的选项）
-allowaccessmodification
-repackageclasses

# 移除日志
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
}

# 项目特定类
-keep class com.yu.questicle.core.** { *; }
-keep class com.yu.questicle.data.** { *; }
-keep class com.yu.questicle.domain.** { *; }

# 网络
-keep class retrofit2.** { *; }
-keep class okhttp3.** { *; }
-keep class com.google.gson.** { *; }

# 忽略警告
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# 保持 Compose 相关类
-keep class androidx.compose.** { *; }
-keepclassmembers class androidx.compose.** { *; }

# Compose Navigation
-keep class * extends androidx.navigation.Navigator
-keepnames class androidx.navigation.fragment.FragmentNavigator$Destination

# Android API 35+ 特性支持
-keep class androidx.window.** { *; }
-dontwarn androidx.window.**

# 调试信息
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

