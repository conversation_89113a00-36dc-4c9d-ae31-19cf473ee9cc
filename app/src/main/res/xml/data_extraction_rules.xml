<?xml version="1.0" encoding="utf-8"?><!--
   Sample data extraction rules file; uncomment and customize as necessary.
   See https://developer.android.com/about/versions/12/backup-restore#xml-changes
   for details.
-->
<data-extraction-rules>
    <cloud-backup>
        <!-- 包含所有共享首选项 -->
        <include domain="sharedpref" path="."/>

        <!-- 排除敏感数据 -->
        <exclude domain="sharedpref" path="com.yu.questicle_preferences.xml"/>

        <!-- 包含游戏状态文件 -->
        <include domain="file" path="game_state.xml"/>
        <include domain="file" path="user_settings.xml"/>

        <!-- 包含数据库文件 -->
        <include domain="database" path="questicle.db"/>

        <!-- 包含所有文件 -->
        <include domain="file" path="."/>

        <!-- 排除缓存和临时文件 -->
        <exclude domain="file" path="cache/"/>
        <exclude domain="file" path="temp/"/>
        <exclude domain="file" path="no_backup/"/>
    </cloud-backup>

    <device-transfer>
        <!-- 包含所有共享首选项 -->
        <include domain="sharedpref" path="."/>

        <!-- 排除敏感数据 -->
        <exclude domain="sharedpref" path="com.yu.questicle_preferences.xml"/>

        <!-- 包含游戏状态文件 -->
        <include domain="file" path="game_state.xml"/>
        <include domain="file" path="user_settings.xml"/>

        <!-- 包含数据库文件 -->
        <include domain="database" path="questicle.db"/>

        <!-- 包含所有文件 -->
        <include domain="file" path="."/>

        <!-- 排除缓存和临时文件 -->
        <exclude domain="file" path="cache/"/>
        <exclude domain="file" path="temp/"/>
        <exclude domain="file" path="no_backup/"/>
    </device-transfer>
</data-extraction-rules>