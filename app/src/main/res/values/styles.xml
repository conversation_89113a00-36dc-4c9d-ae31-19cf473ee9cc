<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 游戏控制按钮样式 -->
    <style name="GameControlButton" parent="Widget.MaterialComponents.Button.TextButton" />

    <!-- 游戏分数文本样式 -->
    <style name="GameScoreText" parent="TextAppearance.MaterialComponents.Headline6" />

    <!-- 游戏对话框样式 -->
    <style name="GameDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="materialAlertDialogTitleTextStyle">@style/GameDialog.Title</item>
        <item name="materialAlertDialogBodyTextStyle">@style/GameDialog.Body</item>
        <item name="buttonBarPositiveButtonStyle">@style/GameDialog.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/GameDialog.Button</item>
    </style>

    <!-- 对话框标题样式 -->
    <style name="GameDialog.Title" parent="TextAppearance.MaterialComponents.Headline6" />

    <!-- 对话框内容样式 -->
    <style name="GameDialog.Body" parent="TextAppearance.MaterialComponents.Body1" />

    <!-- 对话框按钮样式 -->
    <style name="GameDialog.Button" parent="Widget.MaterialComponents.Button.TextButton.Dialog" />
</resources>
