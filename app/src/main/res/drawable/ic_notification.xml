<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorOnSurface">
    
    <!-- Simple tetris block icon for notifications -->
    <group android:scaleX="0.8" android:scaleY="0.8" android:pivotX="12" android:pivotY="12">
        
        <!-- T-piece simplified -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M8,6 L16,6 L16,8 L14,8 L14,12 L10,12 L10,8 L8,8 Z" />
        
        <!-- O-piece -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M6,10 L10,10 L10,14 L6,14 Z" />
        
        <!-- I-piece -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M14,10 L16,10 L16,18 L14,18 Z" />
        
        <!-- L-piece -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M18,14 L20,14 L20,18 L16,18 L16,16 L18,16 Z" />
        
    </group>
    
</vector>
