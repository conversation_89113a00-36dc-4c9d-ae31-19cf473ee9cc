<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Background circle with modern gradient -->
    <group android:scaleX="0.85" android:scaleY="0.85" android:pivotX="54" android:pivotY="54">
        <path
            android:fillColor="#667EEA"
            android:pathData="M54,8 C78.3,8 98,27.7 98,52 C98,76.3 78.3,96 54,96 C29.7,96 10,76.3 10,52 C10,27.7 29.7,8 54,8 Z" />

        <!-- Inner gradient circle -->
        <path
            android:fillColor="#764BA2"
            android:pathData="M54,16 C73.88,16 90,32.12 90,52 C90,71.88 73.88,88 54,88 C34.12,88 18,71.88 18,52 C18,32.12 34.12,16 54,16 Z" />
    </group>

    <!-- Modern Tetris pieces forming "Q" -->
    <group android:scaleX="0.7" android:scaleY="0.7" android:pivotX="54" android:pivotY="54">

        <!-- T-piece (cyan) - top horizontal -->
        <group android:translateX="28" android:translateY="28">
            <path
                android:fillColor="#00D4FF"
                android:pathData="M8,0 L24,0 L24,8 L32,8 L32,16 L24,16 L24,8 L16,8 L16,16 L8,16 L8,8 L0,8 L0,0 L8,0 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.3"
                android:pathData="M8,0 L24,0 L24,2 L16,2 L16,8 L14,8 L14,2 L8,2 Z" />
        </group>

        <!-- O-piece (yellow) - center -->
        <group android:translateX="38" android:translateY="42">
            <path
                android:fillColor="#FFD700"
                android:pathData="M0,0 L16,0 L16,16 L0,16 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.3"
                android:pathData="M0,0 L16,0 L16,2 L2,2 L2,16 L0,16 Z" />
        </group>

        <!-- I-piece (light blue) - vertical left -->
        <group android:translateX="22" android:translateY="38">
            <path
                android:fillColor="#00BFFF"
                android:pathData="M0,0 L8,0 L8,32 L0,32 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.3"
                android:pathData="M0,0 L8,0 L8,2 L2,2 L2,32 L0,32 Z" />
        </group>

        <!-- L-piece (orange) - bottom right -->
        <group android:translateX="54" android:translateY="58">
            <path
                android:fillColor="#FF8C00"
                android:pathData="M0,0 L8,0 L8,16 L16,16 L16,24 L0,24 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.3"
                android:pathData="M0,0 L8,0 L8,2 L2,2 L2,24 L0,24 Z" />
        </group>

        <!-- S-piece (green) - decorative -->
        <group android:translateX="62" android:translateY="38">
            <path
                android:fillColor="#32CD32"
                android:pathData="M0,8 L8,8 L8,0 L16,0 L16,8 L24,8 L24,16 L16,16 L16,24 L8,24 L8,16 L0,16 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.3"
                android:pathData="M8,0 L16,0 L16,2 L10,2 L10,8 L8,8 Z" />
        </group>

    </group>

    <!-- Subtle highlight effect -->
    <group android:scaleX="0.75" android:scaleY="0.75" android:pivotX="54" android:pivotY="54">
        <path
            android:fillColor="#FFFFFF"
            android:fillAlpha="0.15"
            android:pathData="M54,12 C75.54,12 93,29.46 93,51 C93,72.54 75.54,90 54,90 C32.46,90 15,72.54 15,51 C15,29.46 32.46,12 54,12 Z" />
    </group>

</vector>