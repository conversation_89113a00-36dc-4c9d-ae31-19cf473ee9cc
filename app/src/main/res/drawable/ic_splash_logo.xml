<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
    
    <!-- Large version for splash screen -->
    <group android:scaleX="1.2" android:scaleY="1.2" android:pivotX="100" android:pivotY="100">
        
        <!-- Background circle -->
        <path
            android:fillColor="#667EEA"
            android:pathData="M100,20 C144.18,20 180,55.82 180,100 C180,144.18 144.18,180 100,180 C55.82,180 20,144.18 20,100 C20,55.82 55.82,20 100,20 Z" />
        
        <!-- Inner circle -->
        <path
            android:fillColor="#764BA2"
            android:pathData="M100,35 C135.9,35 165,64.1 165,100 C165,135.9 135.9,165 100,165 C64.1,165 35,135.9 35,100 C35,64.1 64.1,35 100,35 Z" />
        
        <!-- Tetris pieces forming "Q" -->
        
        <!-- T-piece (cyan) -->
        <group android:translateX="60" android:translateY="60">
            <path
                android:fillColor="#00D4FF"
                android:pathData="M0,0 L12,0 L12,4 L16,4 L16,8 L20,8 L20,12 L16,12 L16,8 L12,8 L12,12 L8,12 L8,8 L4,8 L4,12 L0,12 L0,8 L4,8 L4,4 L8,4 L8,0 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.4"
                android:pathData="M0,0 L12,0 L12,2 L8,2 L8,4 L4,4 L4,2 L0,2 Z" />
        </group>
        
        <!-- O-piece (yellow) -->
        <group android:translateX="85" android:translateY="85">
            <path
                android:fillColor="#FFD700"
                android:pathData="M0,0 L16,0 L16,16 L0,16 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.4"
                android:pathData="M0,0 L16,0 L16,2 L2,2 L2,16 L0,16 Z" />
        </group>
        
        <!-- I-piece (light blue) -->
        <group android:translateX="70" android:translateY="75">
            <path
                android:fillColor="#00BFFF"
                android:pathData="M0,0 L8,0 L8,40 L0,40 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.4"
                android:pathData="M0,0 L8,0 L8,2 L2,2 L2,40 L0,40 Z" />
        </group>
        
        <!-- L-piece (orange) -->
        <group android:translateX="110" android:translateY="110">
            <path
                android:fillColor="#FF8C00"
                android:pathData="M0,0 L8,0 L8,20 L20,20 L20,28 L0,28 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.4"
                android:pathData="M0,0 L8,0 L8,2 L2,2 L2,28 L0,28 Z" />
        </group>
        
        <!-- S-piece (green) -->
        <group android:translateX="120" android:translateY="75">
            <path
                android:fillColor="#32CD32"
                android:pathData="M0,8 L8,8 L8,0 L16,0 L16,8 L24,8 L24,16 L16,16 L16,24 L8,24 L8,16 L0,16 Z" />
            <path
                android:fillColor="#FFFFFF"
                android:fillAlpha="0.4"
                android:pathData="M8,0 L16,0 L16,2 L10,2 L10,8 L8,8 Z" />
        </group>
        
    </group>
    
    <!-- Highlight effect -->
    <path
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.2"
        android:pathData="M100,25 C140.87,25 174,58.13 174,99 C174,139.87 140.87,173 100,173 C59.13,173 26,139.87 26,99 C26,58.13 59.13,25 100,25 Z" />
    
</vector>
