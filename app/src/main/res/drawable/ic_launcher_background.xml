<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">

    <!-- Modern gradient background -->
    <path
        android:fillColor="#1A1A2E"
        android:pathData="M0,0h108v108h-108z" />

    <!-- Subtle pattern overlay -->
    <path
        android:fillColor="#16213E"
        android:fillAlpha="0.8"
        android:pathData="M0,0 L108,0 L108,108 L0,108 Z" />

    <!-- Grid pattern for tetris theme -->
    <group android:strokeColor="#0F3460" android:strokeWidth="0.5" android:strokeAlpha="0.3">
        <path android:pathData="M0,12 L108,12" />
        <path android:pathData="M0,24 L108,24" />
        <path android:pathData="M0,36 L108,36" />
        <path android:pathData="M0,48 L108,48" />
        <path android:pathData="M0,60 L108,60" />
        <path android:pathData="M0,72 L108,72" />
        <path android:pathData="M0,84 L108,84" />
        <path android:pathData="M0,96 L108,96" />

        <path android:pathData="M12,0 L12,108" />
        <path android:pathData="M24,0 L24,108" />
        <path android:pathData="M36,0 L36,108" />
        <path android:pathData="M48,0 L48,108" />
        <path android:pathData="M60,0 L60,108" />
        <path android:pathData="M72,0 L72,108" />
        <path android:pathData="M84,0 L84,108" />
        <path android:pathData="M96,0 L96,108" />
    </group>

</vector>
