<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 主题颜色 - 暗色配色 -->
    <color name="color_primary">#2196F3</color>
    <color name="color_primary_variant">#1976D2</color>
    <color name="color_secondary">#4CAF50</color>
    <color name="color_secondary_variant">#388E3C</color>
    <color name="color_background">#121212</color>
    <color name="color_surface">#1E1E1E</color>
    <color name="color_on_primary">#000000</color>
    <color name="color_on_secondary">#000000</color>
    <color name="color_on_background">#FFFFFF</color>
    <color name="color_on_surface">#FFFFFF</color>
    
    <!-- 俄罗斯方块暗色主题颜色 - 基于统一颜色系统 -->
    <color name="tetris_i_block">#0097A7</color>  <!-- 暗色变体：深青色 -->
    <color name="tetris_j_block">#1565C0</color>  <!-- 暗色变体：深蓝色 -->
    <color name="tetris_l_block">#E65100</color>  <!-- 暗色变体：深橙色 -->
    <color name="tetris_o_block">#FF8F00</color>  <!-- 暗色变体：深黄色 -->
    <color name="tetris_s_block">#388E3C</color>  <!-- 暗色变体：深绿色 -->
    <color name="tetris_t_block">#9C27B0</color>  <!-- 暗色变体：深紫色 -->
    <color name="tetris_z_block">#B71C1C</color>  <!-- 暗色变体：深红色 -->

    <!-- 状态颜色 - 暗色主题优化 -->
    <color name="success_green">#81C784</color>
    <color name="warning_amber">#FFD54F</color>
    <color name="error_red">#EF5350</color>
    <color name="info_blue">#42A5F5</color>
    
    <!-- 2025年低光模式专用颜色 -->
    <color name="low_light_primary_dark">#2196F3B3</color>
    <color name="low_light_surface_dark">#0F0F0F</color>
    <color name="low_light_on_surface_dark">#E0E0E0CC</color>
</resources>

