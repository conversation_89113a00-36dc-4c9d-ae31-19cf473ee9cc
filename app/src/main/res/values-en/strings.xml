<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Questicle</string>
    <string name="next_block">Next</string>

    <!-- Game Controls -->
    <string name="control_rotate">Rotate</string>
    <string name="control_left">Move Left</string>
    <string name="control_right">Move Right</string>
    <string name="control_down">Drop Fast</string>
    <string name="control_start">Start Game</string>
    <string name="control_pause">Pause Game</string>



    <!-- Dialog Messages -->
    <string name="game_over">Game Over</string>
    <string name="dialog_game_over_title">Game Over</string>
    <string name="dialog_restart">Restart</string>
    <string name="dialog_game_over">"Game over "</string>
    <string name="dialog_quit">Quit</string>
    <string name="dialog_resume">Resume</string>

    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="settings_sound">Sound Effects</string>
    <string name="settings_vibration">Vibration</string>
    <string name="settings_dark_mode">Dark Mode</string>

    <!-- Tutorial -->
    <string name="tutorial_title">How to Play</string>
    <string name="tutorial_content">• Swipe left/right: Move block\n• Swipe down: Speed up\n• Tap rotate: Rotate block\n• Tap drop: Quick drop\n• More lines = Higher score\n• Higher level = Faster drop</string>
    <string name="tutorial_start_game">Start Game</string>
    <string name="level">Level</string>
    <string name="lines">Lines</string>
    <string name="score">Score</string>

    <!-- Game Controls -->
    <string name="game_control_left">Move Left: ←</string>
    <string name="game_control_right">Move Right: →</string>
    <string name="game_control_down">Move Down: ↓</string>
    <string name="game_control_rotate">Rotate: ↑</string>
    <string name="game_control_hard_drop">Hard Drop: Space</string>
    <string name="game_control_hold">Hold: C</string>
    <string name="game_control_pause">Pause: P</string>

    <!-- Game Status -->
    <string name="game_status_ready">Ready</string>
    <string name="game_status_playing">Playing</string>
    <string name="game_status_paused">Paused</string>
    <string name="game_status_game_over">Game Over</string>

    <!-- Game Info -->
    <string name="game_score">Score: %1$d</string>
    <string name="game_high_score">High Score: %1$d</string>
    <string name="game_level">Level: %1$d</string>
    <string name="game_lines">Lines: %1$d</string>
    <string name="game_next">Next</string>
    <string name="game_hold">Hold</string>
    <string name="dialog_game_over_message">Your score: %1$d\n%2$s</string>
    <string name="dialog_game_over_new_high_score">New High Score!</string>
    <string name="dialog_game_over_restart">Restart</string>
    <string name="dialog_settings_title">Game Settings</string>
    <string name="dialog_settings_sound">Sound</string>
    <string name="dialog_settings_vibration">Vibration</string>
    <string name="dialog_settings_ghost_piece">Show Ghost Piece</string>
    <string name="dialog_settings_hard_drop">Enable Hard Drop</string>
    <string name="dialog_settings_hold">Enable Hold</string>
    <string name="dialog_settings_initial_level">Initial Level</string>
    <string name="dialog_settings_speed_multiplier">Speed Multiplier</string>
    <string name="nav_home">Home</string>
    <string name="nav_games">Games</string>
    <string name="nav_settings">Settings</string>
    <string name="tutorial_tetris_title">Tetris</string>
    <string name="tutorial_tetris_content">1. Use arrow keys to move and rotate\n2. Space for hard drop\n3. C to hold current piece\n4. P to pause game\n5. More lines = higher score\n6. Higher level = faster drop</string>
</resources>
