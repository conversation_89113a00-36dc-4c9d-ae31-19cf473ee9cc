package com.yu.questicle.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.feature.home.api.HomeDestinations
import com.yu.questicle.feature.settings.api.SettingsDestinations
import com.yu.questicle.feature.tetris.api.TetrisDestinations
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.feature.user.api.UserDestinations

/**
 * Main navigation host for the application
 */
@Composable
fun QuesticleNavHost(
    navController: NavHostController,
    startDestination: String,
    modifier: Modifier = Modifier
) {
    val logger: QLogger = logger("QuesticleNavHost")
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier
    ) {
        // Home screen
        composable(HomeDestinations.HOME_SCREEN) {
            val homeViewModel: com.yu.questicle.feature.home.impl.ui.HomeScreenViewModel = hiltViewModel()

            com.yu.questicle.feature.home.impl.ui.HomeScreen(
                modifier = Modifier,
                controller = homeViewModel.homeController,
                onNavigateToGame = { gameType ->
                    when (gameType) {
                        GameType.TETRIS -> navController.navigate(TetrisDestinations.GAME_SCREEN)
                        GameType.PUZZLE -> {
                            logger.i("导航到拼图游戏")
                            // 暂时导航到俄罗斯方块，后续可以实现专门的拼图游戏
                            navController.navigate(TetrisDestinations.GAME_SCREEN)
                        }
                        GameType.ACTION -> {
                            logger.i("导航到动作游戏")
                            // 暂时导航到俄罗斯方块，后续可以实现专门的动作游戏
                            navController.navigate(TetrisDestinations.GAME_SCREEN)
                        }
                        else -> {
                            logger.w("尝试导航到未支持的游戏类型", mapOf(
                                "gameType" to gameType.name
                            ))
                            // 对于未知游戏类型，显示提示信息
                            // 这里可以导航到一个通用的"即将推出"页面
                            logger.i("显示即将推出提示")
                        }
                    }
                },
                onNavigateToProfile = {
                    navController.navigate(HomeDestinations.PROFILE_SCREEN)
                },
                onNavigateToSettings = {
                    navController.navigate(SettingsDestinations.SETTINGS_SCREEN)
                }
            )
        }

        // Tetris game screen
        composable(TetrisDestinations.GAME_SCREEN) {
            val tetrisViewModel: com.yu.questicle.feature.tetris.impl.ui.TetrisViewModel = hiltViewModel()

            com.yu.questicle.feature.tetris.impl.ui.TetrisGameScreen(
                modifier = Modifier,
                controller = tetrisViewModel.tetrisController,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onGameComplete = { score ->
                    try {
                        logger.withContext(mapOf(
                            "score" to score
                        )).i("游戏结束，处理结果")
                        
                        // 这里可以处理游戏结果，比如保存到数据库、显示成就等
                        // 当前先返回主页
                        navController.popBackStack()
                    } catch (e: Exception) {
                        logger.e(e, "游戏完成处理失败")
                        // 即使处理失败，也要返回主页
                        navController.popBackStack()
                    }
                }
            )
        }

        // Settings screen
        composable(SettingsDestinations.SETTINGS_SCREEN) {
            val settingsViewModel: com.yu.questicle.feature.settings.impl.ui.SettingsScreenViewModel = hiltViewModel()

            com.yu.questicle.feature.settings.impl.ui.SettingsScreen(
                modifier = Modifier,
                controller = settingsViewModel.settingsController,
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Profile screen
        composable(HomeDestinations.PROFILE_SCREEN) {
            val userViewModel: com.yu.questicle.feature.user.impl.ui.UserViewModel = hiltViewModel()

            com.yu.questicle.feature.user.impl.ui.ProfileScreen(
                modifier = Modifier,
                controller = userViewModel.userController,
                onNavigateToEditProfile = {
                    logger.i("用户点击编辑个人资料")
                    navController.navigate(UserDestinations.EDIT_PROFILE_SCREEN)
                },
                onNavigateToSettings = {
                    navController.navigate(SettingsDestinations.SETTINGS_SCREEN)
                },
                onNavigateToLogin = {
                    logger.i("用户点击立即登录")
                    navController.navigate(UserDestinations.LOGIN_SCREEN)
                },
                onNavigateToRegister = {
                    logger.i("用户点击立即注册")
                    navController.navigate(UserDestinations.REGISTER_SCREEN)
                },
                onNavigateToGuestMode = {
                    logger.i("用户选择游客模式")
                    // 执行游客登录
                    userViewModel.loginAsGuest()
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Login screen
        composable(UserDestinations.LOGIN_SCREEN) {
            val userViewModel: com.yu.questicle.feature.user.impl.ui.UserViewModel = hiltViewModel()

            com.yu.questicle.feature.user.impl.ui.LoginScreen(
                modifier = Modifier,
                controller = userViewModel.userController,
                onNavigateToRegister = {
                    navController.navigate(UserDestinations.REGISTER_SCREEN)
                },
                onNavigateToHome = {
                    try {
                        logger.i("从登录页面导航到主页")
                        navController.navigate(HomeDestinations.HOME_SCREEN) {
                            popUpTo(navController.graph.startDestinationId) {
                                inclusive = true
                            }
                            // 避免重复导航
                            launchSingleTop = true
                            // 恢复状态
                            restoreState = true
                        }
                    } catch (e: Exception) {
                        logger.e(e, "导航到主页失败")
                        // 如果导航失败，尝试简单导航
                        try {
                            navController.navigate(HomeDestinations.HOME_SCREEN)
                        } catch (fallbackException: Exception) {
                            logger.e(fallbackException, "简单导航也失败")
                        }
                    }
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Register screen
        composable(UserDestinations.REGISTER_SCREEN) {
            val userViewModel: com.yu.questicle.feature.user.impl.ui.UserViewModel = hiltViewModel()

            com.yu.questicle.feature.user.impl.ui.RegisterScreen(
                modifier = Modifier,
                controller = userViewModel.userController,
                onNavigateToLogin = {
                    navController.navigate(UserDestinations.LOGIN_SCREEN) {
                        popUpTo(UserDestinations.REGISTER_SCREEN) {
                            inclusive = true
                        }
                    }
                },
                onNavigateToHome = {
                    try {
                        logger.i("从注册页面导航到主页")
                        navController.navigate(HomeDestinations.HOME_SCREEN) {
                            popUpTo(navController.graph.startDestinationId) {
                                inclusive = true
                            }
                            // 避免重复导航
                            launchSingleTop = true
                            // 恢复状态
                            restoreState = true
                        }
                    } catch (e: Exception) {
                        logger.e(e, "从注册页面导航到主页失败")
                        // 如果导航失败，尝试简单导航
                        try {
                            navController.navigate(HomeDestinations.HOME_SCREEN)
                        } catch (fallbackException: Exception) {
                            logger.e(fallbackException, "简单导航也失败")
                        }
                    }
                },
                onNavigateBack = {
                    navController.popBackStack()
                }
            )
        }

        // Change Password screen - 新增
        composable(UserDestinations.CHANGE_PASSWORD_SCREEN) {
            val localPasswordViewModel: com.yu.questicle.feature.user.impl.ui.LocalPasswordViewModel = hiltViewModel()
            
            com.yu.questicle.feature.user.impl.ui.ChangePasswordScreen(
                controller = localPasswordViewModel.localPasswordController,
                onNavigateBack = { navController.popBackStack() },
                onPasswordChangeSuccess = {
                    // 密码修改成功后的处理
                    navController.popBackStack()
                }
            )
        }

        // Edit Profile screen - 新增
        composable(UserDestinations.EDIT_PROFILE_SCREEN) {
            val profileViewModel: com.yu.questicle.feature.user.impl.ui.ProfileViewModel = hiltViewModel()
            
            com.yu.questicle.feature.user.impl.ui.EditProfileScreen(
                controller = profileViewModel.profileController,
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Statistics screen
        composable(HomeDestinations.STATISTICS_SCREEN) {
            PlaceholderScreen(
                title = "游戏统计",
                message = "游戏统计功能正在开发中...",
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Achievements screen
        composable(HomeDestinations.ACHIEVEMENTS_SCREEN) {
            PlaceholderScreen(
                title = "成就系统",
                message = "成就系统功能正在开发中...",
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Tetris settings
        composable(TetrisDestinations.SETTINGS_SCREEN) {
            PlaceholderScreen(
                title = "俄罗斯方块设置",
                message = "俄罗斯方块专用设置正在开发中...",
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Tetris statistics
        composable(TetrisDestinations.STATISTICS_SCREEN) {
            PlaceholderScreen(
                title = "俄罗斯方块统计",
                message = "俄罗斯方块统计功能正在开发中...",
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // Tetris leaderboard
        composable(TetrisDestinations.LEADERBOARD_SCREEN) {
            PlaceholderScreen(
                title = "排行榜",
                message = "排行榜功能正在开发中...",
                onNavigateBack = { navController.popBackStack() }
            )
        }
    }
}

/**
 * 占位符屏幕，用于尚未实现的功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PlaceholderScreen(
    title: String,
    message: String,
    onNavigateBack: () -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                Button(onClick = onNavigateBack) {
                    Text("返回")
                }
            }
        }
    }
}
