package com.yu.questicle

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * Main application class for Questicle
 */
@HiltAndroidApp
class QuesticleApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize application-level components
        initializeLogging()
        initializeAnalytics()
        initializeCrashReporting()
    }
    
    private fun initializeLogging() {
        // 日志框架已通过Hilt自动初始化
        // QLogger系统在core.common模块中已配置
        if (BuildConfig.DEBUG) {
            // 开发环境启用详细日志
            android.util.Log.d("QuesticleApp", "日志系统初始化完成")
        }
    }
    
    private fun initializeAnalytics() {
        // 分析框架初始化
        // 在生产环境中可以集成Firebase Analytics或其他分析工具
        if (BuildConfig.DEBUG) {
            android.util.Log.d("QuesticleApp", "分析系统初始化完成（开发模式）")
        } else {
            // 生产环境分析初始化
            android.util.Log.i("QuesticleApp", "分析系统初始化完成")
        }
    }
    
    private fun initializeCrashReporting() {
        // 崩溃报告初始化
        // 在生产环境中可以集成Crashlytics或其他崩溃报告工具
        try {
            if (BuildConfig.DEBUG) {
                android.util.Log.d("QuesticleApp", "崩溃报告系统初始化完成（开发模式）")
            } else {
                // 生产环境崩溃报告初始化
                android.util.Log.i("QuesticleApp", "崩溃报告系统初始化完成")
            }
        } catch (e: Exception) {
            android.util.Log.e("QuesticleApp", "崩溃报告系统初始化失败", e)
        }
    }
}
