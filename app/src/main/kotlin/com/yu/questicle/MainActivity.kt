package com.yu.questicle

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.navigation.compose.rememberNavController
import com.yu.questicle.core.designsystem.theme.QuesticleTheme
import com.yu.questicle.navigation.QuesticleNavHost
import com.yu.questicle.ui.splash.SplashScreen
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.feature.home.api.HomeDestinations
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main activity for Questicle application
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val logger: QLogger = logger("MainActivity")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            logger.i("MainActivity onCreate 开始")

            // 配置窗口
            setupWindow()

            setContent {
                QuesticleTheme {
                    var showSplash by remember { mutableStateOf(true) }

                    if (showSplash) {
                        SplashScreen(
                            onSplashFinished = {
                                try {
                                    logger.i("启动画面完成，切换到主界面")
                                    showSplash = false
                                } catch (e: Exception) {
                                    logger.e(e, "切换到主界面异常")
                                    showSplash = false // 确保即使异常也能切换
                                }
                            }
                        )
                    } else {
                        Surface(
                            modifier = Modifier.fillMaxSize(),
                            color = MaterialTheme.colorScheme.background
                        ) {
                            val navController = rememberNavController()
                            QuesticleNavHost(
                                navController = navController,
                                startDestination = HomeDestinations.HOME_SCREEN
                            )
                        }
                    }
                }
            }

            logger.i("MainActivity onCreate 完成")
        } catch (e: Exception) {
            logger.e(e, "MainActivity onCreate 异常")
            // 确保即使出现异常也能正常显示界面
            setContent {
                QuesticleTheme {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        val navController = rememberNavController()
                        QuesticleNavHost(
                            navController = navController,
                            startDestination = HomeDestinations.HOME_SCREEN
                        )
                    }
                }
            }
        }
    }

    private fun setupWindow() {
        try {
            // 启用边到边显示
            enableEdgeToEdge()

            // 配置窗口兼容性
            WindowCompat.setDecorFitsSystemWindows(window, false)

            // 配置状态栏和导航栏
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

            logger.d("窗口配置完成")
        } catch (e: Exception) {
            logger.e(e, "窗口配置异常")
            // 如果窗口配置失败，使用基本配置
            try {
                enableEdgeToEdge()
            } catch (fallbackException: Exception) {
                logger.e(fallbackException, "基本窗口配置也失败")
            }
        }
    }

    override fun onStart() {
        super.onStart()
        logger.d("MainActivity onStart")
    }

    override fun onResume() {
        super.onResume()
        logger.d("MainActivity onResume")
    }

    override fun onPause() {
        super.onPause()
        logger.d("MainActivity onPause")
    }

    override fun onStop() {
        super.onStop()
        logger.d("MainActivity onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        logger.d("MainActivity onDestroy")
    }
}
// Test incremental build - Thu Jul 10 20:07:55 CST 2025
