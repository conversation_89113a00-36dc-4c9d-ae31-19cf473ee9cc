package com.yu.questicle.ui.features.tetris.components

import androidx.compose.ui.test.*
import de.mannodermaus.junit5.compose.createComposeExtension
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.yu.questicle.R
import com.yu.questicle.core.games.tetris.model.Block
import com.yu.questicle.core.games.tetris.model.BlockType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class GameInfoAreaTest {
    @JvmField
    @RegisterExtension
    val composeTestRule = createComposeExtension()

    @Test
    fun gameInfoArea_displaysCorrectInfo() {
        // Given
        val score = 100
        val level = 2
        val linesCleared = 5
        val nextBlock = Block(
            type = BlockType.O,
            shape = arrayOf(
                intArrayOf(1, 1),
                intArrayOf(1, 1)
            ),
            x = 0,
            y = 0
        )

        // When
        composeTestRule.setContent {
            GameInfoArea(
                score = score,
                level = level,
                linesCleared = linesCleared,
                nextBlock = nextBlock
            )
        }

        // Then
        composeTestRule.onNodeWithText("100").assertExists()
        composeTestRule.onNodeWithText("2").assertExists()
        composeTestRule.onNodeWithText("5").assertExists()
        composeTestRule.onNodeWithText("俄罗斯方块").assertExists()
        composeTestRule.onNodeWithText("下一个").assertExists()
    }

    @Test
    fun gameInfoArea_withZeroValues_displaysCorrectly() {
        // Given
        val score = 0
        val level = 1
        val linesCleared = 0

        // When
        composeTestRule.setContent {
            GameInfoArea(
                score = score,
                level = level,
                linesCleared = linesCleared,
                nextBlock = null
            )
        }

        // Then
        composeTestRule.onNodeWithText("0").assertExists()
        composeTestRule.onNodeWithText("1").assertExists()
        composeTestRule.onNodeWithText("俄罗斯方块").assertExists()
        composeTestRule.onNodeWithText("下一个").assertExists()
    }

    @Test
    fun gameInfoArea_withoutNextBlock_displaysCorrectly() {
        // Given
        val score = 100
        val level = 2
        val linesCleared = 5

        // When
        composeTestRule.setContent {
            GameInfoArea(
                score = score,
                level = level,
                linesCleared = linesCleared,
                nextBlock = null
            )
        }

        // Then
        composeTestRule.onNodeWithText("100").assertExists()
        composeTestRule.onNodeWithText("2").assertExists()
        composeTestRule.onNodeWithText("5").assertExists()
        composeTestRule.onNodeWithText("俄罗斯方块").assertExists()
        composeTestRule.onNodeWithText("下一个").assertExists()
    }

    @Test
    fun gameInfoArea_withLargeValues_displaysCorrectly() {
        // Given
        val score = 999999
        val level = 99
        val linesCleared = 999

        // When
        composeTestRule.setContent {
            GameInfoArea(
                score = score,
                level = level,
                linesCleared = linesCleared,
                nextBlock = null
            )
        }

        // Then
        composeTestRule.onNodeWithText("999999").assertExists()
        composeTestRule.onNodeWithText("99").assertExists()
        composeTestRule.onNodeWithText("999").assertExists()
    }

    @Test
    fun gameInfoArea_withDifferentNextBlockShapes_displaysCorrectly() {
        // Given
        val blockShapes = listOf(
            arrayOf(
                intArrayOf(1, 1),
                intArrayOf(1, 1)
            ),
            arrayOf(
                intArrayOf(1, 1, 1),
                intArrayOf(0, 1, 0)
            ),
            arrayOf(
                intArrayOf(1, 1, 1, 1)
            )
        )

        // When & Then
        blockShapes.forEach { shape ->
            val nextBlock = Block(
                type = BlockType.I,
                shape = shape,
                x = 0,
                y = 0
            )
            composeTestRule.setContent {
                GameInfoArea(
                    score = 100,
                    level = 1,
                    linesCleared = 0,
                    nextBlock = nextBlock
                )
            }
            composeTestRule.onNodeWithText("下一个").assertExists()
        }
    }
} 