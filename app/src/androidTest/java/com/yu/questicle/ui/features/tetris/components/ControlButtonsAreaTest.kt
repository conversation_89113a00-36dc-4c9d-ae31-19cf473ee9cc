package com.yu.questicle.ui.features.tetris.components

import androidx.compose.ui.test.*
import de.mannodermaus.junit5.compose.createComposeExtension
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.yu.questicle.ui.theme.QuesticleTheme
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.RegisterExtension
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class ControlButtonsAreaTest {

    @JvmField
    @RegisterExtension
    val composeTestRule = createComposeExtension()

    @Test
    fun controlButtonsArea_displaysAllButtons() {
        var leftClicked = false
        var rightClicked = false
        var downClicked = false
        var rotateClicked = false
        var hardDropClicked = false
        var holdClicked = false
        var pauseClicked = false

        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = { leftClicked = true },
                    onMoveRight = { rightClicked = true },
                    onMoveDown = { downClicked = true },
                    onRotate = { rotateClicked = true },
                    onHardDrop = { hardDropClicked = true },
                    onHold = { holdClicked = true },
                    onPause = { pauseClicked = true },
                    isGameRunning = true
                )
            }
        }

        // 验证所有按钮都存在
        composeTestRule.onNodeWithContentDescription("左移").assertExists()
        composeTestRule.onNodeWithContentDescription("右移").assertExists()
        composeTestRule.onNodeWithContentDescription("软降").assertExists()
        composeTestRule.onNodeWithContentDescription("旋转").assertExists()
        composeTestRule.onNodeWithContentDescription("硬降").assertExists()
        composeTestRule.onNodeWithContentDescription("暂存").assertExists()
        composeTestRule.onNodeWithContentDescription("暂停游戏").assertExists()
    }

    @Test
    fun controlButtonsArea_buttonsClickable_whenGameRunning() {
        var leftClicked = false
        var rightClicked = false
        var downClicked = false
        var rotateClicked = false

        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = { leftClicked = true },
                    onMoveRight = { rightClicked = true },
                    onMoveDown = { downClicked = true },
                    onRotate = { rotateClicked = true },
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true
                )
            }
        }

        // 测试按钮点击
        composeTestRule.onNodeWithContentDescription("左移").performClick()
        assert(leftClicked)

        composeTestRule.onNodeWithContentDescription("右移").performClick()
        assert(rightClicked)

        composeTestRule.onNodeWithContentDescription("软降").performClick()
        assert(downClicked)

        composeTestRule.onNodeWithContentDescription("旋转").performClick()
        assert(rotateClicked)
    }

    @Test
    fun controlButtonsArea_buttonsDisabled_whenGamePaused() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = false
                )
            }
        }

        // 验证游戏按钮被禁用
        composeTestRule.onNodeWithContentDescription("左移").assertIsNotEnabled()
        composeTestRule.onNodeWithContentDescription("右移").assertIsNotEnabled()
        composeTestRule.onNodeWithContentDescription("软降").assertIsNotEnabled()
        composeTestRule.onNodeWithContentDescription("旋转").assertIsNotEnabled()
        composeTestRule.onNodeWithContentDescription("硬降").assertIsNotEnabled()
        composeTestRule.onNodeWithContentDescription("暂存").assertIsNotEnabled()
        
        // 暂停按钮应该始终可用
        composeTestRule.onNodeWithContentDescription("继续游戏").assertIsEnabled()
    }

    @Test
    fun controlButtonsArea_showsCorrectPauseButton() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true
                )
            }
        }

        // 游戏运行时显示暂停按钮
        composeTestRule.onNodeWithContentDescription("暂停游戏").assertExists()
    }

    @Test
    fun controlButtonsArea_showsCorrectResumeButton() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = false
                )
            }
        }

        // 游戏暂停时显示继续按钮
        composeTestRule.onNodeWithContentDescription("继续游戏").assertExists()
    }

    @Test
    fun controlButtonsArea_crossLayout_displaysCorrectly() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true,
                    layoutStyle = ControlLayoutStyle.CROSS
                )
            }
        }

        // 验证十字布局的存在
        composeTestRule.onNodeWithText("游戏控制").assertExists()
        composeTestRule.onAllNodesWithContentDescription("旋转").assertCountEquals(1)
    }

    @Test
    fun controlButtonsArea_gridLayout_displaysCorrectly() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true,
                    layoutStyle = ControlLayoutStyle.GRID
                )
            }
        }

        // 验证网格布局的存在
        composeTestRule.onNodeWithText("游戏控制").assertExists()
        composeTestRule.onAllNodesWithContentDescription("旋转").assertCountEquals(1)
    }

    @Test
    fun controlButtonsArea_linearLayout_displaysCorrectly() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true,
                    layoutStyle = ControlLayoutStyle.LINEAR
                )
            }
        }

        // 验证线性布局的存在
        composeTestRule.onNodeWithText("游戏控制").assertExists()
        composeTestRule.onAllNodesWithContentDescription("旋转").assertCountEquals(1)
    }

    @Test
    fun controlButtonsArea_statusIndicator_showsCorrectState() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true
                )
            }
        }

        // 验证状态指示器显示正确状态
        composeTestRule.onNodeWithText("运行中").assertExists()
    }

    @Test
    fun controlButtonsArea_statusIndicator_showsPausedState() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = false
                )
            }
        }

        // 验证状态指示器显示暂停状态
        composeTestRule.onNodeWithText("已暂停").assertExists()
    }

    @Test
    fun controlButtonsArea_advancedControls_workCorrectly() {
        var hardDropClicked = false
        var holdClicked = false
        var pauseClicked = false

        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = { hardDropClicked = true },
                    onHold = { holdClicked = true },
                    onPause = { pauseClicked = true },
                    isGameRunning = true
                )
            }
        }

        // 测试高级控制按钮
        composeTestRule.onNodeWithContentDescription("硬降").performClick()
        assert(hardDropClicked)

        composeTestRule.onNodeWithContentDescription("暂存").performClick()
        assert(holdClicked)

        composeTestRule.onNodeWithContentDescription("暂停游戏").performClick()
        assert(pauseClicked)
    }

    @Test
    fun controlButtonsArea_buttonSizes_displayCorrectly() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true,
                    buttonSize = ButtonSize.LARGE
                )
            }
        }

        // 验证大按钮模式下按钮存在
        composeTestRule.onNodeWithContentDescription("旋转").assertExists()
        composeTestRule.onNodeWithContentDescription("左移").assertExists()
        composeTestRule.onNodeWithContentDescription("右移").assertExists()
    }

    @Test
    fun controlButtonsArea_labelsDisplay_whenEnabled() {
        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = {},
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true,
                    showLabels = true
                )
            }
        }

        // 验证标签显示
        composeTestRule.onNodeWithText("左移").assertExists()
        composeTestRule.onNodeWithText("右移").assertExists()
        composeTestRule.onNodeWithText("软降").assertExists()
        composeTestRule.onNodeWithText("旋转").assertExists()
        composeTestRule.onNodeWithText("硬降").assertExists()
        composeTestRule.onNodeWithText("暂存").assertExists()
    }

    @Test
    fun controlButtonsArea_multipleClicks_handleCorrectly() {
        var clickCount = 0

        composeTestRule.setContent {
            QuesticleTheme {
                ControlButtonsArea(
                    onMoveLeft = { clickCount++ },
                    onMoveRight = {},
                    onMoveDown = {},
                    onRotate = {},
                    onHardDrop = {},
                    onHold = {},
                    onPause = {},
                    isGameRunning = true
                )
            }
        }

        // 测试多次点击
        repeat(5) {
            composeTestRule.onNodeWithContentDescription("左移").performClick()
        }
        
        assert(clickCount == 5)
    }
} 