package com.yu.questicle

import android.content.ComponentCallbacks2
import androidx.activity.ComponentActivity
import androidx.test.core.app.ActivityScenario
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * Activity Transaction 状态测试
 * 专门测试 endActivityTransaction: margin state not match 问题的修复
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [34])
class ActivityTransactionTest {

    @Test
    fun testMainActivityLifecycle() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity)
                assertTrue(activity is ComponentActivity)
            }
        }
    }

    @Test
    fun testActivityStateChanges() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            // 测试各种状态变化
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.CREATED)
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.STARTED)
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.RESUMED)
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.STARTED)
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.CREATED)
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.DESTROYED)
        }
    }

    @Test
    fun testActivityRecreation() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity)
            }
            
            // 模拟配置变化导致的重建
            scenario.recreate()
            
            scenario.onActivity { activity ->
                assertNotNull(activity)
                assertTrue(activity is MainActivity)
            }
        }
    }

    @Test
    fun testWindowConfiguration() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity.window)
                assertNotNull(activity.window.decorView)
            }
        }
    }

    @Test
    fun testEdgeToEdgeConfiguration() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                // 验证窗口配置
                assertNotNull(activity.window)
                
                // 验证decorView存在
                assertNotNull(activity.window.decorView)
            }
        }
    }

    @Test
    fun testActivityCreationExceptionHandling() {
        // 这个测试验证即使在异常情况下Activity也能正常工作
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity)
                
                // 验证Activity处于正常状态
                assertTrue(activity.lifecycle.currentState.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED))
            }
        }
    }

    @Test
    fun testWindowConfigurationFallback() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                // 验证即使窗口配置出现问题，Activity仍然可用
                assertNotNull(activity)
                assertNotNull(activity.window)
            }
        }
    }

    @Test
    fun testLowMemoryHandling() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                // 模拟低内存情况 - 使用标准的ComponentCallbacks2常量
                @Suppress("DEPRECATION")
                activity.onTrimMemory(ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL)
                
                // 验证Activity仍然正常
                assertNotNull(activity)
                assertTrue(activity.lifecycle.currentState.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED))
            }
        }
    }

    @Test
    fun testActivityDestruction() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity)
            }
            
            // Activity会在scenario.close()时自动销毁
            // 这里验证没有异常抛出
        }
        // 如果到这里没有异常，说明销毁过程正常
    }

    @Test
    fun testStateSaveAndRestore() {
        ActivityScenario.launch(MainActivity::class.java).use { scenario ->
            scenario.onActivity { activity ->
                assertNotNull(activity)
                // 验证Activity正常创建
                assertTrue(activity.lifecycle.currentState.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED))
            }
            
            // 重建Activity来模拟状态保存和恢复
            scenario.recreate()
            
            scenario.onActivity { activity ->
                assertNotNull(activity)
                // 验证Activity正常重建
                assertTrue(activity.lifecycle.currentState.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED))
            }
        }
    }
}
