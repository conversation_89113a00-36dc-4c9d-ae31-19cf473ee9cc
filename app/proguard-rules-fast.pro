# 极简ProGuard规则 - 专为最快构建速度设计
# 仅保留必要的类，让R8做最多的优化

# 基础配置
-ignorewarnings
-dontwarn **
-dontusemixedcaseclassnames
-verbose

# 应用入口点
-keep class com.yu.questicle.QuesticleApplication { *; }
-keep class com.yu.questicle.MainActivity { *; }

# Android基础组件
-keep class * extends android.app.Activity
-keep class * extends android.app.Service
-keep class * extends android.content.BroadcastReceiver
-keep class * extends android.content.ContentProvider

# ViewModels (简化)
-keep class * extends androidx.lifecycle.ViewModel

# Room (简化)
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *

# Hilt (最小化)
-keep class dagger.hilt.android.HiltAndroidApp
-keep class * extends dagger.hilt.android.lifecycle.HiltViewModel

# Kotlin基础
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler

# R8最大优化
-allowaccessmodification
-repackageclasses

# 移除所有日志
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
}

# 忽略所有警告以加速处理
-dontwarn ** 