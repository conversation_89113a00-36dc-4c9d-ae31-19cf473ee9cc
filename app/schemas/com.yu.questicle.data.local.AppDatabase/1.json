{"formatVersion": 1, "database": {"version": 1, "identityHash": "61a555909f4b9758c964222c47124736", "entities": [{"tableName": "game_states", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `gameType` TEXT NOT NULL, `config` TEXT NOT NULL, `score` INTEGER NOT NULL, `level` INTEGER NOT NULL, `time` INTEGER NOT NULL, `data` TEXT NOT NULL, `reason` TEXT, `isPaused` INTEGER NOT NULL, `isGameOver` INTEGER NOT NULL, `isHighScore` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameType", "columnName": "gameType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "reason", "columnName": "reason", "affinity": "TEXT"}, {"fieldPath": "isPaused", "columnName": "isPaused", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isGameOver", "columnName": "isGameOver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHighScore", "columnName": "isHighScore", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '61a555909f4b9758c964222c47124736')"]}}