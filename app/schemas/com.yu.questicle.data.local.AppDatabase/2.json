{"formatVersion": 1, "database": {"version": 2, "identityHash": "2c311b250fe248fe675baa9a2238ab13", "entities": [{"tableName": "game_states", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `gameType` TEXT NOT NULL, `config` TEXT NOT NULL, `score` INTEGER NOT NULL, `level` INTEGER NOT NULL, `time` INTEGER NOT NULL, `data` TEXT NOT NULL, `reason` TEXT, `isPaused` INTEGER NOT NULL, `isGameOver` INTEGER NOT NULL, `isHighScore` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameType", "columnName": "gameType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "reason", "columnName": "reason", "affinity": "TEXT"}, {"fieldPath": "isPaused", "columnName": "isPaused", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isGameOver", "columnName": "isGameOver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isHighScore", "columnName": "isHighScore", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "game_saves", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `gameType` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `gameState` TEXT NOT NULL, `screenshot` BLOB, `playTime` INTEGER NOT NULL, `score` INTEGER NOT NULL, `level` INTEGER NOT NULL, `lines` INTEGER NOT NULL, `version` INTEGER NOT NULL, `isAutoSave` INTEGER NOT NULL, `metadata` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameType", "columnName": "gameType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gameState", "columnName": "gameState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "screenshot", "columnName": "screenshot", "affinity": "BLOB"}, {"fieldPath": "playTime", "columnName": "playTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lines", "columnName": "lines", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "version", "columnName": "version", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAutoSave", "columnName": "isAutoSave", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "game_time_records", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `date` TEXT NOT NULL, `totalPlayTime` INTEGER NOT NULL, `sessionCount` INTEGER NOT NULL, `longestSession` INTEGER NOT NULL, `gameCompletions` INTEGER NOT NULL, `averageScore` INTEGER NOT NULL, `highestScore` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalPlayTime", "columnName": "totalPlayTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionCount", "columnName": "sessionCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "longestSession", "columnName": "longestSession", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gameCompletions", "columnName": "gameCompletions", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "averageScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "highestScore", "columnName": "highestScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "game_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `duration` INTEGER NOT NULL, `gameType` TEXT NOT NULL, `finalScore` INTEGER NOT NULL, `finalLevel` INTEGER NOT NULL, `linesCleared` INTEGER NOT NULL, `completed` INTEGER NOT NULL, `pauseCount` INTEGER NOT NULL, `date` TEXT NOT NULL, `metadata` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER"}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gameType", "columnName": "gameType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "finalScore", "columnName": "finalScore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finalLevel", "columnName": "finalLevel", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "linesCleared", "columnName": "linesCleared", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completed", "columnName": "completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pauseCount", "columnName": "pauseCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '2c311b250fe248fe675baa9a2238ab13')"]}}