plugins {
    id("questicle.android.application.compose")
    id("questicle.hilt")
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.android.junit5)
}

android {
    namespace = "com.yu.questicle"

    defaultConfig {
        applicationId = "com.yu.questicle"
        versionCode = 2
        versionName = "1.0.1"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        debug {
            // 开发构建优化 - 禁用不必要的处理
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
        }
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        buildConfig = true
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            // 解决资源冲突 - 排除所有许可证和通知文件
            excludes += "win32-x86/attach_hotspot_windows.dll"
            excludes += "win32-x86-64/attach_hotspot_windows.dll"
            excludes += "META-INF/licenses/**"
            excludes += "META-INF/AL2.0"
            excludes += "META-INF/LGPL2.1"
            excludes += "META-INF/LICENSE*"
            excludes += "META-INF/NOTICE*"
            excludes += "META-INF/ASL2.0"
            excludes += "META-INF/*.kotlin_module"
            excludes += "META-INF/gradle/**"
            excludes += "META-INF/maven/**"
            excludes += "META-INF/proguard/**"
            excludes += "META-INF/com.android.tools/**"
            excludes += "META-INF/services/**"
            excludes += "META-INF/versions/**"
            excludes += "**/*.proto"
            excludes += "**/*.properties"
            excludes += "DebugProbesKt.bin"
        }
    }
}

dependencies {
    // Core modules
    implementation(project(":core:common"))
    implementation(project(":core:domain"))
    implementation(project(":core:data"))
    implementation(project(":core:database"))
    implementation(project(":core:datastore"))
    implementation(project(":core:network"))
    implementation(project(":core:designsystem"))
    implementation(project(":core:testing"))

    // Feature modules
    implementation(project(":feature:home:api"))
    implementation(project(":feature:home:impl"))
    implementation(project(":feature:settings:api"))
    implementation(project(":feature:settings:impl"))
    implementation(project(":feature:tetris:api"))
    implementation(project(":feature:tetris:impl"))
    implementation(project(":feature:user:api"))
    implementation(project(":feature:user:impl"))

    // Navigation
    implementation(libs.androidx.navigation.compose)
    implementation(libs.hilt.navigation.compose)

    // Image loading
    implementation(libs.coil.compose)

    // System UI
    implementation(libs.accompanist.systemuicontroller)

    // Serialization
    implementation(libs.kotlinx.serialization.json)

    // Material Design
    implementation(libs.material)

    // Runtime profile installer
    implementation("androidx.profileinstaller:profileinstaller:1.3.1")

    // 标准化测试依赖 - 由 convention plugin 统一管理
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)

    // Android测试依赖 - 由 convention plugin 统一管理
    androidTestImplementation(libs.bundles.android.testing)
    androidTestImplementation(libs.bundles.compose.testing)
}
