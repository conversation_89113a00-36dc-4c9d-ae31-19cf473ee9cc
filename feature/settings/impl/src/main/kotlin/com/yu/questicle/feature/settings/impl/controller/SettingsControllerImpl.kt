package com.yu.questicle.feature.settings.impl.controller

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.toQuesticleException
import com.yu.questicle.core.domain.model.Theme
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.repository.UserPreferencesRepository
import com.yu.questicle.feature.settings.api.SettingsController
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsControllerImpl @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository
) : SettingsController {

    private val mutex = Mutex()
    private val _isLoading = MutableStateFlow(false)
    private val _error = MutableStateFlow<String?>(null)

    override val userPreferences: Flow<UserPreferences> =
        userPreferencesRepository.getUserPreferences()

    override val isLoading: Flow<Boolean> = _isLoading.asStateFlow()
    override val error: Flow<String?> = _error.asStateFlow()

    override suspend fun updateTheme(theme: Theme): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateTheme(theme)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateLanguage(language: String): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateLanguage(language)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateSoundEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateSoundEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateMusicEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateMusicEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateVibrationEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateVibrationEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateMasterVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            val clampedVolume = volume.coerceIn(0f, 1f)
            when (val result = userPreferencesRepository.updateMasterVolume(clampedVolume)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateSoundEffectsVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            val clampedVolume = volume.coerceIn(0f, 1f)
            when (val result = userPreferencesRepository.updateSoundEffectsVolume(clampedVolume)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateMusicVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            val clampedVolume = volume.coerceIn(0f, 1f)
            when (val result = userPreferencesRepository.updateMusicVolume(clampedVolume)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateNotificationsEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateNotificationsEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateAutoSaveEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateAutoSaveEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateShowHintsEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.updateShowHintsEnabled(enabled)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateAnimationSpeed(speed: Float): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            val clampedSpeed = speed.coerceIn(0.1f, 2.0f)
            when (val result = userPreferencesRepository.updateAnimationSpeed(clampedSpeed)) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun resetToDefaults(): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.resetToDefaults()) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("重置中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun exportUserData(): Result<String> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.exportUserData()) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("导出中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun clearAllData(): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val result = userPreferencesRepository.clearAllData()) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("清除中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun toggleSound(): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val currentResult = userPreferencesRepository.getCurrentUserPreferences()) {
                is Result.Success -> {
                    val currentPrefs = currentResult.data
                    val newSoundEnabled = !currentPrefs.soundEnabled
                    // 直接调用repository，避免重复获取锁
                    when (val result = userPreferencesRepository.updateSoundEnabled(newSoundEnabled)) {
                        is Result.Success -> {
                            _isLoading.value = false
                            result
                        }
                        is Result.Error -> {
                            _isLoading.value = false
                            _error.value = result.exception.message
                            result
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("更新设置中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = currentResult.exception.message
                    currentResult
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("获取设置中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun toggleMusic(): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val currentResult = userPreferencesRepository.getCurrentUserPreferences()) {
                is Result.Success -> {
                    val currentPrefs = currentResult.data
                    val newMusicEnabled = !currentPrefs.musicEnabled
                    // 直接调用repository，避免重复获取锁
                    when (val result = userPreferencesRepository.updateMusicEnabled(newMusicEnabled)) {
                        is Result.Success -> {
                            _isLoading.value = false
                            result
                        }
                        is Result.Error -> {
                            _isLoading.value = false
                            _error.value = result.exception.message
                            result
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("更新设置中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = currentResult.exception.message
                    currentResult
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("获取设置中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun toggleVibration(): Result<Unit> = mutex.withLock {
        return try {
            _isLoading.value = true
            _error.value = null

            when (val currentResult = userPreferencesRepository.getCurrentUserPreferences()) {
                is Result.Success -> {
                    val currentPrefs = currentResult.data
                    val newVibrationEnabled = !currentPrefs.vibrationEnabled
                    // 直接调用repository，避免重复获取锁
                    when (val result = userPreferencesRepository.updateVibrationEnabled(newVibrationEnabled)) {
                        is Result.Success -> {
                            _isLoading.value = false
                            result
                        }
                        is Result.Error -> {
                            _isLoading.value = false
                            _error.value = result.exception.message
                            result
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("更新设置中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _error.value = currentResult.exception.message
                    currentResult
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("获取设置中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _error.value = e.message
            Result.Error(e.toQuesticleException())
        }
    }

    override fun clearError() {
        _error.value = null
    }


}
