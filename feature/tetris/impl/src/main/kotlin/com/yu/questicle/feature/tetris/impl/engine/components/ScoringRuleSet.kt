package com.yu.questicle.feature.tetris.impl.engine.components

/**
 * Configuration class for customizable scoring rules.
 * Allows for creating custom scoring systems beyond the predefined ones.
 */
data class ScoringRuleSet(
    val name: String,
    val description: String,
    val lineClearScores: Map<Int, Int>,
    val tSpinMultipliers: Map<Int, Float>,
    val perfectClearBonus: Int,
    val backToBackMultiplier: Float,
    val softDropPointsPerCell: Int,
    val hardDropPointsPerCell: Int,
    val maxLevel: Int,
    val linesPerLevel: Int,
    val baseDropIntervalMs: Long,
    val minDropIntervalMs: Long,
    val comboMultipliers: List<Float>,
    val specialComboBonuses: Map<String, Int>
) {
    companion object {
        /**
         * Create a classic Tetris rule set (NES style)
         */
        fun classic(): ScoringRuleSet = ScoringRuleSet(
            name = "Classic",
            description = "Original NES Tetris scoring system",
            lineClearScores = mapOf(
                1 to 40,
                2 to 100,
                3 to 300,
                4 to 1200
            ),
            tSpinMultipliers = emptyMap(), // No T-spins in classic
            perfectClearBonus = 0,         // No perfect clear bonus in classic
            backToBackMultiplier = 1.0f,   // No back-to-back in classic
            softDropPointsPerCell = 1,
            hardDropPointsPerCell = 0,     // No hard drop in classic
            maxLevel = 20,
            linesPerLevel = 10,
            baseDropIntervalMs = 800L,
            minDropIntervalMs = 80L,
            comboMultipliers = List(20) { 1.0f }, // No combo system in classic
            specialComboBonuses = emptyMap()
        )
        
        /**
         * Create a modern Tetris rule set (Guideline style)
         */
        fun modern(): ScoringRuleSet = ScoringRuleSet(
            name = "Modern",
            description = "Tetris Guideline scoring system with T-Spin and combo bonuses",
            lineClearScores = mapOf(
                1 to 100,
                2 to 300,
                3 to 500,
                4 to 800
            ),
            tSpinMultipliers = mapOf(
                1 to 2.0f,  // T-Spin Single
                2 to 2.5f,  // T-Spin Double
                3 to 3.0f   // T-Spin Triple
            ),
            perfectClearBonus = 1000,
            backToBackMultiplier = 1.5f,
            softDropPointsPerCell = 1,
            hardDropPointsPerCell = 2,
            maxLevel = 20,
            linesPerLevel = 10,
            baseDropIntervalMs = 1000L,
            minDropIntervalMs = 100L,
            comboMultipliers = listOf(
                1.0f, 1.0f, 1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.5f, 1.5f, 1.5f,
                1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f, 1.5f
            ),
            specialComboBonuses = mapOf(
                "tetris_back_to_back" to 500,
                "t_spin_back_to_back" to 400,
                "combo_3plus" to 100,
                "combo_5plus" to 300
            )
        )
        
        /**
         * Create a competitive Tetris rule set
         */
        fun competitive(): ScoringRuleSet = ScoringRuleSet(
            name = "Competitive",
            description = "Tournament-optimized scoring system with enhanced bonuses",
            lineClearScores = mapOf(
                1 to 100,
                2 to 300,
                3 to 500,
                4 to 800
            ),
            tSpinMultipliers = mapOf(
                1 to 2.0f,  // T-Spin Single
                2 to 3.0f,  // T-Spin Double
                3 to 4.0f   // T-Spin Triple
            ),
            perfectClearBonus = 2000,
            backToBackMultiplier = 1.75f,
            softDropPointsPerCell = 1,
            hardDropPointsPerCell = 2,
            maxLevel = 30,
            linesPerLevel = 10,
            baseDropIntervalMs = 1000L,
            minDropIntervalMs = 50L,
            comboMultipliers = listOf(
                1.0f, 1.0f, 1.2f, 1.4f, 1.6f, 1.8f, 2.0f, 2.1f, 2.2f, 2.3f,
                2.4f, 2.5f, 2.6f, 2.7f, 2.8f, 2.9f, 3.0f, 3.0f, 3.0f, 3.0f
            ),
            specialComboBonuses = mapOf(
                "tetris_back_to_back" to 800,
                "t_spin_back_to_back" to 600,
                "combo_3plus" to 200,
                "combo_5plus" to 500,
                "combo_7plus" to 1000,
                "perfect_clear" to 2000
            )
        )
        
        /**
         * Create a custom rule set with specified parameters
         */
        fun custom(
            name: String,
            description: String,
            singleLineScore: Int = 100,
            doubleLineScore: Int = 300,
            tripleLineScore: Int = 500,
            tetrisScore: Int = 800,
            tSpinMultiplier: Float = 2.0f,
            perfectClearBonus: Int = 1000,
            backToBackMultiplier: Float = 1.5f,
            maxLevel: Int = 20,
            baseDropIntervalMs: Long = 1000L
        ): ScoringRuleSet = ScoringRuleSet(
            name = name,
            description = description,
            lineClearScores = mapOf(
                1 to singleLineScore,
                2 to doubleLineScore,
                3 to tripleLineScore,
                4 to tetrisScore
            ),
            tSpinMultipliers = mapOf(
                1 to tSpinMultiplier,
                2 to tSpinMultiplier * 1.25f,
                3 to tSpinMultiplier * 1.5f
            ),
            perfectClearBonus = perfectClearBonus,
            backToBackMultiplier = backToBackMultiplier,
            softDropPointsPerCell = 1,
            hardDropPointsPerCell = 2,
            maxLevel = maxLevel,
            linesPerLevel = 10,
            baseDropIntervalMs = baseDropIntervalMs,
            minDropIntervalMs = 100L,
            comboMultipliers = List(20) { index ->
                if (index <= 1) 1.0f else 1.0f + (index - 1) * 0.1f
            },
            specialComboBonuses = mapOf(
                "tetris_back_to_back" to tetrisScore / 2,
                "t_spin_back_to_back" to tetrisScore / 2,
                "combo_3plus" to singleLineScore,
                "combo_5plus" to doubleLineScore
            )
        )
    }
}

/**
 * Custom scoring system that uses a configurable rule set.
 */
class CustomScoringSystem(private val ruleSet: ScoringRuleSet) : ScoringSystem {
    
    override fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        combo: Int,
        isBackToBack: Boolean
    ): Int {
        // Base score based on lines cleared
        val baseScore = ruleSet.lineClearScores[linesCleared] ?: 0
        
        // Apply level multiplier
        var score = baseScore * (level + 1)
        
        // Apply T-Spin bonus if applicable
        if (isTSpin) {
            val tSpinMultiplier = ruleSet.tSpinMultipliers[linesCleared] ?: 1.0f
            score = (score * tSpinMultiplier).toInt()
        }
        
        // Apply back-to-back bonus if applicable
        if (isBackToBack && (linesCleared == 4 || isTSpin)) {
            score = (score * ruleSet.backToBackMultiplier).toInt()
        }
        
        // Apply perfect clear bonus if applicable
        if (isPerfectClear) {
            score += ruleSet.perfectClearBonus * (level + 1)
        }
        
        // Apply combo multiplier
        if (combo > 1 && combo < ruleSet.comboMultipliers.size) {
            val comboMultiplier = ruleSet.comboMultipliers[combo]
            score = (score * comboMultiplier).toInt()
        }
        
        return score
    }
    
    override fun calculateSoftDropScore(cellsDropped: Int): Int {
        return cellsDropped * ruleSet.softDropPointsPerCell
    }
    
    override fun calculateHardDropScore(cellsDropped: Int): Int {
        return cellsDropped * ruleSet.hardDropPointsPerCell
    }
    
    override fun calculateLevel(totalLines: Int): Int {
        return minOf(totalLines / ruleSet.linesPerLevel, ruleSet.maxLevel)
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // Formula: BASE_INTERVAL * (0.8^level)
        val factor = Math.pow(0.8, level.toDouble())
        val interval = (ruleSet.baseDropIntervalMs * factor).toLong()
        return maxOf(interval, ruleSet.minDropIntervalMs)
    }
    
    override fun calculateComboMultiplier(consecutiveClears: Int): Float {
        val index = minOf(consecutiveClears, ruleSet.comboMultipliers.size - 1)
        return ruleSet.comboMultipliers[index]
    }
    
    override fun getName(): String = ruleSet.name
    
    override fun getDescription(): String = ruleSet.description
}