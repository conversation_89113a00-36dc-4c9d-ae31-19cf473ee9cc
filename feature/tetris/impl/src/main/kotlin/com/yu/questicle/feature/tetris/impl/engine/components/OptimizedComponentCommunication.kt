package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.feature.tetris.impl.engine.memory.TetrisObjectPoolManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Optimizes communication between components by providing direct access methods
 * that minimize object creation and method call overhead.
 * 
 * This class acts as a facade for the most frequently used component interactions,
 * optimizing hot paths in the game engine.
 */
@Singleton
class OptimizedComponentCommunication @Inject constructor(
    private val collisionDetector: TetrisCollisionDetector,
    private val gameLogicProcessor: TetrisGameLogicProcessor,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val objectPoolManager: TetrisObjectPoolManager
) {
    
    /**
     * Optimized method for checking if a move is valid and getting the new piece position.
     * 
     * This method combines collision detection and move processing into a single call,
     * avoiding intermediate object creation.
     */
    suspend fun checkAndProcessMove(
        piece: TetrisPiece,
        direction: Direction,
        board: TetrisBoard
    ): Pair<Boolean, TetrisPiece?> {
        // Calculate new position directly
        val (dx, dy) = when (direction) {
            Direction.LEFT -> -1 to 0
            Direction.RIGHT -> 1 to 0
            Direction.DOWN -> 0 to 1
        }
        
        // Create new piece position
        val newPiece = objectPoolManager.acquirePiece(
            piece.type,
            piece.x + dx,
            piece.y + dy,
            piece.rotation
        )
        
        // Check if position is valid
        val isValid = collisionDetector.isValidPosition(newPiece, board)
        
        return if (isValid) {
            Pair(true, newPiece)
        } else {
            // Release the piece back to the pool
            objectPoolManager.releasePiece(newPiece)
            Pair(false, null)
        }
    }
    
    /**
     * Optimized method for checking if a rotation is valid and getting the new piece rotation.
     * 
     * This method combines collision detection and rotation processing into a single call,
     * avoiding intermediate object creation.
     */
    suspend fun checkAndProcessRotation(
        piece: TetrisPiece,
        clockwise: Boolean,
        board: TetrisBoard
    ): Pair<Boolean, TetrisPiece?> {
        // Calculate new rotation directly
        val newRotation = (piece.rotation + (if (clockwise) 1 else 3)) % 4
        
        // Create new piece with rotation
        val rotatedPiece = objectPoolManager.acquirePiece(
            piece.type,
            piece.x,
            piece.y,
            newRotation
        )
        
        // Check if rotation is valid
        val isValid = collisionDetector.isValidPosition(rotatedPiece, board)
        
        return if (isValid) {
            Pair(true, rotatedPiece)
        } else {
            // Try wall kicks
            val wallKickOffsets = getWallKickOffsets(piece.type, piece.rotation, newRotation)
            
            for ((kickX, kickY) in wallKickOffsets) {
                val kickedPiece = objectPoolManager.acquirePiece(
                    piece.type,
                    piece.x + kickX,
                    piece.y + kickY,
                    newRotation
                )
                
                if (collisionDetector.isValidPosition(kickedPiece, board)) {
                    // Release the original rotated piece
                    objectPoolManager.releasePiece(rotatedPiece)
                    return Pair(true, kickedPiece)
                }
                
                // Release the kicked piece
                objectPoolManager.releasePiece(kickedPiece)
            }
            
            // Release the original rotated piece
            objectPoolManager.releasePiece(rotatedPiece)
            Pair(false, null)
        }
    }
    
    /**
     * Optimized method for finding the landing position of a piece.
     * 
     * This method uses binary search to efficiently find the landing position,
     * avoiding repeated collision checks.
     */
    fun findLandingPositionOptimized(
        piece: TetrisPiece,
        board: TetrisBoard
    ): TetrisPiece {
        // Use binary search to find landing position
        var low = piece.y
        var high = board.height
        
        while (low < high - 1) {
            val mid = (low + high) / 2
            val testPiece = objectPoolManager.acquirePiece(
                piece.type,
                piece.x,
                mid,
                piece.rotation
            )
            
            if (collisionDetector.isValidPosition(testPiece, board)) {
                low = mid
                objectPoolManager.releasePiece(testPiece)
            } else {
                high = mid
                objectPoolManager.releasePiece(testPiece)
            }
        }
        
        // Create the final landing piece
        return objectPoolManager.acquirePiece(
            piece.type,
            piece.x,
            low,
            piece.rotation
        )
    }
    
    /**
     * Optimized method for calculating score from line clears.
     * 
     * This method combines statistics calculation and score updates into a single call,
     * avoiding intermediate object creation.
     */
    fun calculateScoreOptimized(
        linesCleared: Int,
        level: Int,
        combo: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        isBackToBack: Boolean
    ): Int {
        // Calculate base score
        val baseScore = statisticsCalculator.calculateLineClearScore(linesCleared, level)
        
        // Apply combo multiplier
        val comboMultiplier = statisticsCalculator.calculateComboMultiplier(combo)
        
        // Apply T-spin bonus
        val tSpinMultiplier = if (isTSpin) 1.5f else 1.0f
        
        // Apply perfect clear bonus
        val perfectClearBonus = if (isPerfectClear) 1000 * level else 0
        
        // Apply back-to-back bonus
        val backToBackMultiplier = if (isBackToBack) 1.5f else 1.0f
        
        // Calculate final score
        return (baseScore * comboMultiplier * tSpinMultiplier * backToBackMultiplier).toInt() + perfectClearBonus
    }
    
    /**
     * Get wall kick offsets for rotation.
     */
    private fun getWallKickOffsets(
        pieceType: TetrisPieceType,
        fromRotation: Int,
        toRotation: Int
    ): List<Pair<Int, Int>> {
        // Simplified wall kick data
        return when (pieceType) {
            TetrisPieceType.I -> listOf(0 to 0, -2 to 0, 1 to 0, -2 to -1, 1 to 2)
            TetrisPieceType.O -> listOf(0 to 0)
            else -> listOf(0 to 0, -1 to 0, -1 to 1, 0 to -2, -1 to -2)
        }
    }
}