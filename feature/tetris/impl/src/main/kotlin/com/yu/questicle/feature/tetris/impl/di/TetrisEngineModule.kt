package com.yu.questicle.feature.tetris.impl.di

import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.feature.tetris.impl.engine.TetrisEngineImpl
import com.yu.questicle.feature.tetris.impl.engine.components.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 俄罗斯方块引擎依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object TetrisEngineModule {
    
    /**
     * 提供俄罗斯方块引擎实现
     */
    @Provides
    @Singleton
    fun provideTetrisEngine(
        impl: TetrisEngineImpl
    ): TetrisEngine = impl
    
    /**
     * 提供俄罗斯方块游戏逻辑处理器
     */
    @Provides
    @Singleton
    fun provideTetrisGameLogicProcessor(
        collisionDetector: TetrisCollisionDetector
    ): TetrisGameLogicProcessor = TetrisGameLogicProcessorImpl(collisionDetector)
    
    /**
     * 提供俄罗斯方块碰撞检测器
     */
    @Provides
    @Singleton
    fun provideTetrisCollisionDetector(): TetrisCollisionDetector = TetrisCollisionDetectorImpl()
    
    /**
     * 提供俄罗斯方块统计计算器
     */
    @Provides
    @Singleton
    fun provideTetrisStatisticsCalculator(): TetrisStatisticsCalculator = TetrisStatisticsCalculatorImpl()
    
    /**
     * 提供俄罗斯方块性能管理器
     */
    @Provides
    @Singleton
    fun provideTetrisPerformanceManager(): TetrisPerformanceManager = TetrisPerformanceManagerImpl()
    
    /**
     * 提供俄罗斯方块缓存管理器
     */
    @Provides
    @Singleton
    fun provideTetrisCacheManager(): TetrisCacheManager = TetrisCacheManagerImpl()
}