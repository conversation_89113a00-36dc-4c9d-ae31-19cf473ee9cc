package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Specialized component for handling line clearing operations.
 * Optimized for performance with various clearing algorithms.
 */
@Singleton
class BoardClearingProcessor @Inject constructor(
    private val performanceManager: TetrisPerformanceManager
) {
    /**
     * Find and clear completed lines from the board
     */
    fun clearLines(board: TetrisBoard): Pair<TetrisBoard, List<Int>> {
        val startTime = System.nanoTime()
        
        try {
            // Find completed lines
            val completedLines = findCompletedLines(board)
            
            if (completedLines.isEmpty()) {
                return board to emptyList()
            }
            
            // Create new board with cleared lines
            val clearedBoard = removeLines(board, completedLines)
            
            return clearedBoard to completedLines
        } finally {
            performanceManager.trackOperation("clearLines", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Find all completed lines in the board
     */
    fun findCompletedLines(board: TetrisBoard): List<Int> {
        val startTime = System.nanoTime()
        
        try {
            val completedLines = mutableListOf<Int>()
            
            // Use early exit optimization if enabled
            if (performanceManager.shouldUseOptimization(OptimizationType.EARLY_EXIT)) {
                // Check from bottom to top for early exit opportunities
                for (y in board.height - 1 downTo 0) {
                    // Skip empty rows quickly
                    if (board.cells[y].all { it == TetrisCellType.EMPTY }) {
                        // All rows above this one are also empty in a typical Tetris game
                        break
                    }
                    
                    // Check if line is complete
                    if (board.cells[y].none { it == TetrisCellType.EMPTY }) {
                        completedLines.add(y)
                    }
                }
            } else {
                // Standard algorithm
                for (y in 0 until board.height) {
                    var lineComplete = true
                    for (x in 0 until board.width) {
                        if (board.cells[y][x] == TetrisCellType.EMPTY) {
                            lineComplete = false
                            break
                        }
                    }
                    if (lineComplete) {
                        completedLines.add(y)
                    }
                }
            }
            
            return completedLines
        } finally {
            performanceManager.trackOperation("findCompletedLines", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Remove lines from the board and shift remaining lines down
     */
    fun removeLines(board: TetrisBoard, linesToRemove: List<Int>): TetrisBoard {
        val startTime = System.nanoTime()
        
        try {
            if (linesToRemove.isEmpty()) {
                return board
            }
            
            // Create new board cells
            val newCells = Array(board.height) { y ->
                Array(board.width) { x ->
                    TetrisCellType.EMPTY
                }
            }
            
            // Copy non-cleared lines
            var destY = board.height - 1
            for (srcY in board.height - 1 downTo 0) {
                if (srcY !in linesToRemove) {
                    for (x in 0 until board.width) {
                        newCells[destY][x] = board.cells[srcY][x]
                    }
                    destY--
                }
            }
            
            return TetrisBoard(newCells, board.width, board.height)
        } finally {
            performanceManager.trackOperation("removeLines", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Check if the board is completely empty (perfect clear)
     */
    fun isPerfectClear(board: TetrisBoard): Boolean {
        return board.cells.all { row -> row.all { it == TetrisCellType.EMPTY } }
    }
    
    /**
     * Count filled cells on the board
     */
    fun countFilledCells(board: TetrisBoard): Int {
        var count = 0
        for (y in 0 until board.height) {
            for (x in 0 until board.width) {
                if (board.cells[y][x] != TetrisCellType.EMPTY) {
                    count++
                }
            }
        }
        return count
    }
    
    /**
     * Calculate board fullness percentage
     */
    fun calculateBoardFullness(board: TetrisBoard): Float {
        val totalCells = board.width * board.height
        val filledCells = countFilledCells(board)
        return filledCells.toFloat() / totalCells
    }
}