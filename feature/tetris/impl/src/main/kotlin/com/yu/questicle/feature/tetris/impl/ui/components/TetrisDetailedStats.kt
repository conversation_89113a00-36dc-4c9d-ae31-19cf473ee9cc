package com.yu.questicle.feature.tetris.impl.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme
import com.yu.questicle.core.domain.model.tetris.TetrisStatistics
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import kotlin.math.roundToInt

/**
 * 详细的Tetris游戏统计信息组件
 * 显示完整的游戏统计数据，包括效率指标、操作统计等
 */
@Composable
fun TetrisDetailedStats(
    statistics: TetrisStatistics,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        // 基础统计
        item {
            StatsSection(
                title = "基础统计",
                stats = listOf(
                    "放置方块" to statistics.piecesPlaced.toString(),
                    "消除行数" to statistics.linesCleared.toString(),
                    "当前等级" to statistics.maxLevel.toString(),
                    "最大连击" to statistics.maxCombo.toString()
                )
            )
        }
        
        // 消除类型统计
        item {
            StatsSection(
                title = "消除类型",
                stats = listOf(
                    "单行消除" to statistics.singles.toString(),
                    "双行消除" to statistics.doubles.toString(),
                    "三行消除" to statistics.triples.toString(),
                    "四行消除 (Tetris)" to statistics.tetrises.toString(),
                    "T-Spin" to statistics.tSpins.toString(),
                    "T-Spin单行" to statistics.tSpinSingles.toString(),
                    "T-Spin双行" to statistics.tSpinDoubles.toString(),
                    "T-Spin三行" to statistics.tSpinTriples.toString()
                )
            )
        }
        
        // 效率统计
        item {
            val gameTimeMinutes = statistics.gameTime / 60000.0
            val gameTimeFormatted = if (gameTimeMinutes < 1) {
                "${(statistics.gameTime / 1000).toInt()}秒"
            } else {
                "${gameTimeMinutes.roundToInt()}分钟"
            }
            
            StatsSection(
                title = "效率指标",
                stats = listOf(
                    "游戏时间" to gameTimeFormatted,
                    "每分钟方块数" to "%.1f".format(statistics.piecesPerMinute),
                    "每分钟消除行数" to "%.1f".format(statistics.linesPerMinute),
                    "消除效率" to "%.2f".format(statistics.efficiency),
                    "攻击力" to statistics.getAttackPower().toString()
                )
            )
        }
        
        // 操作统计
        item {
            StatsSection(
                title = "操作统计",
                stats = listOf(
                    "总移动次数" to statistics.totalMoves.toString(),
                    "总旋转次数" to statistics.totalRotations.toString(),
                    "总下降次数" to statistics.totalDrops.toString(),
                    "软降次数" to statistics.softDrops.toString(),
                    "硬降次数" to statistics.hardDrops.toString(),
                    "Hold使用次数" to statistics.holdUsed.toString()
                )
            )
        }
        
        // 高级统计
        item {
            StatsSection(
                title = "高级统计",
                stats = listOf(
                    "完美消除" to statistics.perfectClears.toString(),
                    "平均高度" to "%.1f".format(statistics.averageHeight),
                    "最大高度" to statistics.maxHeight.toString(),
                    "当前干旱长度" to statistics.droughtLength.toString(),
                    "最大干旱长度" to statistics.maxDroughtLength.toString(),
                    "连续特殊消除" to statistics.backToBackCount.toString(),
                    "最大连续特殊消除" to statistics.maxBackToBack.toString()
                )
            )
        }
        
        // 方块统计
        item {
            PieceStatsSection(
                title = "方块统计",
                pieceStats = statistics.pieceStats
            )
        }
    }
}

@Composable
private fun StatsSection(
    title: String,
    stats: List<Pair<String, String>>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            HorizontalDivider()
            
            stats.forEach { (label, value) ->
                StatItem(label = label, value = value)
            }
        }
    }
}

@Composable
private fun PieceStatsSection(
    title: String,
    pieceStats: Map<TetrisPieceType, Int>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            HorizontalDivider()
            
            TetrisPieceType.entries.forEach { pieceType ->
                val count = pieceStats[pieceType] ?: 0
                StatItem(
                    label = pieceType.name,
                    value = count.toString()
                )
            }
            
            // 总计
            val total = pieceStats.values.sum()
            if (total > 0) {
                HorizontalDivider()
                StatItem(
                    label = "总计",
                    value = total.toString(),
                    isTotal = true
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    isTotal: Boolean = false,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = if (isTotal) MaterialTheme.typography.bodyLarge else MaterialTheme.typography.bodyMedium,
            fontWeight = if (isTotal) FontWeight.Bold else FontWeight.Normal,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = value,
            style = if (isTotal) MaterialTheme.typography.bodyLarge else MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.End
        )
    }
}

// ===== Preview Functions =====

@Preview(showBackground = true, name = "Detailed Stats")
@Composable
private fun TetrisDetailedStatsPreview() {
    QuesticleTheme {
        TetrisDetailedStats(
            statistics = TetrisStatistics(
                piecesPlaced = 150,
                linesCleared = 45,
                singles = 20,
                doubles = 10,
                triples = 3,
                tetrises = 2,
                tSpins = 5,
                tSpinSingles = 2,
                tSpinDoubles = 2,
                tSpinTriples = 1,
                maxCombo = 8,
                totalMoves = 500,
                totalRotations = 200,
                totalDrops = 100,
                holdUsed = 15,
                softDrops = 80,
                hardDrops = 20,
                perfectClears = 1,
                maxLevel = 8,
                averageHeight = 6.5,
                maxHeight = 15,
                droughtLength = 12,
                maxDroughtLength = 25,
                backToBackCount = 3,
                maxBackToBack = 5,
                gameTime = 300000L, // 5 minutes
                pieceStats = mapOf(
                    TetrisPieceType.I to 20,
                    TetrisPieceType.O to 22,
                    TetrisPieceType.T to 25,
                    TetrisPieceType.S to 18,
                    TetrisPieceType.Z to 19,
                    TetrisPieceType.J to 23,
                    TetrisPieceType.L to 23
                )
            ).calculateEfficiency()
        )
    }
}

@Preview(showBackground = true, name = "Empty Stats")
@Composable
private fun TetrisDetailedStatsEmptyPreview() {
    QuesticleTheme {
        TetrisDetailedStats(
            statistics = TetrisStatistics()
        )
    }
}
