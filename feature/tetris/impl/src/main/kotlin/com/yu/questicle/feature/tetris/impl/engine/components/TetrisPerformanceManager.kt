package com.yu.questicle.feature.tetris.impl.engine.components

/**
 * 俄罗斯方块性能管理器
 * 负责监控和优化游戏性能
 */
interface TetrisPerformanceManager {
    
    /**
     * 初始化性能管理器
     */
    fun initialize()
    
    /**
     * 重置性能优化
     */
    fun resetOptimizations()
    
    /**
     * 开始性能监控
     * @param operationName 操作名称
     */
    fun startMonitoring(operationName: String)
    
    /**
     * 结束性能监控
     * @param operationName 操作名称
     * @return 操作耗时（毫秒）
     */
    fun endMonitoring(operationName: String): Long
    
    /**
     * 获取性能统计
     * @return 性能统计数据
     */
    fun getPerformanceStats(): Map<String, Any>
    
    /**
     * 自动调整优化级别
     * @param fps 当前帧率
     */
    fun autoAdjustOptimizationLevel(fps: Float)
    
    /**
     * 手动设置优化级别
     * @param level 优化级别（0-3）
     */
    fun setOptimizationLevel(level: Int)
    
    /**
     * 获取当前优化级别
     * @return 优化级别（0-3）
     */
    fun getOptimizationLevel(): Int
    
    /**
     * 获取内存使用情况
     * @return 内存使用统计（KB）
     */
    fun getMemoryUsage(): Map<String, Long>
}

/**
 * 俄罗斯方块性能管理器实现
 */
class TetrisPerformanceManagerImpl : TetrisPerformanceManager {
    
    // 性能监控数据
    private val operationTimes = mutableMapOf<String, Long>()
    private val operationStats = mutableMapOf<String, OperationStats>()
    
    // 优化级别（0=无优化，1=低，2=中，3=高）
    private var optimizationLevel = 1
    
    // 启动时间
    private val startTime = System.currentTimeMillis()
    
    override fun initialize() {
        // 重置所有统计
        operationTimes.clear()
        operationStats.clear()
        
        // 设置默认优化级别
        optimizationLevel = 1
    }
    
    override fun resetOptimizations() {
        optimizationLevel = 0
    }
    
    override fun startMonitoring(operationName: String) {
        operationTimes[operationName] = System.nanoTime()
    }
    
    override fun endMonitoring(operationName: String): Long {
        val startTime = operationTimes[operationName] ?: return 0
        val endTime = System.nanoTime()
        val duration = (endTime - startTime) / 1_000_000 // 转换为毫秒
        
        // 更新统计
        val stats = operationStats.getOrPut(operationName) { OperationStats() }
        stats.count++
        stats.totalTime += duration
        stats.minTime = minOf(stats.minTime, duration)
        stats.maxTime = maxOf(stats.maxTime, duration)
        
        return duration
    }
    
    override fun getPerformanceStats(): Map<String, Any> {
        val result = mutableMapOf<String, Any>()
        
        // 添加总体统计
        result["uptime"] = System.currentTimeMillis() - startTime
        result["optimizationLevel"] = optimizationLevel
        
        // 添加操作统计
        val operations = mutableMapOf<String, Map<String, Any>>()
        operationStats.forEach { (name, stats) ->
            operations[name] = mapOf(
                "count" to stats.count,
                "totalTime" to stats.totalTime,
                "avgTime" to if (stats.count > 0) stats.totalTime / stats.count else 0,
                "minTime" to stats.minTime,
                "maxTime" to stats.maxTime
            )
        }
        result["operations"] = operations
        
        // 添加内存使用
        result["memory"] = getMemoryUsage()
        
        return result
    }
    
    override fun autoAdjustOptimizationLevel(fps: Float) {
        optimizationLevel = when {
            fps < 30 -> 3 // 高优化
            fps < 45 -> 2 // 中优化
            fps < 55 -> 1 // 低优化
            else -> 0 // 无优化
        }
    }
    
    override fun setOptimizationLevel(level: Int) {
        optimizationLevel = level.coerceIn(0, 3)
    }
    
    override fun getOptimizationLevel(): Int = optimizationLevel
    
    override fun getMemoryUsage(): Map<String, Long> {
        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory() / 1024 // KB
        val freeMemory = runtime.freeMemory() / 1024 // KB
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory() / 1024 // KB
        
        return mapOf(
            "total" to totalMemory,
            "free" to freeMemory,
            "used" to usedMemory,
            "max" to maxMemory
        )
    }
    
    /**
     * 操作统计数据类
     */
    private data class OperationStats(
        var count: Long = 0,
        var totalTime: Long = 0,
        var minTime: Long = Long.MAX_VALUE,
        var maxTime: Long = 0
    )
}