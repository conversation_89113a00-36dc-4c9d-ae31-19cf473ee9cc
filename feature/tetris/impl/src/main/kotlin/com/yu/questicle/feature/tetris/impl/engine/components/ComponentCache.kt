package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Cache for frequently used component operations to reduce redundant calculations.
 * 
 * This class caches results of expensive operations to improve performance
 * in hot paths of the game engine.
 */
@Singleton
class ComponentCache @Inject constructor() {
    
    // Cache for collision detection results
    private val collisionCache = ConcurrentHashMap<CollisionCacheKey, Boolean>()
    
    // Cache for landing positions
    private val landingPositionCache = ConcurrentHashMap<LandingPositionCacheKey, Int>()
    
    // Cache for line clear scores
    private val scoreCache = ConcurrentHashMap<ScoreCacheKey, Int>()
    
    // Cache for rotation results
    private val rotationCache = ConcurrentHashMap<RotationCacheKey, RotationCacheResult>()
    
    // Cache statistics
    private var collisionCacheHits = 0
    private var collisionCacheMisses = 0
    private var landingPositionCacheHits = 0
    private var landingPositionCacheMisses = 0
    private var scoreCacheHits = 0
    private var scoreCacheMisses = 0
    private var rotationCacheHits = 0
    private var rotationCacheMisses = 0
    
    // Maximum cache sizes
    private val maxCollisionCacheSize = 1000
    private val maxLandingPositionCacheSize = 500
    private val maxScoreCacheSize = 200
    private val maxRotationCacheSize = 500
    
    /**
     * Get cached collision detection result or compute and cache it.
     */
    fun getCachedCollisionResult(
        piece: TetrisPiece,
        board: TetrisBoard,
        compute: () -> Boolean
    ): Boolean {
        val key = CollisionCacheKey(piece, board.hashCode())
        
        // Check cache
        val cachedResult = collisionCache[key]
        if (cachedResult != null) {
            collisionCacheHits++
            return cachedResult
        }
        
        // Compute result
        collisionCacheMisses++
        val result = compute()
        
        // Cache result
        if (collisionCache.size < maxCollisionCacheSize) {
            collisionCache[key] = result
        }
        
        return result
    }
    
    /**
     * Get cached landing position or compute and cache it.
     */
    fun getCachedLandingPosition(
        piece: TetrisPiece,
        board: TetrisBoard,
        compute: () -> Int
    ): Int {
        val key = LandingPositionCacheKey(piece, board.hashCode())
        
        // Check cache
        val cachedResult = landingPositionCache[key]
        if (cachedResult != null) {
            landingPositionCacheHits++
            return cachedResult
        }
        
        // Compute result
        landingPositionCacheMisses++
        val result = compute()
        
        // Cache result
        if (landingPositionCache.size < maxLandingPositionCacheSize) {
            landingPositionCache[key] = result
        }
        
        return result
    }
    
    /**
     * Get cached score calculation or compute and cache it.
     */
    fun getCachedScore(
        linesCleared: Int,
        level: Int,
        combo: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        isBackToBack: Boolean,
        compute: () -> Int
    ): Int {
        val key = ScoreCacheKey(linesCleared, level, combo, isTSpin, isPerfectClear, isBackToBack)
        
        // Check cache
        val cachedResult = scoreCache[key]
        if (cachedResult != null) {
            scoreCacheHits++
            return cachedResult
        }
        
        // Compute result
        scoreCacheMisses++
        val result = compute()
        
        // Cache result
        if (scoreCache.size < maxScoreCacheSize) {
            scoreCache[key] = result
        }
        
        return result
    }
    
    /**
     * Get cached rotation result or compute and cache it.
     */
    fun getCachedRotationResult(
        piece: TetrisPiece,
        clockwise: Boolean,
        board: TetrisBoard,
        compute: () -> RotationCacheResult
    ): RotationCacheResult {
        val key = RotationCacheKey(piece, clockwise, board.hashCode())
        
        // Check cache
        val cachedResult = rotationCache[key]
        if (cachedResult != null) {
            rotationCacheHits++
            return cachedResult
        }
        
        // Compute result
        rotationCacheMisses++
        val result = compute()
        
        // Cache result
        if (rotationCache.size < maxRotationCacheSize) {
            rotationCache[key] = result
        }
        
        return result
    }
    
    /**
     * Invalidate caches when the board changes.
     */
    fun invalidateBoardDependentCaches() {
        collisionCache.clear()
        landingPositionCache.clear()
        rotationCache.clear()
    }
    
    /**
     * Get cache statistics.
     */
    fun getCacheStatistics(): Map<String, Any> {
        return mapOf(
            "collisionCache" to mapOf(
                "size" to collisionCache.size,
                "hits" to collisionCacheHits,
                "misses" to collisionCacheMisses,
                "hitRate" to if (collisionCacheHits + collisionCacheMisses > 0) {
                    collisionCacheHits.toFloat() / (collisionCacheHits + collisionCacheMisses)
                } else 0f
            ),
            "landingPositionCache" to mapOf(
                "size" to landingPositionCache.size,
                "hits" to landingPositionCacheHits,
                "misses" to landingPositionCacheMisses,
                "hitRate" to if (landingPositionCacheHits + landingPositionCacheMisses > 0) {
                    landingPositionCacheHits.toFloat() / (landingPositionCacheHits + landingPositionCacheMisses)
                } else 0f
            ),
            "scoreCache" to mapOf(
                "size" to scoreCache.size,
                "hits" to scoreCacheHits,
                "misses" to scoreCacheMisses,
                "hitRate" to if (scoreCacheHits + scoreCacheMisses > 0) {
                    scoreCacheHits.toFloat() / (scoreCacheHits + scoreCacheMisses)
                } else 0f
            ),
            "rotationCache" to mapOf(
                "size" to rotationCache.size,
                "hits" to rotationCacheHits,
                "misses" to rotationCacheMisses,
                "hitRate" to if (rotationCacheHits + rotationCacheMisses > 0) {
                    rotationCacheHits.toFloat() / (rotationCacheHits + rotationCacheMisses)
                } else 0f
            )
        )
    }
    
    /**
     * Reset cache statistics.
     */
    fun resetCacheStatistics() {
        collisionCacheHits = 0
        collisionCacheMisses = 0
        landingPositionCacheHits = 0
        landingPositionCacheMisses = 0
        scoreCacheHits = 0
        scoreCacheMisses = 0
        rotationCacheHits = 0
        rotationCacheMisses = 0
    }
    
    /**
     * Clear all caches.
     */
    fun clearAllCaches() {
        collisionCache.clear()
        landingPositionCache.clear()
        scoreCache.clear()
        rotationCache.clear()
        resetCacheStatistics()
    }
    
    /**
     * Configure cache sizes based on performance settings.
     */
    fun configureCacheSizes(
        collisionCacheSize: Int,
        landingPositionCacheSize: Int,
        scoreCacheSize: Int,
        rotationCacheSize: Int
    ) {
        // Resize caches if needed
        if (collisionCache.size > collisionCacheSize) {
            collisionCache.clear()
        }
        
        if (landingPositionCache.size > landingPositionCacheSize) {
            landingPositionCache.clear()
        }
        
        if (scoreCache.size > scoreCacheSize) {
            scoreCache.clear()
        }
        
        if (rotationCache.size > rotationCacheSize) {
            rotationCache.clear()
        }
    }
    
    /**
     * Cache key for collision detection.
     */
    private data class CollisionCacheKey(
        val piece: TetrisPiece,
        val boardHash: Int
    )
    
    /**
     * Cache key for landing position.
     */
    private data class LandingPositionCacheKey(
        val piece: TetrisPiece,
        val boardHash: Int
    )
    
    /**
     * Cache key for score calculation.
     */
    private data class ScoreCacheKey(
        val linesCleared: Int,
        val level: Int,
        val combo: Int,
        val isTSpin: Boolean,
        val isPerfectClear: Boolean,
        val isBackToBack: Boolean
    )
    
    /**
     * Cache key for rotation.
     */
    private data class RotationCacheKey(
        val piece: TetrisPiece,
        val clockwise: Boolean,
        val boardHash: Int
    )
    
    /**
     * Cache result for rotation.
     */
    data class RotationCacheResult(
        val success: Boolean,
        val resultPiece: TetrisPiece?,
        val isWallKick: Boolean
    )
}