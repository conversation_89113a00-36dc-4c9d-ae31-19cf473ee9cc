package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of TetrisCollisionDetector with optimized collision detection algorithms.
 * 
 * Features:
 * - Early boundary checking for fast rejection
 * - Spatial partitioning for efficient collision detection
 * - Multi-level caching for repeated queries
 * - Binary search optimization for hard drop calculations
 * - Algorithm flexibility with strategy pattern
 */
@Singleton
class TetrisCollisionDetectorImpl @Inject constructor(
    private val performanceManager: TetrisPerformanceManager,
    private val cacheManager: TetrisCacheManager
) : TetrisCollisionDetector {
    
    // Cache for collision detection results
    private val collisionCache = ConcurrentHashMap<String, Boolean>()
    
    // Spatial grid for optimized collision detection
    private val spatialGrid = SpatialGrid(10, 20)
    
    // Available collision detection algorithms
    private val collisionAlgorithms = mapOf<String, CollisionDetectionAlgorithm>(
        "standard" to StandardCollisionDetection(),
        "spatial" to SpatialCollisionDetection(spatialGrid),
        "optimized" to OptimizedCollisionDetection(spatialGrid)
    )
    
    // Current algorithm - can be changed at runtime
    private var currentAlgorithm: String = "optimized"
    
    /**
     * Set the collision detection algorithm to use
     */
    fun setCollisionAlgorithm(algorithmName: String) {
        if (algorithmName in collisionAlgorithms) {
            currentAlgorithm = algorithmName
        }
    }
    
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // Use cache if enabled
        if (performanceManager.shouldUseOptimization(OptimizationType.CACHING)) {
            val cacheKey = CacheKey.forCollision(
                pieceType = piece.type.name,
                x = piece.x,
                y = piece.y,
                rotation = piece.rotation,
                boardHash = board.hashCode()
            )
            
            return cacheManager.getCached(cacheKey) {
                // Use the selected algorithm
                collisionAlgorithms[currentAlgorithm]?.isValidPosition(piece, board) ?: false
            }
        }
        
        // Direct check without caching
        return collisionAlgorithms[currentAlgorithm]?.isValidPosition(piece, board) ?: false
    }
    
    override fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        // Use cache if enabled
        if (performanceManager.shouldUseOptimization(OptimizationType.CACHING)) {
            val cacheKey = CacheKey.forGhostPiece(
                pieceType = piece.type.name,
                x = piece.x,
                y = piece.y,
                rotation = piece.rotation,
                boardHash = board.hashCode()
            )
            
            return cacheManager.getCached(cacheKey) {
                // Use the selected algorithm
                collisionAlgorithms[currentAlgorithm]?.findLandingPosition(piece, board) ?: piece
            }
        }
        
        // Direct calculation without caching
        return collisionAlgorithms[currentAlgorithm]?.findLandingPosition(piece, board) ?: piece
    }
    
    /**
     * Check collision using spatial partitioning for better performance
     */
    private fun checkCollisionWithSpatialPartitioning(piece: TetrisPiece, board: TetrisBoard): Boolean {
        return spatialGrid.checkCollision(piece, board)
    }
    
    /**
     * Detailed collision check that examines each cell of the piece
     */
    private fun checkCollisionDetailed(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val shape = getPieceShape(piece)
        
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    
                    // Check if out of bounds
                    if (boardX < 0 || boardX >= board.width || 
                        boardY < 0 || boardY >= board.height) {
                        return false
                    }
                    
                    // Check if collides with another piece
                    if (boardY >= 0 && board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                        return false
                    }
                }
            }
        }
        
        return true
    }
    
    /**
     * Get piece dimensions based on type and rotation
     */
    private fun getPieceDimensions(piece: TetrisPiece): Pair<Int, Int> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 2) {
                0 -> 4 to 1  // Horizontal I piece
                else -> 1 to 4  // Vertical I piece
            }
            TetrisPieceType.O -> 2 to 2  // O piece is always 2x2
            else -> 3 to 2  // Most other pieces fit in 3x2
        }
    }
    
    override fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo {
        // Use cache if enabled
        if (performanceManager.shouldUseOptimization(OptimizationType.CACHING)) {
            val cacheKey = CacheKey(
                type = "collision_info",
                id = "${piece.type.name}_${piece.x}_${piece.y}_${piece.rotation}",
                params = mapOf("boardHash" to board.hashCode())
            )
            
            return cacheManager.getCached(cacheKey) {
                // Use the selected algorithm
                collisionAlgorithms[currentAlgorithm]?.detectCollisions(piece, board) ?: 
                    CollisionInfo(hasCollision = false)
            }
        }
        
        // Direct calculation without caching
        return collisionAlgorithms[currentAlgorithm]?.detectCollisions(piece, board) ?: 
            CollisionInfo(hasCollision = false)
    }
    
    override fun isWithinBounds(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val shape = getPieceShape(piece)
        
        // Check piece boundaries
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    
                    // Check if any part of the piece is outside the board
                    if (boardX < 0 || boardX >= board.width || 
                        boardY < 0 || boardY >= board.height) {
                        return false
                    }
                }
            }
        }
        
        return true
    }
    
    override fun clearCache(pieceType: String?) {
        if (pieceType == null) {
            // Clear all collision-related caches
            collisionCache.clear()
            cacheManager.invalidateCache("collision")
            cacheManager.invalidateCache("ghost")
            cacheManager.invalidateCache("collision_info")
        } else {
            // Clear only caches related to the specified piece type
            val keysToRemove = collisionCache.keys.filter { it.startsWith(pieceType) }
            keysToRemove.forEach { collisionCache.remove(it) }
            
            cacheManager.invalidateCache("collision_${pieceType}")
            cacheManager.invalidateCache("ghost_${pieceType}")
            cacheManager.invalidateCache("collision_info_${pieceType}")
        }
    }
    
    /**
     * Get available collision detection algorithms
     */
    fun getAvailableAlgorithms(): List<String> {
        return collisionAlgorithms.keys.toList()
    }
    
    /**
     * Get current collision detection algorithm
     */
    fun getCurrentAlgorithm(): String {
        return currentAlgorithm
    }
    
    /**
     * Get cache statistics for collision detection
     */
    fun getCacheStatistics(): Map<String, Any> {
        val cacheStats = cacheManager.getCacheStatistics()
        
        return mapOf(
            "hitRate" to cacheStats.hitRate,
            "size" to cacheStats.size,
            "hits" to cacheStats.hits,
            "misses" to cacheStats.misses,
            "collisionCacheSize" to collisionCache.size,
            "algorithm" to currentAlgorithm
        )
    }
    
    // Helper methods
    
    private fun checkCollisionDetailed(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val shape = getPieceShape(piece)
        
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    
                    // Check if out of bounds
                    if (boardX < 0 || boardX >= board.width || 
                        boardY < 0 || boardY >= board.height) {
                        return false
                    }
                    
                    // Check if collides with another piece
                    if (boardY >= 0 && board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                        return false
                    }
                }
            }
        }
        
        return true
    }
    
    private fun getPieceWidth(piece: TetrisPiece): Int {
        return getPieceShape(piece)[0].size
    }
    
    private fun getPieceHeight(piece: TetrisPiece): Int {
        return getPieceShape(piece).size
    }
    
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        // This is a simplified version - in a real implementation, we would have
        // proper piece shape definitions for all rotations
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 2) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            TetrisPieceType.T -> when (piece.rotation) {
                0 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, true, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            // Simplified shapes for other piece types
            else -> arrayOf(
                booleanArrayOf(true, true, true),
                booleanArrayOf(true, false, false),
                booleanArrayOf(false, false, false)
            )
        }
    }
}