package com.yu.questicle.feature.tetris.impl.engine.memory

import com.yu.questicle.core.domain.model.tetris.GameStatistics

/**
 * Immutable implementation of GameStatistics with structural sharing.
 * This class provides efficient updates through the with() method that only
 * creates new objects for changed properties, sharing unchanged properties.
 */
class ImmutableGameStatistics private constructor(
    override val piecesPlaced: Int,
    override val linesCleared: Int,
    override val singles: Int,
    override val doubles: Int,
    override val triples: Int,
    override val tetrises: Int,
    override val tSpins: Int,
    override val maxCombo: Int,
    override val perfectClears: Int,
    override val backToBackTetrises: Int,
    override val backToBackTSpins: Int
) : GameStatistics {

    /**
     * Creates a new ImmutableGameStatistics with the specified properties changed,
     * while keeping all other properties the same (structural sharing).
     */
    fun with(
        piecesPlaced: Int = this.piecesPlaced,
        linesCleared: Int = this.linesCleared,
        singles: Int = this.singles,
        doubles: Int = this.doubles,
        triples: Int = this.triples,
        tetrises: Int = this.tetrises,
        tSpins: Int = this.tSpins,
        maxCombo: Int = this.maxCombo,
        perfectClears: Int = this.perfectClears,
        backToBackTetrises: Int = this.backToBackTetrises,
        backToBackTSpins: Int = this.backToBackTSpins
    ): ImmutableGameStatistics {
        // Only create a new instance if at least one property has changed
        if (piecesPlaced == this.piecesPlaced &&
            linesCleared == this.linesCleared &&
            singles == this.singles &&
            doubles == this.doubles &&
            triples == this.triples &&
            tetrises == this.tetrises &&
            tSpins == this.tSpins &&
            maxCombo == this.maxCombo &&
            perfectClears == this.perfectClears &&
            backToBackTetrises == this.backToBackTetrises &&
            backToBackTSpins == this.backToBackTSpins) {
            return this
        }

        // Create a new instance with the updated properties
        return ImmutableGameStatistics(
            piecesPlaced = piecesPlaced,
            linesCleared = linesCleared,
            singles = singles,
            doubles = doubles,
            triples = triples,
            tetrises = tetrises,
            tSpins = tSpins,
            maxCombo = maxCombo,
            perfectClears = perfectClears,
            backToBackTetrises = backToBackTetrises,
            backToBackTSpins = backToBackTSpins
        )
    }

    /**
     * Updates statistics based on a line clear event.
     */
    fun withLineClear(
        linesCleared: Int,
        isTSpin: Boolean = false,
        isPerfectClear: Boolean = false,
        isBackToBack: Boolean = false
    ): ImmutableGameStatistics {
        val newLinesCleared = this.linesCleared + linesCleared
        val newSingles = if (linesCleared == 1) singles + 1 else singles
        val newDoubles = if (linesCleared == 2) doubles + 1 else doubles
        val newTriples = if (linesCleared == 3) triples + 1 else triples
        val newTetrises = if (linesCleared == 4) tetrises + 1 else tetrises
        val newTSpins = if (isTSpin) tSpins + 1 else tSpins
        val newPerfectClears = if (isPerfectClear) perfectClears + 1 else perfectClears
        val newBackToBackTetrises = if (isBackToBack && linesCleared == 4) backToBackTetrises + 1 else backToBackTetrises
        val newBackToBackTSpins = if (isBackToBack && isTSpin) backToBackTSpins + 1 else backToBackTSpins

        return with(
            linesCleared = newLinesCleared,
            singles = newSingles,
            doubles = newDoubles,
            triples = newTriples,
            tetrises = newTetrises,
            tSpins = newTSpins,
            perfectClears = newPerfectClears,
            backToBackTetrises = newBackToBackTetrises,
            backToBackTSpins = newBackToBackTSpins
        )
    }

    /**
     * Updates statistics when a piece is placed.
     */
    fun withPiecePlaced(): ImmutableGameStatistics {
        return with(piecesPlaced = piecesPlaced + 1)
    }

    /**
     * Updates statistics with a new combo value.
     */
    fun withCombo(combo: Int): ImmutableGameStatistics {
        return if (combo > maxCombo) {
            with(maxCombo = combo)
        } else {
            this
        }
    }

    /**
     * Converts this immutable game statistics to a standard game statistics.
     */
    fun toStandardStatistics(): GameStatistics {
        return object : GameStatistics {
            override val piecesPlaced: Int = <EMAIL>
            override val linesCleared: Int = <EMAIL>
            override val singles: Int = <EMAIL>
            override val doubles: Int = <EMAIL>
            override val triples: Int = <EMAIL>
            override val tetrises: Int = <EMAIL>
            override val tSpins: Int = <EMAIL>
            override val maxCombo: Int = <EMAIL>
            override val perfectClears: Int = <EMAIL>
            override val backToBackTetrises: Int = <EMAIL>
            override val backToBackTSpins: Int = <EMAIL>
        }
    }

    companion object {
        /**
         * Creates an ImmutableGameStatistics from a standard GameStatistics.
         */
        fun from(statistics: GameStatistics): ImmutableGameStatistics {
            // If it's already an ImmutableGameStatistics, return it directly
            if (statistics is ImmutableGameStatistics) {
                return statistics
            }

            return ImmutableGameStatistics(
                piecesPlaced = statistics.piecesPlaced,
                linesCleared = statistics.linesCleared,
                singles = statistics.singles,
                doubles = statistics.doubles,
                triples = statistics.triples,
                tetrises = statistics.tetrises,
                tSpins = statistics.tSpins,
                maxCombo = statistics.maxCombo,
                perfectClears = statistics.perfectClears,
                backToBackTetrises = statistics.backToBackTetrises,
                backToBackTSpins = statistics.backToBackTSpins
            )
        }

        /**
         * Creates an empty ImmutableGameStatistics.
         */
        fun empty(): ImmutableGameStatistics {
            return ImmutableGameStatistics(
                piecesPlaced = 0,
                linesCleared = 0,
                singles = 0,
                doubles = 0,
                triples = 0,
                tetrises = 0,
                tSpins = 0,
                maxCombo = 0,
                perfectClears = 0,
                backToBackTetrises = 0,
                backToBackTSpins = 0
            )
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is GameStatistics) return false

        if (piecesPlaced != other.piecesPlaced) return false
        if (linesCleared != other.linesCleared) return false
        if (singles != other.singles) return false
        if (doubles != other.doubles) return false
        if (triples != other.triples) return false
        if (tetrises != other.tetrises) return false
        if (tSpins != other.tSpins) return false
        if (maxCombo != other.maxCombo) return false
        if (perfectClears != other.perfectClears) return false
        if (backToBackTetrises != other.backToBackTetrises) return false
        if (backToBackTSpins != other.backToBackTSpins) return false

        return true
    }

    override fun hashCode(): Int {
        var result = piecesPlaced
        result = 31 * result + linesCleared
        result = 31 * result + singles
        result = 31 * result + doubles
        result = 31 * result + triples
        result = 31 * result + tetrises
        result = 31 * result + tSpins
        result = 31 * result + maxCombo
        result = 31 * result + perfectClears
        result = 31 * result + backToBackTetrises
        result = 31 * result + backToBackTSpins
        return result
    }

    override fun toString(): String {
        return "ImmutableGameStatistics(piecesPlaced=$piecesPlaced, linesCleared=$linesCleared, maxCombo=$maxCombo)"
    }
}