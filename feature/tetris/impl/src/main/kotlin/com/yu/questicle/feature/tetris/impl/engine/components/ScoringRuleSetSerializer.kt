package com.yu.questicle.feature.tetris.impl.engine.components

import kotlinx.serialization.json.*
import java.io.File

/**
 * Serializer for ScoringRuleSet to enable saving and loading custom rule sets.
 */
object ScoringRuleSetSerializer {
    
    /**
     * Convert a ScoringRuleSet to JSON
     */
    fun toJson(ruleSet: ScoringRuleSet): JsonObject {
        return buildJsonObject {
            put("name", ruleSet.name)
            put("description", ruleSet.description)
            
            // Line clear scores
            putJsonObject("lineClearScores") {
                ruleSet.lineClearScores.forEach { (lines, score) ->
                    put(lines.toString(), score)
                }
            }
            
            // T-Spin multipliers
            putJsonObject("tSpinMultipliers") {
                ruleSet.tSpinMultipliers.forEach { (lines, multiplier) ->
                    put(lines.toString(), multiplier)
                }
            }
            
            put("perfectClearBonus", ruleSet.perfectClearBonus)
            put("backToBackMultiplier", ruleSet.backToBackMultiplier)
            put("softDropPointsPerCell", ruleSet.softDropPointsPerCell)
            put("hardDropPointsPerCell", ruleSet.hardDropPointsPerCell)
            put("maxLevel", ruleSet.maxLevel)
            put("linesPerLevel", ruleSet.linesPerLevel)
            put("baseDropIntervalMs", ruleSet.baseDropIntervalMs)
            put("minDropIntervalMs", ruleSet.minDropIntervalMs)
            
            // Combo multipliers
            putJsonArray("comboMultipliers") {
                ruleSet.comboMultipliers.forEach { multiplier ->
                    add(multiplier)
                }
            }
            
            // Special combo bonuses
            putJsonObject("specialComboBonuses") {
                ruleSet.specialComboBonuses.forEach { (combo, bonus) ->
                    put(combo, bonus)
                }
            }
        }
    }
    
    /**
     * Convert JSON to a ScoringRuleSet
     */
    fun fromJson(json: JsonObject): ScoringRuleSet {
        val name = json["name"]?.jsonPrimitive?.content ?: "Custom"
        val description = json["description"]?.jsonPrimitive?.content ?: "Custom scoring rule set"
        
        // Parse line clear scores
        val lineClearScores = mutableMapOf<Int, Int>()
        json["lineClearScores"]?.jsonObject?.forEach { (lines, scoreJson) ->
            val linesInt = lines.toIntOrNull() ?: return@forEach
            val score = scoreJson.jsonPrimitive.intOrNull ?: return@forEach
            lineClearScores[linesInt] = score
        }
        
        // Parse T-Spin multipliers
        val tSpinMultipliers = mutableMapOf<Int, Float>()
        json["tSpinMultipliers"]?.jsonObject?.forEach { (lines, multiplierJson) ->
            val linesInt = lines.toIntOrNull() ?: return@forEach
            val multiplier = multiplierJson.jsonPrimitive.floatOrNull ?: return@forEach
            tSpinMultipliers[linesInt] = multiplier
        }
        
        // Parse other simple properties
        val perfectClearBonus = json["perfectClearBonus"]?.jsonPrimitive?.intOrNull ?: 1000
        val backToBackMultiplier = json["backToBackMultiplier"]?.jsonPrimitive?.floatOrNull ?: 1.5f
        val softDropPointsPerCell = json["softDropPointsPerCell"]?.jsonPrimitive?.intOrNull ?: 1
        val hardDropPointsPerCell = json["hardDropPointsPerCell"]?.jsonPrimitive?.intOrNull ?: 2
        val maxLevel = json["maxLevel"]?.jsonPrimitive?.intOrNull ?: 20
        val linesPerLevel = json["linesPerLevel"]?.jsonPrimitive?.intOrNull ?: 10
        val baseDropIntervalMs = json["baseDropIntervalMs"]?.jsonPrimitive?.longOrNull ?: 1000L
        val minDropIntervalMs = json["minDropIntervalMs"]?.jsonPrimitive?.longOrNull ?: 100L
        
        // Parse combo multipliers
        val comboMultipliers = mutableListOf<Float>()
        json["comboMultipliers"]?.jsonArray?.forEach { multiplierJson ->
            val multiplier = multiplierJson.jsonPrimitive.floatOrNull ?: return@forEach
            comboMultipliers.add(multiplier)
        }
        
        // Parse special combo bonuses
        val specialComboBonuses = mutableMapOf<String, Int>()
        json["specialComboBonuses"]?.jsonObject?.forEach { (combo, bonusJson) ->
            val bonus = bonusJson.jsonPrimitive.intOrNull ?: return@forEach
            specialComboBonuses[combo] = bonus
        }
        
        return ScoringRuleSet(
            name = name,
            description = description,
            lineClearScores = lineClearScores,
            tSpinMultipliers = tSpinMultipliers,
            perfectClearBonus = perfectClearBonus,
            backToBackMultiplier = backToBackMultiplier,
            softDropPointsPerCell = softDropPointsPerCell,
            hardDropPointsPerCell = hardDropPointsPerCell,
            maxLevel = maxLevel,
            linesPerLevel = linesPerLevel,
            baseDropIntervalMs = baseDropIntervalMs,
            minDropIntervalMs = minDropIntervalMs,
            comboMultipliers = comboMultipliers,
            specialComboBonuses = specialComboBonuses
        )
    }
    
    /**
     * Save a rule set to a file
     */
    fun saveToFile(ruleSet: ScoringRuleSet, file: File): Boolean {
        return try {
            val json = toJson(ruleSet)
            file.writeText(json.toString())
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Load a rule set from a file
     */
    fun loadFromFile(file: File): ScoringRuleSet? {
        return try {
            val jsonString = file.readText()
            val json = Json.parseToJsonElement(jsonString).jsonObject
            fromJson(json)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Save multiple rule sets to a file
     */
    fun saveRuleSetsToFile(ruleSets: List<ScoringRuleSet>, file: File): Boolean {
        return try {
            val jsonArray = buildJsonArray {
                ruleSets.forEach { ruleSet ->
                    add(toJson(ruleSet))
                }
            }
            file.writeText(jsonArray.toString())
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Load multiple rule sets from a file
     */
    fun loadRuleSetsFromFile(file: File): List<ScoringRuleSet> {
        return try {
            val jsonString = file.readText()
            val jsonArray = Json.parseToJsonElement(jsonString).jsonArray
            jsonArray.mapNotNull { 
                try {
                    fromJson(it.jsonObject)
                } catch (e: Exception) {
                    null
                }
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
}