package com.yu.questicle.feature.tetris.impl.engine.board

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Adapter for integrating BitPackedTetrisBoard with the existing TetrisBoard interface.
 * 
 * This adapter provides methods to convert between standard TetrisBoard and BitPackedTetrisBoard,
 * and implements the TetrisBoard extension methods using the optimized bit-packed representation.
 */
@Singleton
class TetrisBoardAdapter @Inject constructor(
    private val boardFactory: TetrisBoardFactory
) {
    
    /**
     * Convert a standard TetrisBoard to a BitPackedTetrisBoard
     */
    fun toBitPackedBoard(board: TetrisBoard): BitPackedTetrisBoard {
        return BitPackedTetrisBoard.fromCells(board.cells)
    }
    
    /**
     * Place a piece on the board
     */
    fun placePiece(board: TetrisBoard, piece: TetrisPiece): TetrisBoard {
        val bitPackedBoard = toBitPackedBoard(board)
        return bitPackedBoard.placePiece(piece)
    }
    
    /**
     * Check if a piece can be placed at its current position
     */
    fun canPlacePiece(board: TetrisBoard, piece: TetrisPiece): Boolean {
        val bitPackedBoard = toBitPackedBoard(board)
        return bitPackedBoard.canPlacePiece(piece)
    }
    
    /**
     * Clear completed lines and return the new board and the indices of cleared lines
     */
    fun clearCompletedLines(board: TetrisBoard): Pair<TetrisBoard, List<Int>> {
        val bitPackedBoard = toBitPackedBoard(board)
        return bitPackedBoard.clearCompletedLines()
    }
    
    /**
     * Check if a row is completely filled
     */
    fun isRowFilled(board: TetrisBoard, y: Int): Boolean {
        val bitPackedBoard = toBitPackedBoard(board)
        return bitPackedBoard.isRowFilled(y)
    }
    
    /**
     * Check if a row is completely empty
     */
    fun isRowEmpty(board: TetrisBoard, y: Int): Boolean {
        val bitPackedBoard = toBitPackedBoard(board)
        return bitPackedBoard.isRowEmpty(y)
    }
    
    /**
     * Create an empty board with the specified dimensions
     */
    fun createEmptyBoard(width: Int = 10, height: Int = 20): TetrisBoard {
        return boardFactory.createEmptyBoard(width, height)
    }
    
    /**
     * Create a board from a 2D array of cell types
     */
    fun createBoardFromCells(cells: Array<Array<TetrisCellType>>): TetrisBoard {
        return boardFactory.createBoardFromCells(cells)
    }
}

/**
 * Extension function to place a piece on the board
 */
fun TetrisBoard.placePiece(piece: TetrisPiece): TetrisBoard {
    val bitPackedBoard = BitPackedTetrisBoard.fromCells(this.cells)
    return bitPackedBoard.placePiece(piece)
}

/**
 * Extension function to check if a piece can be placed at its current position
 */
fun TetrisBoard.canPlacePiece(piece: TetrisPiece): Boolean {
    val bitPackedBoard = BitPackedTetrisBoard.fromCells(this.cells)
    return bitPackedBoard.canPlacePiece(piece)
}

/**
 * Extension function to clear completed lines
 */
fun TetrisBoard.clearLines(): Pair<TetrisBoard, Int> {
    val bitPackedBoard = BitPackedTetrisBoard.fromCells(this.cells)
    val (newBoard, clearedLines) = bitPackedBoard.clearCompletedLines()
    return newBoard to clearedLines.size
}

/**
 * Extension function to check if the board is empty
 */
fun TetrisBoard.isEmpty(): Boolean {
    for (y in 0 until height) {
        for (x in 0 until width) {
            if (cells[y][x] != TetrisCellType.EMPTY) {
                return false
            }
        }
    }
    return true
}

/**
 * Extension function to check if the board is full
 */
fun TetrisBoard.isFull(): Boolean {
    for (y in 0 until height) {
        for (x in 0 until width) {
            if (cells[y][x] == TetrisCellType.EMPTY) {
                return false
            }
        }
    }
    return true
}

/**
 * Extension function to get the number of filled cells
 */
fun TetrisBoard.getFilledCellCount(): Int {
    var count = 0
    for (y in 0 until height) {
        for (x in 0 until width) {
            if (cells[y][x] != TetrisCellType.EMPTY) {
                count++
            }
        }
    }
    return count
}

/**
 * Extension function to check if a position is valid for a piece
 */
fun TetrisBoard.isValidPosition(piece: TetrisPiece): Boolean {
    val bitPackedBoard = BitPackedTetrisBoard.fromCells(this.cells)
    return bitPackedBoard.canPlacePiece(piece)
}