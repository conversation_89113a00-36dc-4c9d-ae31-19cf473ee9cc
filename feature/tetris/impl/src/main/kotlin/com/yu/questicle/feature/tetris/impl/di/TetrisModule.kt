package com.yu.questicle.feature.tetris.impl.di

import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.feature.tetris.impl.engine.TetrisEngineImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class TetrisModule {

    @Binds
    @Singleton
    abstract fun bindTetrisEngine(
        tetrisEngineImpl: TetrisEngineImpl
    ): TetrisEngine
}
