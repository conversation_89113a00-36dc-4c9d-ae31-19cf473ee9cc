package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.rotation.SuperRotationSystem
import com.yu.questicle.core.domain.rotation.TSpinType
import com.yu.questicle.core.domain.generator.TetrisPieceGenerator
import com.yu.questicle.core.domain.generator.TetrisPieceGeneratorFactory
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * Implementation of TetrisGameLogicProcessor that handles core game mechanics.
 * 
 * Features:
 * - Piece movement and rotation with wall kick support
 * - Line clearing and scoring
 * - T-spin detection
 * - Game state validation
 * - Next piece generation with bag randomizer
 */
@Singleton
class TetrisGameLogicProcessorImpl @Inject constructor(
    private val collisionDetector: TetrisCollisionDetector,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val performanceManager: TetrisPerformanceManager,
    private val boardClearingProcessor: BoardClearingProcessor,
    private val gameStateValidator: GameStateValidator
) : TetrisGameLogicProcessor {
    
    // Piece generator using 7-bag randomization
    private val pieceGenerator: TetrisPieceGenerator = TetrisPieceGeneratorFactory.createSevenBag()
    
    // Super Rotation System for wall kicks
    private val rotationSystem = SuperRotationSystem()
    
    // Track the last move for T-spin detection
    private var lastMoveWasRotation = false
    private var lastRotationPosition: Pair<Int, Int>? = null
    
    override suspend fun processMove(
        piece: TetrisPiece, 
        direction: Direction, 
        board: TetrisBoard
    ): GameMoveResult {
        val startTime = System.nanoTime()
        
        try {
            // Reset rotation tracking
            lastMoveWasRotation = false
            
            // Calculate new position based on direction
            val dx = when (direction) {
                Direction.LEFT -> -1
                Direction.RIGHT -> 1
                Direction.DOWN -> 0
            }
            
            val dy = when (direction) {
                Direction.DOWN -> 1
                else -> 0
            }
            
            // Create new piece with updated position
            val newPiece = piece.copy(
                x = piece.x + dx,
                y = piece.y + dy
            )
            
            // Check if new position is valid
            return if (collisionDetector.isValidPosition(newPiece, board)) {
                GameMoveResult.Success(newPiece)
            } else if (direction == Direction.DOWN) {
                // If moving down and blocked, the piece has landed
                GameMoveResult.Landed(piece)
            } else {
                // Otherwise just blocked
                GameMoveResult.Blocked(piece)
            }
        } finally {
            // Track operation performance
            performanceManager.trackOperation("processMove", System.nanoTime() - startTime)
        }
    }
    
    override suspend fun processRotation(
        piece: TetrisPiece, 
        clockwise: Boolean, 
        board: TetrisBoard
    ): GameRotationResult {
        val startTime = System.nanoTime()
        
        try {
            // Set rotation tracking for T-spin detection
            lastMoveWasRotation = true
            lastRotationPosition = piece.x to piece.y
            
            // Calculate new rotation
            val currentRotation = piece.rotation
            val newRotation = if (clockwise) {
                (currentRotation + 1) % 4
            } else {
                (currentRotation + 3) % 4  // +3 is equivalent to -1 in modulo 4
            }
            
            // Create rotated piece
            val rotatedPiece = piece.copy(rotation = newRotation)
            
            // Check if rotation is valid without wall kicks
            if (collisionDetector.isValidPosition(rotatedPiece, board)) {
                return GameRotationResult.Success(rotatedPiece)
            }
            
            // Try wall kicks using Super Rotation System
            val kickedPiece = rotationSystem.tryRotation(piece, clockwise, board, collisionDetector)
            
            return if (kickedPiece != null) {
                GameRotationResult.WallKick(kickedPiece)
            } else {
                GameRotationResult.Blocked(piece)
            }
        } finally {
            // Track operation performance
            performanceManager.trackOperation("processRotation", System.nanoTime() - startTime)
        }
    }
    
    override suspend fun processLineClear(board: TetrisBoard): LineClearResult {
        val startTime = System.nanoTime()
        
        try {
            // Use the BoardClearingProcessor to find and clear lines
            val (clearedBoard, completedLines) = boardClearingProcessor.clearLines(board)
            
            if (completedLines.isEmpty()) {
                return LineClearResult.NoLines
            }
            
            // Check for T-spin
            val isTSpin = detectTSpin(board)
            
            // Check for perfect clear
            val isPerfectClear = boardClearingProcessor.isPerfectClear(clearedBoard)
            
            // Check for back-to-back (Tetris or T-spin)
            val isSpecialClear = completedLines.size == 4 || isTSpin
            val isBackToBack = isSpecialClear // In a real implementation, we would track previous clears
            
            // Calculate score increase
            val scoreIncrease = statisticsCalculator.calculateLineClearScore(
                completedLines.size,
                1,  // Default level for now
                isTSpin,
                isPerfectClear
            )
            
            return LineClearResult.Success(
                clearedBoard = clearedBoard,
                linesCleared = completedLines.size,
                clearedLineIndices = completedLines,
                scoreIncrease = scoreIncrease,
                isTSpin = isTSpin,
                isPerfectClear = isPerfectClear,
                isBackToBack = isBackToBack
            )
        } finally {
            // Track operation performance
            performanceManager.trackOperation("processLineClear", System.nanoTime() - startTime)
        }
    }
    
    override suspend fun validateGameState(gameState: TetrisGameState): ValidationResult {
        val startTime = System.nanoTime()
        
        try {
            // Delegate to the GameStateValidator
            return gameStateValidator.validateGameState(gameState)
        } finally {
            // Track operation performance
            performanceManager.trackOperation("validateGameState", System.nanoTime() - startTime)
        }
    }
    
    override suspend fun generateNextPiece(gameState: TetrisGameState): TetrisPiece {
        val startTime = System.nanoTime()
        
        try {
            // Use the piece generator for balanced randomization
            val piece = pieceGenerator.generateNext()
            
            // Calculate correct starting position based on piece type
            val startX = getCorrectStartX(piece.type)
            
            return piece.copy(
                x = startX,
                y = 0,
                rotation = 0
            )
        } finally {
            // Track operation performance
            performanceManager.trackOperation("generateNextPiece", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Calculate correct starting X position based on piece type
     */
    private fun getCorrectStartX(pieceType: TetrisPieceType): Int {
        return when (pieceType) {
            TetrisPieceType.I -> 3  // I piece is 4 blocks wide
            TetrisPieceType.O -> 4  // O piece is 2x2
            else -> 3               // Other pieces are 3 blocks wide
        }
    }
    
    /**
     * Detect if the last move resulted in a T-spin
     */
    private fun detectTSpin(board: TetrisBoard): Boolean {
        // T-spin detection requires:
        // 1. Last move was a rotation
        // 2. Piece type is T
        // 3. At least 3 of the 4 corners around the T are filled
        
        if (!lastMoveWasRotation || lastRotationPosition == null) {
            return false
        }
        
        val (centerX, centerY) = lastRotationPosition!!
        
        // Check if the corners around the T are filled
        val corners = listOf(
            centerX to centerY,           // Top-left
            centerX + 2 to centerY,       // Top-right
            centerX to centerY + 2,       // Bottom-left
            centerX + 2 to centerY + 2    // Bottom-right
        )
        
        val filledCorners = corners.count { (x, y) ->
            x < 0 || x >= board.width ||
            y < 0 || y >= board.height ||
            (x >= 0 && y >= 0 && x < board.width && y < board.height && 
             board.cells[y][x] != TetrisCellType.EMPTY)
        }
        
        return filledCorners >= 3
    }
    
    /**
     * Place a piece on the board and return the updated board
     */
    fun placePiece(piece: TetrisPiece, board: TetrisBoard): TetrisBoard {
        val startTime = System.nanoTime()
        
        try {
            // Get the piece shape
            val shape = getPieceShape(piece)
            
            // Create a copy of the board cells
            val newCells = Array(board.height) { y ->
                Array(board.width) { x ->
                    board.cells[y][x]
                }
            }
            
            // Place the piece on the board
            for (i in shape.indices) {
                for (j in shape[i].indices) {
                    if (shape[i][j]) {
                        val boardX = piece.x + j
                        val boardY = piece.y + i
                        
                        // Check if within bounds
                        if (boardX >= 0 && boardX < board.width && 
                            boardY >= 0 && boardY < board.height) {
                            newCells[boardY][boardX] = piece.type.toCellType()
                        }
                    }
                }
            }
            
            return TetrisBoard(newCells, board.width, board.height)
        } finally {
            // Track operation performance
            performanceManager.trackOperation("placePiece", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Get the shape of a piece based on its type and rotation
     */
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            TetrisPieceType.T -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, true, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            else -> arrayOf(
                booleanArrayOf(true, true, true),
                booleanArrayOf(true, false, false),
                booleanArrayOf(false, false, false)
            )
        }
    }
    
    /**
     * Convert piece type to cell type
     */
    private fun TetrisPieceType.toCellType(): TetrisCellType {
        return when (this) {
            TetrisPieceType.I -> TetrisCellType.I
            TetrisPieceType.O -> TetrisCellType.O
            TetrisPieceType.T -> TetrisCellType.T
            TetrisPieceType.J -> TetrisCellType.J
            TetrisPieceType.L -> TetrisCellType.L
            TetrisPieceType.S -> TetrisCellType.S
            TetrisPieceType.Z -> TetrisCellType.Z
        }
    }
}