package com.yu.questicle.feature.tetris.impl.controller

import com.yu.questicle.core.common.di.Dispatcher
import com.yu.questicle.core.common.di.QuesticleDispatchers
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.model.GameStatus
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.testing.performance.PerformanceMonitor
import com.yu.questicle.feature.tetris.api.TetrisController
import com.yu.questicle.feature.tetris.impl.engine.TetrisEngineImpl
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import com.yu.questicle.core.common.di.ApplicationScope

@Singleton
class TetrisControllerImpl @Inject constructor(
    private val tetrisEngine: TetrisEngineImpl,
    private val gameRepository: GameRepository,
    @Dispatcher(QuesticleDispatchers.Main) private val mainDispatcher: CoroutineDispatcher,
    @Dispatcher(QuesticleDispatchers.Default) private val defaultDispatcher: CoroutineDispatcher,
    @ApplicationScope private val appScope: CoroutineScope
) : TetrisController {

    private val logger: QLogger = logger()

    private val _gameState = MutableStateFlow(TetrisGameState.initial())
    override val gameState: Flow<TetrisGameState> = _gameState.asStateFlow()
    
    private var gameLoopJob: kotlinx.coroutines.Job? = null
    private var currentPlayerId: String? = null
    private var gameStartTime: Long = 0L

    override suspend fun startNewGame(playerId: String) = withContext(defaultDispatcher) {
        try {
            currentPlayerId = playerId
            gameStartTime = System.currentTimeMillis()
            
            // Initialize game
            when (val initResult = tetrisEngine.initializeGame(playerId)) {
                is Result.Success -> {
                    _gameState.value = initResult.data
                    
                    // Start game
                    when (val startResult = tetrisEngine.startGame(initResult.data)) {
                        is Result.Success -> {
                            _gameState.value = startResult.data
                            startGameLoop()
                        }
                        is Result.Error -> {
                            // Handle start error
                        }
                        is Result.Loading -> {
                            // Handle loading state
                        }
                    }
                }
                is Result.Error -> {
                    // Handle initialization error
                }
                is Result.Loading -> {
                    // Handle loading state
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }

    override suspend fun processAction(action: TetrisAction) = withContext(defaultDispatcher) {
        try {
            val currentState = _gameState.value
            
            if (tetrisEngine.isValidAction(action, currentState)) {
                when (val result = tetrisEngine.processAction(action, currentState)) {
                    is Result.Success -> {
                        var newState = result.data
                        
                        // Check for line clears after piece placement
                        if (action is TetrisAction.Move || action is TetrisAction.Drop) {
                            when (val lineClearResult = tetrisEngine.checkLineClear(newState)) {
                                is Result.Success -> {
                                    newState = lineClearResult.data
                                }
                                is Result.Error -> {
                                    // Handle line clear error
                                }
                                is Result.Loading -> {
                                    // Handle loading state
                                }
                            }
                        }
                        
                        _gameState.value = newState
                        
                        // Check for game over
                        if (tetrisEngine.isGameOver(newState)) {
                            endGame()
                        }
                    }
                    is Result.Error -> {
                        // Handle action error
                    }
                    is Result.Loading -> {
                        // Handle loading state
                    }
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }

    override suspend fun pauseGame() = withContext(defaultDispatcher) {
        try {
            val currentState = _gameState.value
            when (val result = tetrisEngine.pauseGame(currentState)) {
                is Result.Success -> {
                    _gameState.value = result.data
                    stopGameLoop()
                }
                is Result.Error -> {
                    // Handle pause error
                }
                is Result.Loading -> {
                    // Handle loading state
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }

    override suspend fun resumeGame() = withContext(defaultDispatcher) {
        try {
            val currentState = _gameState.value
            when (val result = tetrisEngine.resumeGame(currentState)) {
                is Result.Success -> {
                    _gameState.value = result.data
                    startGameLoop()
                }
                is Result.Error -> {
                    // Handle resume error
                }
                is Result.Loading -> {
                    // Handle loading state
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }

    override suspend fun endGame() = withContext(defaultDispatcher) {
        try {
            val currentState = _gameState.value
            val playerId = currentPlayerId ?: return@withContext

            stopGameLoop()

            logger.i("游戏结束", mapOf(
                "playerId" to playerId,
                "finalScore" to currentState.score,
                "level" to currentState.level,
                "lines" to currentState.lines
            ))

            // 保存最终游戏状态和统计
            appScope.launch {
                try {
                    // 创建游戏记录
                    val game = Game(
                        type = GameType.TETRIS,
                        status = GameStatus.COMPLETED,
                        playerId = playerId,
                        score = currentState.score,
                        level = currentState.level,
                        startTime = gameStartTime / 1000,
                        endTime = System.currentTimeMillis() / 1000,
                        duration = (System.currentTimeMillis() - gameStartTime) / 1000,
                        metadata = mapOf(
                            "lines" to currentState.lines.toString(),
                            "level" to currentState.level.toString()
                        )
                    )

                    // 保存游戏记录
                    gameRepository.saveGame(game)

                    logger.i("游戏记录保存成功", mapOf("gameId" to game.id))
                } catch (e: Exception) {
                    logger.e(e, "保存游戏记录失败")
                }
            }

            // 调用引擎结束游戏
            when (val result = tetrisEngine.endGame(currentState)) {
                is Result.Success -> {
                    // 引擎成功结束游戏
                }
                is Result.Error -> {
                    logger.e(result.exception, "引擎结束游戏失败")
                }
                is Result.Loading -> {
                    // Handle loading state
                }
            }
        } catch (e: Exception) {
            logger.e(e, "结束游戏异常")
        }
    }

    override suspend fun saveGame(): Unit = withContext(defaultDispatcher) {
        try {
            val currentState = _gameState.value
            tetrisEngine.saveGameState(currentState)
        } catch (e: Exception) {
            // Handle save error
        }
    }

    override suspend fun loadGame(gameId: String) = withContext(defaultDispatcher) {
        try {
            when (val result = tetrisEngine.loadGameState(gameId)) {
                is Result.Success -> {
                    _gameState.value = result.data
                    
                    // Resume game if it was playing
                    if (result.data.status == com.yu.questicle.core.domain.model.tetris.TetrisStatus.PLAYING) {
                        startGameLoop()
                    }
                }
                is Result.Error -> {
                    // Handle load error
                }
                is Result.Loading -> {
                    // Handle loading state
                }
            }
        } catch (e: Exception) {
            // Handle exception
        }
    }

    private fun startGameLoop() {
        stopGameLoop() // Stop any existing loop
        
        gameLoopJob = appScope.launch(mainDispatcher) {
            while (isActive) {
                val currentState = _gameState.value
                
                if (currentState.status == com.yu.questicle.core.domain.model.tetris.TetrisStatus.PLAYING) {
                    val currentTime = System.currentTimeMillis()
                    val timeSinceLastDrop = currentTime - currentState.lastDropTime
                    val dropInterval = tetrisEngine.calculateDropInterval(currentState.level)
                    
                    if (timeSinceLastDrop >= dropInterval) {
                        // Auto-drop piece
                        currentPlayerId?.let { playerId ->
                            processAction(
                                TetrisAction.Move(
                                    Direction.DOWN,
                                    playerId
                                )
                            )
                        }
                    }
                }
                
                delay(16) // ~60 FPS
            }
        }
    }

    private fun stopGameLoop() {
        gameLoopJob?.cancel()
        gameLoopJob = null
    }
}
