package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType

/**
 * 俄罗斯方块碰撞检测器
 * 负责检测方块是否与游戏板发生碰撞
 */
interface TetrisCollisionDetector {
    
    /**
     * 检查方块位置是否有效（不与边界或其他方块碰撞）
     * @param piece 方块
     * @param board 游戏板
     * @return 位置是否有效
     */
    fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean
    
    /**
     * 检查方块是否在游戏板边界内
     * @param piece 方块
     * @param board 游戏板
     * @return 是否在边界内
     */
    fun isWithinBounds(piece: TetrisPiece, board: TetrisBoard): Boolean
    
    /**
     * 检查方块是否与其他方块重叠
     * @param piece 方块
     * @param board 游戏板
     * @return 是否重叠
     */
    fun isOverlapping(piece: TetrisPiece, board: TetrisBoard): Boolean
    
    /**
     * 检查方块是否已到达底部
     * @param piece 方块
     * @param board 游戏板
     * @return 是否到达底部
     */
    fun isAtBottom(piece: TetrisPiece, board: TetrisBoard): Boolean
    
    /**
     * 清除缓存
     * @param pieceType 方块类型，如果为null则清除所有缓存
     */
    fun clearCache(pieceType: String? = null)
}

/**
 * 俄罗斯方块碰撞检测器实现
 */
class TetrisCollisionDetectorImpl : TetrisCollisionDetector {
    
    // 碰撞检测缓存
    private val positionCache = mutableMapOf<String, Boolean>()
    
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // 生成缓存键
        val cacheKey = "${piece.type.name}_${piece.x}_${piece.y}_${piece.rotation}_${board.hashCode()}"
        
        // 检查缓存
        positionCache[cacheKey]?.let { return it }
        
        // 检查边界和重叠
        val result = isWithinBounds(piece, board) && !isOverlapping(piece, board)
        
        // 缓存结果
        positionCache[cacheKey] = result
        
        return result
    }
    
    override fun isWithinBounds(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val positions = piece.getOccupiedPositions()
        
        return positions.all { (x, y) ->
            x in 0 until board.width && y in 0 until board.height
        }
    }
    
    override fun isOverlapping(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val positions = piece.getOccupiedPositions()
        
        return positions.any { (x, y) ->
            // 检查是否在边界内
            if (x !in 0 until board.width || y !in 0 until board.height) {
                return@any false // 超出边界的部分不检查重叠
            }
            
            // 检查该位置是否已有方块
            board.cells[y][x] != TetrisCellType.EMPTY
        }
    }
    
    override fun isAtBottom(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val positions = piece.getOccupiedPositions()
        
        // 检查是否有任何部分在底部
        val atBottom = positions.any { (_, y) -> y == board.height - 1 }
        
        // 检查下方是否有方块
        val belowBlocked = positions.any { (x, y) ->
            val belowY = y + 1
            belowY < board.height && board.cells[belowY][x] != TetrisCellType.EMPTY
        }
        
        return atBottom || belowBlocked
    }
    
    override fun clearCache(pieceType: String?) {
        if (pieceType == null) {
            positionCache.clear()
        } else {
            positionCache.keys.removeIf { it.startsWith(pieceType) }
        }
    }
}