package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType

/**
 * Interface for collision detection algorithms.
 * Allows for different collision detection strategies to be used.
 */
interface CollisionDetectionAlgorithm {
    /**
     * Check if a piece position is valid (no collisions)
     */
    fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean
    
    /**
     * Get detailed collision information
     */
    fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo
    
    /**
     * Find landing position for a piece (for ghost piece or hard drop)
     */
    fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece
}

/**
 * Standard collision detection algorithm.
 * Simple but reliable implementation that checks each cell.
 */
class StandardCollisionDetection : CollisionDetectionAlgorithm {
    
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val shape = getPieceShape(piece)
        
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    
                    // Check if out of bounds
                    if (boardX < 0 || boardX >= board.width || 
                        boardY < 0 || boardY >= board.height) {
                        return false
                    }
                    
                    // Check if collides with another piece
                    if (boardY >= 0 && board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                        return false
                    }
                }
            }
        }
        
        return true
    }
    
    override fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo {
        val shape = getPieceShape(piece)
        val width = shape[0].size
        val height = shape.size
        
        val leftWallCollision = piece.x < 0
        val rightWallCollision = piece.x + width > board.width
        val floorCollision = piece.y + height > board.height
        
        val collisionPoints = mutableListOf<Pair<Int, Int>>()
        var pieceCollision = false
        
        // Check for collisions with other pieces
        if (!leftWallCollision && !rightWallCollision && !floorCollision) {
            for (i in shape.indices) {
                for (j in shape[i].indices) {
                    if (shape[i][j]) {
                        val boardX = piece.x + j
                        val boardY = piece.y + i
                        
                        if (boardY >= 0 && boardY < board.height && 
                            boardX >= 0 && boardX < board.width &&
                            board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                            pieceCollision = true
                            collisionPoints.add(boardX to boardY)
                        }
                    }
                }
            }
        }
        
        return CollisionInfo(
            hasCollision = leftWallCollision || rightWallCollision || floorCollision || pieceCollision,
            leftWallCollision = leftWallCollision,
            rightWallCollision = rightWallCollision,
            floorCollision = floorCollision,
            pieceCollision = pieceCollision,
            collisionPoints = collisionPoints
        )
    }
    
    override fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        var landingPiece = piece
        var nextPiece = piece.copy(y = piece.y + 1)
        
        while (isValidPosition(nextPiece, board)) {
            landingPiece = nextPiece
            nextPiece = nextPiece.copy(y = nextPiece.y + 1)
        }
        
        return landingPiece
    }
    
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            else -> arrayOf(
                booleanArrayOf(true, true, true),
                booleanArrayOf(true, false, false),
                booleanArrayOf(false, false, false)
            )
        }
    }
}

/**
 * Spatial collision detection algorithm.
 * Uses spatial partitioning for better performance.
 */
class SpatialCollisionDetection(private val spatialGrid: SpatialGrid) : CollisionDetectionAlgorithm {
    
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        return spatialGrid.checkCollision(piece, board)
    }
    
    override fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo {
        // Early boundary checks
        val shape = getPieceShape(piece)
        val width = shape[0].size
        val height = shape.size
        
        val leftWallCollision = piece.x < 0
        val rightWallCollision = piece.x + width > board.width
        val floorCollision = piece.y + height > board.height
        
        if (leftWallCollision || rightWallCollision || floorCollision) {
            return CollisionInfo(
                hasCollision = true,
                leftWallCollision = leftWallCollision,
                rightWallCollision = rightWallCollision,
                floorCollision = floorCollision,
                pieceCollision = false,
                collisionPoints = emptyList()
            )
        }
        
        // Get potential collision points using spatial grid
        val potentialPoints = spatialGrid.getPotentialCollisionPoints(piece)
        
        // Check each potential collision point
        val collisionPoints = mutableListOf<Pair<Int, Int>>()
        var pieceCollision = false
        
        for ((boardX, boardY) in potentialPoints) {
            if (boardY >= 0 && boardY < board.height && 
                boardX >= 0 && boardX < board.width &&
                board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                pieceCollision = true
                collisionPoints.add(boardX to boardY)
            }
        }
        
        return CollisionInfo(
            hasCollision = pieceCollision,
            leftWallCollision = false,
            rightWallCollision = false,
            floorCollision = false,
            pieceCollision = pieceCollision,
            collisionPoints = collisionPoints
        )
    }
    
    override fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        // Binary search optimization for landing position
        var low = piece.y
        var high = board.height
        
        while (low < high - 1) {
            val mid = (low + high) / 2
            val testPiece = piece.copy(y = mid)
            
            if (isValidPosition(testPiece, board)) {
                low = mid
            } else {
                high = mid
            }
        }
        
        return piece.copy(y = low)
    }
    
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        // Same implementation as in StandardCollisionDetection
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            else -> arrayOf(
                booleanArrayOf(true, true, true),
                booleanArrayOf(true, false, false),
                booleanArrayOf(false, false, false)
            )
        }
    }
}

/**
 * Optimized collision detection algorithm.
 * Combines early exit strategies, spatial partitioning, and caching.
 */
class OptimizedCollisionDetection(private val spatialGrid: SpatialGrid) : CollisionDetectionAlgorithm {
    
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // Early boundary check - fastest path
        if (!isWithinBoundsFast(piece, board)) {
            return false
        }
        
        // Use spatial partitioning for detailed check
        return spatialGrid.checkCollision(piece, board)
    }
    
    override fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo {
        // Early boundary checks
        val (width, height) = getPieceDimensions(piece)
        val leftWallCollision = piece.x < 0
        val rightWallCollision = piece.x + width > board.width
        val floorCollision = piece.y + height > board.height
        
        if (leftWallCollision || rightWallCollision || floorCollision) {
            return CollisionInfo(
                hasCollision = true,
                leftWallCollision = leftWallCollision,
                rightWallCollision = rightWallCollision,
                floorCollision = floorCollision,
                pieceCollision = false,
                collisionPoints = emptyList()
            )
        }
        
        // Get potential collision points using spatial grid
        val potentialPoints = spatialGrid.getPotentialCollisionPoints(piece)
        
        // Check each potential collision point
        val collisionPoints = mutableListOf<Pair<Int, Int>>()
        var pieceCollision = false
        
        for ((boardX, boardY) in potentialPoints) {
            if (boardY >= 0 && boardY < board.height && 
                boardX >= 0 && boardX < board.width &&
                board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                pieceCollision = true
                collisionPoints.add(boardX to boardY)
                
                // Early exit if we only need to know if there's a collision
                if (collisionPoints.size == 1) {
                    break
                }
            }
        }
        
        return CollisionInfo(
            hasCollision = pieceCollision,
            leftWallCollision = false,
            rightWallCollision = false,
            floorCollision = false,
            pieceCollision = pieceCollision,
            collisionPoints = collisionPoints
        )
    }
    
    override fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        // Binary search optimization for landing position
        var low = piece.y
        var high = board.height
        
        while (low < high - 1) {
            val mid = (low + high) / 2
            val testPiece = piece.copy(y = mid)
            
            if (isValidPosition(testPiece, board)) {
                low = mid
            } else {
                high = mid
            }
        }
        
        return piece.copy(y = low)
    }
    
    /**
     * Fast boundary check that only checks piece extremes, not individual cells
     */
    private fun isWithinBoundsFast(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // Get piece dimensions based on type and rotation
        val (width, height) = getPieceDimensions(piece)
        
        // Check if piece is within board boundaries
        return piece.x >= 0 && 
               piece.x + width <= board.width &&
               piece.y >= 0 && 
               piece.y + height <= board.height
    }
    
    /**
     * Get piece dimensions based on type and rotation
     */
    private fun getPieceDimensions(piece: TetrisPiece): Pair<Int, Int> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 2) {
                0 -> 4 to 1  // Horizontal I piece
                else -> 1 to 4  // Vertical I piece
            }
            TetrisPieceType.O -> 2 to 2  // O piece is always 2x2
            else -> 3 to 2  // Most other pieces fit in 3x2
        }
    }
}