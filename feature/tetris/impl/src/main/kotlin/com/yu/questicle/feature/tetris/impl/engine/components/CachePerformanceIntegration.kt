package com.yu.questicle.feature.tetris.impl.engine.components

import javax.inject.Inject
import javax.inject.Singleton
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * Integration between caching and performance management systems.
 * Provides coordinated optimization and monitoring of cache performance.
 */
@Singleton
class CachePerformanceIntegration @Inject constructor(
    private val cacheManager: TetrisCacheManager,
    private val performanceManager: TetrisPerformanceManager
) {
    // Background monitoring
    private val scheduler: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()
    
    // Cache performance thresholds
    private val MIN_ACCEPTABLE_HIT_RATE = 0.6f
    private val OPTIMAL_HIT_RATE = 0.85f
    
    // Cache size adjustment factors
    private val CACHE_INCREASE_FACTOR = 1.5f
    private val CACHE_DECREASE_FACTOR = 0.8f
    
    // Current cache configuration
    private var currentCacheConfig = CacheConfiguration(
        l1CacheSize = 200,
        l2CacheSize = 1000,
        useMultiLevelCache = true,
        useBloomFilter = true,
        useTtl = true,
        defaultTtlMs = 60000L
    )
    
    init {
        // Start background monitoring
        scheduler.scheduleAtFixedRate(
            { monitorCachePerformance() },
            30, 30, TimeUnit.SECONDS
        )
    }
    
    /**
     * Initialize the integration with device-specific settings
     */
    fun initialize(deviceSpecs: DeviceSpecs) {
        // Configure initial cache sizes based on device specs
        val initialConfig = when {
            deviceSpecs.isHighEnd() -> CacheConfiguration(
                l1CacheSize = 500,
                l2CacheSize = 2000,
                useMultiLevelCache = true,
                useBloomFilter = true,
                useTtl = true,
                defaultTtlMs = 120000L
            )
            deviceSpecs.isLowEnd() -> CacheConfiguration(
                l1CacheSize = 100,
                l2CacheSize = 500,
                useMultiLevelCache = false,
                useBloomFilter = false,
                useTtl = true,
                defaultTtlMs = 30000L
            )
            else -> CacheConfiguration(
                l1CacheSize = 200,
                l2CacheSize = 1000,
                useMultiLevelCache = true,
                useBloomFilter = true,
                useTtl = true,
                defaultTtlMs = 60000L
            )
        }
        
        applyCacheConfiguration(initialConfig)
    }
    
    /**
     * Optimize cache settings based on performance metrics
     */
    fun optimizeCacheSettings() {
        val cacheStats = cacheManager.getCacheStatistics()
        val performanceMetrics = (performanceManager as TetrisPerformanceManagerImpl).getPerformanceMetrics()
        
        // Create a new configuration based on current settings
        val newConfig = currentCacheConfig.copy()
        
        // Adjust based on hit rate
        if (cacheStats.hitRate < MIN_ACCEPTABLE_HIT_RATE) {
            // Hit rate is too low - increase cache sizes
            newConfig.l1CacheSize = (newConfig.l1CacheSize * CACHE_INCREASE_FACTOR).toInt()
            newConfig.l2CacheSize = (newConfig.l2CacheSize * CACHE_INCREASE_FACTOR).toInt()
            
            // Enable bloom filter for better performance
            newConfig.useBloomFilter = true
        } else if (cacheStats.hitRate > OPTIMAL_HIT_RATE && cacheStats.size > 1000) {
            // Hit rate is very good and cache is large - can reduce size
            newConfig.l1CacheSize = (newConfig.l1CacheSize * CACHE_DECREASE_FACTOR).toInt().coerceAtLeast(100)
            newConfig.l2CacheSize = (newConfig.l2CacheSize * CACHE_DECREASE_FACTOR).toInt().coerceAtLeast(500)
        }
        
        // Adjust based on memory usage
        val heapUsagePercent = performanceMetrics.heapMemoryUsed.toFloat() / 
                              performanceMetrics.heapMemoryMax.coerceAtLeast(1) * 100
        
        if (heapUsagePercent > 80) {
            // Memory pressure is high - reduce cache sizes
            newConfig.l1CacheSize = (newConfig.l1CacheSize * 0.7f).toInt().coerceAtLeast(50)
            newConfig.l2CacheSize = (newConfig.l2CacheSize * 0.7f).toInt().coerceAtLeast(200)
            
            // Enable TTL to clear old entries
            newConfig.useTtl = true
            newConfig.defaultTtlMs = 30000L
        }
        
        // Apply the new configuration if it's different
        if (newConfig != currentCacheConfig) {
            applyCacheConfiguration(newConfig)
        }
    }
    
    /**
     * Apply a cache configuration
     */
    private fun applyCacheConfiguration(config: CacheConfiguration) {
        // Update current config
        currentCacheConfig = config
        
        // Create performance config from cache config
        val performanceConfig = PerformanceConfig(
            useSpatialPartitioning = true,
            useObjectPooling = true,
            useCaching = true,
            useMultiLevelCache = config.useMultiLevelCache,
            useBloomFilters = config.useBloomFilter,
            maxCacheSize = config.l2CacheSize,
            useBackgroundProcessing = true,
            optimizationLevel = if (config.useTtl) 2 else 1
        )
        
        // Apply to cache manager
        cacheManager.configureForPerformance(performanceConfig)
        
        // Log configuration change
        println("Cache configuration updated: L1=${config.l1CacheSize}, L2=${config.l2CacheSize}, " +
                "MultiLevel=${config.useMultiLevelCache}, Bloom=${config.useBloomFilter}")
    }
    
    /**
     * Monitor cache performance and adjust settings if needed
     */
    private fun monitorCachePerformance() {
        try {
            val cacheStats = cacheManager.getCacheStatistics()
            
            // Log current cache statistics
            println("Cache stats: size=${cacheStats.size}, hitRate=${cacheStats.hitRate}, " +
                    "hits=${cacheStats.hits}, misses=${cacheStats.misses}")
            
            // Check if optimization is needed
            if (shouldOptimizeCacheSettings(cacheStats)) {
                optimizeCacheSettings()
            }
        } catch (e: Exception) {
            // Log error but don't crash
            println("Error in cache performance monitoring: ${e.message}")
        }
    }
    
    /**
     * Determine if cache settings should be optimized
     */
    private fun shouldOptimizeCacheSettings(cacheStats: CacheStatistics): Boolean {
        // Check if we have enough data to make decisions
        if (cacheStats.hits + cacheStats.misses < 100) {
            return false
        }
        
        // Check if hit rate is outside acceptable range
        return cacheStats.hitRate < MIN_ACCEPTABLE_HIT_RATE || 
               (cacheStats.hitRate > OPTIMAL_HIT_RATE && cacheStats.size > 1000)
    }
    
    /**
     * Warm up cache with frequently used data
     */
    fun warmupCache(gameState: Any) {
        cacheManager.warmupCache(gameState)
    }
    
    /**
     * Get current cache configuration
     */
    fun getCurrentCacheConfiguration(): CacheConfiguration {
        return currentCacheConfig
    }
    
    /**
     * Cache configuration data class
     */
    data class CacheConfiguration(
        val l1CacheSize: Int,
        val l2CacheSize: Int,
        val useMultiLevelCache: Boolean,
        val useBloomFilter: Boolean,
        val useTtl: Boolean,
        val defaultTtlMs: Long
    )
}