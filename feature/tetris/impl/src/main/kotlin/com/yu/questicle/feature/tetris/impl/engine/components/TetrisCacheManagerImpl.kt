package com.yu.questicle.feature.tetris.impl.engine.components

import javax.inject.Inject
import javax.inject.Singleton
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import java.util.LinkedHashMap
import java.util.Collections

/**
 * Implementation of TetrisCacheManager that provides intelligent caching.
 * 
 * Features:
 * - Multi-level caching (L1/L2 caches)
 * - Bloom filter for fast negative lookups
 * - LRU eviction policy
 * - Cache statistics and monitoring
 * - Configurable cache sizes and strategies
 * - Type-specific caching policies
 */
@Singleton
class TetrisCacheManagerImpl @Inject constructor() : TetrisCacheManager {
    
    // L1 cache (hot items, limited size, fast access)
    private val l1Cache = Collections.synchronizedMap(
        object : LinkedHashMap<CacheKey, CacheEntry<Any>>(16, 0.75f, true) {
            override fun removeEldestEntry(eldest: Map.Entry<CacheKey, CacheEntry<Any>>): Boolean {
                return size > l1CacheSize
            }
        }
    )
    
    // L2 cache (warm items, larger size, slower access)
    private val l2Cache = ConcurrentHashMap<CacheKey, CacheEntry<Any>>()
    
    // Cache statistics
    private val hits = AtomicLong(0)
    private val misses = AtomicLong(0)
    private val evictions = AtomicLong(0)
    private val l1Hits = AtomicLong(0)
    private val l2Hits = AtomicLong(0)
    private val accessTimes = ConcurrentHashMap<String, MutableList<Long>>()
    
    // Cache configuration
    private var l1CacheSize = 200
    private var l2CacheSize = 1000
    private var useMultiLevelCache = true
    private var useBloomFilter = true
    private var useTtl = false
    private var defaultTtlMs = 60000L // 1 minute
    private var typePriorities = mutableMapOf<String, Int>()
    
    // Bloom filter for fast negative lookups
    private val bloomFilter = BloomFilterImpl<CacheKey>(100000, 5)
    
    // Last cache cleanup time
    private var lastCleanupTime = System.currentTimeMillis()
    private val CLEANUP_INTERVAL_MS = 30000L // 30 seconds
    
    init {
        // Set default type priorities
        typePriorities["collision"] = 1  // High priority
        typePriorities["ghost"] = 2
        typePriorities["board"] = 1
        typePriorities["score"] = 3      // Lower priority
    }
    
    override fun <T> getCached(key: CacheKey, factory: () -> T): T {
        val startTime = System.nanoTime()
        
        try {
            // Check bloom filter first if enabled
            if (useBloomFilter && !bloomFilter.mightContain(key)) {
                misses.incrementAndGet()
                val value = factory()
                putInCache(key, value as Any)
                recordAccessTime(key.type, System.nanoTime() - startTime)
                return value
            }
            
            // Check L1 cache
            val l1Entry = l1Cache[key]
            if (l1Entry != null && !isExpired(l1Entry)) {
                hits.incrementAndGet()
                l1Hits.incrementAndGet()
                l1Entry.accessCount++
                recordAccessTime(key.type, System.nanoTime() - startTime)
                @Suppress("UNCHECKED_CAST")
                return l1Entry.value as T
            }
            
            // Check L2 cache if enabled
            if (useMultiLevelCache) {
                val l2Entry = l2Cache[key]
                if (l2Entry != null && !isExpired(l2Entry)) {
                    hits.incrementAndGet()
                    l2Hits.incrementAndGet()
                    l2Entry.accessCount++
                    
                    // Promote to L1 cache if frequently accessed
                    if (l2Entry.accessCount >= 3) {
                        synchronized(l1Cache) {
                            l1Cache[key] = l2Entry
                            l2Cache.remove(key)
                        }
                    }
                    
                    recordAccessTime(key.type, System.nanoTime() - startTime)
                    @Suppress("UNCHECKED_CAST")
                    return l2Entry.value as T
                }
            }
            
            // Cache miss
            misses.incrementAndGet()
            val value = factory()
            putInCache(key, value as Any)
            recordAccessTime(key.type, System.nanoTime() - startTime)
            return value
        } finally {
            // Periodically clean up expired entries
            cleanupIfNeeded()
        }
    }
    
    override fun invalidateCache(pattern: String) {
        // Remove entries matching pattern
        val l1KeysToRemove = l1Cache.keys.filter { it.toString().contains(pattern) }
        l1KeysToRemove.forEach { l1Cache.remove(it) }
        
        if (useMultiLevelCache) {
            val l2KeysToRemove = l2Cache.keys.filter { it.toString().contains(pattern) }
            l2KeysToRemove.forEach { l2Cache.remove(it) }
        }
        
        // If invalidating a significant portion of the cache, also clear bloom filter
        if (l1KeysToRemove.size > l1CacheSize / 10 || 
            (useMultiLevelCache && l2KeysToRemove.size > l2CacheSize / 10)) {
            bloomFilter.clear()
        }
    }
    
    override fun configureForPerformance(config: PerformanceConfig) {
        // Configure cache sizes
        l1CacheSize = config.maxCacheSize / 5  // 20% for L1
        l2CacheSize = config.maxCacheSize      // 100% for L2
        
        // Configure features
        useMultiLevelCache = config.useMultiLevelCache
        useBloomFilter = config.useBloomFilters
        useTtl = config.optimizationLevel >= 2
        
        // Adjust TTL based on optimization level
        defaultTtlMs = when (config.optimizationLevel) {
            0 -> 30000L    // 30 seconds
            1 -> 60000L    // 1 minute
            2 -> 120000L   // 2 minutes
            else -> 300000L // 5 minutes
        }
        
        // Clean up caches if needed
        cleanupCaches()
    }
    
    override fun getCacheStatistics(): CacheStatistics {
        val totalHits = hits.get()
        val totalMisses = misses.get()
        val totalAccesses = totalHits + totalMisses
        val hitRate = if (totalAccesses > 0) {
            totalHits.toFloat() / totalAccesses
        } else {
            0f
        }
        
        // Calculate average access time
        var totalTime = 0L
        var totalSamples = 0
        accessTimes.values.forEach { times ->
            totalTime += times.sum()
            totalSamples += times.size
        }
        val averageAccessTime = if (totalSamples > 0) totalTime / totalSamples else 0L
        
        // Count cache entries by type
        val cachesByType = mutableMapOf<String, Int>()
        l1Cache.keys.forEach { key ->
            val type = key.type
            cachesByType[type] = (cachesByType[type] ?: 0) + 1
        }
        
        if (useMultiLevelCache) {
            l2Cache.keys.forEach { key ->
                val type = key.type
                cachesByType[type] = (cachesByType[type] ?: 0) + 1
            }
        }
        
        return CacheStatistics(
            size = l1Cache.size + (if (useMultiLevelCache) l2Cache.size else 0),
            hits = totalHits,
            misses = totalMisses,
            hitRate = hitRate,
            evictions = evictions.get(),
            averageAccessTime = averageAccessTime,
            cachesByType = cachesByType
        )
    }
    
    override fun clearAllCaches() {
        l1Cache.clear()
        l2Cache.clear()
        if (useBloomFilter) {
            bloomFilter.clear()
        }
        accessTimes.clear()
        hits.set(0)
        misses.set(0)
        evictions.set(0)
        l1Hits.set(0)
        l2Hits.set(0)
    }
    
    override fun warmupCache(gameState: Any) {
        // In a real implementation, we would pre-compute and cache
        // frequently used values based on the current game state
    }
    
    // Helper methods
    
    private fun putInCache(key: CacheKey, value: Any) {
        // Add to bloom filter if enabled
        if (useBloomFilter) {
            bloomFilter.put(key)
        }
        
        // Create cache entry
        val entry = CacheEntry(
            value = value,
            creationTime = System.currentTimeMillis(),
            expirationTime = if (useTtl) System.currentTimeMillis() + getTtlForType(key.type) else Long.MAX_VALUE
        )
        
        // Determine which cache to use based on type priority
        val priority = typePriorities[key.type] ?: 2
        
        if (priority == 1 || !useMultiLevelCache) {
            // High priority or single-level cache - put in L1
            synchronized(l1Cache) {
                l1Cache[key] = entry
            }
        } else {
            // Lower priority - put in L2
            l2Cache[key] = entry
            
            // Ensure L2 cache doesn't grow too large
            if (l2Cache.size > l2CacheSize) {
                evictExcessFromL2()
            }
        }
    }
    
    private fun evictExcessFromL2() {
        // Simple eviction strategy - remove oldest entries
        val keysToEvict = l2Cache.entries
            .sortedBy { it.value.creationTime }
            .take((l2Cache.size - l2CacheSize * 0.8).toInt())
            .map { it.key }
            
        keysToEvict.forEach { 
            l2Cache.remove(it)
            evictions.incrementAndGet()
        }
    }
    
    private fun isExpired(entry: CacheEntry<*>): Boolean {
        return useTtl && System.currentTimeMillis() > entry.expirationTime
    }
    
    private fun getTtlForType(type: String): Long {
        return when (type) {
            "collision" -> defaultTtlMs * 2  // Longer TTL for collision cache
            "ghost" -> defaultTtlMs          // Standard TTL for ghost pieces
            "board" -> defaultTtlMs * 3      // Longer TTL for board states
            else -> defaultTtlMs
        }
    }
    
    private fun cleanupIfNeeded() {
        val now = System.currentTimeMillis()
        if (now - lastCleanupTime > CLEANUP_INTERVAL_MS) {
            cleanupCaches()
            lastCleanupTime = now
        }
    }
    
    private fun cleanupCaches() {
        if (useTtl) {
            // Remove expired entries from L1 cache
            val l1ExpiredKeys = l1Cache.entries
                .filter { isExpired(it.value) }
                .map { it.key }
                
            l1ExpiredKeys.forEach { l1Cache.remove(it) }
            
            // Remove expired entries from L2 cache
            val l2ExpiredKeys = l2Cache.entries
                .filter { isExpired(it.value) }
                .map { it.key }
                
            l2ExpiredKeys.forEach { l2Cache.remove(it) }
            
            // Update eviction count
            evictions.addAndGet((l1ExpiredKeys.size + l2ExpiredKeys.size).toLong())
        }
        
        // Trim access time statistics
        accessTimes.forEach { (_, times) ->
            if (times.size > 100) {
                synchronized(times) {
                    while (times.size > 50) {
                        times.removeAt(0)
                    }
                }
            }
        }
    }
    
    private fun recordAccessTime(type: String, nanos: Long) {
        val times = accessTimes.computeIfAbsent(type) { mutableListOf() }
        synchronized(times) {
            times.add(nanos)
            if (times.size > 100) {
                times.removeAt(0)
            }
        }
    }
    
    /**
     * Cache entry with metadata
     */
    private data class CacheEntry<T>(
        val value: T,
        val creationTime: Long,
        val expirationTime: Long,
        var accessCount: Int = 1
    )
    
    /**
     * Bloom filter implementation for fast negative lookups
     */
    private class BloomFilterImpl<T>(size: Int, numHashes: Int) {
        private val bitArray = java.util.BitSet(size)
        private val numHashFunctions = numHashes
        private val size = size
        
        fun put(item: T) {
            val hashValues = generateHashes(item)
            synchronized(bitArray) {
                for (i in 0 until numHashFunctions) {
                    bitArray.set(hashValues[i] % size)
                }
            }
        }
        
        fun mightContain(item: T): Boolean {
            val hashValues = generateHashes(item)
            synchronized(bitArray) {
                for (i in 0 until numHashFunctions) {
                    if (!bitArray.get(hashValues[i] % size)) {
                        return false
                    }
                }
            }
            return true
        }
        
        fun clear() {
            synchronized(bitArray) {
                bitArray.clear()
            }
        }
        
        private fun generateHashes(item: T): IntArray {
            val result = IntArray(numHashFunctions)
            val hash1 = item.hashCode()
            val hash2 = hash1 * 31
            
            for (i in 0 until numHashFunctions) {
                result[i] = Math.abs(hash1 + i * hash2)
            }
            
            return result
        }
    }
}