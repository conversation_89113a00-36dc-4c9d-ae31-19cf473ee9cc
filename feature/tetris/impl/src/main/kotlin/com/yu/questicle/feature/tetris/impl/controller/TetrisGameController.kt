package com.yu.questicle.feature.tetris.impl.controller

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.domain.timer.TetrisGameTimer
import com.yu.questicle.feature.tetris.api.TetrisController
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.milliseconds

/**
 * 完整的俄罗斯方块游戏控制器
 * 集成了游戏引擎、定时器和状态管理
 */
@Singleton
class TetrisGameController @Inject constructor(
    private val tetrisEngine: TetrisEngine,
    private val gameScope: CoroutineScope
) : TetrisController {

    private val gameTimer = TetrisGameTimer(gameScope)
    
    private val _gameState = MutableStateFlow(TetrisGameState.initial())
    override val gameState = _gameState.asStateFlow()
    
    private val _gameEvents = MutableSharedFlow<TetrisGameEvent>()
    val gameEvents = _gameEvents.asSharedFlow()
    
    private var currentPlayerId: String = ""
    private var lastAction: TetrisAction? = null
    
    init {
        // 监听游戏状态变化
        gameScope.launch {
            gameState.collect { state ->
                handleGameStateChange(state)
            }
        }
    }

    override suspend fun startNewGame(playerId: String) {
        currentPlayerId = playerId

        when (val result = tetrisEngine.initializeGame(playerId)) {
            is Result.Success -> {
                _gameState.value = result.data
                _gameEvents.emit(TetrisGameEvent.GameInitialized(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "初始化失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    // 保留原有方法以兼容其他调用
    suspend fun initializeGame(playerId: String) = startNewGame(playerId)

    suspend fun startGame() {
        val currentState = _gameState.value

        when (val result = tetrisEngine.startGame(currentState)) {
            is Result.Success -> {
                _gameState.value = result.data
                startGameTimers()
                _gameEvents.emit(TetrisGameEvent.GameStarted(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "开始游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun pauseGame() {
        val currentState = _gameState.value

        when (val result = tetrisEngine.pauseGame(currentState)) {
            is Result.Success -> {
                _gameState.value = result.data
                gameTimer.pauseAll()
                _gameEvents.emit(TetrisGameEvent.GamePaused(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "暂停游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun resumeGame() {
        val currentState = _gameState.value

        when (val result = tetrisEngine.resumeGame(currentState)) {
            is Result.Success -> {
                _gameState.value = result.data
                gameTimer.resumeAll()
                _gameEvents.emit(TetrisGameEvent.GameResumed(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "恢复游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun endGame() {
        val currentState = _gameState.value

        when (val result = tetrisEngine.endGame(currentState)) {
            is Result.Success -> {
                gameTimer.stopAll()
                _gameEvents.emit(TetrisGameEvent.GameEnded(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "结束游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun processAction(action: TetrisAction) {
        val currentState = _gameState.value
        
        // 检查动作是否有效
        if (!tetrisEngine.isValidAction(action, currentState)) {
            return
        }
        
        lastAction = action
        
        when (val result = tetrisEngine.processAction(action, currentState)) {
            is Result.Success -> {
                var newState = result.data

                // 检查行消除
                when (val lineClearResult = tetrisEngine.checkLineClear(newState)) {
                    is Result.Success -> {
                        newState = lineClearResult.data

                        // 检查游戏是否结束
                        if (tetrisEngine.isGameOver(newState)) {
                            newState = newState.copy(status = TetrisStatus.GAME_OVER)
                            gameTimer.stopAll()
                            _gameEvents.emit(TetrisGameEvent.GameOver(newState))
                        }

                        _gameState.value = newState
                        _gameEvents.emit(TetrisGameEvent.ActionProcessed(action, newState))
                    }
                    is Result.Error -> {
                        _gameEvents.emit(TetrisGameEvent.Error(lineClearResult.exception.message ?: "行消除失败"))
                    }
                    is Result.Loading -> {
                        // 加载状态，暂时不处理
                    }
                }
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "动作处理失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun saveGame() {
        val currentState = _gameState.value

        when (val result = tetrisEngine.saveGameState(currentState)) {
            is Result.Success -> {
                _gameEvents.emit(TetrisGameEvent.GameSaved)
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "保存游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    override suspend fun loadGame(gameId: String) {
        when (val result = tetrisEngine.loadGameState(gameId)) {
            is Result.Success -> {
                _gameState.value = result.data
                _gameEvents.emit(TetrisGameEvent.GameLoaded(result.data))
            }
            is Result.Error -> {
                _gameEvents.emit(TetrisGameEvent.Error(result.exception.message ?: "加载游戏失败"))
            }
            is Result.Loading -> {
                // 加载状态，暂时不处理
            }
        }
    }

    // 便捷方法
    suspend fun moveLeft() = processAction(TetrisAction.Move(Direction.LEFT, currentPlayerId))
    suspend fun moveRight() = processAction(TetrisAction.Move(Direction.RIGHT, currentPlayerId))
    suspend fun moveDown() = processAction(TetrisAction.Move(Direction.DOWN, currentPlayerId))
    suspend fun rotate() = processAction(TetrisAction.Rotate(true, currentPlayerId))
    suspend fun rotateCounterClockwise() = processAction(TetrisAction.Rotate(false, currentPlayerId))
    suspend fun hardDrop() = processAction(TetrisAction.Drop(true, currentPlayerId))
    suspend fun softDrop() = processAction(TetrisAction.Drop(false, currentPlayerId))
    suspend fun hold() = processAction(TetrisAction.Hold(currentPlayerId))

    private fun startGameTimers() {
        val currentState = _gameState.value
        val dropInterval = tetrisEngine.calculateDropInterval(currentState.level).milliseconds
        
        // 启动下降定时器
        gameTimer.startDropTimer(dropInterval) {
            processAction(TetrisAction.Move(Direction.DOWN, currentPlayerId))
        }
        
        // 启动游戏时间计时
        gameTimer.startGameTime()
    }

    private suspend fun handleGameStateChange(state: TetrisGameState) {
        // 根据等级更新下降间隔
        val newDropInterval = tetrisEngine.calculateDropInterval(state.level).milliseconds
        gameTimer.updateDropInterval(newDropInterval)
        
        // 发送状态变化事件
        when (state.status) {
            TetrisStatus.READY -> {
                // 游戏准备状态，不需要特殊处理
            }
            TetrisStatus.PLAYING -> {
                if (gameTimer.isDropTimerRunning.value == false) {
                    startGameTimers()
                }
            }
            TetrisStatus.PAUSED -> {
                gameTimer.pauseAll()
            }
            TetrisStatus.GAME_OVER -> {
                gameTimer.stopAll()
            }
            TetrisStatus.COMPLETED -> {
                gameTimer.stopAll()
            }
        }
    }
}

/**
 * 游戏事件
 */
sealed class TetrisGameEvent {
    data class GameInitialized(val state: TetrisGameState) : TetrisGameEvent()
    data class GameStarted(val state: TetrisGameState) : TetrisGameEvent()
    data class GamePaused(val state: TetrisGameState) : TetrisGameEvent()
    data class GameResumed(val state: TetrisGameState) : TetrisGameEvent()
    data class GameEnded(val game: com.yu.questicle.core.domain.model.Game) : TetrisGameEvent()
    data class GameOver(val state: TetrisGameState) : TetrisGameEvent()
    data class ActionProcessed(val action: TetrisAction, val state: TetrisGameState) : TetrisGameEvent()
    data class GameLoaded(val state: TetrisGameState) : TetrisGameEvent()
    object GameSaved : TetrisGameEvent()
    data class Error(val message: String) : TetrisGameEvent()
    
    // 特殊事件
    data class LineCleared(val lines: Int, val isTetris: Boolean) : TetrisGameEvent()
    data class LevelUp(val newLevel: Int) : TetrisGameEvent()
    data class ScoreUpdate(val score: Int, val delta: Int) : TetrisGameEvent()
    data class PiecePlaced(val piece: TetrisPiece) : TetrisGameEvent()
    data class TSpin(val type: com.yu.questicle.core.domain.rotation.TSpinType) : TetrisGameEvent()
}
