package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import java.util.BitSet

/**
 * Spatial partitioning grid for optimized collision detection.
 * Divides the board into cells for faster collision checks.
 * 
 * Performance optimizations:
 * - Uses BitSet for efficient storage of occupied cells
 * - Employs early exit strategies for boundary checks
 * - Caches grid state for board configurations
 * - Optimizes memory usage with bit-packed representation
 */
class SpatialGrid(private val width: Int, private val height: Int) {
    
    // Grid cell size (each cell contains multiple board cells)
    // Tuned for optimal performance based on typical Tetris board size
    private val CELL_SIZE = 4
    
    // Number of grid cells in each dimension
    private val gridWidth = (width + CELL_SIZE - 1) / CELL_SIZE
    private val gridHeight = (height + CELL_SIZE - 1) / CELL_SIZE
    
    // Grid cells - each cell contains a BitSet of occupied positions
    // Using BitSet instead of Set<Pair> for memory efficiency
    private val grid = Array(gridHeight) { Array(gridWidth) { BitSet(CELL_SIZE * CELL_SIZE) } }
    
    // Cache for board hash to avoid unnecessary grid updates
    private var lastBoardHash = 0
    
    /**
     * Update the spatial grid with the current board state
     * Uses caching to avoid unnecessary updates
     */
    fun updateGrid(board: TetrisBoard): Boolean {
        val boardHash = board.hashCode()
        
        // Skip update if board hasn't changed
        if (boardHash == lastBoardHash) {
            return false
        }
        
        // Clear the grid
        for (y in 0 until gridHeight) {
            for (x in 0 until gridWidth) {
                grid[y][x].clear()
            }
        }
        
        // Populate the grid with occupied cells
        for (y in 0 until board.height) {
            for (x in 0 until board.width) {
                if (board.cells[y][x] != TetrisCellType.EMPTY) {
                    val gridX = x / CELL_SIZE
                    val gridY = y / CELL_SIZE
                    
                    // Convert 2D position to 1D index within the cell
                    val localX = x % CELL_SIZE
                    val localY = y % CELL_SIZE
                    val bitIndex = localY * CELL_SIZE + localX
                    
                    grid[gridY][gridX].set(bitIndex)
                }
            }
        }
        
        lastBoardHash = boardHash
        return true
    }
    
    /**
     * Check if a piece collides with any occupied cells using spatial partitioning
     */
    fun checkCollision(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // Update grid with current board state
        updateGrid(board)
        
        // Get piece shape
        val shape = getPieceShape(piece)
        
        // Track which grid cells we've already checked
        val checkedCells = mutableSetOf<Pair<Int, Int>>()
        
        // Check each cell of the piece
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    
                    // Check if out of bounds
                    if (boardX < 0 || boardX >= board.width || 
                        boardY < 0 || boardY >= board.height) {
                        return false
                    }
                    
                    // Get grid cell for this position
                    val gridX = boardX / CELL_SIZE
                    val gridY = boardY / CELL_SIZE
                    
                    // Check if grid cell is valid
                    if (gridX < 0 || gridX >= gridWidth || gridY < 0 || gridY >= gridHeight) {
                        continue
                    }
                    
                    // Skip if we've already checked this grid cell and it was empty
                    val gridCell = gridX to gridY
                    if (gridCell in checkedCells) {
                        continue
                    }
                    
                    // Convert to local coordinates within the cell
                    val localX = boardX % CELL_SIZE
                    val localY = boardY % CELL_SIZE
                    val bitIndex = localY * CELL_SIZE + localX
                    
                    // Check if this position is occupied
                    if (grid[gridY][gridX].get(bitIndex)) {
                        return false
                    }
                    
                    // Mark this grid cell as checked
                    checkedCells.add(gridCell)
                }
            }
        }
        
        return true
    }
    
    /**
     * Get all potential collision points for a piece
     */
    fun getPotentialCollisionPoints(piece: TetrisPiece): List<Pair<Int, Int>> {
        val shape = getPieceShape(piece)
        val points = mutableListOf<Pair<Int, Int>>()
        
        // Get all occupied cells of the piece
        for (i in shape.indices) {
            for (j in shape[i].indices) {
                if (shape[i][j]) {
                    val boardX = piece.x + j
                    val boardY = piece.y + i
                    points.add(boardX to boardY)
                }
            }
        }
        
        return points
    }
    
    /**
     * Get piece shape based on type and rotation
     */
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            TetrisPieceType.T -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, true, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            TetrisPieceType.J -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(true, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, true)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false)
                )
            }
            TetrisPieceType.L -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, true),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(true, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            TetrisPieceType.S -> when (piece.rotation % 2) {
                0 -> arrayOf(
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, false, true)
                )
            }
            TetrisPieceType.Z -> when (piece.rotation % 2) {
                0 -> arrayOf(
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, false, true),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
            }
        }
    }
}