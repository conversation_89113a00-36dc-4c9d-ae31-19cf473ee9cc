package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.GameStatistics
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType

/**
 * 俄罗斯方块统计计算器
 * 负责计算和更新游戏统计信息
 */
interface TetrisStatisticsCalculator {
    
    /**
     * 更新统计信息
     * @param currentStats 当前统计信息
     * @param linesCleared 消除的行数
     * @param pieceType 放置的方块类型
     * @param isTSpin 是否为T-Spin
     * @param combo 当前连击数
     * @return 更新后的统计信息
     */
    fun updateStatistics(
        currentStats: GameStatistics,
        linesCleared: Int,
        pieceType: TetrisPieceType,
        isTSpin: Boolean,
        combo: Int
    ): GameStatistics
    
    /**
     * 计算效率指标
     * @param statistics 统计信息
     * @return 更新后的统计信息
     */
    fun calculateEfficiency(statistics: GameStatistics): GameStatistics
    
    /**
     * 获取游戏得分明细
     * @param statistics 统计信息
     * @return 得分明细
     */
    fun getScoreBreakdown(statistics: GameStatistics): Map<String, Int>
    
    /**
     * 获取游戏性能指标
     * @param statistics 统计信息
     * @return 性能指标
     */
    fun getPerformanceMetrics(statistics: GameStatistics): Map<String, Double>
}

/**
 * 俄罗斯方块统计计算器实现
 */
class TetrisStatisticsCalculatorImpl : TetrisStatisticsCalculator {
    
    override fun updateStatistics(
        currentStats: GameStatistics,
        linesCleared: Int,
        pieceType: TetrisPieceType,
        isTSpin: Boolean,
        combo: Int
    ): GameStatistics {
        // 更新方块统计
        val updatedStats = currentStats.updatePieceStats(pieceType)
        
        // 更新消行统计
        val statsWithLines = if (linesCleared > 0) {
            updatedStats.updateLineClearStats(linesCleared, isTSpin, combo > 1)
        } else {
            updatedStats
        }
        
        // 更新总方块数
        val newPiecesPlaced = statsWithLines.piecesPlaced + 1
        
        // 更新总消行数
        val newLinesCleared = statsWithLines.linesCleared + linesCleared
        
        // 更新最大连击
        val newMaxCombo = maxOf(statsWithLines.maxCombo, combo)
        
        // 更新游戏时间（假设外部会更新）
        val gameTime = statsWithLines.gameTime
        
        // 计算效率指标
        val result = statsWithLines.copy(
            piecesPlaced = newPiecesPlaced,
            linesCleared = newLinesCleared,
            maxCombo = newMaxCombo
        )
        
        return calculateEfficiency(result)
    }
    
    override fun calculateEfficiency(statistics: GameStatistics): GameStatistics {
        val gameTimeMinutes = statistics.gameTime / 60000.0
        val piecesPerMinute = if (gameTimeMinutes > 0) statistics.piecesPlaced / gameTimeMinutes else 0.0
        val linesPerMinute = if (gameTimeMinutes > 0) statistics.linesCleared / gameTimeMinutes else 0.0
        val efficiency = if (statistics.piecesPlaced > 0) statistics.linesCleared.toDouble() / statistics.piecesPlaced else 0.0
        
        return statistics.copy(
            piecesPerMinute = piecesPerMinute,
            linesPerMinute = linesPerMinute,
            efficiency = efficiency
        )
    }
    
    override fun getScoreBreakdown(statistics: GameStatistics): Map<String, Int> {
        // 计算各项得分
        val singleScore = statistics.singles * 100
        val doubleScore = statistics.doubles * 300
        val tripleScore = statistics.triples * 500
        val tetrisScore = statistics.tetrises * 800
        val tSpinSingleScore = statistics.tSpinSingles * 800
        val tSpinDoubleScore = statistics.tSpinDoubles * 1200
        val tSpinTripleScore = statistics.tSpinTriples * 1600
        
        return mapOf(
            "singles" to singleScore,
            "doubles" to doubleScore,
            "triples" to tripleScore,
            "tetrises" to tetrisScore,
            "tSpinSingles" to tSpinSingleScore,
            "tSpinDoubles" to tSpinDoubleScore,
            "tSpinTriples" to tSpinTripleScore,
            "total" to (singleScore + doubleScore + tripleScore + tetrisScore + 
                        tSpinSingleScore + tSpinDoubleScore + tSpinTripleScore)
        )
    }
    
    override fun getPerformanceMetrics(statistics: GameStatistics): Map<String, Double> {
        return mapOf(
            "piecesPerMinute" to statistics.piecesPerMinute,
            "linesPerMinute" to statistics.linesPerMinute,
            "efficiency" to statistics.efficiency,
            "tetrisRate" to if (statistics.linesCleared > 0) 
                statistics.tetrises * 4.0 / statistics.linesCleared else 0.0,
            "tSpinRate" to if (statistics.piecesPlaced > 0)
                statistics.tSpins.toDouble() / statistics.piecesPlaced else 0.0
        )
    }
}