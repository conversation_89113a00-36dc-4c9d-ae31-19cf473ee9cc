package com.yu.questicle.feature.tetris.impl.engine.memory

import com.yu.questicle.core.domain.model.tetris.GameStatistics
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisStatus
import com.yu.questicle.feature.tetris.impl.engine.board.BitPackedTetrisBoard
import com.yu.questicle.feature.tetris.impl.engine.board.TetrisBoard

/**
 * Immutable implementation of TetrisGameState with structural sharing.
 * This class provides efficient updates through the with() method that only
 * creates new objects for changed properties, sharing unchanged properties.
 */
class ImmutableGameState private constructor(
    override val board: TetrisBoard,
    override val currentPiece: TetrisPiece?,
    override val nextPiece: TetrisPiece?,
    override val holdPiece: TetrisPiece?,
    override val score: Int,
    override val level: Int,
    override val lines: Int,
    override val combo: Int,
    override val canHold: Boolean,
    override val status: TetrisStatus,
    override val lastDropTime: Long,
    override val statistics: GameStatistics
) : TetrisGameState {

    /**
     * Creates a new ImmutableGameState with the specified properties changed,
     * while keeping all other properties the same (structural sharing).
     */
    fun with(
        board: TetrisBoard = this.board,
        currentPiece: TetrisPiece? = this.currentPiece,
        nextPiece: TetrisPiece? = this.nextPiece,
        holdPiece: TetrisPiece? = this.holdPiece,
        score: Int = this.score,
        level: Int = this.level,
        lines: Int = this.lines,
        combo: Int = this.combo,
        canHold: Boolean = this.canHold,
        status: TetrisStatus = this.status,
        lastDropTime: Long = this.lastDropTime,
        statistics: GameStatistics = this.statistics
    ): ImmutableGameState {
        // Only create a new instance if at least one property has changed
        if (board === this.board &&
            currentPiece === this.currentPiece &&
            nextPiece === this.nextPiece &&
            holdPiece === this.holdPiece &&
            score == this.score &&
            level == this.level &&
            lines == this.lines &&
            combo == this.combo &&
            canHold == this.canHold &&
            status == this.status &&
            lastDropTime == this.lastDropTime &&
            statistics === this.statistics) {
            return this
        }

        // Create a new instance with the updated properties
        return ImmutableGameState(
            board = board,
            currentPiece = currentPiece,
            nextPiece = nextPiece,
            holdPiece = holdPiece,
            score = score,
            level = level,
            lines = lines,
            combo = combo,
            canHold = canHold,
            status = status,
            lastDropTime = lastDropTime,
            statistics = statistics
        )
    }

    /**
     * Converts this immutable game state to a standard game state.
     */
    fun toStandardGameState(): TetrisGameState {
        return TetrisGameState(
            board = board,
            currentPiece = currentPiece,
            nextPiece = nextPiece,
            holdPiece = holdPiece,
            score = score,
            level = level,
            lines = lines,
            combo = combo,
            canHold = canHold,
            status = status,
            lastDropTime = lastDropTime,
            statistics = statistics
        )
    }

    companion object {
        /**
         * Creates an ImmutableGameState from a standard TetrisGameState.
         */
        fun from(state: TetrisGameState): ImmutableGameState {
            // If it's already an ImmutableGameState, return it directly
            if (state is ImmutableGameState) {
                return state
            }

            // Convert statistics to ImmutableGameStatistics if needed
            val immutableStats = if (state.statistics is ImmutableGameStatistics) {
                state.statistics
            } else {
                ImmutableGameStatistics.from(state.statistics)
            }

            return ImmutableGameState(
                board = state.board,
                currentPiece = state.currentPiece,
                nextPiece = state.nextPiece,
                holdPiece = state.holdPiece,
                score = state.score,
                level = state.level,
                lines = state.lines,
                combo = state.combo,
                canHold = state.canHold,
                status = state.status,
                lastDropTime = state.lastDropTime,
                statistics = immutableStats
            )
        }

        /**
         * Creates an initial ImmutableGameState.
         */
        fun initial(): ImmutableGameState {
            return ImmutableGameState(
                board = BitPackedTetrisBoard.empty(10, 20),
                currentPiece = null,
                nextPiece = null,
                holdPiece = null,
                score = 0,
                level = 1,
                lines = 0,
                combo = 0,
                canHold = true,
                status = TetrisStatus.READY,
                lastDropTime = System.currentTimeMillis(),
                statistics = ImmutableGameStatistics.empty()
            )
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is TetrisGameState) return false

        if (board != other.board) return false
        if (currentPiece != other.currentPiece) return false
        if (nextPiece != other.nextPiece) return false
        if (holdPiece != other.holdPiece) return false
        if (score != other.score) return false
        if (level != other.level) return false
        if (lines != other.lines) return false
        if (combo != other.combo) return false
        if (canHold != other.canHold) return false
        if (status != other.status) return false
        if (lastDropTime != other.lastDropTime) return false
        if (statistics != other.statistics) return false

        return true
    }

    override fun hashCode(): Int {
        var result = board.hashCode()
        result = 31 * result + (currentPiece?.hashCode() ?: 0)
        result = 31 * result + (nextPiece?.hashCode() ?: 0)
        result = 31 * result + (holdPiece?.hashCode() ?: 0)
        result = 31 * result + score
        result = 31 * result + level
        result = 31 * result + lines
        result = 31 * result + combo
        result = 31 * result + canHold.hashCode()
        result = 31 * result + status.hashCode()
        result = 31 * result + lastDropTime.hashCode()
        result = 31 * result + statistics.hashCode()
        return result
    }

    override fun toString(): String {
        return "ImmutableGameState(score=$score, level=$level, lines=$lines, status=$status)"
    }
}