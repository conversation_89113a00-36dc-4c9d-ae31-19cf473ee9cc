package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.common.di.Dispatcher
import com.yu.questicle.core.common.di.QuesticleDispatchers
import com.yu.questicle.core.common.performance.PerformanceMonitor
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.domain.repository.TetrisGameStateRepository
import com.yu.questicle.core.domain.service.PlayerSessionManager
import com.yu.questicle.feature.tetris.impl.engine.components.*
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Refactored implementation of TetrisEngine that delegates to specialized components.
 * 
 * This implementation follows SOLID principles with:
 * - Single Responsibility: Each component handles one aspect of the game
 * - Open/Closed: Easy to extend with new components without modifying existing code
 * - Liskov Substitution: Components can be replaced with different implementations
 * - Interface Segregation: Clean interfaces for each component
 * - Dependency Inversion: Components depend on abstractions, not concrete implementations
 */
@Singleton
class RefactoredTetrisEngineImpl @Inject constructor(
    private val gameRepository: GameRepository,
    private val tetrisGameStateRepository: TetrisGameStateRepository,
    private val playerSessionManager: PlayerSessionManager,
    private val performanceMonitor: PerformanceMonitor,
    private val gameLogicProcessor: TetrisGameLogicProcessor,
    private val collisionDetector: TetrisCollisionDetector,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val performanceManager: TetrisPerformanceManager,
    private val cacheManager: TetrisCacheManager,
    private val gameStateValidator: GameStateValidator,
    @Dispatcher(QuesticleDispatchers.Default) private val defaultDispatcher: CoroutineDispatcher
) : TetrisEngine {

    private val _gameState = MutableStateFlow(TetrisGameState.initial())
    private val gameState = _gameState.asStateFlow()

    override suspend fun initializeGame(playerId: String): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("initializeGame", "TetrisEngine") {
            try {
                // Clear caches
                cacheManager.clearAllCaches()
                
                // Initialize game state
                val initialState = TetrisGameState.initial().copy(
                    currentPiece = gameLogicProcessor.generateNextPiece(TetrisGameState.initial()),
                    nextPiece = gameLogicProcessor.generateNextPiece(TetrisGameState.initial())
                )
                _gameState.value = initialState
                
                // Record memory usage
                performanceManager.recordMemoryUsage("TetrisEngine", getMemoryUsageMB())
                
                Result.Success(initialState)
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }

    override suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            val startedState = gameState.copy(
                status = TetrisStatus.PLAYING,
                lastDropTime = System.currentTimeMillis()
            )
            _gameState.value = startedState
            Result.Success(startedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun processAction(action: TetrisAction, currentState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("processAction", "TetrisEngine") {
            try {
                // Process the action based on its type
                val newState = when (action) {
                    is TetrisAction.Move -> processMove(action.direction, currentState)
                    is TetrisAction.Rotate -> processRotate(action.clockwise, currentState)
                    is TetrisAction.Drop -> processDrop(action.hard, currentState)
                    is TetrisAction.Hold -> processHold(currentState)
                    is TetrisAction.Pause -> processPause(currentState)
                }
                
                _gameState.value = newState
                
                // Record performance metrics
                performanceManager.recordMemoryUsage("TetrisEngine", getMemoryUsageMB())
                
                Result.Success(newState)
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }

    override suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            val pausedState = gameState.copy(status = TetrisStatus.PAUSED)
            _gameState.value = pausedState
            Result.Success(pausedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            val resumedState = gameState.copy(
                status = TetrisStatus.PLAYING,
                lastDropTime = System.currentTimeMillis()
            )
            _gameState.value = resumedState
            Result.Success(resumedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun endGame(gameState: TetrisGameState): Result<Game> = withContext(defaultDispatcher) {
        try {
            val endedState = gameState.copy(status = TetrisStatus.GAME_OVER)
            _gameState.value = endedState
            
            val playerId = playerSessionManager.getCurrentPlayerId()
            val game = Game(
                type = GameType.TETRIS,
                status = com.yu.questicle.core.domain.model.GameStatus.COMPLETED,
                playerId = playerId,
                score = gameState.score,
                level = gameState.level,
                endTime = System.currentTimeMillis() / 1000
            )
            
            // End game session
            playerSessionManager.endGameSession(
                finalScore = gameState.score,
                gameData = getGameStatistics(gameState)
            )
            
            Result.Success(game)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override fun observeGameState(): Flow<TetrisGameState> = gameState

    override fun isValidAction(action: TetrisAction, currentState: TetrisGameState): Boolean {
        return when (action) {
            is TetrisAction.Move -> currentState.currentPiece?.let { piece ->
                val (dx, dy) = when (action.direction) {
                    Direction.LEFT -> -1 to 0
                    Direction.RIGHT -> 1 to 0
                    Direction.DOWN -> 0 to 1
                }
                val newPiece = piece.copy(x = piece.x + dx, y = piece.y + dy)
                collisionDetector.isValidPosition(newPiece, currentState.board)
            } ?: false
            is TetrisAction.Rotate -> currentState.currentPiece?.let { piece ->
                val newRotation = (piece.rotation + (if (action.clockwise) 1 else 3)) % 4
                val rotatedPiece = piece.copy(rotation = newRotation)
                collisionDetector.isValidPosition(rotatedPiece, currentState.board)
            } ?: false
            is TetrisAction.Drop -> currentState.currentPiece != null
            is TetrisAction.Hold -> currentState.canHold && currentState.currentPiece != null
            is TetrisAction.Pause -> currentState.status == TetrisStatus.PLAYING
        }
    }

    override fun calculateScore(gameState: TetrisGameState): Int = gameState.score

    override fun isGameOver(gameState: TetrisGameState): Boolean {
        return gameStateValidator.isGameOver(gameState)
    }

    override fun getGameStatistics(gameState: TetrisGameState): Map<String, Any> {
        val stats = gameState.statistics
        return mapOf(
            "score" to gameState.score,
            "level" to gameState.level,
            "lines" to gameState.lines,
            "pieces_placed" to stats.piecesPlaced,
            "lines_cleared" to stats.linesCleared,
            "singles" to stats.singles,
            "doubles" to stats.doubles,
            "triples" to stats.triples,
            "tetrises" to stats.tetrises,
            "t_spins" to stats.tSpins,
            "max_combo" to stats.maxCombo,
            "perfect_clears" to stats.perfectClears
        )
    }

    override suspend fun saveGameState(gameState: TetrisGameState): Result<Unit> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("saveGameState", "TetrisEngine") {
            try {
                val playerId = playerSessionManager.getCurrentPlayerId()
                tetrisGameStateRepository.saveGameState(
                    gameState = gameState,
                    playerId = playerId,
                    slotId = "autosave"
                )
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }

    override suspend fun loadGameState(gameId: String): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("loadGameState", "TetrisEngine") {
            try {
                val playerId = playerSessionManager.getCurrentPlayerId()
                val result = tetrisGameStateRepository.loadGameState(
                    playerId = playerId,
                    slotId = gameId
                )
                
                if (result is Result.Success) {
                    _gameState.value = result.data
                }
                
                result
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }

    // TetrisEngine specific methods
    override fun generateNextPiece(): TetrisPiece {
        return gameLogicProcessor.generateNextPiece(_gameState.value)
    }

    override suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Move(direction, playerId), gameState)
    }

    override suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Rotate(clockwise, playerId), gameState)
    }

    override suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Drop(hard, playerId), gameState)
    }

    override suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Hold(playerId), gameState)
    }

    override suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            val lineClearResult = gameLogicProcessor.processLineClear(gameState.board)
            
            if (lineClearResult is LineClearResult.Success) {
                val newScore = gameState.score + lineClearResult.scoreIncrease
                val newLines = gameState.lines + lineClearResult.linesCleared
                val newLevel = statisticsCalculator.calculateLevel(newLines)
                val newCombo = gameState.combo + 1
                
                // Update statistics
                val updatedStats = statisticsCalculator.updateGameStatistics(
                    gameState.statistics,
                    lineClearResult.linesCleared,
                    null,
                    lineClearResult.isTSpin,
                    lineClearResult.isPerfectClear,
                    lineClearResult.isBackToBack
                )
                
                val updatedState = gameState.copy(
                    board = lineClearResult.clearedBoard,
                    score = newScore,
                    lines = newLines,
                    level = newLevel,
                    combo = newCombo,
                    statistics = updatedStats
                )
                
                Result.Success(updatedState)
            } else {
                // Reset combo if no lines cleared
                Result.Success(gameState.copy(combo = 0))
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override fun calculateDropInterval(level: Int): Long {
        return statisticsCalculator.calculateDropInterval(level)
    }

    override fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int {
        return statisticsCalculator.calculateLineClearScore(linesCleared, level, combo = combo)
    }

    override fun getGhostPiece(gameState: TetrisGameState): TetrisPiece? {
        return gameState.currentPiece?.let { piece ->
            collisionDetector.findLandingPosition(piece, gameState.board)
        }
    }

    override fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean {
        return collisionDetector.isValidPosition(piece, gameState.board)
    }

    override fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece> {
        return gameState.currentPiece?.let { piece ->
            (0..3).mapNotNull { rotation ->
                val rotatedPiece = piece.copy(rotation = rotation)
                if (collisionDetector.isValidPosition(rotatedPiece, gameState.board)) rotatedPiece else null
            }
        } ?: emptyList()
    }

    override fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece> {
        return gameState.currentPiece?.let { piece ->
            val positions = mutableListOf<TetrisPiece>()
            for (x in 0 until gameState.board.width) {
                for (y in 0 until gameState.board.height) {
                    val testPiece = piece.copy(x = x, y = y)
                    if (collisionDetector.isValidPosition(testPiece, gameState.board)) {
                        positions.add(testPiece)
                    }
                }
            }
            positions
        } ?: emptyList()
    }

    override suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState> {
        return dropPiece(false, gameState)
    }

    override fun checkTSpin(gameState: TetrisGameState): Boolean {
        // Delegate to game logic processor
        return false // In a real implementation, we would check for T-spin
    }

    override fun calculateLevel(lines: Int): Int {
        return statisticsCalculator.calculateLevel(lines)
    }

    // Private helper methods
    
    private suspend fun processMove(direction: Direction, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState
        
        val moveResult = gameLogicProcessor.processMove(piece, direction, gameState.board)
        
        return when (moveResult) {
            is GameMoveResult.Success -> {
                gameState.copy(
                    currentPiece = moveResult.newPiece,
                    lastDropTime = if (direction == Direction.DOWN) System.currentTimeMillis() else gameState.lastDropTime
                )
            }
            is GameMoveResult.Landed -> {
                // Piece has landed, place it and generate next piece
                placePieceAndGenerateNext(gameState)
            }
            is GameMoveResult.Blocked -> {
                // Piece is blocked, no change
                gameState
            }
        }
    }
    
    private suspend fun processRotate(clockwise: Boolean, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState
        
        val rotationResult = gameLogicProcessor.processRotation(piece, clockwise, gameState.board)
        
        return when (rotationResult) {
            is GameRotationResult.Success -> {
                gameState.copy(currentPiece = rotationResult.rotatedPiece)
            }
            is GameRotationResult.WallKick -> {
                gameState.copy(currentPiece = rotationResult.kickedPiece)
            }
            is GameRotationResult.Blocked -> {
                // Rotation is blocked, no change
                gameState
            }
        }
    }
    
    private suspend fun processDrop(hard: Boolean, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState
        
        if (hard) {
            // Hard drop - move piece to bottom immediately
            val landingPiece = collisionDetector.findLandingPosition(piece, gameState.board)
            
            // Update statistics
            val updatedStats = gameState.statistics.copy(
                hardDrops = gameState.statistics.hardDrops + 1,
                totalDrops = gameState.statistics.totalDrops + 1
            )
            
            val stateWithStats = gameState.copy(
                currentPiece = landingPiece,
                statistics = updatedStats
            )
            
            return placePieceAndGenerateNext(stateWithStats)
        } else {
            // Soft drop - move down one line
            return processMove(Direction.DOWN, gameState)
        }
    }
    
    private suspend fun processHold(gameState: TetrisGameState): TetrisGameState {
        if (!gameState.canHold || gameState.currentPiece == null) return gameState
        
        val currentPiece = gameState.currentPiece
        val heldPiece = gameState.holdPiece
        
        // Update hold usage statistics
        val updatedStats = gameState.statistics.copy(
            holdUsed = gameState.statistics.holdUsed + 1
        )
        
        return if (heldPiece != null) {
            // Swap current piece with held piece
            val startX = getCorrectStartX(heldPiece.type)
            gameState.copy(
                currentPiece = heldPiece.copy(x = startX, y = 0, rotation = 0),
                holdPiece = currentPiece,
                canHold = false,
                statistics = updatedStats
            )
        } else {
            // Hold current piece and use next piece
            val nextPiece = gameState.nextPiece
            val startX = nextPiece?.let { getCorrectStartX(it.type) } ?: 3
            gameState.copy(
                currentPiece = nextPiece?.copy(x = startX, y = 0, rotation = 0),
                nextPiece = generateNextPiece(),
                holdPiece = currentPiece,
                canHold = false,
                statistics = updatedStats
            )
        }
    }
    
    private fun processPause(gameState: TetrisGameState): TetrisGameState {
        return gameState.copy(
            status = if (gameState.status == TetrisStatus.PLAYING) TetrisStatus.PAUSED else TetrisStatus.PLAYING
        )
    }
    
    private suspend fun placePieceAndGenerateNext(gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState
        
        // Place the piece on the board
        val boardWithPiece = (gameLogicProcessor as TetrisGameLogicProcessorImpl).placePiece(piece, gameState.board)
        
        // Process line clearing
        val lineClearResult = gameLogicProcessor.processLineClear(boardWithPiece)
        
        // Calculate new state
        val (newBoard, linesCleared, scoreIncrease, isTSpin, isPerfectClear) = when (lineClearResult) {
            is LineClearResult.Success -> {
                listOf(
                    lineClearResult.clearedBoard,
                    lineClearResult.linesCleared,
                    lineClearResult.scoreIncrease,
                    lineClearResult.isTSpin,
                    lineClearResult.isPerfectClear
                )
            }
            is LineClearResult.NoLines -> {
                listOf(boardWithPiece, 0, 0, false, false)
            }
        }
        
        // Update game state
        val newScore = gameState.score + (scoreIncrease as Int)
        val newLines = gameState.lines + (linesCleared as Int)
        val newLevel = statisticsCalculator.calculateLevel(newLines)
        val newCombo = if ((linesCleared as Int) > 0) gameState.combo + 1 else 0
        
        // Update statistics
        val updatedStats = statisticsCalculator.updateGameStatistics(
            gameState.statistics,
            linesCleared as Int,
            piece.type,
            isTSpin as Boolean,
            isPerfectClear as Boolean
        )
        
        // Generate new piece
        val newCurrentPiece = gameState.nextPiece?.let { nextPiece ->
            val startX = getCorrectStartX(nextPiece.type)
            val newPiece = nextPiece.copy(x = startX, y = 0, rotation = 0)
            
            // Check if new piece can be placed
            if (collisionDetector.isValidPosition(newPiece, newBoard as TetrisBoard)) {
                newPiece
            } else {
                // Game over if new piece can't be placed
                null
            }
        }
        
        // Check for game over
        val newStatus = if (newCurrentPiece == null && gameState.nextPiece != null) {
            TetrisStatus.GAME_OVER
        } else {
            gameState.status
        }
        
        return gameState.copy(
            board = newBoard as TetrisBoard,
            currentPiece = newCurrentPiece,
            nextPiece = generateNextPiece(),
            canHold = true,
            statistics = updatedStats,
            status = newStatus,
            score = newScore,
            lines = newLines,
            level = newLevel,
            combo = newCombo
        )
    }
    
    private fun getCorrectStartX(pieceType: TetrisPieceType): Int {
        return when (pieceType) {
            TetrisPieceType.I -> 3  // I piece is 4 blocks wide
            TetrisPieceType.O -> 4  // O piece is 2x2
            else -> 3               // Other pieces are 3 blocks wide
        }
    }
    
    private fun getMemoryUsageMB(): Long {
        return Runtime.getRuntime().totalMemory() / (1024 * 1024)
    }
}