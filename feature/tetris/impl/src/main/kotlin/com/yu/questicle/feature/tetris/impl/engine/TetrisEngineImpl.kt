package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.common.di.Dispatcher
import com.yu.questicle.core.common.di.QuesticleDispatchers
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.generator.TetrisPieceGenerator
import com.yu.questicle.core.domain.generator.TetrisPieceGeneratorFactory
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.domain.repository.TetrisGameStateRepository
import com.yu.questicle.core.domain.rotation.SuperRotationSystem
import com.yu.questicle.core.domain.rotation.TSpinType
import com.yu.questicle.core.domain.service.PlayerSessionManager
import com.yu.questicle.core.testing.performance.PerformanceMonitor
import com.yu.questicle.feature.tetris.impl.engine.components.*
import com.yu.questicle.feature.tetris.impl.exception.TetrisException
import com.yu.questicle.feature.tetris.impl.model.*
import com.yu.questicle.feature.tetris.impl.scoring.TetrisScoring
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tetris 游戏引擎完整实现
 *
 * 结合了原版的完整游戏逻辑和重构版的现代化架构设计：
 * - 完整的游戏逻辑实现（移动、旋转、消行、计分等）
 * - 现代化的架构设计（组件化、依赖注入、性能监控）
 * - 完善的错误处理和异常管理
 * - 高性能的协程和缓存机制
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Singleton
class TetrisEngineImpl @Inject constructor(
    private val gameRepository: GameRepository,
    private val gameStateRepository: TetrisGameStateRepository,
    private val playerSessionManager: PlayerSessionManager,
    private val performanceMonitor: PerformanceMonitor,
    private val gameLogicProcessor: TetrisGameLogicProcessor,
    private val collisionDetector: TetrisCollisionDetector,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val performanceManager: TetrisPerformanceManager,
    private val cacheManager: TetrisCacheManager,
    private val gameStateValidator: GameStateValidator,
    @Dispatcher(QuesticleDispatchers.Default) private val defaultDispatcher: CoroutineDispatcher
) : TetrisEngine {

    private val pieceGenerator: TetrisPieceGenerator = TetrisPieceGeneratorFactory.createSevenBag()
    private val _gameState = MutableStateFlow<TetrisGameState>(TetrisGameState.initial())

    override fun observeGameState(): Flow<TetrisGameState> = _gameState.asStateFlow()
    
    override suspend fun initializeGame(playerId: String): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("initializeGame", "TetrisEngine") {
            try {
                // 重置生成器
                pieceGenerator.reset()

                // 清理缓存
                cacheManager.clearAllCaches()

                // 创建初始游戏状态
                val initialBoard = TetrisBoard.empty()
                val initialPiece = generateNextPiece()
                val nextPiece = generateNextPiece()

                // 创建游戏状态
                val gameState = TetrisGameState(
                    id = UUID.randomUUID().toString(),
                    board = initialBoard,
                    currentPiece = initialPiece,
                    nextPiece = nextPiece,
                    status = TetrisStatus.READY,
                    dropInterval = calculateDropInterval(1)
                )

                // 更新状态流
                _gameState.update { gameState }

                // 初始化缓存
                cacheManager.initialize()

                // 初始化性能管理
                performanceManager.initialize()

                // 记录内存使用
                performanceManager.recordMemoryUsage("TetrisEngine", getMemoryUsageMB())

                Result.Success(gameState)
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }
    
    override suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            if (gameState.status != TetrisStatus.READY && gameState.status != TetrisStatus.PAUSED) {
                throw TetrisException(
                    message = "Cannot start game in ${gameState.status} status",
                    errorCode = "INVALID_GAME_STATE"
                )
            }

            val updatedState = gameState.copy(
                status = TetrisStatus.PLAYING,
                lastDropTime = System.currentTimeMillis()
            )

            _gameState.update { updatedState }

            // 保存游戏状态
            saveGameState(updatedState)

            Result.Success(updatedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            if (gameState.status != TetrisStatus.PLAYING) {
                throw TetrisException(
                    message = "Cannot pause game in ${gameState.status} status",
                    errorCode = "INVALID_GAME_STATE"
                )
            }

            val updatedState = gameState.copy(status = TetrisStatus.PAUSED)
            _gameState.update { updatedState }

            // 保存游戏状态
            saveGameState(updatedState)

            Result.Success(updatedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            if (gameState.status != TetrisStatus.PAUSED) {
                throw TetrisException(
                    message = "Cannot resume game in ${gameState.status} status",
                    errorCode = "INVALID_GAME_STATE"
                )
            }

            val updatedState = gameState.copy(
                status = TetrisStatus.PLAYING,
                lastDropTime = System.currentTimeMillis()
            )

            _gameState.update { updatedState }

            // 保存游戏状态
            saveGameState(updatedState)

            Result.Success(updatedState)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun endGame(gameState: TetrisGameState): Result<Game> = withContext(defaultDispatcher) {
        try {
            val finalState = gameState.copy(status = TetrisStatus.GAME_OVER)
            _gameState.update { finalState }

            // 保存游戏状态
            saveGameState(finalState)

            // 清理缓存
            cacheManager.clear()

            // 获取玩家ID
            val playerId = playerSessionManager.getCurrentPlayerId()

            // 创建游戏记录
            val game = Game(
                type = GameType.TETRIS,
                status = finalState.status.toGameStatus(),
                playerId = playerId,
                score = finalState.score,
                level = finalState.level,
                endTime = System.currentTimeMillis() / 1000
            )

            // 结束游戏会话
            playerSessionManager.endGameSession(
                finalScore = finalState.score,
                gameData = getGameStatistics(finalState)
            )

            Result.Success(game)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun processAction(action: TetrisAction, currentState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("processAction", "TetrisEngine") {
            try {
                if (!isValidAction(action, currentState)) {
                    return@measureSuspendOperation Result.Error(
                        TetrisException(
                            message = "Invalid action $action for current state",
                            errorCode = "INVALID_ACTION"
                        )
                    )
                }

                val newState = when (action) {
                    is TetrisAction.Move -> processMove(action.direction, currentState)
                    is TetrisAction.Rotate -> processRotate(action.clockwise, currentState)
                    is TetrisAction.Drop -> processDrop(action.hard, currentState)
                    is TetrisAction.Hold -> processHold(currentState)
                    is TetrisAction.Pause -> processPause(currentState)
                }

                _gameState.update { newState }

                // 记录性能指标
                performanceManager.recordMemoryUsage("TetrisEngine", getMemoryUsageMB())

                Result.Success(newState)
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }
    
    override suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Move(direction, playerId), gameState)
    }
    
    override suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Rotate(clockwise, playerId), gameState)
    }
    
    override suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Drop(hard, playerId), gameState)
    }
    
    override suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState> {
        val playerId = playerSessionManager.getCurrentPlayerId()
        return processAction(TetrisAction.Hold(playerId), gameState)
    }
    
    override suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState> = withContext(defaultDispatcher) {
        try {
            if (gameState.status != TetrisStatus.PLAYING) {
                throw TetrisException(
                    message = "Cannot check line clear when game is not playing",
                    errorCode = "INVALID_GAME_STATE"
                )
            }

            val lineClearResult = gameLogicProcessor.processLineClear(gameState.board)

            when (lineClearResult) {
                is LineClearResult.Cleared -> {
                    val newScore = gameState.score + lineClearResult.scoreIncrease
                    val newLines = gameState.lines + lineClearResult.linesCleared
                    val newLevel = calculateLevel(newLines)
                    val newCombo = gameState.combo + 1

                    // 更新统计信息
                    val updatedStats = statisticsCalculator.updateStatistics(
                        gameState.statistics,
                        lineClearResult.linesCleared,
                        gameState.currentPiece?.type ?: TetrisPieceType.I,
                        lineClearResult.isTSpin,
                        newCombo
                    )

                    val updatedState = gameState.copy(
                        board = lineClearResult.clearedBoard,
                        score = newScore,
                        lines = newLines,
                        level = newLevel,
                        combo = newCombo,
                        dropInterval = calculateDropInterval(newLevel),
                        statistics = updatedStats
                    )

                    _gameState.update { updatedState }
                    Result.Success(updatedState)
                }
                is LineClearResult.None -> {
                    // 重置连击
                    val updatedState = gameState.copy(combo = 0)
                    _gameState.update { updatedState }
                    Result.Success(updatedState)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState> {
        if (gameState.status != TetrisStatus.PLAYING) {
            return Result.Error(TetrisException(
                message = "Cannot auto drop when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            ))
        }

        val currentTime = System.currentTimeMillis()
        val timeSinceLastDrop = currentTime - gameState.lastDropTime

        if (timeSinceLastDrop < gameState.dropInterval) {
            // 还没到下落时间
            return Result.Success(gameState)
        }

        // 自动下落一格
        return movePiece(Direction.DOWN, gameState)
    }
    
    override fun generateNextPiece(): TetrisPiece {
        return pieceGenerator.generateNext()
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // 经典俄罗斯方块下落速度公式：(0.8 - ((level - 1) * 0.007))^(level - 1) * 1000
        val baseInterval = Math.pow(0.8 - ((level - 1) * 0.007), level - 1.toDouble()) * 1000
        return baseInterval.toLong().coerceAtLeast(50) // 最小50毫秒
    }
    
    override fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int {
        return TetrisScoring.calculateLineClearScore(linesCleared, level, false, false, false)
    }
    
    override fun getGhostPiece(gameState: TetrisGameState): TetrisPiece? {
        val currentPiece = gameState.currentPiece ?: return null
        
        // 找到最低可能的位置
        var ghostPiece = currentPiece
        var testY = ghostPiece.y + 1
        
        while (collisionDetector.isValidPosition(ghostPiece.copy(y = testY), gameState.board)) {
            testY++
        }
        
        // 最后一个有效位置
        return ghostPiece.copy(y = testY - 1)
    }
    
    override fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean {
        return collisionDetector.isValidPosition(piece, gameState.board)
    }
    
    override fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的旋转
        for (rotation in 0 until 4) {
            val rotatedPiece = currentPiece.copy(rotation = rotation)
            if (collisionDetector.isValidPosition(rotatedPiece, gameState.board)) {
                result.add(rotatedPiece)
            }
        }
        
        return result
    }
    
    override fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的水平位置
        for (x in -2..gameState.board.width + 2) {
            val movedPiece = currentPiece.copy(x = x)
            if (collisionDetector.isValidPosition(movedPiece, gameState.board)) {
                result.add(movedPiece)
            }
        }
        
        return result
    }
    
    override fun checkTSpin(gameState: TetrisGameState): Boolean {
        val currentPiece = gameState.currentPiece ?: return false
        
        // 只有T方块才能形成T-Spin
        if (currentPiece.type != TetrisPieceType.T) {
            return false
        }
        
        // 使用SuperRotationSystem检测T-Spin
        val lastAction = TetrisAction.Rotate(true, "player") // 假设最后一个动作是旋转
        val tSpinType = SuperRotationSystem.detectTSpin(currentPiece, gameState.board, lastAction)
        
        return tSpinType != TSpinType.NONE
    }
    
    override fun calculateLevel(lines: Int): Int {
        // 每10行升一级
        return (lines / 10) + 1
    }
    
    override fun isValidAction(action: TetrisAction, currentState: TetrisGameState): Boolean {
        return when (action) {
            is TetrisAction.Move -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Rotate -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Drop -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Hold -> currentState.status == TetrisStatus.PLAYING && currentState.canHold
            is TetrisAction.Pause -> currentState.status == TetrisStatus.PLAYING
        }
    }
    
    override fun calculateScore(gameState: TetrisGameState): Int {
        return gameState.score
    }
    
    override fun isGameOver(gameState: TetrisGameState): Boolean {
        return gameState.status == TetrisStatus.GAME_OVER
    }
    
    override fun getGameStatistics(gameState: TetrisGameState): Map<String, Any> {
        return mapOf(
            "score" to gameState.score,
            "level" to gameState.level,
            "lines" to gameState.lines,
            "pieces" to gameState.statistics.piecesPlaced,
            "tetrises" to gameState.statistics.tetrises,
            "tSpins" to gameState.statistics.tSpins,
            "maxCombo" to gameState.statistics.maxCombo,
            "efficiency" to gameState.statistics.efficiency
        )
    }
    
    override suspend fun saveGameState(gameState: TetrisGameState): Result<Unit> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("saveGameState", "TetrisEngine") {
            try {
                val playerId = playerSessionManager.getCurrentPlayerId()
                gameStateRepository.saveGameState(
                    gameState = gameState,
                    playerId = playerId,
                    slotId = "autosave"
                )
                Result.Success(Unit)
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }
    
    override suspend fun loadGameState(gameId: String): Result<TetrisGameState> = withContext(defaultDispatcher) {
        performanceMonitor.measureSuspendOperation("loadGameState", "TetrisEngine") {
            try {
                val playerId = playerSessionManager.getCurrentPlayerId()
                val result = gameStateRepository.loadGameState(playerId, gameId)

                if (result is Result.Success) {
                    _gameState.update { result.data }
                }

                result
            } catch (e: Exception) {
                Result.Error(e.toQuesticleException())
            }
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 处理方块移动
     */
    private suspend fun processMove(direction: Direction, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState

        val moveResult = gameLogicProcessor.processMove(piece, direction, gameState.board)

        return when (moveResult) {
            is GameMoveResult.Success -> {
                gameState.copy(
                    currentPiece = moveResult.newPiece,
                    lastDropTime = if (direction == Direction.DOWN) System.currentTimeMillis() else gameState.lastDropTime
                )
            }
            is GameMoveResult.Landed -> {
                // 方块已着陆，放置并生成新方块
                placePieceAndGenerateNext(gameState.copy(currentPiece = moveResult.landedPiece))
            }
            is GameMoveResult.Blocked -> {
                // 方块被阻挡，无变化
                gameState
            }
        }
    }

    /**
     * 处理方块旋转
     */
    private suspend fun processRotate(clockwise: Boolean, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState

        val rotationResult = gameLogicProcessor.processRotation(piece, clockwise, gameState.board)

        return when (rotationResult) {
            is GameRotationResult.Success -> {
                gameState.copy(currentPiece = rotationResult.rotatedPiece)
            }
            is GameRotationResult.SuccessWithKick -> {
                gameState.copy(currentPiece = rotationResult.kickedPiece)
            }
            is GameRotationResult.Failed -> {
                // 旋转失败，无变化
                gameState
            }
        }
    }

    /**
     * 处理方块下落
     */
    private suspend fun processDrop(hard: Boolean, gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState

        if (hard) {
            // 硬降 - 立即移动到底部
            val landingPiece = collisionDetector.findLandingPosition(piece, gameState.board)

            // 计算下落距离得分
            val dropDistance = landingPiece.y - piece.y
            val dropScore = dropDistance * TetrisScoring.HARD_DROP_SCORE

            // 更新统计信息
            val updatedStats = gameState.statistics.copy(
                hardDrops = gameState.statistics.hardDrops + 1,
                totalDrops = gameState.statistics.totalDrops + 1
            )

            val stateWithStats = gameState.copy(
                currentPiece = landingPiece,
                statistics = updatedStats,
                score = gameState.score + dropScore
            )

            return placePieceAndGenerateNext(stateWithStats)
        } else {
            // 软降 - 向下移动一行
            return processMove(Direction.DOWN, gameState)
        }
    }

    /**
     * 处理保留方块
     */
    private suspend fun processHold(gameState: TetrisGameState): TetrisGameState {
        if (!gameState.canHold || gameState.currentPiece == null) return gameState

        val currentPiece = gameState.currentPiece
        val heldPiece = gameState.holdPiece

        // 更新保留使用统计
        val updatedStats = gameState.statistics.copy(
            holdUsed = gameState.statistics.holdUsed + 1
        )

        return if (heldPiece != null) {
            // 交换当前方块和保留方块
            val startX = getCorrectStartX(heldPiece.type)
            gameState.copy(
                currentPiece = heldPiece.copy(x = startX, y = 0, rotation = 0),
                holdPiece = currentPiece,
                canHold = false,
                statistics = updatedStats
            )
        } else {
            // 保留当前方块并使用下一个方块
            val nextPiece = gameState.nextPiece
            val startX = nextPiece?.let { getCorrectStartX(it.type) } ?: 3
            gameState.copy(
                currentPiece = nextPiece?.copy(x = startX, y = 0, rotation = 0),
                nextPiece = generateNextPiece(),
                holdPiece = currentPiece,
                canHold = false,
                statistics = updatedStats
            )
        }
    }

    /**
     * 处理暂停
     */
    private fun processPause(gameState: TetrisGameState): TetrisGameState {
        return gameState.copy(
            status = if (gameState.status == TetrisStatus.PLAYING) TetrisStatus.PAUSED else TetrisStatus.PLAYING
        )
    }

    /**
     * 放置方块并生成新方块
     */
    private suspend fun placePieceAndGenerateNext(gameState: TetrisGameState): TetrisGameState {
        val piece = gameState.currentPiece ?: return gameState

        // 放置方块到棋盘
        val boardWithPiece = gameState.board.placePiece(piece)

        // 处理消行
        val lineClearResult = gameLogicProcessor.processLineClear(boardWithPiece)

        // 计算新状态
        val (newBoard, linesCleared, scoreIncrease, isTSpin, isPerfectClear, isBackToBack) = when (lineClearResult) {
            is LineClearResult.Cleared -> {
                listOf(
                    lineClearResult.clearedBoard,
                    lineClearResult.linesCleared,
                    lineClearResult.scoreIncrease,
                    lineClearResult.isTSpin,
                    lineClearResult.isPerfectClear,
                    lineClearResult.isBackToBack
                )
            }
            is LineClearResult.None -> {
                listOf(boardWithPiece, 0, 0, false, false, false)
            }
        }

        // 更新游戏状态
        val newScore = gameState.score + (scoreIncrease as Int)
        val newLines = gameState.lines + (linesCleared as Int)
        val newLevel = calculateLevel(newLines)
        val newCombo = if ((linesCleared as Int) > 0) gameState.combo + 1 else 0

        // 更新统计信息
        val updatedStats = statisticsCalculator.updateStatistics(
            gameState.statistics,
            linesCleared as Int,
            piece.type,
            isTSpin as Boolean,
            newCombo
        )

        // 生成新方块
        val newCurrentPiece = gameState.nextPiece?.let { nextPiece ->
            val startX = getCorrectStartX(nextPiece.type)
            val newPiece = nextPiece.copy(x = startX, y = 0, rotation = 0)

            // 检查新方块是否可以放置
            if (collisionDetector.isValidPosition(newPiece, newBoard as TetrisBoard)) {
                newPiece
            } else {
                // 游戏结束，如果新方块无法放置
                null
            }
        }

        // 检查游戏结束
        val newStatus = if (newCurrentPiece == null && gameState.nextPiece != null) {
            TetrisStatus.GAME_OVER
        } else {
            gameState.status
        }

        return gameState.copy(
            board = newBoard as TetrisBoard,
            currentPiece = newCurrentPiece,
            nextPiece = generateNextPiece(),
            canHold = true,
            statistics = updatedStats,
            status = newStatus,
            score = newScore,
            lines = newLines,
            level = newLevel,
            combo = newCombo,
            dropInterval = calculateDropInterval(newLevel)
        )
    }

    /**
     * 获取方块类型的正确起始X位置
     */
    private fun getCorrectStartX(pieceType: TetrisPieceType): Int {
        return when (pieceType) {
            TetrisPieceType.I -> 3  // I方块宽4格
            TetrisPieceType.O -> 4  // O方块2x2
            else -> 3               // 其他方块宽3格
        }
    }

    /**
     * 获取内存使用量（MB）
     */
    private fun getMemoryUsageMB(): Long {
        return Runtime.getRuntime().totalMemory() / (1024 * 1024)
    }
}