package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.feature.tetris.impl.exception.TetrisException
import com.yu.questicle.feature.tetris.impl.scoring.TetrisScoring
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.generator.TetrisPieceGenerator
import com.yu.questicle.core.domain.generator.TetrisPieceGeneratorFactory
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.domain.repository.TetrisGameStateRepository
import com.yu.questicle.core.domain.rotation.SuperRotationSystem
import com.yu.questicle.core.domain.rotation.TSpinType
import com.yu.questicle.feature.tetris.impl.engine.components.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 俄罗斯方块游戏引擎实现
 */
@Singleton
class TetrisEngineImpl @Inject constructor(
    private val gameStateRepository: TetrisGameStateRepository,
    private val gameLogicProcessor: TetrisGameLogicProcessor,
    private val collisionDetector: TetrisCollisionDetector,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val performanceManager: TetrisPerformanceManager,
    private val cacheManager: TetrisCacheManager
) : TetrisEngine {

    private val pieceGenerator: TetrisPieceGenerator = TetrisPieceGeneratorFactory.createSevenBag()
    private val _gameState = MutableStateFlow<TetrisGameState>(TetrisGameState.initial())
    
    override fun observeGameState(): Flow<TetrisGameState> = _gameState.asStateFlow()
    
    override suspend fun initializeGame(playerId: String): Result<TetrisGameState> = runCatching {
        // 重置生成器
        pieceGenerator.reset()
        
        // 创建初始游戏状态
        val initialBoard = TetrisBoard.empty()
        val initialPiece = generateNextPiece()
        val nextPiece = generateNextPiece()
        
        // 创建游戏状态
        val gameState = TetrisGameState(
            id = UUID.randomUUID().toString(),
            board = initialBoard,
            currentPiece = initialPiece,
            nextPiece = nextPiece,
            status = TetrisStatus.READY,
            dropInterval = calculateDropInterval(1)
        )
        
        // 更新状态流
        _gameState.update { gameState }
        
        // 初始化缓存
        cacheManager.initialize()
        
        // 初始化性能管理
        performanceManager.initialize()
        
        return@resultCatching gameState
    }
    
    override suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.READY && gameState.status != TetrisStatus.PAUSED) {
            throw TetrisException(
                message = "Cannot start game in ${gameState.status} status",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val updatedState = gameState.copy(
            status = TetrisStatus.PLAYING,
            lastDropTime = System.currentTimeMillis()
        )
        
        _gameState.update { updatedState }
        
        // 保存游戏状态
        saveGameState(updatedState)
        
        return@resultCatching updatedState
    }
    
    override suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot pause game in ${gameState.status} status",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val updatedState = gameState.copy(status = TetrisStatus.PAUSED)
        _gameState.update { updatedState }
        
        // 保存游戏状态
        saveGameState(updatedState)
        
        return@resultCatching updatedState
    }
    
    override suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PAUSED) {
            throw TetrisException(
                message = "Cannot resume game in ${gameState.status} status",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val updatedState = gameState.copy(
            status = TetrisStatus.PLAYING,
            lastDropTime = System.currentTimeMillis()
        )
        
        _gameState.update { updatedState }
        
        // 保存游戏状态
        saveGameState(updatedState)
        
        return@resultCatching updatedState
    }
    
    override suspend fun endGame(gameState: TetrisGameState): Result<Game> = resultCatching {
        val finalState = gameState.copy(status = TetrisStatus.GAME_OVER)
        _gameState.update { finalState }
        
        // 保存游戏状态
        saveGameState(finalState)
        
        // 清理缓存
        cacheManager.clear()
        
        // 创建游戏记录
        return@resultCatching Game(
            id = finalState.id,
            type = GameType.TETRIS,
            score = finalState.score,
            level = finalState.level,
            duration = System.currentTimeMillis() - finalState.lastDropTime,
            status = finalState.status.toGameStatus(),
            statistics = mapOf(
                "lines" to finalState.lines,
                "pieces" to finalState.statistics.piecesPlaced,
                "tetrises" to finalState.statistics.tetrises,
                "tSpins" to finalState.statistics.tSpins,
                "maxCombo" to finalState.statistics.maxCombo
            )
        )
    }
    
    override suspend fun processAction(action: TetrisAction, currentState: TetrisGameState): Result<TetrisGameState> {
        if (!isValidAction(action, currentState)) {
            return Result.Error(
                TetrisException(
                    message = "Invalid action $action for current state",
                    errorCode = "INVALID_ACTION"
                )
            )
        }
        
        return when (action) {
            is TetrisAction.Move -> movePiece(action.direction, currentState)
            is TetrisAction.Rotate -> rotatePiece(action.clockwise, currentState)
            is TetrisAction.Drop -> dropPiece(action.hard, currentState)
            is TetrisAction.Hold -> holdPiece(currentState)
            is TetrisAction.Pause -> pauseGame(currentState)
        }
    }
    
    override suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot move piece when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val currentPiece = gameState.currentPiece ?: throw TetrisException(
            message = "No current piece to move",
            errorCode = "NO_CURRENT_PIECE"
        )
        
        // 使用游戏逻辑处理器移动方块
        val result = gameLogicProcessor.movePiece(currentPiece, direction, gameState.board)
        
        if (result == null) {
            // 无法移动，返回原状态
            return@resultCatching gameState
        }
        
        // 更新游戏状态
        val updatedState = gameState.copy(
            currentPiece = result,
            lastDropTime = if (direction == Direction.DOWN) System.currentTimeMillis() else gameState.lastDropTime
        )
        
        _gameState.update { updatedState }
        
        return@resultCatching updatedState
    }
    
    override suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot rotate piece when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val currentPiece = gameState.currentPiece ?: throw TetrisException(
            message = "No current piece to rotate",
            errorCode = "NO_CURRENT_PIECE"
        )
        
        // 使用游戏逻辑处理器旋转方块
        val result = gameLogicProcessor.rotatePiece(currentPiece, clockwise, gameState.board)
        
        if (result == null) {
            // 无法旋转，返回原状态
            return@resultCatching gameState
        }
        
        // 更新游戏状态
        val updatedState = gameState.copy(currentPiece = result)
        _gameState.update { updatedState }
        
        return@resultCatching updatedState
    }
    
    override suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot drop piece when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val currentPiece = gameState.currentPiece ?: throw TetrisException(
            message = "No current piece to drop",
            errorCode = "NO_CURRENT_PIECE"
        )
        
        // 使用游戏逻辑处理器下落方块
        val result = if (hard) {
            gameLogicProcessor.hardDrop(currentPiece, gameState.board)
        } else {
            gameLogicProcessor.softDrop(currentPiece, gameState.board)
        }
        
        // 如果是硬降，则放置方块并生成新方块
        var updatedState = gameState
        
        if (hard) {
            // 计算下落距离得分
            val dropDistance = result.y - currentPiece.y
            val dropScore = dropDistance * 2 // 硬降每格2分
            
            // 放置方块
            val newBoard = gameState.board.placePiece(result)
            
            // 检查消行
            val (clearedBoard, linesCleared) = newBoard.clearLines()
            
            // 计算得分
            val lineScore = calculateLineScore(linesCleared, gameState.level, gameState.combo)
            val newScore = gameState.score + lineScore + dropScore
            
            // 更新消除行数和等级
            val newLines = gameState.lines + linesCleared
            val newLevel = calculateLevel(newLines)
            
            // 更新连击
            val newCombo = if (linesCleared > 0) gameState.combo + 1 else 0
            
            // 生成新方块
            val newCurrentPiece = gameState.nextPiece
            val newNextPiece = generateNextPiece()
            
            // 检查游戏结束
            if (newCurrentPiece != null && !collisionDetector.isValidPosition(newCurrentPiece, clearedBoard)) {
                updatedState = gameState.copy(status = TetrisStatus.GAME_OVER)
            } else {
                // 更新统计信息
                val newStatistics = statisticsCalculator.updateStatistics(
                    gameState.statistics,
                    linesCleared,
                    result.type,
                    false, // 假设不是T-Spin
                    newCombo
                )
                
                // 更新游戏状态
                updatedState = gameState.copy(
                    board = clearedBoard,
                    currentPiece = newCurrentPiece,
                    nextPiece = newNextPiece,
                    score = newScore,
                    level = newLevel,
                    lines = newLines,
                    combo = newCombo,
                    canHold = true,
                    lastDropTime = System.currentTimeMillis(),
                    dropInterval = calculateDropInterval(newLevel),
                    statistics = newStatistics
                )
            }
        } else {
            // 软降只更新当前方块位置
            updatedState = gameState.copy(
                currentPiece = result,
                lastDropTime = System.currentTimeMillis()
            )
        }
        
        _gameState.update { updatedState }
        
        // 保存游戏状态
        if (hard) {
            saveGameState(updatedState)
        }
        
        return@resultCatching updatedState
    }
    
    override suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot hold piece when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        if (!gameState.canHold) {
            // 本回合已经使用过Hold
            return@resultCatching gameState
        }
        
        val currentPiece = gameState.currentPiece ?: throw TetrisException(
            message = "No current piece to hold",
            errorCode = "NO_CURRENT_PIECE"
        )
        
        // 如果没有保留方块，则使用下一个方块
        val newCurrentPiece = if (gameState.holdPiece == null) {
            gameState.nextPiece
        } else {
            // 重置保留方块的位置和旋转
            gameState.holdPiece.copy(
                x = 4, // 居中位置
                y = 0, // 顶部
                rotation = 0 // 重置旋转
            )
        }
        
        // 生成新的下一个方块（如果需要）
        val newNextPiece = if (gameState.holdPiece == null) {
            generateNextPiece()
        } else {
            gameState.nextPiece
        }
        
        // 重置当前方块的位置和旋转，作为新的保留方块
        val newHoldPiece = currentPiece.copy(
            x = 0,
            y = 0,
            rotation = 0
        )
        
        // 更新游戏状态
        val updatedState = gameState.copy(
            currentPiece = newCurrentPiece,
            nextPiece = newNextPiece,
            holdPiece = newHoldPiece,
            canHold = false // 本回合已使用Hold
        )
        
        _gameState.update { updatedState }
        
        return@resultCatching updatedState
    }
    
    override suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot check line clear when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        // 检查消行
        val (clearedBoard, linesCleared) = gameState.board.clearLines()
        
        if (linesCleared == 0) {
            // 没有消行，返回原状态
            return@resultCatching gameState
        }
        
        // 计算得分
        val lineScore = calculateLineScore(linesCleared, gameState.level, gameState.combo)
        val newScore = gameState.score + lineScore
        
        // 更新消除行数和等级
        val newLines = gameState.lines + linesCleared
        val newLevel = calculateLevel(newLines)
        
        // 更新连击
        val newCombo = gameState.combo + 1
        
        // 更新统计信息
        val newStatistics = statisticsCalculator.updateStatistics(
            gameState.statistics,
            linesCleared,
            gameState.currentPiece?.type ?: TetrisPieceType.I,
            false, // 假设不是T-Spin
            newCombo
        )
        
        // 更新游戏状态
        val updatedState = gameState.copy(
            board = clearedBoard,
            score = newScore,
            level = newLevel,
            lines = newLines,
            combo = newCombo,
            dropInterval = calculateDropInterval(newLevel),
            statistics = newStatistics
        )
        
        _gameState.update { updatedState }
        
        return@resultCatching updatedState
    }
    
    override suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState> = resultCatching {
        if (gameState.status != TetrisStatus.PLAYING) {
            throw TetrisException(
                message = "Cannot auto drop when game is not playing",
                errorCode = "INVALID_GAME_STATE"
            )
        }
        
        val currentTime = System.currentTimeMillis()
        val timeSinceLastDrop = currentTime - gameState.lastDropTime
        
        if (timeSinceLastDrop < gameState.dropInterval) {
            // 还没到下落时间
            return@resultCatching gameState
        }
        
        // 自动下落一格
        return@resultCatching movePiece(Direction.DOWN, gameState).getOrThrow()
    }
    
    override fun generateNextPiece(): TetrisPiece {
        return pieceGenerator.generateNext()
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // 经典俄罗斯方块下落速度公式：(0.8 - ((level - 1) * 0.007))^(level - 1) * 1000
        val baseInterval = Math.pow(0.8 - ((level - 1) * 0.007), level - 1.toDouble()) * 1000
        return baseInterval.toLong().coerceAtLeast(50) // 最小50毫秒
    }
    
    override fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int {
        return TetrisScoring.calculateScore(linesCleared, level, false, combo)
    }
    
    override fun getGhostPiece(gameState: TetrisGameState): TetrisPiece? {
        val currentPiece = gameState.currentPiece ?: return null
        
        // 找到最低可能的位置
        var ghostPiece = currentPiece
        var testY = ghostPiece.y + 1
        
        while (collisionDetector.isValidPosition(ghostPiece.copy(y = testY), gameState.board)) {
            testY++
        }
        
        // 最后一个有效位置
        return ghostPiece.copy(y = testY - 1)
    }
    
    override fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean {
        return collisionDetector.isValidPosition(piece, gameState.board)
    }
    
    override fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的旋转
        for (rotation in 0 until 4) {
            val rotatedPiece = currentPiece.copy(rotation = rotation)
            if (collisionDetector.isValidPosition(rotatedPiece, gameState.board)) {
                result.add(rotatedPiece)
            }
        }
        
        return result
    }
    
    override fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的水平位置
        for (x in -2..gameState.board.width + 2) {
            val movedPiece = currentPiece.copy(x = x)
            if (collisionDetector.isValidPosition(movedPiece, gameState.board)) {
                result.add(movedPiece)
            }
        }
        
        return result
    }
    
    override fun checkTSpin(gameState: TetrisGameState): Boolean {
        val currentPiece = gameState.currentPiece ?: return false
        
        // 只有T方块才能形成T-Spin
        if (currentPiece.type != TetrisPieceType.T) {
            return false
        }
        
        // 使用SuperRotationSystem检测T-Spin
        val lastAction = TetrisAction.Rotate(true, "player") // 假设最后一个动作是旋转
        val tSpinType = SuperRotationSystem.detectTSpin(currentPiece, gameState.board, lastAction)
        
        return tSpinType != TSpinType.NONE
    }
    
    override fun calculateLevel(lines: Int): Int {
        // 每10行升一级
        return (lines / 10) + 1
    }
    
    override fun isValidAction(action: TetrisAction, currentState: TetrisGameState): Boolean {
        return when (action) {
            is TetrisAction.Move -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Rotate -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Drop -> currentState.status == TetrisStatus.PLAYING
            is TetrisAction.Hold -> currentState.status == TetrisStatus.PLAYING && currentState.canHold
            is TetrisAction.Pause -> currentState.status == TetrisStatus.PLAYING
        }
    }
    
    override fun calculateScore(gameState: TetrisGameState): Int {
        return gameState.score
    }
    
    override fun isGameOver(gameState: TetrisGameState): Boolean {
        return gameState.status == TetrisStatus.GAME_OVER
    }
    
    override fun getGameStatistics(gameState: TetrisGameState): Map<String, Any> {
        return mapOf(
            "score" to gameState.score,
            "level" to gameState.level,
            "lines" to gameState.lines,
            "pieces" to gameState.statistics.piecesPlaced,
            "tetrises" to gameState.statistics.tetrises,
            "tSpins" to gameState.statistics.tSpins,
            "maxCombo" to gameState.statistics.maxCombo,
            "efficiency" to gameState.statistics.efficiency
        )
    }
    
    override suspend fun saveGameState(gameState: TetrisGameState): Result<Unit> = resultCatching {
        gameStateRepository.saveGameState(gameState)
    }
    
    override suspend fun loadGameState(gameId: String): Result<TetrisGameState> = resultCatching {
        val gameState = gameStateRepository.getGameState(gameId)
        _gameState.update { gameState }
        return@resultCatching gameState
    }
}