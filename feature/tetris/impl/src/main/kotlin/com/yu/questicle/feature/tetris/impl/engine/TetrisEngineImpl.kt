package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.common.exception.ErrorSeverity
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.model.tetris.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tetris 游戏引擎简化实现
 *
 * 提供基本的游戏功能，确保编译通过
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Singleton
class TetrisEngineImpl @Inject constructor() : TetrisEngine {

    // 游戏状态
    private val _gameState = MutableStateFlow(TetrisGameState.initial())

    // 游戏组件
    private val pieceGenerator = TetrisPieceGenerator()
    private val collisionDetector = TetrisCollisionDetector()

    // 实现 GameEngine 接口方法
    override suspend fun initializeGame(playerId: String): Result<TetrisGameState> {
        return try {
            val currentPiece = pieceGenerator.generateNext()
            val nextPiece = pieceGenerator.generateNext()

            val initialState = TetrisGameState.initial().copy(
                currentPiece = currentPiece,
                nextPiece = nextPiece
            )

            _gameState.value = initialState
            Result.Success(initialState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to initialize game", "TETRIS_INIT_ERROR", ErrorSeverity.HIGH, e))
        }
    }

    override suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val startedState = gameState.copy(status = TetrisStatus.PLAYING)
            _gameState.value = startedState
            Result.Success(startedState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to start game", "TETRIS_START_ERROR", ErrorSeverity.HIGH, e))
        }
    }

    override suspend fun processAction(action: com.yu.questicle.core.domain.model.TetrisAction, currentState: TetrisGameState): Result<TetrisGameState> {
        return try {
            when (action) {
                is com.yu.questicle.core.domain.model.TetrisAction.Move -> {
                    movePiece(action.direction, currentState)
                }
                is com.yu.questicle.core.domain.model.TetrisAction.Rotate -> {
                    rotatePiece(action.clockwise, currentState)
                }
                is com.yu.questicle.core.domain.model.TetrisAction.Drop -> {
                    dropPiece(action.hard, currentState)
                }
                is com.yu.questicle.core.domain.model.TetrisAction.Hold -> {
                    holdPiece(currentState)
                }
                is com.yu.questicle.core.domain.model.TetrisAction.Pause -> {
                    pauseGame(currentState)
                }
            }
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to process action", "TETRIS_ACTION_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    override suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val pausedState = gameState.copy(status = TetrisStatus.PAUSED)
            _gameState.value = pausedState
            Result.Success(pausedState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to pause game", "TETRIS_PAUSE_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    override suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val resumedState = gameState.copy(status = TetrisStatus.PLAYING)
            _gameState.value = resumedState
            Result.Success(resumedState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to resume game", "TETRIS_RESUME_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    override suspend fun endGame(gameState: TetrisGameState): Result<Game> {
        return try {
            val endedState = gameState.copy(status = TetrisStatus.GAME_OVER)
            _gameState.value = endedState
            val game = Game(
                id = gameState.id,
                type = GameType.TETRIS,
                score = gameState.score,
                level = gameState.level,
                duration = 0L,
                status = GameStatus.FAILED,
                playerId = ""
            )
            Result.Success(game)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to end game", "TETRIS_END_ERROR", ErrorSeverity.HIGH, e))
        }
    }

    override fun observeGameState(): Flow<TetrisGameState> = _gameState.asStateFlow()

    override fun isValidAction(action: com.yu.questicle.core.domain.model.TetrisAction, currentState: TetrisGameState): Boolean = true

    override fun calculateScore(gameState: TetrisGameState): Int = gameState.score

    override fun isGameOver(gameState: TetrisGameState): Boolean = gameState.status == TetrisStatus.GAME_OVER

    override fun getGameStatistics(gameState: TetrisGameState): Map<String, Any> {
        return mapOf(
            "score" to gameState.score,
            "level" to gameState.level,
            "lines" to gameState.lines
        )
    }

    override suspend fun saveGameState(gameState: TetrisGameState): Result<Unit> {
        return try {
            // 简化实现：什么都不做
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to save game state", "TETRIS_SAVE_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    override suspend fun loadGameState(gameId: String): Result<TetrisGameState> {
        return try {
            // 简化实现：返回初始状态
            Result.Success(TetrisGameState.initial())
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to load game state", "TETRIS_LOAD_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    // 实现 TetrisEngine 特有方法
    override fun generateNextPiece(): TetrisPiece {
        return TetrisPiece(TetrisPieceType.I, 4, 0, 0)
    }

    override suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)

            val (dx, dy) = when (direction) {
                Direction.LEFT -> -1 to 0
                Direction.RIGHT -> 1 to 0
                Direction.DOWN -> 0 to 1
            }

            if (collisionDetector.canMove(currentPiece, dx, dy, gameState.board)) {
                val movedPiece = currentPiece.move(dx, dy)
                val newState = gameState.copy(currentPiece = movedPiece)
                _gameState.value = newState
                Result.Success(newState)
            } else if (direction == Direction.DOWN) {
                // 向下移动失败时，锁定方块
                lockPiece(gameState)
            } else {
                Result.Success(gameState)
            }
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to move piece", "TETRIS_MOVE_ERROR", ErrorSeverity.LOW, e))
        }
    }

    override suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)

            // 目前只支持顺时针旋转，逆时针可以通过旋转3次实现
            val rotations = if (clockwise) 1 else 3
            var rotatedPiece = currentPiece

            repeat(rotations) {
                val (canRotate, newPiece) = collisionDetector.canRotateWithKick(rotatedPiece, gameState.board)
                if (canRotate && newPiece != null) {
                    rotatedPiece = newPiece
                } else {
                    return Result.Success(gameState) // 无法旋转，返回原状态
                }
            }

            val newState = gameState.copy(currentPiece = rotatedPiece)
            _gameState.value = newState
            Result.Success(newState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to rotate piece", "TETRIS_ROTATE_ERROR", ErrorSeverity.LOW, e))
        }
    }

    override suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)

            if (hard) {
                // 硬降：直接降到底部并锁定
                val dropPiece = collisionDetector.calculateDropPosition(currentPiece, gameState.board)
                val newState = gameState.copy(currentPiece = dropPiece)
                lockPiece(newState)
            } else {
                // 软降：向下移动一格
                movePiece(Direction.DOWN, gameState)
            }
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to drop piece", "TETRIS_DROP_ERROR", ErrorSeverity.LOW, e))
        }
    }

    override suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)

            // 检查是否可以Hold
            if (!gameState.canHold) {
                return Result.Success(gameState)
            }

            val newCurrentPiece = if (gameState.holdPiece != null) {
                // 交换当前方块和Hold方块
                gameState.holdPiece
            } else {
                // 第一次Hold，使用下一个方块
                gameState.nextPiece
            }

            val newNextPiece = if (gameState.holdPiece == null) {
                // 第一次Hold，生成新的下一个方块
                pieceGenerator.generateNext()
            } else {
                // 交换时保持下一个方块不变
                gameState.nextPiece
            }

            val newState = gameState.copy(
                currentPiece = newCurrentPiece,
                nextPiece = newNextPiece,
                holdPiece = currentPiece,
                canHold = false // 本轮不能再Hold
            )

            _gameState.value = newState
            Result.Success(newState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to hold piece", "TETRIS_HOLD_ERROR", ErrorSeverity.LOW, e))
        }
    }

    override suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            val newStatus = when (gameState.status) {
                TetrisStatus.PLAYING -> TetrisStatus.PAUSED
                TetrisStatus.PAUSED -> TetrisStatus.PLAYING
                else -> gameState.status
            }

            val newState = gameState.copy(status = newStatus)
            _gameState.value = newState
            Result.Success(newState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to check line clear", "TETRIS_CLEAR_ERROR", ErrorSeverity.MEDIUM, e))
        }
    }

    override fun calculateDropInterval(level: Int): Long = 1000L / level

    override fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int = linesCleared * 100 * level

    override fun getGhostPiece(gameState: TetrisGameState): TetrisPiece? = gameState.currentPiece

    override fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean = true

    override fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece> = emptyList()

    override fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece> = emptyList()

    override suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState> {
        return try {
            // 简化实现：返回当前状态
            Result.Success(gameState)
        } catch (e: Exception) {
            Result.Error(BusinessException("Failed to auto drop", "TETRIS_AUTO_DROP_ERROR", ErrorSeverity.LOW, e))
        }
    }

    override fun checkTSpin(gameState: TetrisGameState): Boolean = false

    override fun calculateLevel(lines: Int): Int = (lines / 10) + 1

    /**
     * 锁定当前方块并生成新方块
     */
    private suspend fun lockPiece(gameState: TetrisGameState): Result<TetrisGameState> {
        val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)

        // 将方块放置到游戏板上
        val newBoard = gameState.board.placePiece(currentPiece)

        // 检查行消除
        val (clearedBoard, linesCleared) = newBoard.clearLines()

        // 计算分数
        val lineScore = calculateLineScore(linesCleared, gameState.level, gameState.combo)
        val newScore = gameState.score + lineScore
        val newLines = gameState.lines + linesCleared
        val newLevel = calculateLevel(newLines)
        val newCombo = if (linesCleared > 0) gameState.combo + 1 else 0

        // 生成新方块
        val newCurrentPiece = gameState.nextPiece
        val newNextPiece = pieceGenerator.generateNext()

        // 检查游戏是否结束
        val isGameOver = newCurrentPiece?.let {
            collisionDetector.isGameOver(it, clearedBoard)
        } ?: true

        val newStatus = if (isGameOver) TetrisStatus.GAME_OVER else gameState.status

        val newState = gameState.copy(
            board = clearedBoard,
            currentPiece = newCurrentPiece,
            nextPiece = newNextPiece,
            score = newScore,
            level = newLevel,
            lines = newLines,
            combo = newCombo,
            status = newStatus,
            canHold = true // 重置hold状态
        )

        _gameState.value = newState
        return Result.Success(newState)
    }
}
