package com.yu.questicle.feature.tetris.impl.model

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisBoard

/**
 * 游戏移动结果
 */
sealed class GameMoveResult {
    /**
     * 移动成功
     */
    data class Success(
        val newPiece: TetrisPiece,
        val newBoard: TetrisBoard
    ) : GameMoveResult()
    
    /**
     * 移动失败 - 碰撞
     */
    data class Collision(
        val reason: String
    ) : GameMoveResult()
    
    /**
     * 移动失败 - 方块锁定
     */
    data class Locked(
        val finalPiece: TetrisPiece,
        val finalBoard: TetrisBoard
    ) : GameMoveResult()
}

/**
 * 游戏旋转结果
 */
sealed class GameRotationResult {
    /**
     * 旋转成功
     */
    data class Success(
        val rotatedPiece: TetrisPiece
    ) : GameRotationResult()
    
    /**
     * 旋转成功 - 使用了踢墙
     */
    data class SuccessWithKick(
        val kickedPiece: TetrisPiece,
        val kickOffset: Pair<Int, Int>
    ) : GameRotationResult()
    
    /**
     * 旋转失败
     */
    data class Failed(
        val reason: String
    ) : GameRotationResult()
}

/**
 * 行消除结果
 */
sealed class LineClearResult {
    /**
     * 没有行被消除
     */
    object None : LineClearResult()
    
    /**
     * 有行被消除
     */
    data class Cleared(
        val clearedBoard: TetrisBoard,
        val linesCleared: Int,
        val scoreIncrease: Int,
        val isTSpin: Boolean = false,
        val isPerfectClear: Boolean = false,
        val isBackToBack: Boolean = false
    ) : LineClearResult()
}
