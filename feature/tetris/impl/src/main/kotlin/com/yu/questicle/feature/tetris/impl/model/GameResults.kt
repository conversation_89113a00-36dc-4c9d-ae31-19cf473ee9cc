package com.yu.questicle.feature.tetris.impl.model

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisBoard

/**
 * 游戏移动结果
 */
sealed class GameMoveResult {
    /**
     * 移动成功
     */
    data class Success(
        val newPiece: TetrisPiece
    ) : GameMoveResult()

    /**
     * 移动被阻挡
     */
    data class Blocked(
        val originalPiece: TetrisPiece
    ) : GameMoveResult()

    /**
     * 方块已着陆
     */
    data class Landed(
        val landedPiece: TetrisPiece
    ) : GameMoveResult()
}

/**
 * 游戏旋转结果
 */
sealed class GameRotationResult {
    /**
     * 旋转成功
     */
    data class Success(
        val rotatedPiece: TetrisPiece
    ) : GameRotationResult()

    /**
     * 旋转成功 - 使用了踢墙
     */
    data class WallKick(
        val kickedPiece: TetrisPiece
    ) : GameRotationResult()

    /**
     * 旋转被阻挡
     */
    object Blocked : GameRotationResult()
}

/**
 * 行消除结果
 */
sealed class LineClearResult {
    /**
     * 没有行被消除
     */
    object None : LineClearResult()

    /**
     * 有行被消除
     */
    data class Cleared(
        val clearedBoard: TetrisBoard,
        val linesCleared: Int,
        val scoreIncrease: Int,
        val isTSpin: Boolean = false,
        val isPerfectClear: Boolean = false,
        val isBackToBack: Boolean = false
    ) : LineClearResult()

    /**
     * 成功处理（兼容性）
     */
    data class Success(
        val clearedBoard: TetrisBoard,
        val linesCleared: Int,
        val scoreIncrease: Int,
        val isTSpin: Boolean = false,
        val isPerfectClear: Boolean = false,
        val isBackToBack: Boolean = false
    ) : LineClearResult()

    /**
     * 无行消除（兼容性）
     */
    object NoLines : LineClearResult()
}
