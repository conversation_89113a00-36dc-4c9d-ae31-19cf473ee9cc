package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import kotlin.random.Random

/**
 * Tetris 方块生成器
 * 
 * 实现标准的7-bag随机生成算法，确保方块分布均匀
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
class TetrisPieceGenerator {
    
    private val bag = mutableListOf<TetrisPieceType>()
    private val random = Random.Default
    
    /**
     * 生成下一个方块
     * 使用7-bag算法确保每7个方块包含所有类型
     */
    fun generateNext(): TetrisPiece {
        if (bag.isEmpty()) {
            refillBag()
        }
        
        val pieceType = bag.removeAt(random.nextInt(bag.size))
        return createPiece(pieceType)
    }
    
    /**
     * 预览下一个方块（不消耗）
     */
    fun peekNext(): TetrisPiece {
        if (bag.isEmpty()) {
            refillBag()
        }
        
        val pieceType = bag[random.nextInt(bag.size)]
        return createPiece(pieceType)
    }
    
    /**
     * 重新填充方块袋
     */
    private fun refillBag() {
        bag.clear()
        bag.addAll(TetrisPieceType.values())
    }
    
    /**
     * 创建方块实例
     * 方块初始位置在顶部中央
     */
    private fun createPiece(type: TetrisPieceType): TetrisPiece {
        val startX = when (type) {
            TetrisPieceType.I -> 3  // I型方块需要特殊位置
            TetrisPieceType.O -> 4  // O型方块居中
            else -> 3               // 其他方块标准位置
        }
        
        return TetrisPiece(
            type = type,
            x = startX,
            y = 0,
            rotation = 0
        )
    }
    
    /**
     * 生成指定类型的方块（用于测试）
     */
    fun generateSpecific(type: TetrisPieceType): TetrisPiece {
        return createPiece(type)
    }
}
