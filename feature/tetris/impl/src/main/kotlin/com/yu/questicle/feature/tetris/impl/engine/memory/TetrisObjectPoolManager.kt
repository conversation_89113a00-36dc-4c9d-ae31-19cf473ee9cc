package com.yu.questicle.feature.tetris.impl.engine.memory

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import com.yu.questicle.feature.tetris.impl.engine.components.GameMoveResult
import com.yu.questicle.feature.tetris.impl.engine.components.GameRotationResult
import com.yu.questicle.feature.tetris.impl.engine.components.LineClearResult
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages object pools for frequently created objects in the Tetris engine.
 * 
 * This class centralizes the creation and management of object pools for various
 * game objects, ensuring efficient memory usage and reduced garbage collection.
 */
@Singleton
class TetrisObjectPoolManager @Inject constructor() {

    // Pool sizes based on expected usage patterns
    companion object {
        private const val PIECE_POOL_SIZE = 100
        private const val MOVE_RESULT_POOL_SIZE = 50
        private const val ROTATION_RESULT_POOL_SIZE = 50
        private const val LINE_CLEAR_RESULT_POOL_SIZE = 20
        private const val POSITION_POOL_SIZE = 200
    }

    // Object pools for frequently created objects
    private val piecePool = ObjectPool<TetrisPiece>(
        maxSize = PIECE_POOL_SIZE,
        factory = { TetrisPiece(TetrisPieceType.I, 0, 0, 0) },
        reset = { piece -> 
            // Reset piece to default state
            piece.apply {
                // Note: Since TetrisPiece is likely immutable, this reset function
                // might not be applicable. In a real implementation, we would need
                // to make TetrisPiece mutable or use a mutable wrapper.
            }
        }
    )

    private val moveResultSuccessPool = ObjectPool<GameMoveResult.Success>(
        maxSize = MOVE_RESULT_POOL_SIZE,
        factory = { GameMoveResult.Success(TetrisPiece(TetrisPieceType.I, 0, 0, 0)) }
    )

    private val moveResultBlockedPool = ObjectPool<GameMoveResult.Blocked>(
        maxSize = MOVE_RESULT_POOL_SIZE,
        factory = { GameMoveResult.Blocked(TetrisPiece(TetrisPieceType.I, 0, 0, 0)) }
    )

    private val moveResultLandedPool = ObjectPool<GameMoveResult.Landed>(
        maxSize = MOVE_RESULT_POOL_SIZE,
        factory = { GameMoveResult.Landed(TetrisPiece(TetrisPieceType.I, 0, 0, 0)) }
    )

    private val rotationResultSuccessPool = ObjectPool<GameRotationResult.Success>(
        maxSize = ROTATION_RESULT_POOL_SIZE,
        factory = { GameRotationResult.Success(TetrisPiece(TetrisPieceType.I, 0, 0, 0)) }
    )

    private val rotationResultWallKickPool = ObjectPool<GameRotationResult.WallKick>(
        maxSize = ROTATION_RESULT_POOL_SIZE,
        factory = { GameRotationResult.WallKick(TetrisPiece(TetrisPieceType.I, 0, 0, 0)) }
    )

    private val rotationResultBlockedPool = ObjectPool<GameRotationResult.Blocked>(
        maxSize = ROTATION_RESULT_POOL_SIZE,
        factory = { GameRotationResult.Blocked() }
    )

    // Singleton instance for the no-lines result to avoid creating any instances
    private val lineClearResultNoLines = LineClearResult.NoLines

    /**
     * Get a TetrisPiece from the pool
     */
    fun acquirePiece(type: TetrisPieceType, x: Int, y: Int, rotation: Int): TetrisPiece {
        // Since TetrisPiece is likely immutable, we create a new instance
        // In a real implementation with mutable pieces, we would use the pool
        return TetrisPiece(type, x, y, rotation)
    }

    /**
     * Return a TetrisPiece to the pool
     */
    fun releasePiece(piece: TetrisPiece) {
        // In a real implementation with mutable pieces, we would return to the pool
        // piecePool.release(piece)
    }

    /**
     * Get a successful move result from the pool
     */
    fun acquireMoveResultSuccess(newPiece: TetrisPiece): GameMoveResult.Success {
        return GameMoveResult.Success(newPiece)
    }

    /**
     * Return a successful move result to the pool
     */
    fun releaseMoveResultSuccess(result: GameMoveResult.Success) {
        // In a real implementation, we would return to the pool
        // moveResultSuccessPool.release(result)
    }

    /**
     * Get a blocked move result from the pool
     */
    fun acquireMoveResultBlocked(originalPiece: TetrisPiece): GameMoveResult.Blocked {
        return GameMoveResult.Blocked(originalPiece)
    }

    /**
     * Get a landed move result from the pool
     */
    fun acquireMoveResultLanded(landedPiece: TetrisPiece): GameMoveResult.Landed {
        return GameMoveResult.Landed(landedPiece)
    }

    /**
     * Get a successful rotation result from the pool
     */
    fun acquireRotationResultSuccess(rotatedPiece: TetrisPiece): GameRotationResult.Success {
        return GameRotationResult.Success(rotatedPiece)
    }

    /**
     * Get a wall kick rotation result from the pool
     */
    fun acquireRotationResultWallKick(kickedPiece: TetrisPiece): GameRotationResult.WallKick {
        return GameRotationResult.WallKick(kickedPiece)
    }

    /**
     * Get a blocked rotation result from the pool
     */
    fun acquireRotationResultBlocked(): GameRotationResult.Blocked {
        return GameRotationResult.Blocked()
    }

    /**
     * Get the no-lines line clear result (singleton)
     */
    fun getLineClearResultNoLines(): LineClearResult.NoLines {
        return lineClearResultNoLines
    }

    /**
     * Get memory usage statistics for all pools
     */
    fun getMemoryStatistics(): Map<String, Any> {
        return mapOf(
            "piecePool" to mapOf(
                "created" to piecePool.created,
                "reused" to piecePool.reused,
                "returned" to piecePool.returned,
                "currentSize" to piecePool.currentSize,
                "efficiency" to piecePool.getEfficiency()
            ),
            "moveResultSuccessPool" to mapOf(
                "created" to moveResultSuccessPool.created,
                "reused" to moveResultSuccessPool.reused,
                "returned" to moveResultSuccessPool.returned,
                "currentSize" to moveResultSuccessPool.currentSize,
                "efficiency" to moveResultSuccessPool.getEfficiency()
            ),
            "moveResultBlockedPool" to mapOf(
                "created" to moveResultBlockedPool.created,
                "reused" to moveResultBlockedPool.reused,
                "returned" to moveResultBlockedPool.returned,
                "currentSize" to moveResultBlockedPool.currentSize,
                "efficiency" to moveResultBlockedPool.getEfficiency()
            ),
            "rotationResultSuccessPool" to mapOf(
                "created" to rotationResultSuccessPool.created,
                "reused" to rotationResultSuccessPool.reused,
                "returned" to rotationResultSuccessPool.returned,
                "currentSize" to rotationResultSuccessPool.currentSize,
                "efficiency" to rotationResultSuccessPool.getEfficiency()
            )
        )
    }

    /**
     * Clear all object pools
     */
    fun clearAllPools() {
        piecePool.clear()
        moveResultSuccessPool.clear()
        moveResultBlockedPool.clear()
        moveResultLandedPool.clear()
        rotationResultSuccessPool.clear()
        rotationResultWallKickPool.clear()
        rotationResultBlockedPool.clear()
    }
}