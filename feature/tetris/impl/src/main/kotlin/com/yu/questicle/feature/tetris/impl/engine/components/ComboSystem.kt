package com.yu.questicle.feature.tetris.impl.engine.components

/**
 * Interface for combo tracking and calculation systems.
 * Handles combo tracking, chain detection, and related bonuses.
 */
interface ComboSystem {
    /**
     * Update combo counter based on line clear
     */
    fun updateCombo(linesCleared: Int, currentCombo: Int): Int
    
    /**
     * Calculate combo multiplier for scoring
     */
    fun calculateComboMultiplier(combo: Int): Float
    
    /**
     * Check if the current combo is a special combo
     */
    fun isSpecialCombo(combo: Int, linesCleared: Int): Boolean
    
    /**
     * Calculate bonus points for special combos
     */
    fun calculateSpecialComboBonus(combo: Int, linesCleared: Int, level: Int): Int
    
    /**
     * Get name of the combo system
     */
    fun getName(): String
}

/**
 * Standard combo system used in modern Tetris games.
 * Tracks consecutive line clears and provides multipliers.
 */
class StandardComboSystem : ComboSystem {
    
    override fun updateCombo(linesCleared: Int, currentCombo: Int): Int {
        return if (linesCleared > 0) {
            // Increment combo if lines were cleared
            currentCombo + 1
        } else {
            // Reset combo if no lines were cleared
            0
        }
    }
    
    override fun calculateComboMultiplier(combo: Int): Float {
        return when {
            combo <= 1 -> 1.0f
            combo == 2 -> 1.1f
            combo == 3 -> 1.2f
            combo == 4 -> 1.3f
            combo == 5 -> 1.4f
            combo >= 6 -> 1.5f
            else -> 1.0f
        }
    }
    
    override fun isSpecialCombo(combo: Int, linesCleared: Int): Boolean {
        // Special combos: 
        // - Triple combo (3+ consecutive line clears)
        // - Back-to-back Tetris (consecutive 4-line clears)
        return combo >= 3 || (combo >= 2 && linesCleared == 4)
    }
    
    override fun calculateSpecialComboBonus(combo: Int, linesCleared: Int, level: Int): Int {
        return when {
            combo >= 3 && linesCleared == 4 -> 500 * level  // Back-to-back Tetris
            combo >= 5 -> 300 * level                       // 5+ combo
            combo >= 3 -> 100 * level                       // 3+ combo
            else -> 0
        }
    }
    
    override fun getName(): String = "Standard"
}

/**
 * Advanced combo system with more complex combo tracking and bonuses.
 * Used in competitive Tetris games.
 */
class AdvancedComboSystem : ComboSystem {
    
    override fun updateCombo(linesCleared: Int, currentCombo: Int): Int {
        return if (linesCleared > 0) {
            // Increment combo if lines were cleared
            currentCombo + 1
        } else {
            // Reset combo if no lines were cleared
            0
        }
    }
    
    override fun calculateComboMultiplier(combo: Int): Float {
        // More aggressive scaling for competitive play
        return when {
            combo <= 1 -> 1.0f
            combo == 2 -> 1.2f
            combo == 3 -> 1.4f
            combo == 4 -> 1.6f
            combo == 5 -> 1.8f
            combo >= 6 -> 2.0f + (combo - 6) * 0.1f  // Continues to scale beyond 6
            else -> 1.0f
        }
    }
    
    override fun isSpecialCombo(combo: Int, linesCleared: Int): Boolean {
        // More special combo conditions
        return combo >= 3 || 
               (combo >= 2 && linesCleared == 4) ||
               (combo >= 2 && linesCleared == 3)
    }
    
    override fun calculateSpecialComboBonus(combo: Int, linesCleared: Int, level: Int): Int {
        return when {
            combo >= 3 && linesCleared == 4 -> 800 * level  // Back-to-back Tetris
            combo >= 2 && linesCleared == 3 -> 400 * level  // Back-to-back Triple
            combo >= 7 -> 1000 * level                      // 7+ combo (very rare)
            combo >= 5 -> 500 * level                       // 5+ combo
            combo >= 3 -> 200 * level                       // 3+ combo
            else -> 0
        }
    }
    
    override fun getName(): String = "Advanced"
}

/**
 * Chain system for tracking consecutive line clears with specific patterns.
 * Used for detecting and rewarding advanced techniques.
 */
class ChainSystem {
    
    // Track the history of line clears for chain detection
    private val clearHistory = mutableListOf<LineClearEvent>()
    
    // Maximum history size to prevent memory leaks
    private val MAX_HISTORY_SIZE = 10
    
    /**
     * Record a line clear event
     */
    fun recordLineClear(linesCleared: Int, pieceType: String, isTSpin: Boolean) {
        clearHistory.add(LineClearEvent(linesCleared, pieceType, isTSpin))
        
        // Trim history if needed
        if (clearHistory.size > MAX_HISTORY_SIZE) {
            clearHistory.removeAt(0)
        }
    }
    
    /**
     * Check if the current sequence contains a specific chain pattern
     */
    fun hasChainPattern(pattern: ChainPattern): Boolean {
        if (clearHistory.size < pattern.requiredLength) {
            return false
        }
        
        // Check the most recent events against the pattern
        val recentEvents = clearHistory.takeLast(pattern.requiredLength)
        
        return pattern.matches(recentEvents)
    }
    
    /**
     * Calculate bonus for detected chains
     */
    fun calculateChainBonus(level: Int): Int {
        var bonus = 0
        
        // Check for each pattern and add bonuses
        ChainPattern.values().forEach { pattern ->
            if (hasChainPattern(pattern)) {
                bonus += pattern.getBonus(level)
            }
        }
        
        return bonus
    }
    
    /**
     * Clear the chain history
     */
    fun clearHistory() {
        clearHistory.clear()
    }
    
    /**
     * Get the current chain length
     */
    fun getCurrentChainLength(): Int {
        return clearHistory.size
    }
    
    /**
     * Data class for line clear events
     */
    data class LineClearEvent(
        val linesCleared: Int,
        val pieceType: String,
        val isTSpin: Boolean
    )
    
    /**
     * Enum for different chain patterns
     */
    enum class ChainPattern(val requiredLength: Int) {
        TETRIS_BACK_TO_BACK(2) {
            override fun matches(events: List<LineClearEvent>): Boolean {
                return events.size >= 2 &&
                       events.takeLast(2).all { it.linesCleared == 4 }
            }
            
            override fun getBonus(level: Int): Int = 1000 * level
        },
        
        T_SPIN_BACK_TO_BACK(2) {
            override fun matches(events: List<LineClearEvent>): Boolean {
                return events.size >= 2 &&
                       events.takeLast(2).all { it.isTSpin }
            }
            
            override fun getBonus(level: Int): Int = 800 * level
        },
        
        PERFECT_CLEAR_SEQUENCE(3) {
            override fun matches(events: List<LineClearEvent>): Boolean {
                // This would require additional data in LineClearEvent
                // to track perfect clears
                return false
            }
            
            override fun getBonus(level: Int): Int = 2000 * level
        },
        
        ALTERNATING_PIECES(4) {
            override fun matches(events: List<LineClearEvent>): Boolean {
                if (events.size < 4) return false
                
                val last4 = events.takeLast(4)
                return last4[0].pieceType != last4[1].pieceType &&
                       last4[1].pieceType != last4[2].pieceType &&
                       last4[2].pieceType != last4[3].pieceType
            }
            
            override fun getBonus(level: Int): Int = 500 * level
        };
        
        /**
         * Check if the pattern matches the given events
         */
        abstract fun matches(events: List<LineClearEvent>): Boolean
        
        /**
         * Get the bonus points for this pattern
         */
        abstract fun getBonus(level: Int): Int
    }
}