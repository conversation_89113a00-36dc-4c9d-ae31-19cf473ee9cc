package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.model.tetris.TetrisStatus
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Specialized component for validating game state integrity.
 * Ensures the game state is consistent and valid.
 */
@Singleton
class GameStateValidator @Inject constructor(
    private val collisionDetector: TetrisCollisionDetector,
    private val statisticsCalculator: TetrisStatisticsCalculator,
    private val performanceManager: TetrisPerformanceManager
) {
    /**
     * Validate the game state for consistency and correctness
     */
    fun validateGameState(gameState: TetrisGameState): ValidationResult {
        val startTime = System.nanoTime()
        
        try {
            val errors = mutableListOf<String>()
            
            // Check if current piece is valid
            gameState.currentPiece?.let { piece ->
                if (!collisionDetector.isValidPosition(piece, gameState.board)) {
                    errors.add("Current piece position is invalid")
                }
            } ?: errors.add("Current piece is null")
            
            // Check if next piece is valid
            if (gameState.nextPiece == null) {
                errors.add("Next piece is null")
            }
            
            // Check if board is valid
            if (gameState.board.width < 4 || gameState.board.height < 4) {
                errors.add("Board dimensions are invalid")
            }
            
            // Check if score is valid
            if (gameState.score < 0) {
                errors.add("Score cannot be negative")
            }
            
            // Check if level is valid
            if (gameState.level < 1) {
                errors.add("Level must be at least 1")
            }
            
            // Check if lines cleared is valid
            if (gameState.lines < 0) {
                errors.add("Lines cleared cannot be negative")
            }
            
            // Check if level matches lines cleared
            val expectedLevel = statisticsCalculator.calculateLevel(gameState.lines)
            if (gameState.level != expectedLevel) {
                errors.add("Level does not match lines cleared (expected: $expectedLevel, actual: ${gameState.level})")
            }
            
            // Check if game status is valid
            if (gameState.status !in TetrisStatus.values()) {
                errors.add("Invalid game status: ${gameState.status}")
            }
            
            // Check if statistics are consistent
            if (gameState.statistics.linesCleared != gameState.lines) {
                errors.add("Statistics line count (${gameState.statistics.linesCleared}) does not match game state line count (${gameState.lines})")
            }
            
            // Check if hold piece is valid when present
            gameState.holdPiece?.let { piece ->
                if (piece.type !in com.yu.questicle.core.domain.model.tetris.TetrisPieceType.values()) {
                    errors.add("Hold piece has invalid type: ${piece.type}")
                }
            }
            
            return if (errors.isEmpty()) {
                ValidationResult.Valid
            } else {
                ValidationResult.Invalid(errors)
            }
        } finally {
            performanceManager.trackOperation("validateGameState", System.nanoTime() - startTime)
        }
    }
    
    /**
     * Check if the game is over based on the current state
     */
    fun isGameOver(gameState: TetrisGameState): Boolean {
        // Game is over if status is GAME_OVER
        if (gameState.status == TetrisStatus.GAME_OVER) {
            return true
        }
        
        // Game is over if current piece overlaps with existing pieces at spawn
        return gameState.currentPiece?.let { piece ->
            piece.y == 0 && !collisionDetector.isValidPosition(piece, gameState.board)
        } ?: false
    }
    
    /**
     * Create a corrected game state by fixing any inconsistencies
     */
    fun createCorrectedGameState(gameState: TetrisGameState): TetrisGameState {
        val validationResult = validateGameState(gameState)
        
        if (validationResult is ValidationResult.Valid) {
            return gameState
        }
        
        // Create a corrected copy of the game state
        var correctedState = gameState
        
        // Fix level based on lines cleared
        val correctLevel = statisticsCalculator.calculateLevel(gameState.lines)
        if (gameState.level != correctLevel) {
            correctedState = correctedState.copy(level = correctLevel)
        }
        
        // Fix statistics line count
        if (gameState.statistics.linesCleared != gameState.lines) {
            correctedState = correctedState.copy(
                statistics = gameState.statistics.copy(linesCleared = gameState.lines)
            )
        }
        
        // Fix current piece if invalid
        gameState.currentPiece?.let { piece ->
            if (!collisionDetector.isValidPosition(piece, gameState.board)) {
                // Try to move the piece up until it's valid
                var validPiece = piece
                for (y in 0 until 4) {
                    val adjustedPiece = piece.copy(y = piece.y - y)
                    if (collisionDetector.isValidPosition(adjustedPiece, gameState.board)) {
                        validPiece = adjustedPiece
                        break
                    }
                }
                
                // If still invalid, game is over
                if (!collisionDetector.isValidPosition(validPiece, gameState.board)) {
                    correctedState = correctedState.copy(status = TetrisStatus.GAME_OVER)
                } else {
                    correctedState = correctedState.copy(currentPiece = validPiece)
                }
            }
        }
        
        return correctedState
    }
}