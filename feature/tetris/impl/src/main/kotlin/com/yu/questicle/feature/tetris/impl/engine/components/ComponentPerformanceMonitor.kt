package com.yu.questicle.feature.tetris.impl.engine.components

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Monitors performance of component interactions to identify bottlenecks.
 * 
 * This class tracks execution time and call frequency for component methods,
 * providing insights for optimization.
 */
@Singleton
class ComponentPerformanceMonitor @Inject constructor() {
    
    // Track execution time for each operation
    private val operationTimes = ConcurrentHashMap<String, AtomicLong>()
    
    // Track call count for each operation
    private val operationCalls = ConcurrentHashMap<String, AtomicLong>()
    
    // Track maximum execution time for each operation
    private val operationMaxTimes = ConcurrentHashMap<String, AtomicLong>()
    
    // Track minimum execution time for each operation
    private val operationMinTimes = ConcurrentHashMap<String, AtomicLong>()
    
    // Track component interaction counts
    private val componentInteractions = ConcurrentHashMap<Pair<String, String>, AtomicLong>()
    
    // Start time for performance monitoring
    private val startTime = System.currentTimeMillis()
    
    /**
     * Record execution time for an operation.
     */
    fun recordOperationTime(operation: String, timeNanos: Long) {
        operationTimes.computeIfAbsent(operation) { AtomicLong(0) }.addAndGet(timeNanos)
        operationCalls.computeIfAbsent(operation) { AtomicLong(0) }.incrementAndGet()
        
        // Update max time
        val currentMax = operationMaxTimes.computeIfAbsent(operation) { AtomicLong(0) }.get()
        if (timeNanos > currentMax) {
            operationMaxTimes[operation]?.set(timeNanos)
        }
        
        // Update min time
        val currentMin = operationMinTimes.computeIfAbsent(operation) { AtomicLong(Long.MAX_VALUE) }.get()
        if (timeNanos < currentMin) {
            operationMinTimes[operation]?.set(timeNanos)
        }
    }
    
    /**
     * Record a component interaction.
     */
    fun recordComponentInteraction(fromComponent: String, toComponent: String) {
        val key = fromComponent to toComponent
        componentInteractions.computeIfAbsent(key) { AtomicLong(0) }.incrementAndGet()
    }
    
    /**
     * Measure execution time for a block of code.
     */
    inline fun <T> measureOperation(operation: String, block: () -> T): T {
        val startTime = System.nanoTime()
        try {
            return block()
        } finally {
            val endTime = System.nanoTime()
            recordOperationTime(operation, endTime - startTime)
        }
    }
    
    /**
     * Measure execution time for a block of code with component interaction.
     */
    inline fun <T> measureComponentInteraction(
        operation: String,
        fromComponent: String,
        toComponent: String,
        block: () -> T
    ): T {
        recordComponentInteraction(fromComponent, toComponent)
        return measureOperation(operation, block)
    }
    
    /**
     * Get performance metrics for all operations.
     */
    fun getOperationMetrics(): Map<String, Map<String, Any>> {
        val metrics = mutableMapOf<String, Map<String, Any>>()
        
        for (operation in operationTimes.keys) {
            val totalTime = operationTimes[operation]?.get() ?: 0
            val calls = operationCalls[operation]?.get() ?: 0
            val maxTime = operationMaxTimes[operation]?.get() ?: 0
            val minTime = operationMinTimes[operation]?.get()?.takeIf { it != Long.MAX_VALUE } ?: 0
            
            val averageTime = if (calls > 0) totalTime.toDouble() / calls else 0.0
            
            metrics[operation] = mapOf(
                "totalTimeNanos" to totalTime,
                "totalTimeMs" to totalTime / 1_000_000.0,
                "calls" to calls,
                "averageTimeNanos" to averageTime,
                "averageTimeMs" to averageTime / 1_000_000.0,
                "maxTimeNanos" to maxTime,
                "maxTimeMs" to maxTime / 1_000_000.0,
                "minTimeNanos" to minTime,
                "minTimeMs" to minTime / 1_000_000.0
            )
        }
        
        return metrics
    }
    
    /**
     * Get component interaction metrics.
     */
    fun getComponentInteractionMetrics(): Map<String, Map<String, Long>> {
        val metrics = mutableMapOf<String, MutableMap<String, Long>>()
        
        for ((key, count) in componentInteractions) {
            val (fromComponent, toComponent) = key
            
            val componentMetrics = metrics.computeIfAbsent(fromComponent) { mutableMapOf() }
            componentMetrics[toComponent] = count.get()
        }
        
        return metrics
    }
    
    /**
     * Get overall performance summary.
     */
    fun getPerformanceSummary(): Map<String, Any> {
        val totalOperationTime = operationTimes.values.sumOf { it.get() }
        val totalCalls = operationCalls.values.sumOf { it.get() }
        val totalInteractions = componentInteractions.values.sumOf { it.get() }
        val upTimeMs = System.currentTimeMillis() - startTime
        
        return mapOf(
            "totalOperationTimeNanos" to totalOperationTime,
            "totalOperationTimeMs" to totalOperationTime / 1_000_000.0,
            "totalCalls" to totalCalls,
            "totalInteractions" to totalInteractions,
            "upTimeMs" to upTimeMs,
            "operationsPerSecond" to if (upTimeMs > 0) totalCalls * 1000.0 / upTimeMs else 0.0,
            "interactionsPerSecond" to if (upTimeMs > 0) totalInteractions * 1000.0 / upTimeMs else 0.0,
            "averageOperationTimeNanos" to if (totalCalls > 0) totalOperationTime.toDouble() / totalCalls else 0.0
        )
    }
    
    /**
     * Get hot path operations (most time-consuming).
     */
    fun getHotPathOperations(limit: Int = 10): List<Map<String, Any>> {
        return operationTimes.keys
            .map { operation ->
                val totalTime = operationTimes[operation]?.get() ?: 0
                val calls = operationCalls[operation]?.get() ?: 0
                
                mapOf(
                    "operation" to operation,
                    "totalTimeNanos" to totalTime,
                    "totalTimeMs" to totalTime / 1_000_000.0,
                    "calls" to calls,
                    "averageTimeNanos" to if (calls > 0) totalTime.toDouble() / calls else 0.0,
                    "averageTimeMs" to if (calls > 0) totalTime / 1_000_000.0 / calls else 0.0
                )
            }
            .sortedByDescending { it["totalTimeNanos"] as Long }
            .take(limit)
    }
    
    /**
     * Get most frequent component interactions.
     */
    fun getMostFrequentInteractions(limit: Int = 10): List<Map<String, Any>> {
        return componentInteractions.entries
            .map { (key, count) ->
                val (fromComponent, toComponent) = key
                
                mapOf(
                    "fromComponent" to fromComponent,
                    "toComponent" to toComponent,
                    "count" to count.get()
                )
            }
            .sortedByDescending { it["count"] as Long }
            .take(limit)
    }
    
    /**
     * Reset all metrics.
     */
    fun resetMetrics() {
        operationTimes.clear()
        operationCalls.clear()
        operationMaxTimes.clear()
        operationMinTimes.clear()
        componentInteractions.clear()
    }
}