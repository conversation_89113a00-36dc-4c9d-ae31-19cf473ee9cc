package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.rotation.SuperRotationSystem

/**
 * 俄罗斯方块游戏逻辑处理器
 * 负责处理方块移动、旋转和放置等核心游戏逻辑
 */
interface TetrisGameLogicProcessor {
    
    /**
     * 移动方块
     * @param piece 当前方块
     * @param direction 移动方向
     * @param board 游戏板
     * @return 移动后的方块，如果无法移动则返回null
     */
    fun movePiece(piece: TetrisPiece, direction: Direction, board: TetrisBoard): TetrisPiece?
    
    /**
     * 旋转方块
     * @param piece 当前方块
     * @param clockwise 是否顺时针旋转
     * @param board 游戏板
     * @return 旋转后的方块，如果无法旋转则返回null
     */
    fun rotatePiece(piece: TetrisPiece, clockwise: Boolean, board: TetrisBoard): TetrisPiece?
    
    /**
     * 软降（下落一格）
     * @param piece 当前方块
     * @param board 游戏板
     * @return 下落后的方块，如果无法下落则返回原方块
     */
    fun softDrop(piece: TetrisPiece, board: TetrisBoard): TetrisPiece
    
    /**
     * 硬降（直接下落到底部）
     * @param piece 当前方块
     * @param board 游戏板
     * @return 下落到底部的方块
     */
    fun hardDrop(piece: TetrisPiece, board: TetrisBoard): TetrisPiece
    
    /**
     * 获取方块的所有可能位置
     * @param piece 当前方块
     * @param board 游戏板
     * @return 所有可能的位置列表
     */
    fun getAllPossiblePositions(piece: TetrisPiece, board: TetrisBoard): List<TetrisPiece>
    
    /**
     * 获取方块的所有可能旋转
     * @param piece 当前方块
     * @param board 游戏板
     * @return 所有可能的旋转列表
     */
    fun getAllPossibleRotations(piece: TetrisPiece, board: TetrisBoard): List<TetrisPiece>
}

/**
 * 俄罗斯方块游戏逻辑处理器实现
 */
class TetrisGameLogicProcessorImpl(
    private val collisionDetector: TetrisCollisionDetector
) : TetrisGameLogicProcessor {
    
    override fun movePiece(piece: TetrisPiece, direction: Direction, board: TetrisBoard): TetrisPiece? {
        val dx = when (direction) {
            Direction.LEFT -> -1
            Direction.RIGHT -> 1
            Direction.DOWN -> 0
        }
        
        val dy = when (direction) {
            Direction.DOWN -> 1
            else -> 0
        }
        
        val newPiece = piece.move(dx, dy)
        
        return if (collisionDetector.isValidPosition(newPiece, board)) {
            newPiece
        } else {
            null
        }
    }
    
    override fun rotatePiece(piece: TetrisPiece, clockwise: Boolean, board: TetrisBoard): TetrisPiece? {
        return SuperRotationSystem.tryRotate(piece, clockwise, board)
    }
    
    override fun softDrop(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        val newPiece = piece.move(0, 1)
        
        return if (collisionDetector.isValidPosition(newPiece, board)) {
            newPiece
        } else {
            piece
        }
    }
    
    override fun hardDrop(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        var testPiece = piece
        
        while (true) {
            val newPiece = testPiece.move(0, 1)
            
            if (collisionDetector.isValidPosition(newPiece, board)) {
                testPiece = newPiece
            } else {
                break
            }
        }
        
        return testPiece
    }
    
    override fun getAllPossiblePositions(piece: TetrisPiece, board: TetrisBoard): List<TetrisPiece> {
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的水平位置
        for (x in -2..board.width + 2) {
            val movedPiece = piece.copy(x = x)
            if (collisionDetector.isValidPosition(movedPiece, board)) {
                result.add(movedPiece)
            }
        }
        
        return result
    }
    
    override fun getAllPossibleRotations(piece: TetrisPiece, board: TetrisBoard): List<TetrisPiece> {
        val result = mutableListOf<TetrisPiece>()
        
        // 尝试所有可能的旋转
        for (rotation in 0 until 4) {
            val rotatedPiece = piece.copy(rotation = rotation)
            if (collisionDetector.isValidPosition(rotatedPiece, board)) {
                result.add(rotatedPiece)
            }
        }
        
        return result
    }
}