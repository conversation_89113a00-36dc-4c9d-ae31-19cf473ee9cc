package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisCellType

/**
 * Tetris 碰撞检测器
 * 
 * 负责检测方块与游戏板边界和其他方块的碰撞
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
class TetrisCollisionDetector {
    
    /**
     * 检测方块是否可以放置在指定位置
     */
    fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val positions = piece.getOccupiedPositions()
        
        return positions.all { (x, y) ->
            // 检查边界
            x >= 0 && x < board.width && y >= 0 && y < board.height &&
            // 检查是否与已有方块冲突
            board.cells[y][x] == TetrisCellType.EMPTY
        }
    }
    
    /**
     * 检测方块是否可以向指定方向移动
     */
    fun canMove(piece: TetrisPiece, dx: Int, dy: Int, board: TetrisBoard): Bo<PERSON>an {
        val movedPiece = piece.move(dx, dy)
        return isValidPosition(movedPiece, board)
    }
    
    /**
     * 检测方块是否可以旋转
     */
    fun canRotate(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val rotatedPiece = piece.rotate()
        return isValidPosition(rotatedPiece, board)
    }
    
    /**
     * 检测方块是否可以旋转（带踢墙检测）
     * 实现SRS（Super Rotation System）踢墙算法
     */
    fun canRotateWithKick(piece: TetrisPiece, board: TetrisBoard): Pair<Boolean, TetrisPiece?> {
        val rotatedPiece = piece.rotate()
        
        // 首先尝试原位旋转
        if (isValidPosition(rotatedPiece, board)) {
            return true to rotatedPiece
        }
        
        // 尝试踢墙偏移
        val kickOffsets = getKickOffsets(piece.type, piece.rotation)
        
        for ((dx, dy) in kickOffsets) {
            val kickedPiece = rotatedPiece.move(dx, dy)
            if (isValidPosition(kickedPiece, board)) {
                return true to kickedPiece
            }
        }
        
        return false to null
    }
    
    /**
     * 获取踢墙偏移量
     * 简化版本的SRS踢墙数据
     */
    private fun getKickOffsets(pieceType: com.yu.questicle.core.domain.model.tetris.TetrisPieceType, currentRotation: Int): List<Pair<Int, Int>> {
        return when (pieceType) {
            com.yu.questicle.core.domain.model.tetris.TetrisPieceType.I -> {
                // I型方块的踢墙数据
                when (currentRotation % 4) {
                    0 -> listOf(-2 to 0, 1 to 0, -2 to -1, 1 to 2)
                    1 -> listOf(-1 to 0, 2 to 0, -1 to 2, 2 to -1)
                    2 -> listOf(2 to 0, -1 to 0, 2 to 1, -1 to -2)
                    3 -> listOf(1 to 0, -2 to 0, 1 to -2, -2 to 1)
                    else -> emptyList()
                }
            }
            com.yu.questicle.core.domain.model.tetris.TetrisPieceType.O -> {
                // O型方块不需要踢墙
                emptyList()
            }
            else -> {
                // 其他方块的通用踢墙数据
                when (currentRotation % 4) {
                    0 -> listOf(-1 to 0, -1 to 1, 0 to -2, -1 to -2)
                    1 -> listOf(1 to 0, 1 to -1, 0 to 2, 1 to 2)
                    2 -> listOf(1 to 0, 1 to 1, 0 to -2, 1 to -2)
                    3 -> listOf(-1 to 0, -1 to -1, 0 to 2, -1 to 2)
                    else -> emptyList()
                }
            }
        }
    }
    
    /**
     * 计算方块的最终下落位置（用于幽灵方块）
     */
    fun calculateDropPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        var dropPiece = piece
        
        while (canMove(dropPiece, 0, 1, board)) {
            dropPiece = dropPiece.move(0, 1)
        }
        
        return dropPiece
    }
    
    /**
     * 检测游戏是否结束
     * 当新生成的方块无法放置时游戏结束
     */
    fun isGameOver(piece: TetrisPiece, board: TetrisBoard): Boolean {
        return !isValidPosition(piece, board)
    }
}
