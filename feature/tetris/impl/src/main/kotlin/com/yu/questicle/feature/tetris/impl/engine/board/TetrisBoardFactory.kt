package com.yu.questicle.feature.tetris.impl.engine.board

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Factory for creating optimized TetrisBoard instances.
 * 
 * This factory provides methods to create different types of TetrisBoard implementations,
 * with BitPackedTetrisBoard as the default for optimal performance.
 */
@Singleton
class TetrisBoardFactory @Inject constructor() {
    
    /**
     * Create an empty board with the specified dimensions
     */
    fun createEmptyBoard(width: Int = 10, height: Int = 20): TetrisBoard {
        return BitPackedTetrisBoard.empty(width, height)
    }
    
    /**
     * Create a board from a 2D array of cell types
     */
    fun createBoardFromCells(cells: Array<Array<TetrisCellType>>): TetrisBoard {
        return BitPackedTetrisBoard.fromCells(cells)
    }
    
    /**
     * Create a board with a specific pattern for testing
     */
    fun createTestBoard(pattern: BoardPattern, width: Int = 10, height: Int = 20): TetrisBoard {
        val board = BitPackedTetrisBoard.empty(width, height)
        
        return when (pattern) {
            BoardPattern.EMPTY -> board
            BoardPattern.FULL -> {
                var result = board
                for (y in 0 until height) {
                    for (x in 0 until width) {
                        result = result.setCellType(x, y, TetrisCellType.I)
                    }
                }
                result
            }
            BoardPattern.CHECKERBOARD -> {
                var result = board
                for (y in 0 until height) {
                    for (x in 0 until width) {
                        if ((x + y) % 2 == 0) {
                            result = result.setCellType(x, y, TetrisCellType.I)
                        }
                    }
                }
                result
            }
            BoardPattern.BOTTOM_ROW -> {
                var result = board
                for (x in 0 until width) {
                    result = result.setCellType(x, height - 1, TetrisCellType.I)
                }
                result
            }
            BoardPattern.ALMOST_FULL_ROW -> {
                var result = board
                for (x in 0 until width - 1) {
                    result = result.setCellType(x, height - 1, TetrisCellType.I)
                }
                result
            }
        }
    }
    
    /**
     * Predefined board patterns for testing
     */
    enum class BoardPattern {
        EMPTY,
        FULL,
        CHECKERBOARD,
        BOTTOM_ROW,
        ALMOST_FULL_ROW
    }
}