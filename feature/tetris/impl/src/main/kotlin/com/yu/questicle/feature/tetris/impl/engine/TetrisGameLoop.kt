package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.model.tetris.TetrisStatus
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tetris 游戏循环管理器
 * 
 * 负责管理游戏的自动下降和性能优化
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Singleton
class TetrisGameLoop @Inject constructor() {
    
    private var gameLoopJob: Job? = null
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning
    
    /**
     * 启动游戏循环
     */
    fun start(
        gameState: StateFlow<TetrisGameState>,
        onTick: suspend () -> Unit
    ) {
        stop() // 停止现有循环
        
        gameLoopJob = CoroutineScope(Dispatchers.Default).launch {
            _isRunning.value = true
            
            while (isActive && _isRunning.value) {
                val currentState = gameState.value
                
                if (currentState.status == TetrisStatus.PLAYING) {
                    val dropInterval = calculateDropInterval(currentState.level)
                    
                    try {
                        onTick()
                        delay(dropInterval)
                    } catch (e: Exception) {
                        // 处理错误但继续循环
                        delay(1000) // 错误时延迟1秒
                    }
                } else {
                    // 非游戏状态时减少CPU使用
                    delay(100)
                }
            }
            
            _isRunning.value = false
        }
    }
    
    /**
     * 停止游戏循环
     */
    fun stop() {
        gameLoopJob?.cancel()
        gameLoopJob = null
        _isRunning.value = false
    }
    
    /**
     * 暂停游戏循环
     */
    fun pause() {
        _isRunning.value = false
    }
    
    /**
     * 恢复游戏循环
     */
    fun resume() {
        _isRunning.value = true
    }
    
    /**
     * 计算下降间隔（毫秒）
     * 等级越高，下降越快
     */
    private fun calculateDropInterval(level: Int): Long {
        return maxOf(50L, 1000L - (level - 1) * 50L)
    }
}
