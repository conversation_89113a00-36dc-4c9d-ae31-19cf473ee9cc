package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType

/**
 * 俄罗斯方块缓存管理器
 * 负责管理游戏中的各种缓存，提高性能
 */
interface TetrisCacheManager {
    
    /**
     * 初始化缓存
     */
    fun initialize()
    
    /**
     * 重新初始化缓存
     */
    fun reinitialize()
    
    /**
     * 清除所有缓存
     */
    fun clear()
    
    /**
     * 获取方块形状缓存
     * @param pieceType 方块类型
     * @param rotation 旋转角度
     * @return 方块形状缓存
     */
    fun getShapeCache(pieceType: TetrisPieceType, rotation: Int): List<List<Boolean>>?
    
    /**
     * 设置方块形状缓存
     * @param pieceType 方块类型
     * @param rotation 旋转角度
     * @param shape 方块形状
     */
    fun setShapeCache(pieceType: TetrisPieceType, rotation: Int, shape: List<List<Boolean>>)
    
    /**
     * 获取碰撞检测缓存
     * @param piece 方块
     * @param boardHash 游戏板哈希值
     * @return 碰撞检测结果，null表示缓存未命中
     */
    fun getCollisionCache(piece: TetrisPiece, boardHash: Int): Boolean?
    
    /**
     * 设置碰撞检测缓存
     * @param piece 方块
     * @param boardHash 游戏板哈希值
     * @param result 碰撞检测结果
     */
    fun setCollisionCache(piece: TetrisPiece, boardHash: Int, result: Boolean)
    
    /**
     * 获取缓存统计
     * @return 缓存统计信息
     */
    fun getCacheStats(): Map<String, Any>
}

/**
 * 俄罗斯方块缓存管理器实现
 */
class TetrisCacheManagerImpl : TetrisCacheManager {
    
    // 方块形状缓存
    private val shapeCache = mutableMapOf<String, List<List<Boolean>>>()
    
    // 碰撞检测缓存
    private val collisionCache = mutableMapOf<String, Boolean>()
    
    // 缓存统计
    private var shapeCacheHits = 0
    private var shapeCacheMisses = 0
    private var collisionCacheHits = 0
    private var collisionCacheMisses = 0
    
    // 缓存大小限制
    private val maxShapeCacheSize = 100
    private val maxCollisionCacheSize = 1000
    
    override fun initialize() {
        clear()
        precomputeShapes()
    }
    
    override fun reinitialize() {
        clear()
        precomputeShapes()
    }
    
    override fun clear() {
        shapeCache.clear()
        collisionCache.clear()
        shapeCacheHits = 0
        shapeCacheMisses = 0
        collisionCacheHits = 0
        collisionCacheMisses = 0
    }
    
    override fun getShapeCache(pieceType: TetrisPieceType, rotation: Int): List<List<Boolean>>? {
        val key = "${pieceType.name}_${rotation % 4}"
        val result = shapeCache[key]
        
        if (result != null) {
            shapeCacheHits++
        } else {
            shapeCacheMisses++
        }
        
        return result
    }
    
    override fun setShapeCache(pieceType: TetrisPieceType, rotation: Int, shape: List<List<Boolean>>) {
        val key = "${pieceType.name}_${rotation % 4}"
        
        // 检查缓存大小
        if (shapeCache.size >= maxShapeCacheSize) {
            // 简单策略：移除一个随机条目
            shapeCache.keys.firstOrNull()?.let { shapeCache.remove(it) }
        }
        
        shapeCache[key] = shape
    }
    
    override fun getCollisionCache(piece: TetrisPiece, boardHash: Int): Boolean? {
        val key = "${piece.type.name}_${piece.x}_${piece.y}_${piece.rotation}_$boardHash"
        val result = collisionCache[key]
        
        if (result != null) {
            collisionCacheHits++
        } else {
            collisionCacheMisses++
        }
        
        return result
    }
    
    override fun setCollisionCache(piece: TetrisPiece, boardHash: Int, result: Boolean) {
        val key = "${piece.type.name}_${piece.x}_${piece.y}_${piece.rotation}_$boardHash"
        
        // 检查缓存大小
        if (collisionCache.size >= maxCollisionCacheSize) {
            // LRU策略：移除最早添加的条目
            val keysToRemove = collisionCache.keys.take(maxCollisionCacheSize / 10)
            keysToRemove.forEach { collisionCache.remove(it) }
        }
        
        collisionCache[key] = result
    }
    
    override fun getCacheStats(): Map<String, Any> {
        return mapOf(
            "shapeCache" to mapOf(
                "size" to shapeCache.size,
                "hits" to shapeCacheHits,
                "misses" to shapeCacheMisses,
                "hitRate" to if (shapeCacheHits + shapeCacheMisses > 0) 
                    shapeCacheHits.toDouble() / (shapeCacheHits + shapeCacheMisses) else 0.0
            ),
            "collisionCache" to mapOf(
                "size" to collisionCache.size,
                "hits" to collisionCacheHits,
                "misses" to collisionCacheMisses,
                "hitRate" to if (collisionCacheHits + collisionCacheMisses > 0)
                    collisionCacheHits.toDouble() / (collisionCacheHits + collisionCacheMisses) else 0.0
            )
        )
    }
    
    /**
     * 预计算所有方块形状
     */
    private fun precomputeShapes() {
        TetrisPieceType.entries.forEach { pieceType ->
            for (rotation in 0 until 4) {
                val shape = computeShape(pieceType, rotation)
                setShapeCache(pieceType, rotation, shape)
            }
        }
    }
    
    /**
     * 计算方块形状
     */
    private fun computeShape(pieceType: TetrisPieceType, rotation: Int): List<List<Boolean>> {
        val baseShape = pieceType.getShape()
        var rotatedShape = baseShape
        
        // 应用旋转
        repeat(rotation % 4) {
            rotatedShape = rotateClockwise(rotatedShape)
        }
        
        return rotatedShape
    }
    
    /**
     * 顺时针旋转矩阵
     */
    private fun rotateClockwise(matrix: List<List<Boolean>>): List<List<Boolean>> {
        val rows = matrix.size
        val cols = matrix[0].size
        
        return List(cols) { col ->
            List(rows) { row ->
                matrix[rows - 1 - row][col]
            }
        }
    }
}