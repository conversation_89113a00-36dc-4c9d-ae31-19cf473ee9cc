package com.yu.questicle.feature.tetris.impl.engine.components.di

import com.yu.questicle.feature.tetris.impl.engine.components.*
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger module for providing Tetris engine components
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class TetrisComponentsModule {
    
    @Binds
    @Singleton
    abstract fun bindTetrisGameLogicProcessor(
        impl: TetrisGameLogicProcessorImpl
    ): TetrisGameLogicProcessor
    
    @Binds
    @Singleton
    abstract fun bindTetrisCollisionDetector(
        impl: TetrisCollisionDetectorImpl
    ): TetrisCollisionDetector
    
    @Binds
    @Singleton
    abstract fun bindTetrisStatisticsCalculator(
        impl: TetrisStatisticsCalculatorImpl
    ): TetrisStatisticsCalculator
    
    @Binds
    @Singleton
    abstract fun bindTetrisPerformanceManager(
        impl: TetrisPerformanceManagerImpl
    ): TetrisPerformanceManager
    
    @Binds
    @Singleton
    abstract fun bindTetrisCacheManager(
        impl: TetrisCacheManagerImpl
    ): TetrisCacheManager
}