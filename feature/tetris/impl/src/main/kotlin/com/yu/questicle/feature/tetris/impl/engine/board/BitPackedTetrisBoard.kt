package com.yu.questicle.feature.tetris.impl.engine.board

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisCellType
import com.yu.questicle.core.domain.model.tetris.TetrisPiece

/**
 * Optimized TetrisBoard implementation using bit-packed storage.
 * 
 * This implementation uses a LongArray to store the board state, where each cell
 * requires only 3 bits (supporting up to 8 different cell types). This reduces
 * memory usage by approximately 8x compared to using a 2D array of objects.
 * 
 * Performance benefits:
 * - Reduced memory usage
 * - Better cache locality
 * - Faster board copying
 * - Efficient line clearing operations
 */
class BitPackedTetrisBoard private constructor(
    private val data: LongArray,
    override val width: Int,
    override val height: Int,
    private val bitsPerCell: Int = 3,
    private val cellMask: Long = (1L shl bitsPerCell) - 1
) : TetrisBoard {

    // The cells property is required by the TetrisBoard interface
    // We compute it on-demand from the bit-packed representation
    override val cells: Array<Array<TetrisCellType>> by lazy {
        Array(height) { y ->
            Array(width) { x ->
                getCellType(x, y)
            }
        }
    }

    /**
     * Get the cell type at the specified position
     */
    private fun getCellType(x: Int, y: Int): TetrisCellType {
        if (x < 0 || x >= width || y < 0 || y >= height) {
            return TetrisCellType.EMPTY
        }
        
        val cellIndex = y * width + x
        val longIndex = cellIndex * bitsPerCell / 64
        val bitOffset = (cellIndex * bitsPerCell) % 64
        
        val cellValue = (data[longIndex] shr bitOffset) and cellMask
        return indexToCellType(cellValue.toInt())
    }

    /**
     * Set the cell type at the specified position
     */
    private fun setCellType(x: Int, y: Int, cellType: TetrisCellType): BitPackedTetrisBoard {
        if (x < 0 || x >= width || y < 0 || y >= height) {
            return this
        }
        
        val newData = data.copyOf()
        val cellIndex = y * width + x
        val longIndex = cellIndex * bitsPerCell / 64
        val bitOffset = (cellIndex * bitsPerCell) % 64
        
        // Clear the bits at the position
        newData[longIndex] = newData[longIndex] and (cellMask shl bitOffset).inv()
        
        // Set the new value
        val cellValue = cellTypeToIndex(cellType).toLong()
        newData[longIndex] = newData[longIndex] or (cellValue shl bitOffset)
        
        return BitPackedTetrisBoard(newData, width, height, bitsPerCell, cellMask)
    }

    /**
     * Convert a cell type to its bit-packed index
     */
    private fun cellTypeToIndex(cellType: TetrisCellType): Int {
        return when (cellType) {
            TetrisCellType.EMPTY -> 0
            TetrisCellType.I -> 1
            TetrisCellType.J -> 2
            TetrisCellType.L -> 3
            TetrisCellType.O -> 4
            TetrisCellType.S -> 5
            TetrisCellType.T -> 6
            TetrisCellType.Z -> 7
        }
    }

    /**
     * Convert a bit-packed index to its cell type
     */
    private fun indexToCellType(index: Int): TetrisCellType {
        return when (index) {
            0 -> TetrisCellType.EMPTY
            1 -> TetrisCellType.I
            2 -> TetrisCellType.J
            3 -> TetrisCellType.L
            4 -> TetrisCellType.O
            5 -> TetrisCellType.S
            6 -> TetrisCellType.T
            7 -> TetrisCellType.Z
            else -> TetrisCellType.EMPTY
        }
    }

    /**
     * Check if a row is completely filled
     */
    fun isRowFilled(y: Int): Boolean {
        if (y < 0 || y >= height) return false
        
        for (x in 0 until width) {
            if (getCellType(x, y) == TetrisCellType.EMPTY) {
                return false
            }
        }
        return true
    }

    /**
     * Check if a row is completely empty
     */
    fun isRowEmpty(y: Int): Boolean {
        if (y < 0 || y >= height) return false
        
        for (x in 0 until width) {
            if (getCellType(x, y) != TetrisCellType.EMPTY) {
                return false
            }
        }
        return true
    }

    /**
     * Clear a row and shift all rows above it down
     */
    fun clearRow(y: Int): BitPackedTetrisBoard {
        if (y < 0 || y >= height) return this
        
        var result = this
        
        // Shift all rows above the cleared row down
        for (row in y downTo 1) {
            for (x in 0 until width) {
                val cellAbove = getCellType(x, row - 1)
                result = result.setCellType(x, row, cellAbove)
            }
        }
        
        // Clear the top row
        for (x in 0 until width) {
            result = result.setCellType(x, 0, TetrisCellType.EMPTY)
        }
        
        return result
    }

    /**
     * Clear multiple rows and shift all rows above them down
     */
    fun clearRows(rows: List<Int>): BitPackedTetrisBoard {
        if (rows.isEmpty()) return this
        
        // Sort rows in descending order to avoid shifting issues
        val sortedRows = rows.sortedDescending()
        
        var result = this
        for (row in sortedRows) {
            result = result.clearRow(row)
        }
        
        return result
    }

    /**
     * Place a piece on the board
     */
    fun placePiece(piece: TetrisPiece): BitPackedTetrisBoard {
        val shape = getPieceShape(piece)
        var result = this
        
        for (y in shape.indices) {
            for (x in shape[y].indices) {
                if (shape[y][x]) {
                    val boardX = piece.x + x
                    val boardY = piece.y + y
                    
                    if (boardX >= 0 && boardX < width && boardY >= 0 && boardY < height) {
                        result = result.setCellType(boardX, boardY, piece.type.toCellType())
                    }
                }
            }
        }
        
        return result
    }

    /**
     * Check if a piece can be placed at its current position
     */
    fun canPlacePiece(piece: TetrisPiece): Boolean {
        val shape = getPieceShape(piece)
        
        for (y in shape.indices) {
            for (x in shape[y].indices) {
                if (shape[y][x]) {
                    val boardX = piece.x + x
                    val boardY = piece.y + y
                    
                    // Check if out of bounds
                    if (boardX < 0 || boardX >= width || boardY < 0 || boardY >= height) {
                        return false
                    }
                    
                    // Check if cell is already occupied
                    if (getCellType(boardX, boardY) != TetrisCellType.EMPTY) {
                        return false
                    }
                }
            }
        }
        
        return true
    }

    /**
     * Find and clear completed lines
     */
    fun clearCompletedLines(): Pair<BitPackedTetrisBoard, List<Int>> {
        val completedLines = mutableListOf<Int>()
        
        for (y in 0 until height) {
            if (isRowFilled(y)) {
                completedLines.add(y)
            }
        }
        
        if (completedLines.isEmpty()) {
            return this to emptyList()
        }
        
        return clearRows(completedLines) to completedLines
    }

    /**
     * Get the shape of a piece based on its type and rotation
     */
    private fun getPieceShape(piece: TetrisPiece): Array<BooleanArray> {
        return when (piece.type) {
            TetrisPieceType.I -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false),
                    booleanArrayOf(false, false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(false, false, false, false),
                    booleanArrayOf(true, true, true, true),
                    booleanArrayOf(false, false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false),
                    booleanArrayOf(false, true, false, false)
                )
            }
            TetrisPieceType.O -> arrayOf(
                booleanArrayOf(true, true),
                booleanArrayOf(true, true)
            )
            TetrisPieceType.T -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, true, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            TetrisPieceType.J -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(true, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, true)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(true, true, false)
                )
            }
            TetrisPieceType.L -> when (piece.rotation % 4) {
                0 -> arrayOf(
                    booleanArrayOf(false, false, true),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(false, false, false)
                )
                1 -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true)
                )
                2 -> arrayOf(
                    booleanArrayOf(false, false, false),
                    booleanArrayOf(true, true, true),
                    booleanArrayOf(true, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, false)
                )
            }
            TetrisPieceType.S -> when (piece.rotation % 4) {
                0, 2 -> arrayOf(
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, false, true)
                )
            }
            TetrisPieceType.Z -> when (piece.rotation % 4) {
                0, 2 -> arrayOf(
                    booleanArrayOf(true, true, false),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, false, false)
                )
                else -> arrayOf(
                    booleanArrayOf(false, false, true),
                    booleanArrayOf(false, true, true),
                    booleanArrayOf(false, true, false)
                )
            }
        }
    }

    /**
     * Convert TetrisPieceType to TetrisCellType
     */
    private fun TetrisPieceType.toCellType(): TetrisCellType {
        return when (this) {
            TetrisPieceType.I -> TetrisCellType.I
            TetrisPieceType.J -> TetrisCellType.J
            TetrisPieceType.L -> TetrisCellType.L
            TetrisPieceType.O -> TetrisCellType.O
            TetrisPieceType.S -> TetrisCellType.S
            TetrisPieceType.T -> TetrisCellType.T
            TetrisPieceType.Z -> TetrisCellType.Z
        }
    }

    companion object {
        /**
         * Create an empty board with the specified dimensions
         */
        fun empty(width: Int = 10, height: Int = 20, bitsPerCell: Int = 3): BitPackedTetrisBoard {
            val totalCells = width * height
            val bitsNeeded = totalCells * bitsPerCell
            val longArraySize = (bitsNeeded + 63) / 64 // Round up to nearest 64 bits
            
            return BitPackedTetrisBoard(
                data = LongArray(longArraySize),
                width = width,
                height = height,
                bitsPerCell = bitsPerCell,
                cellMask = (1L shl bitsPerCell) - 1
            )
        }

        /**
         * Create a board from a 2D array of cell types
         */
        fun fromCells(cells: Array<Array<TetrisCellType>>): BitPackedTetrisBoard {
            if (cells.isEmpty() || cells[0].isEmpty()) {
                return empty()
            }
            
            val height = cells.size
            val width = cells[0].size
            val board = empty(width, height)
            
            var result = board
            for (y in 0 until height) {
                for (x in 0 until width) {
                    result = result.setCellType(x, y, cells[y][x])
                }
            }
            
            return result
        }
    }
}