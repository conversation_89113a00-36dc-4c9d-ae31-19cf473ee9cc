package com.yu.questicle.feature.tetris.impl.scoring

/**
 * Tetris 计分系统
 */
object TetrisScoring {
    
    /**
     * 基础分数表
     */
    private val BASE_SCORES = mapOf(
        1 to 100,  // Single
        2 to 300,  // Double
        3 to 500,  // Triple
        4 to 800   // Tetris
    )
    
    /**
     * T-Spin 分数表
     */
    private val TSPIN_SCORES = mapOf(
        0 to 400,  // T-Spin Mini
        1 to 800,  // T-Spin Single
        2 to 1200, // T-Spin Double
        3 to 1600  // T-Spin Triple
    )
    
    /**
     * 软降分数
     */
    const val SOFT_DROP_SCORE = 1
    
    /**
     * 硬降分数
     */
    const val HARD_DROP_SCORE = 2
    
    /**
     * 完美消除奖励
     */
    const val PERFECT_CLEAR_BONUS = 2000
    
    /**
     * Back-to-Back 倍数
     */
    const val BACK_TO_BACK_MULTIPLIER = 1.5
    
    /**
     * 计算行消除分数
     */
    fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean = false,
        isPerfectClear: Boolean = false,
        isBackToBack: Boolean = false
    ): Int {
        if (linesCleared == 0) return 0
        
        val baseScore = if (isTSpin) {
            TSPIN_SCORES[linesCleared] ?: 0
        } else {
            BASE_SCORES[linesCleared] ?: 0
        }
        
        var score = baseScore * level
        
        // Back-to-Back 奖励
        if (isBackToBack && (linesCleared == 4 || isTSpin)) {
            score = (score * BACK_TO_BACK_MULTIPLIER).toInt()
        }
        
        // 完美消除奖励
        if (isPerfectClear) {
            score += PERFECT_CLEAR_BONUS * level
        }
        
        return score
    }
    
    /**
     * 计算软降分数
     */
    fun calculateSoftDropScore(cells: Int): Int {
        return cells * SOFT_DROP_SCORE
    }
    
    /**
     * 计算硬降分数
     */
    fun calculateHardDropScore(cells: Int): Int {
        return cells * HARD_DROP_SCORE
    }
    
    /**
     * 计算等级
     */
    fun calculateLevel(linesCleared: Int): Int {
        return (linesCleared / 10) + 1
    }
    
    /**
     * 计算下降间隔（毫秒）
     */
    fun calculateDropInterval(level: Int): Long {
        return when {
            level <= 1 -> 1000
            level <= 2 -> 793
            level <= 3 -> 618
            level <= 4 -> 473
            level <= 5 -> 355
            level <= 6 -> 262
            level <= 7 -> 190
            level <= 8 -> 135
            level <= 9 -> 94
            level <= 10 -> 64
            level <= 13 -> 43
            level <= 16 -> 28
            level <= 19 -> 18
            level <= 29 -> 11
            else -> 7
        }
    }
    
    /**
     * 判断是否为 Back-to-Back
     */
    fun isBackToBackEligible(linesCleared: Int, isTSpin: Boolean): Boolean {
        return linesCleared == 4 || isTSpin
    }
}
