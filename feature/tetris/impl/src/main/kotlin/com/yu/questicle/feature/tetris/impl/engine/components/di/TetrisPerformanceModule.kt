package com.yu.questicle.feature.tetris.impl.engine.components.di

import com.yu.questicle.feature.tetris.impl.engine.components.CachePerformanceIntegration
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger module for providing performance-related components.
 */
@Module
@InstallIn(SingletonComponent::class)
object TetrisPerformanceModule {
    
    @Provides
    @Singleton
    fun provideCachePerformanceIntegration(
        integration: CachePerformanceIntegration
    ): CachePerformanceIntegration {
        // Initialize with default device specs until actual specs are provided
        val defaultSpecs = com.yu.questicle.feature.tetris.impl.engine.components.DeviceSpecs(
            cpuCores = Runtime.getRuntime().availableProcessors(),
            memoryMB = (Runtime.getRuntime().maxMemory() / (1024 * 1024)).toInt(),
            isLowEndDevice = false,
            isHighEndDevice = false
        )
        
        // Initialize the integration
        integration.initialize(defaultSpecs)
        
        return integration
    }
}