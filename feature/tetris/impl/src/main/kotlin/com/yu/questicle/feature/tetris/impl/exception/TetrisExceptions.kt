package com.yu.questicle.feature.tetris.impl.exception

import com.yu.questicle.core.common.exception.*

/**
 * Tetris 异常基类
 */
abstract class TetrisException(
    message: String,
    errorCode: String = "TETRIS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.BUSINESS
}

/**
 * 游戏引擎异常
 */
class GameEngineException(
    message: String,
    errorCode: String = "GAME_ENGINE_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.HIGH, cause)

/**
 * 游戏状态异常
 */
class GameStateException(
    message: String,
    errorCode: String = "GAME_STATE_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.MEDIUM, cause)

/**
 * 方块移动异常
 */
class PieceMoveException(
    message: String,
    errorCode: String = "PIECE_MOVE_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.LOW, cause)

/**
 * 方块旋转异常
 */
class PieceRotationException(
    message: String,
    errorCode: String = "PIECE_ROTATION_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.LOW, cause)

/**
 * 行消除异常
 */
class LineClearException(
    message: String,
    errorCode: String = "LINE_CLEAR_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.MEDIUM, cause)

/**
 * 游戏初始化异常
 */
class GameInitializationException(
    message: String,
    errorCode: String = "GAME_INIT_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.CRITICAL, cause)

/**
 * 游戏保存异常
 */
class GameSaveException(
    message: String,
    errorCode: String = "GAME_SAVE_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.HIGH, cause)

/**
 * 游戏加载异常
 */
class GameLoadException(
    message: String,
    errorCode: String = "GAME_LOAD_ERROR",
    cause: Throwable? = null
) : TetrisException(message, errorCode, ErrorSeverity.HIGH, cause)
