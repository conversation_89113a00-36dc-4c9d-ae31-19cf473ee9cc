package com.yu.questicle.feature.tetris.impl.model

import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import kotlin.time.Duration

/**
 * Tetris 统计数据
 */
data class TetrisStatistics(
    // 基础统计
    val piecesPlaced: Int = 0,
    val linesCleared: Int = 0,
    val maxLevel: Int = 1,
    val maxCombo: Int = 0,
    
    // 行消除统计
    val singles: Int = 0,
    val doubles: Int = 0,
    val triples: Int = 0,
    val tetrises: Int = 0,
    val tSpins: Int = 0,
    val tSpinSingles: Int = 0,
    val tSpinDoubles: Int = 0,
    val tSpinTriples: Int = 0,
    
    // 时间统计
    val gameTime: Duration = Duration.ZERO,
    val piecesPerMinute: Double = 0.0,
    val linesPerMinute: Double = 0.0,
    
    // 效率统计
    val efficiency: Double = 0.0,
    
    // 操作统计
    val totalMoves: Int = 0,
    val totalRotations: Int = 0,
    val totalDrops: Int = 0,
    val softDrops: Int = 0,
    val hardDrops: Int = 0,
    val holdUsed: Int = 0,
    
    // 高级统计
    val perfectClears: Int = 0,
    val averageHeight: Double = 0.0,
    val maxHeight: Int = 0,
    val droughtLength: Int = 0,
    val maxDroughtLength: Int = 0,
    val backToBackCount: Int = 0,
    val maxBackToBack: Int = 0,
    
    // 方块统计
    val pieceStats: Map<TetrisPieceType, Int> = emptyMap()
) {
    /**
     * 获取攻击力
     */
    fun getAttackPower(): Double {
        return (tetrises * 4.0 + tSpins * 3.0 + triples * 2.0 + doubles * 1.0) / 
               maxOf(1, piecesPlaced).toDouble()
    }
    
    /**
     * 计算总得分
     */
    fun getTotalScore(): Int {
        return singles * 100 + doubles * 300 + triples * 500 + tetrises * 800 +
               tSpinSingles * 800 + tSpinDoubles * 1200 + tSpinTriples * 1600 +
               perfectClears * 2000
    }
    
    /**
     * 计算平均每分钟方块数
     */
    fun calculatePiecesPerMinute(): Double {
        val minutes = gameTime.inWholeMinutes.toDouble()
        return if (minutes > 0) piecesPlaced / minutes else 0.0
    }
    
    /**
     * 计算平均每分钟行数
     */
    fun calculateLinesPerMinute(): Double {
        val minutes = gameTime.inWholeMinutes.toDouble()
        return if (minutes > 0) linesCleared / minutes else 0.0
    }
    
    /**
     * 计算效率（四行消除比例）
     */
    fun calculateEfficiency(): Double {
        val totalLineClears = singles + doubles + triples + tetrises
        return if (totalLineClears > 0) tetrises.toDouble() / totalLineClears else 0.0
    }
}
