package com.yu.questicle.feature.tetris.impl.engine.components

import com.yu.questicle.core.domain.model.tetris.TetrisBoard
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.feature.tetris.impl.engine.board.TetrisBoardAdapter
import com.yu.questicle.feature.tetris.impl.engine.memory.TetrisObjectPoolManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Optimized implementation of TetrisCollisionDetector with caching and
 * performance optimizations.
 */
@Singleton
class OptimizedTetrisCollisionDetector @Inject constructor(
    private val boardAdapter: TetrisBoardAdapter,
    private val componentCache: ComponentCache,
    private val objectPoolManager: TetrisObjectPoolManager,
    private val performanceMonitor: ComponentPerformanceMonitor
) : TetrisCollisionDetector {
    
    // Reusable arrays for piece shape to avoid allocations
    private val shapeCache = mutableMapOf<Triple<TetrisPieceType, Int, Int>, Array<BooleanArray>>()
    
    /**
     * Check if a piece position is valid (no collisions).
     */
    override fun isValidPosition(piece: TetrisPiece, board: TetrisBoard): Boolean {
        return performanceMonitor.measureOperation("isValidPosition") {
            // Use cache for repeated queries
            componentCache.getCachedCollisionResult(piece, board) {
                // Early boundary check
                if (!isWithinBoardBounds(piece, board)) {
                    return@getCachedCollisionResult false
                }
                
                // Get piece shape from cache or compute it
                val shape = getPieceShape(piece.type, piece.rotation)
                
                // Check for collisions
                for (y in shape.indices) {
                    for (x in shape[y].indices) {
                        if (shape[y][x]) {
                            val boardX = piece.x + x
                            val boardY = piece.y + y
                            
                            // Check if cell is occupied
                            if (boardX >= 0 && boardX < board.width && 
                                boardY >= 0 && boardY < board.height &&
                                board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                                return@getCachedCollisionResult false
                            }
                        }
                    }
                }
                
                true
            }
        }
    }
    
    /**
     * Find the landing position for a piece (for ghost piece or hard drop).
     */
    override fun findLandingPosition(piece: TetrisPiece, board: TetrisBoard): TetrisPiece {
        return performanceMonitor.measureOperation("findLandingPosition") {
            // Use cache for landing position
            val landingY = componentCache.getCachedLandingPosition(piece, board) {
                // Use binary search for efficient landing position calculation
                var low = piece.y
                var high = board.height
                
                while (low < high - 1) {
                    val mid = (low + high) / 2
                    val testPiece = piece.copy(y = mid)
                    
                    if (isValidPosition(testPiece, board)) {
                        low = mid
                    } else {
                        high = mid
                    }
                }
                
                // Find the exact landing position with linear search
                var y = low
                while (y < board.height) {
                    val testPiece = piece.copy(y = y + 1)
                    if (!isValidPosition(testPiece, board)) {
                        break
                    }
                    y++
                }
                
                y
            }
            
            // Create landing piece using object pool
            objectPoolManager.acquirePiece(piece.type, piece.x, landingY, piece.rotation)
        }
    }
    
    /**
     * Detect collisions and return detailed collision information.
     */
    override fun detectCollisions(piece: TetrisPiece, board: TetrisBoard): CollisionInfo {
        return performanceMonitor.measureOperation("detectCollisions") {
            val collisions = mutableListOf<CollisionPoint>()
            
            // Get piece shape
            val shape = getPieceShape(piece.type, piece.rotation)
            
            // Check for collisions
            for (y in shape.indices) {
                for (x in shape[y].indices) {
                    if (shape[y][x]) {
                        val boardX = piece.x + x
                        val boardY = piece.y + y
                        
                        // Check if out of bounds
                        if (boardX < 0 || boardX >= board.width || boardY < 0 || boardY >= board.height) {
                            collisions.add(
                                CollisionPoint(
                                    x = boardX,
                                    y = boardY,
                                    type = CollisionType.BOUNDARY
                                )
                            )
                            continue
                        }
                        
                        // Check if cell is occupied
                        if (board.cells[boardY][boardX] != TetrisCellType.EMPTY) {
                            collisions.add(
                                CollisionPoint(
                                    x = boardX,
                                    y = boardY,
                                    type = CollisionType.BLOCK
                                )
                            )
                        }
                    }
                }
            }
            
            CollisionInfo(
                hasCollision = collisions.isNotEmpty(),
                collisionPoints = collisions
            )
        }
    }
    
    /**
     * Check if a piece is within the board boundaries.
     */
    private fun isWithinBoardBounds(piece: TetrisPiece, board: TetrisBoard): Boolean {
        // Get piece shape
        val shape = getPieceShape(piece.type, piece.rotation)
        
        // Calculate piece bounds
        val minX = piece.x
        val maxX = piece.x + shape[0].size - 1
        val minY = piece.y
        val maxY = piece.y + shape.size - 1
        
        // Check if piece is within board bounds
        return minX >= 0 && maxX < board.width && minY >= 0 && maxY < board.height
    }
    
    /**
     * Get the shape of a piece based on its type and rotation.
     * Uses caching to avoid repeated allocations.
     */
    private fun getPieceShape(type: TetrisPieceType, rotation: Int): Array<BooleanArray> {
        val normalizedRotation = rotation % 4
        val key = Triple(type, normalizedRotation, 0)
        
        // Check cache
        return shapeCache.getOrPut(key) {
            when (type) {
                TetrisPieceType.I -> when (normalizedRotation) {
                    0 -> arrayOf(
                        booleanArrayOf(false, false, false, false),
                        booleanArrayOf(true, true, true, true),
                        booleanArrayOf(false, false, false, false),
                        booleanArrayOf(false, false, false, false)
                    )
                    1 -> arrayOf(
                        booleanArrayOf(false, false, true, false),
                        booleanArrayOf(false, false, true, false),
                        booleanArrayOf(false, false, true, false),
                        booleanArrayOf(false, false, true, false)
                    )
                    2 -> arrayOf(
                        booleanArrayOf(false, false, false, false),
                        booleanArrayOf(false, false, false, false),
                        booleanArrayOf(true, true, true, true),
                        booleanArrayOf(false, false, false, false)
                    )
                    else -> arrayOf(
                        booleanArrayOf(false, true, false, false),
                        booleanArrayOf(false, true, false, false),
                        booleanArrayOf(false, true, false, false),
                        booleanArrayOf(false, true, false, false)
                    )
                }
                TetrisPieceType.O -> arrayOf(
                    booleanArrayOf(true, true),
                    booleanArrayOf(true, true)
                )
                TetrisPieceType.T -> when (normalizedRotation) {
                    0 -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(false, false, false)
                    )
                    1 -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(false, true, false)
                    )
                    2 -> arrayOf(
                        booleanArrayOf(false, false, false),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(false, true, false)
                    )
                    else -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(true, true, false),
                        booleanArrayOf(false, true, false)
                    )
                }
                TetrisPieceType.J -> when (normalizedRotation) {
                    0 -> arrayOf(
                        booleanArrayOf(true, false, false),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(false, false, false)
                    )
                    1 -> arrayOf(
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, false)
                    )
                    2 -> arrayOf(
                        booleanArrayOf(false, false, false),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(false, false, true)
                    )
                    else -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(true, true, false)
                    )
                }
                TetrisPieceType.L -> when (normalizedRotation) {
                    0 -> arrayOf(
                        booleanArrayOf(false, false, true),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(false, false, false)
                    )
                    1 -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, true)
                    )
                    2 -> arrayOf(
                        booleanArrayOf(false, false, false),
                        booleanArrayOf(true, true, true),
                        booleanArrayOf(true, false, false)
                    )
                    else -> arrayOf(
                        booleanArrayOf(true, true, false),
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, false)
                    )
                }
                TetrisPieceType.S -> when (normalizedRotation) {
                    0, 2 -> arrayOf(
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(true, true, false),
                        booleanArrayOf(false, false, false)
                    )
                    else -> arrayOf(
                        booleanArrayOf(false, true, false),
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(false, false, true)
                    )
                }
                TetrisPieceType.Z -> when (normalizedRotation) {
                    0, 2 -> arrayOf(
                        booleanArrayOf(true, true, false),
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(false, false, false)
                    )
                    else -> arrayOf(
                        booleanArrayOf(false, false, true),
                        booleanArrayOf(false, true, true),
                        booleanArrayOf(false, true, false)
                    )
                }
            }
        }
    }
    
    /**
     * Clear caches when board state changes.
     */
    fun clearCaches() {
        componentCache.invalidateBoardDependentCaches()
    }
}