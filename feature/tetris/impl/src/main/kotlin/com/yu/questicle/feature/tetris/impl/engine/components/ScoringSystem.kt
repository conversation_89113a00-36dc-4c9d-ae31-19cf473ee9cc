package com.yu.questicle.feature.tetris.impl.engine.components

/**
 * Interface for different Tetris scoring systems.
 * Allows for flexible scoring rules based on game mode.
 */
interface ScoringSystem {
    /**
     * Calculate score for cleared lines
     */
    fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean = false,
        isPerfectClear: Boolean = false,
        combo: Int = 0,
        isBackToBack: Boolean = false
    ): Int
    
    /**
     * Calculate score for soft drop
     */
    fun calculateSoftDropScore(cellsDropped: Int): Int
    
    /**
     * Calculate score for hard drop
     */
    fun calculateHardDropScore(cellsDropped: Int): Int
    
    /**
     * Calculate level based on total lines cleared
     */
    fun calculateLevel(totalLines: Int): Int
    
    /**
     * Calculate drop interval based on level
     */
    fun calculateDropInterval(level: Int): Long
    
    /**
     * Calculate combo multiplier
     */
    fun calculateComboMultiplier(consecutiveClears: Int): Float
    
    /**
     * Get name of the scoring system
     */
    fun getName(): String
    
    /**
     * Get description of the scoring system
     */
    fun getDescription(): String
}

/**
 * Classic Tetris scoring system based on NES Tetris rules.
 */
class ClassicScoringSystem : ScoringSystem {
    
    companion object {
        // Standard Tetris scoring values
        private val LINE_CLEAR_SCORES = mapOf(
            1 to 40,    // Single
            2 to 100,   // Double
            3 to 300,   // Triple
            4 to 1200   // Tetris
        )
        
        // Maximum level
        private const val MAX_LEVEL = 20
        
        // Lines per level
        private const val LINES_PER_LEVEL = 10
        
        // Base drop interval in milliseconds
        private const val BASE_DROP_INTERVAL = 800L
        
        // Minimum drop interval in milliseconds
        private const val MIN_DROP_INTERVAL = 80L
    }
    
    override fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        combo: Int,
        isBackToBack: Boolean
    ): Int {
        // Classic scoring doesn't have T-spin, perfect clear, combo, or back-to-back bonuses
        val baseScore = LINE_CLEAR_SCORES[linesCleared] ?: 0
        return baseScore * (level + 1)
    }
    
    override fun calculateSoftDropScore(cellsDropped: Int): Int {
        // Classic scoring gives 1 point per cell for soft drop
        return cellsDropped
    }
    
    override fun calculateHardDropScore(cellsDropped: Int): Int {
        // Classic scoring doesn't have hard drop points
        return 0
    }
    
    override fun calculateLevel(totalLines: Int): Int {
        return minOf(totalLines / LINES_PER_LEVEL, MAX_LEVEL)
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // Classic formula: frames = 48 - 5 * level (converted to milliseconds)
        val frames = maxOf(48 - 5 * level, 1)
        return (frames * 16.67).toLong() // 60 FPS = 16.67ms per frame
    }
    
    override fun calculateComboMultiplier(consecutiveClears: Int): Float {
        // Classic scoring doesn't have combo multipliers
        return 1.0f
    }
    
    override fun getName(): String = "Classic"
    
    override fun getDescription(): String = "Original NES Tetris scoring system"
}

/**
 * Modern Tetris scoring system based on Tetris Guideline rules.
 */
class ModernScoringSystem : ScoringSystem {
    
    companion object {
        // Modern Tetris scoring values
        private val LINE_CLEAR_SCORES = mapOf(
            1 to 100,  // Single
            2 to 300,  // Double
            3 to 500,  // Triple
            4 to 800   // Tetris
        )
        
        // T-Spin bonus multipliers
        private val T_SPIN_MULTIPLIERS = mapOf(
            1 to 2.0f,  // T-Spin Single
            2 to 2.5f,  // T-Spin Double
            3 to 3.0f   // T-Spin Triple
        )
        
        // Perfect clear bonus
        private const val PERFECT_CLEAR_BONUS = 1000
        
        // Back-to-back bonus multiplier
        private const val BACK_TO_BACK_MULTIPLIER = 1.5f
        
        // Maximum level
        private const val MAX_LEVEL = 20
        
        // Lines per level
        private const val LINES_PER_LEVEL = 10
        
        // Base drop interval in milliseconds
        private const val BASE_DROP_INTERVAL = 1000L
        
        // Minimum drop interval in milliseconds
        private const val MIN_DROP_INTERVAL = 100L
        
        // Soft drop points per cell
        private const val SOFT_DROP_POINTS = 1
        
        // Hard drop points per cell
        private const val HARD_DROP_POINTS = 2
    }
    
    override fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        combo: Int,
        isBackToBack: Boolean
    ): Int {
        // Base score based on lines cleared
        val baseScore = LINE_CLEAR_SCORES[linesCleared] ?: 0
        
        // Apply level multiplier
        var score = baseScore * (level + 1)
        
        // Apply T-Spin bonus if applicable
        if (isTSpin) {
            val tSpinMultiplier = T_SPIN_MULTIPLIERS[linesCleared] ?: 1.0f
            score = (score * tSpinMultiplier).toInt()
        }
        
        // Apply back-to-back bonus if applicable
        if (isBackToBack && (linesCleared == 4 || isTSpin)) {
            score = (score * BACK_TO_BACK_MULTIPLIER).toInt()
        }
        
        // Apply perfect clear bonus if applicable
        if (isPerfectClear) {
            score += PERFECT_CLEAR_BONUS * (level + 1)
        }
        
        // Apply combo multiplier
        if (combo > 1) {
            val comboMultiplier = calculateComboMultiplier(combo)
            score = (score * comboMultiplier).toInt()
        }
        
        return score
    }
    
    override fun calculateSoftDropScore(cellsDropped: Int): Int {
        return cellsDropped * SOFT_DROP_POINTS
    }
    
    override fun calculateHardDropScore(cellsDropped: Int): Int {
        return cellsDropped * HARD_DROP_POINTS
    }
    
    override fun calculateLevel(totalLines: Int): Int {
        return minOf(totalLines / LINES_PER_LEVEL, MAX_LEVEL)
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // Modern formula: BASE_INTERVAL * (0.8^level)
        val factor = Math.pow(0.8, level.toDouble())
        val interval = (BASE_DROP_INTERVAL * factor).toLong()
        return maxOf(interval, MIN_DROP_INTERVAL)
    }
    
    override fun calculateComboMultiplier(consecutiveClears: Int): Float {
        return when {
            consecutiveClears <= 1 -> 1.0f
            consecutiveClears == 2 -> 1.1f
            consecutiveClears == 3 -> 1.2f
            consecutiveClears == 4 -> 1.3f
            consecutiveClears == 5 -> 1.4f
            consecutiveClears >= 6 -> 1.5f
            else -> 1.0f
        }
    }
    
    override fun getName(): String = "Modern"
    
    override fun getDescription(): String = "Tetris Guideline scoring system with T-Spin and combo bonuses"
}

/**
 * Competitive Tetris scoring system optimized for multiplayer and tournaments.
 */
class CompetitiveScoringSystem : ScoringSystem {
    
    companion object {
        // Competitive Tetris scoring values
        private val LINE_CLEAR_SCORES = mapOf(
            1 to 100,  // Single
            2 to 300,  // Double
            3 to 500,  // Triple
            4 to 800   // Tetris
        )
        
        // T-Spin bonus multipliers (higher for competitive)
        private val T_SPIN_MULTIPLIERS = mapOf(
            1 to 2.0f,  // T-Spin Single
            2 to 3.0f,  // T-Spin Double
            3 to 4.0f   // T-Spin Triple
        )
        
        // Perfect clear bonus
        private const val PERFECT_CLEAR_BONUS = 2000
        
        // Back-to-back bonus multiplier
        private const val BACK_TO_BACK_MULTIPLIER = 1.75f
        
        // Maximum level
        private const val MAX_LEVEL = 30
        
        // Lines per level
        private const val LINES_PER_LEVEL = 10
        
        // Base drop interval in milliseconds
        private const val BASE_DROP_INTERVAL = 1000L
        
        // Minimum drop interval in milliseconds
        private const val MIN_DROP_INTERVAL = 50L
        
        // Soft drop points per cell
        private const val SOFT_DROP_POINTS = 1
        
        // Hard drop points per cell
        private const val HARD_DROP_POINTS = 2
    }
    
    override fun calculateLineClearScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean,
        isPerfectClear: Boolean,
        combo: Int,
        isBackToBack: Boolean
    ): Int {
        // Base score based on lines cleared
        val baseScore = LINE_CLEAR_SCORES[linesCleared] ?: 0
        
        // Apply level multiplier
        var score = baseScore * (level + 1)
        
        // Apply T-Spin bonus if applicable
        if (isTSpin) {
            val tSpinMultiplier = T_SPIN_MULTIPLIERS[linesCleared] ?: 1.0f
            score = (score * tSpinMultiplier).toInt()
        }
        
        // Apply back-to-back bonus if applicable
        if (isBackToBack && (linesCleared == 4 || isTSpin)) {
            score = (score * BACK_TO_BACK_MULTIPLIER).toInt()
        }
        
        // Apply perfect clear bonus if applicable
        if (isPerfectClear) {
            score += PERFECT_CLEAR_BONUS * (level + 1)
        }
        
        // Apply combo multiplier (higher for competitive)
        if (combo > 1) {
            val comboMultiplier = calculateComboMultiplier(combo)
            score = (score * comboMultiplier).toInt()
        }
        
        return score
    }
    
    override fun calculateSoftDropScore(cellsDropped: Int): Int {
        return cellsDropped * SOFT_DROP_POINTS
    }
    
    override fun calculateHardDropScore(cellsDropped: Int): Int {
        return cellsDropped * HARD_DROP_POINTS
    }
    
    override fun calculateLevel(totalLines: Int): Int {
        return minOf(totalLines / LINES_PER_LEVEL, MAX_LEVEL)
    }
    
    override fun calculateDropInterval(level: Int): Long {
        // Competitive formula: faster drop speed
        val factor = Math.pow(0.75, level.toDouble())
        val interval = (BASE_DROP_INTERVAL * factor).toLong()
        return maxOf(interval, MIN_DROP_INTERVAL)
    }
    
    override fun calculateComboMultiplier(consecutiveClears: Int): Float {
        // Higher combo multipliers for competitive
        return when {
            consecutiveClears <= 1 -> 1.0f
            consecutiveClears == 2 -> 1.2f
            consecutiveClears == 3 -> 1.4f
            consecutiveClears == 4 -> 1.6f
            consecutiveClears == 5 -> 1.8f
            consecutiveClears >= 6 -> 2.0f
            else -> 1.0f
        }
    }
    
    override fun getName(): String = "Competitive"
    
    override fun getDescription(): String = "Tournament-optimized scoring system with enhanced bonuses"
}