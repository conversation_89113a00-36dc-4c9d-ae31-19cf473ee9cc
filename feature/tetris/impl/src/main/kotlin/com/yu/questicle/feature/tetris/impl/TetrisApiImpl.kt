package com.yu.questicle.feature.tetris.impl

import com.yu.questicle.feature.tetris.api.TetrisApi
import com.yu.questicle.feature.tetris.api.TetrisController
import com.yu.questicle.feature.tetris.impl.controller.TetrisControllerImpl
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of TetrisApi
 *
 * This is a simple implementation that provides access to the TetrisController.
 * UI components are handled separately in the UI layer.
 */
@Singleton
class TetrisApiImpl @Inject constructor(
    private val tetrisController: TetrisControllerImpl
) : TetrisApi {

    override fun getTetrisController(): TetrisController = tetrisController
}
