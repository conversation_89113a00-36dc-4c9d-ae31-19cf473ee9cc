package com.yu.questicle.feature.tetris.impl

import com.yu.questicle.feature.tetris.api.TetrisApi
import com.yu.questicle.feature.tetris.api.TetrisController
import com.yu.questicle.feature.tetris.impl.controller.TetrisControllerImpl
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tetris API 简化实现
 *
 * 提供对 TetrisController 的访问
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Singleton
class TetrisApiImpl @Inject constructor(
    private val tetrisController: TetrisControllerImpl
) : TetrisApi {

    override fun getTetrisController(): TetrisController = tetrisController
}