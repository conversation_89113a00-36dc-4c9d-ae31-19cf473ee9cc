package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class TetrisPieceGeneratorTest {

    private lateinit var generator: TetrisPieceGenerator

    @BeforeEach
    fun setup() {
        generator = TetrisPieceGenerator()
    }

    @Test
    fun `should generate all piece types in 7-bag`() {
        val generatedTypes = mutableSetOf<TetrisPieceType>()
        
        // Generate 7 pieces (one full bag)
        repeat(7) {
            val piece = generator.generateNext()
            generatedTypes.add(piece.type)
        }
        
        // Should have all 7 types
        assertEquals(7, generatedTypes.size)
        assertTrue(generatedTypes.containsAll(TetrisPieceType.values().toList()))
    }

    @Test
    fun `should place pieces at correct starting positions`() {
        val iPiece = generator.generateSpecific(TetrisPieceType.I)
        val oPiece = generator.generateSpecific(TetrisPieceType.O)
        val tPiece = generator.generateSpecific(TetrisPieceType.T)
        
        assertEquals(3, iPiece.x)
        assertEquals(4, oPiece.x)
        assertEquals(3, tPiece.x)
        
        // All pieces start at top
        assertEquals(0, iPiece.y)
        assertEquals(0, oPiece.y)
        assertEquals(0, tPiece.y)
    }

    @Test
    fun `should generate pieces with zero rotation`() {
        repeat(10) {
            val piece = generator.generateNext()
            assertEquals(0, piece.rotation)
        }
    }
}
