package com.yu.questicle.feature.tetris.impl.engine

import com.yu.questicle.core.domain.model.tetris.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class TetrisCollisionDetectorTest {

    private lateinit var detector: TetrisCollisionDetector
    private lateinit var emptyBoard: TetrisBoard

    @BeforeEach
    fun setup() {
        detector = TetrisCollisionDetector()
        emptyBoard = TetrisBoard.empty()
    }

    @Test
    fun `should allow valid piece placement`() {
        val piece = TetrisPiece(TetrisPieceType.O, 4, 0, 0)
        assertTrue(detector.isValidPosition(piece, emptyBoard))
    }

    @Test
    fun `should reject piece outside left boundary`() {
        val piece = TetrisPiece(TetrisPieceType.O, -1, 0, 0)
        assertFalse(detector.isValidPosition(piece, emptyBoard))
    }

    @Test
    fun `should reject piece outside right boundary`() {
        val piece = TetrisPiece(TetrisPieceType.O, 9, 0, 0)
        assertFalse(detector.isValidPosition(piece, emptyBoard))
    }

    @Test
    fun `should reject piece outside bottom boundary`() {
        val piece = TetrisPiece(TetrisPieceType.O, 4, 19, 0)
        assertFalse(detector.isValidPosition(piece, emptyBoard))
    }

    @Test
    fun `should allow valid movement`() {
        val piece = TetrisPiece(TetrisPieceType.O, 4, 0, 0)
        assertTrue(detector.canMove(piece, 1, 0, emptyBoard)) // Right
        assertTrue(detector.canMove(piece, -1, 0, emptyBoard)) // Left
        assertTrue(detector.canMove(piece, 0, 1, emptyBoard)) // Down
    }

    @Test
    fun `should reject invalid movement`() {
        val piece = TetrisPiece(TetrisPieceType.O, 0, 0, 0)
        assertFalse(detector.canMove(piece, -1, 0, emptyBoard)) // Left boundary
        
        val rightPiece = TetrisPiece(TetrisPieceType.O, 8, 0, 0)
        assertFalse(detector.canMove(rightPiece, 1, 0, emptyBoard)) // Right boundary
    }

    @Test
    fun `should allow rotation in empty space`() {
        val piece = TetrisPiece(TetrisPieceType.T, 4, 4, 0)
        assertTrue(detector.canRotate(piece, emptyBoard))
    }

    @Test
    fun `should calculate drop position correctly`() {
        val piece = TetrisPiece(TetrisPieceType.O, 4, 0, 0)
        val dropPiece = detector.calculateDropPosition(piece, emptyBoard)
        
        // O piece should drop to y=18 (bottom - 1 for O piece height)
        assertTrue(dropPiece.y >= 18)
    }

    @Test
    fun `should detect game over when piece cannot be placed`() {
        val piece = TetrisPiece(TetrisPieceType.O, 4, -1, 0) // Above board
        assertTrue(detector.isGameOver(piece, emptyBoard))
    }
}
