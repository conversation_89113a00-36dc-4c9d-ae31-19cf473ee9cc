package com.yu.questicle.feature.user.impl.controller

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.usecase.user.AuthUseCase
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.feature.user.api.UserController
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.SharingStarted
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户管理控制器实现
 * 实现用户认证、注册、资料管理等功能
 */
@Singleton
class UserControllerImpl @Inject constructor(
    private val authUseCase: AuthUseCase,
    private val userRepository: UserRepository
) : UserController {

    private val scope = CoroutineScope(SupervisorJob())
    private val logger: QLogger = logger()

    private val _isLoading = MutableStateFlow(false)
    override val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    override val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    override val currentUser: StateFlow<User?> = userRepository.getCurrentUser()
        .stateIn(scope, SharingStarted.Eagerly, null)

    override val isLoggedIn: StateFlow<Boolean> = currentUser.map { user ->
        user?.isRegisteredUser() == true
    }.stateIn(scope, SharingStarted.Eagerly, false)
    
    override suspend fun loginAsGuest(): Result<User> {
        logger.i("开始游客登录")
        _isLoading.value = true
        _errorMessage.value = null

        return try {
            val result = authUseCase.loginAsGuest()
            when (result) {
                is Result.Success -> {
                    try {
                        // 设置当前用户
                        val setUserResult = userRepository.setCurrentUser(result.data)
                        when (setUserResult) {
                            is Result.Success -> {
                                _isLoading.value = false
                                logger.i("游客登录成功", mapOf(
                                    "userId" to result.data.id,
                                    "username" to result.data.username,
                                    "displayName" to result.data.displayName
                                ))
                                result
                            }
                            is Result.Error -> {
                                _isLoading.value = false
                                _errorMessage.value = "登录状态保存失败"
                                logger.e(setUserResult.exception, "设置当前用户失败")
                                setUserResult
                            }
                            else -> {
                                _isLoading.value = false
                                result
                            }
                        }
                    } catch (e: Exception) {
                        _isLoading.value = false
                        _errorMessage.value = "登录状态保存失败"
                        logger.e(e, "设置用户状态异常")
                        Result.Error(BusinessException("登录状态保存失败", e))
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.getUserFriendlyMessage()
                    logger.e(result.exception, "游客登录失败", mapOf(
                        "errorCode" to result.exception.errorCode,
                        "errorType" to result.exception.errorType.name
                    ))
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            val businessException = BusinessException("游客登录异常", e)
            _errorMessage.value = businessException.getUserFriendlyMessage()
            logger.e(e, "游客登录发生未预期异常")
            Result.Error(businessException)
        }
    }
    
    override suspend fun loginWithUsername(username: String, password: String): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val result = authUseCase.loginWithUsername(username, password)
            when (result) {
                is Result.Success -> {
                    try {
                        // 设置当前用户
                        val setUserResult = userRepository.setCurrentUser(result.data)
                        when (setUserResult) {
                            is Result.Success -> {
                                _isLoading.value = false
                                logger.i("用户名登录成功", mapOf(
                                    "userId" to result.data.id,
                                    "username" to result.data.username
                                ))
                                result
                            }
                            is Result.Error -> {
                                _isLoading.value = false
                                _errorMessage.value = "登录状态保存失败"
                                logger.e(setUserResult.exception, "设置当前用户失败")
                                setUserResult
                            }
                            else -> {
                                _isLoading.value = false
                                result
                            }
                        }
                    } catch (e: Exception) {
                        _isLoading.value = false
                        _errorMessage.value = "登录状态保存失败"
                        logger.e(e, "设置用户状态异常")
                        Result.Error(BusinessException("登录状态保存失败", e))
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun loginWithEmail(email: String, password: String): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val result = authUseCase.loginWithEmail(email, password)
            when (result) {
                is Result.Success -> {
                    try {
                        // 设置当前用户
                        val setUserResult = userRepository.setCurrentUser(result.data)
                        when (setUserResult) {
                            is Result.Success -> {
                                _isLoading.value = false
                                logger.i("邮箱登录成功", mapOf(
                                    "userId" to result.data.id,
                                    "email" to result.data.email
                                ))
                                result
                            }
                            is Result.Error -> {
                                _isLoading.value = false
                                _errorMessage.value = "登录状态保存失败"
                                logger.e(setUserResult.exception, "设置当前用户失败")
                                setUserResult
                            }
                            else -> {
                                _isLoading.value = false
                                result
                            }
                        }
                    } catch (e: Exception) {
                        _isLoading.value = false
                        _errorMessage.value = "登录状态保存失败"
                        logger.e(e, "设置用户状态异常")
                        Result.Error(BusinessException("登录状态保存失败", e))
                    }
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun registerUser(
        username: String,
        email: String?,
        password: String,
        passwordConfirmation: String
    ): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val result = authUseCase.registerUser(
                username = username,
                email = email,
                password = password,
                passwordConfirmation = passwordConfirmation
            )
            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun logout(): Result<Unit> {
        _isLoading.value = true
        _errorMessage.value = null

        return try {
            val result = authUseCase.logout()
            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun updateProfile(displayName: String?, email: String?): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val currentUserValue = currentUser.first()
            if (currentUserValue == null) {
                _isLoading.value = false
                _errorMessage.value = "用户未登录"
                return Result.Error(BusinessException("用户未登录"))
            }
            
            val updatedUser = currentUserValue.copy(
                displayName = displayName ?: currentUserValue.displayName,
                email = email ?: currentUserValue.email
            )
            
            val result = userRepository.saveUser(updatedUser)
            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(updatedUser)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun updatePassword(
        currentPassword: String,
        newPassword: String,
        confirmPassword: String
    ): Result<Unit> {
        _isLoading.value = true
        _errorMessage.value = null

        return try {
            // 验证输入参数
            if (currentPassword.isBlank()) {
                _isLoading.value = false
                _errorMessage.value = "当前密码不能为空"
                return Result.Error(BusinessException("当前密码不能为空"))
            }

            if (newPassword.isBlank()) {
                _isLoading.value = false
                _errorMessage.value = "新密码不能为空"
                return Result.Error(BusinessException("新密码不能为空"))
            }

            if (newPassword != confirmPassword) {
                _isLoading.value = false
                _errorMessage.value = "新密码和确认密码不匹配"
                return Result.Error(BusinessException("新密码和确认密码不匹配"))
            }

            if (newPassword.length < 6) {
                _isLoading.value = false
                _errorMessage.value = "新密码长度不能少于6位"
                return Result.Error(BusinessException("新密码长度不能少于6位"))
            }

            val currentUserValue = currentUser.first()
            if (currentUserValue == null) {
                _isLoading.value = false
                _errorMessage.value = "用户未登录"
                return Result.Error(BusinessException("用户未登录"))
            }

            // 使用AuthUseCase更新密码
            val result = authUseCase.updatePassword(
                userId = currentUserValue.id,
                currentPassword = currentPassword,
                newPassword = newPassword
            )

            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun uploadAvatar(avatarUrl: String): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val currentUserValue = currentUser.first()
            if (currentUserValue == null) {
                _isLoading.value = false
                _errorMessage.value = "用户未登录"
                return Result.Error(BusinessException("用户未登录"))
            }
            
            val updatedUser = currentUserValue.copy(avatarUrl = avatarUrl)
            val result = userRepository.saveUser(updatedUser)
            
            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    Result.Success(updatedUser)
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override suspend fun upgradeFromGuest(
        username: String,
        email: String?,
        password: String,
        passwordConfirmation: String
    ): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val result = authUseCase.upgradeFromGuest(
                username = username,
                email = email,
                password = password,
                passwordConfirmation = passwordConfirmation
            )
            when (result) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message
                    result
                }
                is Result.Loading -> {
                    result
                }
            }
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
    
    override fun clearError() {
        _errorMessage.value = null
    }
    
    override suspend fun refreshUserData(): Result<User> {
        _isLoading.value = true
        _errorMessage.value = null
        
        return try {
            val currentUserValue = currentUser.first()
            if (currentUserValue == null) {
                _isLoading.value = false
                _errorMessage.value = "用户未登录"
                return Result.Error(BusinessException("用户未登录"))
            }
            
            val result = userRepository.getUserById(currentUserValue.id)
            _isLoading.value = false
            result
        } catch (e: Exception) {
            _isLoading.value = false
            _errorMessage.value = e.message
            Result.Error(BusinessException(e.message ?: "Unknown error", e))
        }
    }
}
