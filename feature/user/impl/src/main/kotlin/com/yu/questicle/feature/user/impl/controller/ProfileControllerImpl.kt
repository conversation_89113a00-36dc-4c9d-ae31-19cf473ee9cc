package com.yu.questicle.feature.user.impl.controller

import android.net.Uri
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.exception.ProfileUpdateException
import com.yu.questicle.core.domain.exception.ProfileValidationException
import com.yu.questicle.core.domain.exception.AvatarUploadException
import com.yu.questicle.core.domain.exception.PasswordResetException
import com.yu.questicle.core.domain.model.Gender
import com.yu.questicle.core.domain.model.ProfileUpdateRequest
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.ValidationError
import com.yu.questicle.core.domain.repository.ProfileRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.validation.ValidationResult
import com.yu.questicle.feature.user.api.AvatarUploadState
import com.yu.questicle.feature.user.api.PasswordResetController
import com.yu.questicle.feature.user.api.PasswordResetStep
import com.yu.questicle.feature.user.api.ProfileController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.stateIn
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.regex.Pattern
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户资料管理控制器实现
 * 严格遵循项目Controller规范和异常处理规范
 */
@Singleton
class ProfileControllerImpl @Inject constructor(
    private val userRepository: UserRepository,
    private val profileRepository: ProfileRepository
) : ProfileController, PasswordResetController {

    private val qLogger: QLogger = logger("ProfileController")
    
    private val _isLoading = MutableStateFlow(false)
    override val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    override val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    private val _resetStep = MutableStateFlow(PasswordResetStep.EMAIL_INPUT)
    override val resetStep: StateFlow<PasswordResetStep> = _resetStep.asStateFlow()
    
    private val _avatarUploadState = MutableStateFlow<AvatarUploadState>(AvatarUploadState.Idle)
    override val avatarUploadState: StateFlow<AvatarUploadState> = _avatarUploadState.asStateFlow()
    
    override val currentUser: StateFlow<User?> = userRepository.getCurrentUser()
        .stateIn(
            scope = CoroutineScope(SupervisorJob() + Dispatchers.IO),
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    override suspend fun updateProfile(
        displayName: String?,
        email: String?,
        bio: String?,
        birthday: String?,
        gender: Gender?
    ): Result<User> {
        return try {
            _isLoading.value = true
            _errorMessage.value = null
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser().first()
            if (currentUser == null) {
                _isLoading.value = false
                return Result.Error(IllegalStateException("用户未登录").toQuesticleException())
            }
            
            val request = ProfileUpdateRequest(
                displayName = displayName,
                email = email,
                bio = bio,
                birthday = birthday,
                gender = gender
            )
            
            when (val result = profileRepository.updateProfile(currentUser.id, request)) {
                is Result.Success -> {
                    _isLoading.value = false
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message ?: "更新失败"
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("更新处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "更新失败: ${e.message}"
            _isLoading.value = false
            _errorMessage.value = errorMessage
            qLogger.e(e, "更新资料失败")
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun uploadAvatar(imageUri: Uri): Result<String> {
        return try {
            _avatarUploadState.value = AvatarUploadState.Processing(0f)
            _errorMessage.value = null
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser().first()
            if (currentUser == null) {
                _avatarUploadState.value = AvatarUploadState.Error("用户未登录")
                return Result.Error(IllegalStateException("用户未登录").toQuesticleException())
            }
            
            // 验证图片
            val validationResult = validateImage(imageUri)
            if (!validationResult.isValid) {
                val errorMessage = "图片验证失败"
                _avatarUploadState.value = AvatarUploadState.Error(errorMessage)
                return Result.Error(IllegalArgumentException(errorMessage).toQuesticleException())
            }
            
            _avatarUploadState.value = AvatarUploadState.Uploading(0.5f)
            
            when (val result = profileRepository.uploadAvatar(currentUser.id, imageUri)) {
                is Result.Success -> {
                    val avatarUrl = result.data.avatarUrl
                    _avatarUploadState.value = AvatarUploadState.Success(avatarUrl)
                    Result.Success(avatarUrl)
                }
                is Result.Error -> {
                    val errorMessage = result.exception.message ?: "上传失败"
                    _avatarUploadState.value = AvatarUploadState.Error(errorMessage)
                    _errorMessage.value = errorMessage
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("上传处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "上传失败: ${e.message}"
            _avatarUploadState.value = AvatarUploadState.Error(errorMessage)
            _errorMessage.value = errorMessage
            qLogger.e(e, "上传头像失败")
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun deleteAvatar(): Result<Unit> {
        return try {
            _isLoading.value = true
            _errorMessage.value = null
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser().first()
            if (currentUser == null) {
                _isLoading.value = false
                return Result.Error(IllegalStateException("用户未登录").toQuesticleException())
            }
            
            when (val result = profileRepository.deleteAvatar(currentUser.id)) {
                is Result.Success -> {
                    _isLoading.value = false
                    _avatarUploadState.value = AvatarUploadState.Idle
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message ?: "删除失败"
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("删除处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "删除失败: ${e.message}"
            _isLoading.value = false
            _errorMessage.value = errorMessage
            qLogger.e(e, "删除头像失败")
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 验证图片文件
     */
    override fun validateImage(uri: Uri): ValidationResult {
        // 简单的验证逻辑
        return ValidationResult.Valid
    }

    /**
     * 重置头像上传状态
     */
    override fun resetAvatarUploadState() {
        _avatarUploadState.value = AvatarUploadState.Idle
    }
    
    override fun clearError() {
        _errorMessage.value = null
    }
    
    // PasswordResetController 实现
    override suspend fun requestPasswordReset(email: String): Result<Unit> {
        return try {
            _isLoading.value = true
            _errorMessage.value = null
            
            when (val result = profileRepository.requestPasswordReset(email)) {
                is Result.Success -> {
                    _isLoading.value = false
                    _resetStep.value = PasswordResetStep.EMAIL_SENT
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message ?: "请求失败"
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("请求处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "请求失败: ${e.message}"
            _isLoading.value = false
            _errorMessage.value = errorMessage
            qLogger.e(e, "密码重置请求失败")
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun confirmPasswordReset(
        token: String,
        newPassword: String,
        confirmPassword: String
    ): Result<Unit> {
        return try {
            _isLoading.value = true
            _errorMessage.value = null
            
            when (val result = profileRepository.confirmPasswordReset(token, newPassword, confirmPassword)) {
                is Result.Success -> {
                    _isLoading.value = false
                    _resetStep.value = PasswordResetStep.SUCCESS
                    result
                }
                is Result.Error -> {
                    _isLoading.value = false
                    _errorMessage.value = result.exception.message ?: "重置失败"
                    result
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("重置处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "重置失败: ${e.message}"
            _isLoading.value = false
            _errorMessage.value = errorMessage
            qLogger.e(e, "密码重置确认失败")
            Result.Error(e.toQuesticleException())
        }
    }
    
    override fun resetFlow() {
        _resetStep.value = PasswordResetStep.EMAIL_INPUT
        _errorMessage.value = null
    }

    /**
     * 验证用户资料输入
     * 遵循项目验证规范，抛出具体的验证异常
     */
    private fun validateProfileInput(
        displayName: String?,
        email: String?,
        bio: String?,
        birthday: String?
    ) {
        val errors = mutableListOf<String>()

        // 验证显示名称
        displayName?.let { name ->
            if (name.trim().length < 2) {
                errors.add("显示名称至少需要2个字符")
            }
            if (name.trim().length > 50) {
                errors.add("显示名称不能超过50个字符")
            }
        }

        // 验证邮箱
        email?.let { emailAddress ->
            if (emailAddress.isNotBlank()) {
                val emailPattern = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")
                if (!emailPattern.matcher(emailAddress).matches()) {
                    errors.add("邮箱格式不正确")
                }
            }
        }

        // 验证个人简介
        bio?.let { bioText ->
            if (bioText.trim().length > 500) {
                errors.add("个人简介不能超过500个字符")
            }
        }

        // 验证生日
        birthday?.let { birthdayStr ->
            if (birthdayStr.isNotBlank()) {
                try {
                    val date = LocalDate.parse(birthdayStr, DateTimeFormatter.ISO_LOCAL_DATE)
                    val today = LocalDate.now()
                    if (date.isAfter(today)) {
                        errors.add("生日不能是未来日期")
                    }
                    if (date.isBefore(today.minusYears(120))) {
                        errors.add("生日日期不合理")
                    }
                } catch (e: DateTimeParseException) {
                    errors.add("生日格式不正确，请使用YYYY-MM-DD格式")
                }
            }
        }

        if (errors.isNotEmpty()) {
            throw ProfileValidationException(
                message = "Profile validation failed",
                errorCode = "PROFILE_VALIDATION_FAILED",
                field = "profile",
                value = errors.joinToString(", ")
            )
        }
    }
}


