package com.yu.questicle.feature.user.impl.controller

import android.content.Context
import android.net.Uri
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.common.result.Result as QuesticleResult
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.exception.AvatarUploadException
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.repository.ProfileRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.validation.ValidationResult
import com.yu.questicle.core.domain.validation.validateImageType
import com.yu.questicle.core.domain.validation.validateFileSize
import com.yu.questicle.feature.user.api.AvatarController
import com.yu.questicle.feature.user.api.AvatarUploadState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 头像上传控制器实现
 */
@Singleton
class AvatarControllerImpl @Inject constructor(
    private val userRepository: UserRepository,
    private val profileRepository: ProfileRepository
) : AvatarController {

    private val qLogger: QLogger = logger("AvatarController")

    private val _uploadState = MutableStateFlow<AvatarUploadState>(AvatarUploadState.Idle)
    override val uploadState: StateFlow<AvatarUploadState> = _uploadState.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    override val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    override val currentUser: StateFlow<User?> = userRepository.getCurrentUser() as StateFlow<User?>

    override suspend fun uploadAvatar(imageUri: Uri): QuesticleResult<String> {
        return try {
            _uploadState.value = AvatarUploadState.Processing(0f)
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser().first()
            if (currentUser == null) {
                _uploadState.value = AvatarUploadState.Error("用户未登录")
                return QuesticleResult.Error(IllegalStateException("用户未登录").toQuesticleException())
            }
            
            // 验证图片
            val validationResult = validateImage(imageUri)
            if (validationResult is ValidationResult.Invalid) {
                val errorMessage = validationResult.errors.firstOrNull() ?: "图片验证失败"
                _uploadState.value = AvatarUploadState.Error(errorMessage)
                return QuesticleResult.Error(IllegalArgumentException(errorMessage).toQuesticleException())
            }
            
            _uploadState.value = AvatarUploadState.Uploading(0.5f)
            
            // 上传头像
            when (val result = profileRepository.uploadAvatar(currentUser.id, imageUri)) {
                is QuesticleResult.Success -> {
                    _uploadState.value = AvatarUploadState.Success(result.data.avatarUrl)
                    QuesticleResult.Success(result.data.avatarUrl)
                }
                is QuesticleResult.Error -> {
                    _uploadState.value = AvatarUploadState.Error(result.exception.message ?: "上传失败")
                    result
                }
                is QuesticleResult.Loading -> {
                    _uploadState.value = AvatarUploadState.Uploading(0.8f)
                    QuesticleResult.Error(IllegalStateException("上传处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "上传失败: ${e.message}"
            _uploadState.value = AvatarUploadState.Error(errorMessage)
            _errorMessage.value = errorMessage
            qLogger.e(e, "上传头像失败")
            QuesticleResult.Error(e.toQuesticleException())
        }
    }

    override suspend fun deleteAvatar(): QuesticleResult<Unit> {
        return try {
            _uploadState.value = AvatarUploadState.Processing(0f)
            
            // 获取当前用户
            val currentUser = userRepository.getCurrentUser().first()
            if (currentUser == null) {
                _uploadState.value = AvatarUploadState.Error("用户未登录")
                return QuesticleResult.Error(IllegalStateException("用户未登录").toQuesticleException())
            }
            
            // 删除头像
            when (val result = profileRepository.deleteAvatar(currentUser.id)) {
                is QuesticleResult.Success -> {
                    _uploadState.value = AvatarUploadState.Success("")
                    QuesticleResult.Success(Unit)
                }
                is QuesticleResult.Error -> {
                    _uploadState.value = AvatarUploadState.Error(result.exception.message ?: "删除失败")
                    result
                }
                is QuesticleResult.Loading -> {
                    _uploadState.value = AvatarUploadState.Processing(0.5f)
                    QuesticleResult.Error(IllegalStateException("删除处理中").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            val errorMessage = "删除失败: ${e.message}"
            _uploadState.value = AvatarUploadState.Error(errorMessage)
            _errorMessage.value = errorMessage
            qLogger.e(e, "删除头像失败")
            QuesticleResult.Error(e.toQuesticleException())
        }
    }

    override fun validateImage(uri: Uri): ValidationResult {
        return try {
            // 基本URI验证
            if (uri.toString().isBlank()) {
                return ValidationResult.Invalid(listOf("图片URI不能为空"))
            }
            
            // 检查URI scheme
            if (uri.scheme != "content" && uri.scheme != "file") {
                return ValidationResult.Invalid(listOf("不支持的文件类型"))
            }
            
            ValidationResult.Valid
        } catch (e: Exception) {
            ValidationResult.Invalid(listOf("验证失败: ${e.message}"))
        }
    }

    override fun clearError() {
        _errorMessage.value = null
    }

    override fun resetUploadState() {
        _uploadState.value = AvatarUploadState.Idle
    }
} 