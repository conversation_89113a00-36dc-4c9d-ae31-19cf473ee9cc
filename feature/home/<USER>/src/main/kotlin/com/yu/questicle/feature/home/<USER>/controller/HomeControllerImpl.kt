package com.yu.questicle.feature.home.impl.controller

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.service.AchievementService
import com.yu.questicle.core.domain.service.MembershipService
import com.yu.questicle.feature.home.api.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HomeControllerImpl @Inject constructor(
    private val userRepository: UserRepository,
    private val achievementService: AchievementService,
    private val membershipService: MembershipService
) : HomeController {

    private val logger: QLogger = logger("HomeController")

    private val _isInitialized = MutableStateFlow(false)
    
    override val currentUser: Flow<User?> = userRepository.getCurrentUser()
    
    override val availableGames: Flow<List<GameInfo>> = MutableStateFlow(
        listOf(
            GameInfo(
                type = GameType.TETRIS,
                name = "俄罗斯方块",
                description = "经典的俄罗斯方块游戏",
                isAvailable = true
            )
        )
    )
    
    override val recentGames: Flow<List<RecentGameInfo>> = MutableStateFlow(emptyList())
    
    override val userStats: Flow<UserStatsInfo> = currentUser.map { user ->
        if (user != null) {
            UserStatsInfo(
                totalGames = user.stats.totalGames,
                totalScore = user.stats.totalScore,
                totalPlayTime = user.stats.totalPlayTime,
                favoriteGame = user.stats.favoriteGameType,
                currentStreak = user.stats.currentStreak,
                achievements = user.achievements.size
            )
        } else {
            UserStatsInfo(
                totalGames = 0,
                totalScore = 0L,
                totalPlayTime = 0L,
                favoriteGame = null,
                currentStreak = 0,
                achievements = 0
            )
        }
    }

    // 新增：用户经验值信息流
    override val userExperience: Flow<UserExperienceInfo> = currentUser.map { user ->
        if (user != null) {
            val currentLevel = membershipService.calculateLevel(user.experience)
            val progress = membershipService.calculateLevelProgress(user.experience)

            UserExperienceInfo(
                currentLevel = currentLevel,
                currentExperience = user.experience,
                experienceToNextLevel = membershipService.getExperienceToNextLevel(user.experience),
                progressToNextLevel = progress,
                totalExperience = user.experience
            )
        } else {
            UserExperienceInfo(
                currentLevel = 1,
                currentExperience = 0L,
                experienceToNextLevel = 100L,
                progressToNextLevel = 0f,
                totalExperience = 0L
            )
        }
    }

    // 新增：成就统计信息流
    private val _achievementStats = MutableStateFlow(
        AchievementStatsInfo(
            totalAchievements = 0,
            unlockedAchievements = 0,
            completionPercentage = 0f,
            recentUnlocks = emptyList()
        )
    )
    override val achievementStats: Flow<AchievementStatsInfo> = _achievementStats.asStateFlow()
    
    override suspend fun initialize() {
        if (_isInitialized.value) {
            logger.d("HomeController already initialized")
            return
        }

        logger.i("Initializing HomeController")

        try {
            // 预加载用户数据
            val user = currentUser.first()
            logger.d("User data preloaded: ${user?.username ?: "guest"}")

            // 预加载成就数据
            if (user != null) {
                when (val result = achievementService.getAchievementStats(user.id)) {
                    is Result.Success -> {
                        val stats = result.data
                        _achievementStats.value = AchievementStatsInfo(
                            totalAchievements = stats.totalAchievements,
                            unlockedAchievements = stats.unlockedAchievements,
                            completionPercentage = stats.completionPercentage,
                            recentUnlocks = getRecentAchievementUnlocks(user.id)
                        )
                        logger.d("Achievement data preloaded: ${stats.unlockedAchievements}/${stats.totalAchievements} achievements")
                    }
                    else -> {
                        logger.w("Failed to load achievement stats")
                    }
                }
            }

            _isInitialized.value = true
            logger.i("HomeController initialization completed successfully")
        } catch (e: Exception) {
            logger.e(e, "HomeController initialization failed")
            // 即使初始化失败，也标记为已初始化，避免重复尝试
            // 用户界面仍然可以正常显示，只是数据可能为空
            _isInitialized.value = true
            throw BusinessException("Failed to initialize home controller", "HOME_INIT_FAILED", cause = e)
        }
    }

    override suspend fun refresh() {
        logger.i("Refreshing home data")

        try {
            // 刷新用户数据
            val user = currentUser.first()
            if (user != null) {
                logger.d("Refreshing data for user: ${user.username}")

                // 触发成就数据重新加载
                when (val result = achievementService.getAchievementStats(user.id)) {
                    is Result.Success -> {
                        val stats = result.data
                        _achievementStats.value = AchievementStatsInfo(
                            totalAchievements = stats.totalAchievements,
                            unlockedAchievements = stats.unlockedAchievements,
                            completionPercentage = stats.completionPercentage,
                            recentUnlocks = getRecentAchievementUnlocks(user.id)
                        )
                        logger.d("Achievement stats refreshed successfully: ${stats.unlockedAchievements}/${stats.totalAchievements} achievements")
                    }
                    is Result.Error -> {
                        logger.w(result.exception, "Failed to refresh achievement stats")
                    }
                    is Result.Loading -> {
                        logger.d("Achievement stats refresh in progress")
                    }
                }

                logger.i("Home data refresh completed for user: ${user.username}")
            } else {
                logger.w("No current user found during refresh")
            }
        } catch (e: Exception) {
            logger.e(e, "Failed to refresh home data")
            throw BusinessException("Failed to refresh home data", "HOME_REFRESH_FAILED", cause = e)
        }
    }

    override suspend fun getGameStats(gameType: GameType): GameStatsInfo? {
        logger.d("Getting game stats for type: $gameType")

        return try {
            val user = currentUser.first()
            if (user == null) {
                logger.w("No current user found when getting game stats")
                return null
            }

            // 获取特定游戏类型的统计数据
            val gameSpecificStats = user.stats.gameStats[gameType]

            // 根据游戏类型返回统计信息
            val statsInfo = when (gameType) {
                GameType.TETRIS -> {
                    if (gameSpecificStats != null) {
                        // 使用游戏特定的统计数据
                        GameStatsInfo(
                            gameType = gameType,
                            gamesPlayed = gameSpecificStats.totalGames,
                            bestScore = gameSpecificStats.bestScore,
                            averageScore = gameSpecificStats.averageScore,
                            totalPlayTime = gameSpecificStats.totalPlayTime,
                            lastPlayed = gameSpecificStats.lastPlayed
                        )
                    } else {
                        // 使用用户总体统计数据作为后备
                        GameStatsInfo(
                            gameType = gameType,
                            gamesPlayed = user.stats.totalGames,
                            bestScore = if (user.stats.totalGames > 0) {
                                (user.stats.totalScore / user.stats.totalGames).toInt()
                            } else 0,
                            averageScore = user.stats.averageScore,
                            totalPlayTime = user.stats.totalPlayTime,
                            lastPlayed = null
                        )
                    }
                }
                else -> {
                    logger.w("Unsupported game type: $gameType")
                    null
                }
            }

            if (statsInfo != null) {
                logger.d("Game stats retrieved for $gameType: ${statsInfo.gamesPlayed} games played, best score: ${statsInfo.bestScore}")
            }

            statsInfo
        } catch (e: Exception) {
            logger.e(e, "Failed to get game stats for type: $gameType")
            throw BusinessException("Failed to retrieve game statistics for $gameType", "GAME_STATS_FAILED", cause = e)
        }
    }

    /**
     * 获取最近解锁的成就
     * 临时实现：返回模拟数据，待实现完整的成就系统
     */
    private suspend fun getRecentAchievementUnlocks(userId: String): List<RecentAchievementInfo> {
        return try {
            // 临时返回模拟的最近解锁成就
            // 在完整的成就系统实现后，这里应该从数据库查询真实数据
            listOf(
                RecentAchievementInfo(
                    achievementId = "first_game",
                    title = "初次尝试",
                    description = "完成第一场游戏",
                    unlockedAt = System.currentTimeMillis() - 86400000L, // 1天前
                    experienceReward = 100L
                ),
                RecentAchievementInfo(
                    achievementId = "score_1000",
                    title = "千分达成",
                    description = "单局得分达到1000分",
                    unlockedAt = System.currentTimeMillis() - 172800000L, // 2天前
                    experienceReward = 200L
                )
            )
        } catch (e: Exception) {
            logger.e(e, "Error loading recent achievement unlocks")
            emptyList()
        }
    }
}
