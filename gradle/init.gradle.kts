// Gradle初始化脚本 - 2025年最佳实践
// 配置Build Cache和其他全局优化

import org.gradle.api.tasks.testing.Test
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

gradle.settingsEvaluated {
    buildCache {
        local {
            isEnabled = true
            directory = File(rootDir, "build-cache")
            removeUnusedEntriesAfterDays = 30
        }

        // 如果有Gradle Enterprise，可以配置远程缓存
        // remote<HttpBuildCache> {
        //     url = uri("https://your-gradle-enterprise-server/cache/")
        //     isEnabled = true
        //     isPush = true
        // }
    }
}

// 配置构建扫描
gradle.projectsEvaluated {
    if (hasProperty("scan")) {
        extensions.findByName("gradleEnterprise")?.let { ge ->
            // 配置构建扫描设置
        }
    }
}

// 全局任务优化 - 2025年现代化配置
allprojects {
    // Kotlin编译优化
    tasks.withType<KotlinCompile>().configureEach {
        compilerOptions {
            // 启用增量编译
            incremental.set(true)

            // 使用并行编译
            freeCompilerArgs.addAll(
                "-Xjsr305=strict",
                "-opt-in=kotlin.RequiresOptIn",
                "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
                "-opt-in=androidx.compose.animation.ExperimentalAnimationApi"
            )

            // 启用编译器缓存
            usePreciseJavaTracking.set(true)
        }
    }

    // 测试任务优化
    tasks.withType<Test>().configureEach {
        // 启用并行测试执行
        maxParallelForks = (Runtime.getRuntime().availableProcessors() / 2).coerceAtLeast(1)

        // 优化测试JVM参数
        jvmArgs(
            "-XX:+UseG1GC",
            "-XX:+UseStringDeduplication",
            "-Xmx2g"
        )

        // 启用测试结果缓存
        outputs.upToDateWhen { false }
    }

    // 通用任务优化
    tasks.configureEach {
        // 启用任务输出缓存
        if (this is Cacheable) {
            outputs.cacheIf { true }
        }
    }
}

// 性能监控和报告
gradle.buildFinished { result ->
    val duration = System.currentTimeMillis() - gradle.startParameter.startTime
    println("🚀 构建完成，耗时: ${duration}ms")

    if (result.failure != null) {
        println("❌ 构建失败: ${result.failure?.message}")
    } else {
        println("✅ 构建成功")
    }
}

maven("https://maven.aliyun.com/repository/public") {
//    content { includeGroup("org.jetbrains.kotlin") }
}
