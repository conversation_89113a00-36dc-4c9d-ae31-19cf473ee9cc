plugins {
    id("questicle.android.library")
    id("questicle.hilt")
    id("tech.apter.junit5.jupiter.robolectric-extension-gradle-plugin")
}

android {
    namespace = "com.yu.questicle.core.audio"
}

// 直接配置所有类型为 Test 的任务，强制使用 JUnit 5 平台
// 这是更底层的配置方式，可以绕过 android testOptions DSL 可能存在的问题
tasks.withType<Test> {
    useJUnitPlatform()
}

dependencies {
    implementation(project(":core:common"))
    implementation(project(":core:domain")) // 新增：提供GameEvent、GameSound等核心领域模型
    implementation(libs.androidx.compose.animation)

    // Coroutines - 使用版本目录
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // 标准化测试依赖
    testImplementation(project(":core:testing"))
    testImplementation(project(":core:domain")) // 新增：确保测试代码可以访问领域模型
    testImplementation(libs.bundles.unit.testing)

    // JUnit 5 + Robolectric扩展
    testImplementation(libs.robolectric)
    testImplementation(libs.robolectric.junit5.extension)
    testImplementation(libs.androidx.test.core)

    // Hilt Testing
    testImplementation(libs.hilt.android.testing)
    kspTest(libs.hilt.compiler)
}
