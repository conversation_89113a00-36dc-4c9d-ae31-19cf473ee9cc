package com.yu.questicle.core.audio

import com.yu.questicle.core.audio.model.GameMusic
import com.yu.questicle.core.audio.model.GameSound
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.AudioException
import com.yu.questicle.core.common.logging.QLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音效事件管理器
 * 负责音效事件的队列管理和批量处理
 */
@Singleton
class AudioEventManager @Inject constructor(
    private val audioManager: AudioManager,
    private val logger: QLogger
) {

    companion object {
        private const val TAG = "AudioEventManager"
    }
    
    // 事件队列
    private val eventChannel = Channel<AudioEvent>(Channel.UNLIMITED)
    
    // 处理状态
    private val _isProcessing = MutableStateFlow(false)
    val isProcessing: StateFlow<Boolean> = _isProcessing.asStateFlow()
    
    // 事件统计
    private var totalEventsProcessed: Long = 0
    private var eventsInQueue: Int = 0
    
    // 协程作用域
    private val processingScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    init {
        startEventProcessing()
    }
    
    /**
     * 启动事件处理协程
     */
    private fun startEventProcessing() {
        processingScope.launch {
            eventChannel.receiveAsFlow().collect { event ->
                processAudioEvent(event)
            }
        }
    }
    
    /**
     * 添加音效事件到队列
     */
    fun queueAudioEvent(event: AudioEvent) {
        val success = eventChannel.trySend(event).isSuccess
        if (success) {
            eventsInQueue++
        }
    }
    
    /**
     * 批量添加音效事件
     */
    fun queueAudioEvents(events: List<AudioEvent>) {
        events.forEach { event ->
            queueAudioEvent(event)
        }
    }
    
    /**
     * 处理单个音效事件
     */
    private suspend fun processAudioEvent(event: AudioEvent) {
        _isProcessing.value = true
        
        try {
            when (event) {
                is AudioEvent.PlaySound -> {
                    audioManager.playSound(event.sound, event.volume)
                }
                is AudioEvent.PlayMusic -> {
                    audioManager.playMusic(event.music, event.loop)
                }
                is AudioEvent.StopMusic -> {
                    audioManager.stopMusic()
                }
                is AudioEvent.PauseMusic -> {
                    audioManager.pauseMusic()
                }
                is AudioEvent.ResumeMusic -> {
                    audioManager.resumeMusic()
                }
                is AudioEvent.SetVolume -> {
                    when (event.type) {
                        AudioType.MASTER -> audioManager.setMasterVolume(event.volume)
                        AudioType.SOUND_EFFECTS -> audioManager.setSoundEffectsVolume(event.volume)
                        AudioType.MUSIC -> audioManager.setMusicVolume(event.volume)
                    }
                }
                is AudioEvent.SetMuted -> {
                    audioManager.setMuted(event.muted)
                }
            }
            
            totalEventsProcessed++
            eventsInQueue--
            
        } catch (e: Exception) {
            // 处理音效播放失败
            handleAudioEventError(event, e)
        } finally {
            _isProcessing.value = false
        }
    }
    
    /**
     * 处理音效事件错误
     */
    private fun handleAudioEventError(event: AudioEvent, error: Exception) {
        logger.e(error, "Audio event failed: $event")

        // 可以实现重试逻辑或错误恢复
        if (error is AudioException && error.retryable) {
            // 可以添加重试逻辑
            logger.w(TAG, "Audio event is retryable, considering retry: $event")
        }
    }
    
    /**
     * 清空事件队列
     */
    fun clearQueue() {
        // 清空channel中的所有事件
        while (!eventChannel.isEmpty) {
            eventChannel.tryReceive()
        }
        eventsInQueue = 0
    }
    
    /**
     * 获取队列状态
     */
    fun getQueueStatus(): AudioQueueStatus {
        return AudioQueueStatus(
            eventsInQueue = eventsInQueue,
            totalEventsProcessed = totalEventsProcessed,
            isProcessing = _isProcessing.value
        )
    }
    
    /**
     * 释放资源
     */
    fun release() {
        processingScope.cancel()
        clearQueue()
    }
}

/**
 * 音效队列状态
 */
data class AudioQueueStatus(
    val eventsInQueue: Int,
    val totalEventsProcessed: Long,
    val isProcessing: Boolean
)

/**
 * 游戏音效触发器
 * 负责根据游戏事件触发相应的音效
 */
@Singleton
class GameAudioTrigger @Inject constructor(
    private val audioEventManager: AudioEventManager
) {
    
    /**
     * 处理俄罗斯方块游戏事件
     */
    fun handleTetrisGameEvent(event: TetrisGameEvent) {
        val audioEvent = when (event) {
            is TetrisGameEvent.PieceMoved -> AudioEvent.PlaySound(GameSound.PIECE_MOVE)
            is TetrisGameEvent.PieceRotated -> AudioEvent.PlaySound(GameSound.PIECE_ROTATE)
            is TetrisGameEvent.PieceDropped -> AudioEvent.PlaySound(GameSound.PIECE_DROP)
            is TetrisGameEvent.PieceLocked -> AudioEvent.PlaySound(GameSound.PIECE_LOCK)
            is TetrisGameEvent.LinesCleared -> {
                when (event.linesCount) {
                    1 -> AudioEvent.PlaySound(GameSound.LINE_CLEAR_SINGLE)
                    2 -> AudioEvent.PlaySound(GameSound.LINE_CLEAR_DOUBLE)
                    3 -> AudioEvent.PlaySound(GameSound.LINE_CLEAR_TRIPLE)
                    4 -> AudioEvent.PlaySound(GameSound.LINE_CLEAR_TETRIS)
                    else -> AudioEvent.PlaySound(GameSound.LINE_CLEAR_SINGLE)
                }
            }
            is TetrisGameEvent.LevelUp -> AudioEvent.PlaySound(GameSound.LEVEL_UP)
            is TetrisGameEvent.GameOver -> AudioEvent.PlaySound(GameSound.GAME_OVER)
            is TetrisGameEvent.GamePaused -> AudioEvent.PlaySound(GameSound.PAUSE)
            is TetrisGameEvent.GameResumed -> AudioEvent.ResumeMusic
            is TetrisGameEvent.GameStarted -> AudioEvent.PlayMusic(GameMusic.GAME_MUSIC_1)
        }
        
        audioEventManager.queueAudioEvent(audioEvent)
    }
    
    /**
     * 处理菜单事件
     */
    fun handleMenuEvent(event: MenuEvent) {
        val audioEvent = when (event) {
            is MenuEvent.ItemSelected -> AudioEvent.PlaySound(GameSound.MENU_SELECT)
            is MenuEvent.MenuOpened -> AudioEvent.PlayMusic(GameMusic.MENU_MUSIC)
            is MenuEvent.MenuClosed -> AudioEvent.StopMusic
        }
        
        audioEventManager.queueAudioEvent(audioEvent)
    }
    
    /**
     * 处理用户界面事件
     */
    fun handleUIEvent(event: UIEvent) {
        val audioEvent = when (event) {
            is UIEvent.ButtonClicked -> AudioEvent.PlaySound(GameSound.MENU_SELECT, 0.5f)
            is UIEvent.SettingsChanged -> AudioEvent.PlaySound(GameSound.MENU_SELECT, 0.3f)
            is UIEvent.AchievementUnlocked -> AudioEvent.PlaySound(GameSound.LEVEL_UP)
        }
        
        audioEventManager.queueAudioEvent(audioEvent)
    }
}

/**
 * 俄罗斯方块游戏事件
 */
sealed class TetrisGameEvent {
    object PieceMoved : TetrisGameEvent()
    object PieceRotated : TetrisGameEvent()
    object PieceDropped : TetrisGameEvent()
    object PieceLocked : TetrisGameEvent()
    data class LinesCleared(val linesCount: Int) : TetrisGameEvent()
    object LevelUp : TetrisGameEvent()
    object GameOver : TetrisGameEvent()
    object GamePaused : TetrisGameEvent()
    object GameResumed : TetrisGameEvent()
    object GameStarted : TetrisGameEvent()
}

/**
 * 菜单事件
 */
sealed class MenuEvent {
    object ItemSelected : MenuEvent()
    object MenuOpened : MenuEvent()
    object MenuClosed : MenuEvent()
}

/**
 * UI事件
 */
sealed class UIEvent {
    object ButtonClicked : UIEvent()
    object SettingsChanged : UIEvent()
    object AchievementUnlocked : UIEvent()
}

/**
 * 音效预设配置
 */
object AudioPresets {
    
    /**
     * 经典俄罗斯方块音效配置
     */
    val CLASSIC_TETRIS = AudioConfig(
        masterVolume = 1.0f,
        soundEffectsVolume = 0.8f,
        musicVolume = 0.6f,
        enableSoundEffects = true,
        enableMusic = true,
        enableVibration = true
    )
    
    /**
     * 静音模式配置
     */
    val SILENT_MODE = AudioConfig(
        masterVolume = 0.0f,
        soundEffectsVolume = 0.0f,
        musicVolume = 0.0f,
        enableSoundEffects = false,
        enableMusic = false,
        enableVibration = false
    )
    
    /**
     * 仅音效模式配置
     */
    val SOUND_EFFECTS_ONLY = AudioConfig(
        masterVolume = 1.0f,
        soundEffectsVolume = 1.0f,
        musicVolume = 0.0f,
        enableSoundEffects = true,
        enableMusic = false,
        enableVibration = true
    )
    
    /**
     * 仅音乐模式配置
     */
    val MUSIC_ONLY = AudioConfig(
        masterVolume = 1.0f,
        soundEffectsVolume = 0.0f,
        musicVolume = 1.0f,
        enableSoundEffects = false,
        enableMusic = true,
        enableVibration = false
    )
}

/**
 * 音效管理器扩展功能
 */
class AudioManagerExtensions(
    private val audioManager: AudioManager,
    private val audioEventManager: AudioEventManager
) {
    
    /**
     * 淡入音乐
     */
    suspend fun fadeInMusic(music: GameMusic, duration: Long = 2000L) {
        audioEventManager.queueAudioEvent(AudioEvent.PlayMusic(music))
        
        // 实现音量渐变
        val steps = 20
        val stepDuration = duration / steps
        val volumeStep = audioManager.musicVolume.value / steps
        
        repeat(steps) { step ->
            delay(stepDuration)
            val volume = volumeStep * (step + 1)
            audioEventManager.queueAudioEvent(AudioEvent.SetVolume(AudioType.MUSIC, volume))
        }
    }
    
    /**
     * 淡出音乐
     */
    suspend fun fadeOutMusic(duration: Long = 2000L) {
        val currentVolume = audioManager.musicVolume.value
        val steps = 20
        val stepDuration = duration / steps
        val volumeStep = currentVolume / steps
        
        repeat(steps) { step ->
            delay(stepDuration)
            val volume = currentVolume - (volumeStep * (step + 1))
            audioEventManager.queueAudioEvent(AudioEvent.SetVolume(AudioType.MUSIC, volume.coerceAtLeast(0f)))
        }
        
        audioEventManager.queueAudioEvent(AudioEvent.StopMusic)
    }
    
    /**
     * 应用音效预设
     */
    fun applyAudioPreset(preset: AudioConfig) {
        audioEventManager.queueAudioEvents(listOf(
            AudioEvent.SetVolume(AudioType.MASTER, preset.masterVolume),
            AudioEvent.SetVolume(AudioType.SOUND_EFFECTS, preset.soundEffectsVolume),
            AudioEvent.SetVolume(AudioType.MUSIC, preset.musicVolume),
            AudioEvent.SetMuted(!preset.enableSoundEffects && !preset.enableMusic)
        ))
    }
}
