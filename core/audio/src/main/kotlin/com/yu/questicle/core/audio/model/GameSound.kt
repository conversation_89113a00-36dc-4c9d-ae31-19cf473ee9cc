package com.yu.questicle.core.audio.model

/**
 * 游戏音效枚举
 * 定义所有游戏中使用的音效
 */
enum class GameSound {
    // 方块操作音效
    PIECE_MOVE,           // 方块移动
    PIECE_ROTATE,         // 方块旋转
    PIECE_DROP,           // 方块下落
    PIECE_LOCK,           // 方块锁定

    // 消除音效
    LINE_CLEAR_SINGLE,    // 单行消除
    LINE_CLEAR_DOUBLE,    // 双行消除
    LINE_CLEAR_TRIPLE,    // 三行消除
    LINE_CLEAR_TETRIS,    // 四行消除

    // 特殊音效
    LEVEL_UP,             // 升级
    GAME_OVER,            // 游戏结束
    PAUSE,                // 暂停

    // UI音效
    MENU_SELECT,          // 菜单选择
    BUTTON_CLICK,
    ERROR
}
