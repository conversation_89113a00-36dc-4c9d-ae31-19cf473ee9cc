package com.yu.questicle.core.audio.exception

import com.yu.questicle.core.common.exception.*

/**
 * 音频异常基类
 */
abstract class AudioException(
    message: String,
    errorCode: String = "AUDIO_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null,
    val retryable: Boolean = false
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.SYSTEM
}

/**
 * 声音文件未找到异常
 */
class SoundNotFound(
    soundId: String,
    cause: Throwable? = null
) : AudioException(
    message = "Sound file not found: $soundId",
    errorCode = "SOUND_NOT_FOUND",
    severity = ErrorSeverity.MEDIUM,
    cause = cause,
    retryable = false
)

/**
 * 音乐文件未找到异常
 */
class MusicNotFound(
    musicId: String,
    cause: Throwable? = null
) : AudioException(
    message = "Music file not found: $musicId",
    errorCode = "MUSIC_NOT_FOUND",
    severity = ErrorSeverity.MEDIUM,
    cause = cause,
    retryable = false
)

/**
 * 播放失败异常
 */
class PlaybackFailed(
    audioId: String,
    reason: String,
    cause: Throwable? = null
) : AudioException(
    message = "Playback failed for $audioId: $reason",
    errorCode = "PLAYBACK_FAILED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
)

/**
 * 音频初始化失败异常
 */
class AudioInitializationFailed(
    reason: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio initialization failed: $reason",
    errorCode = "AUDIO_INIT_FAILED",
    severity = ErrorSeverity.CRITICAL,
    cause = cause,
    retryable = false
)

/**
 * 音频资源加载失败异常
 */
class AudioResourceLoadFailed(
    resourceId: String,
    cause: Throwable? = null
) : AudioException(
    message = "Failed to load audio resource: $resourceId",
    errorCode = "AUDIO_RESOURCE_LOAD_FAILED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
)

/**
 * 音频设备不可用异常
 */
class AudioDeviceUnavailable(
    deviceInfo: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio device unavailable: $deviceInfo",
    errorCode = "AUDIO_DEVICE_UNAVAILABLE",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
)

/**
 * 音频权限被拒绝异常
 */
class AudioPermissionDenied(
    permission: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio permission denied: $permission",
    errorCode = "AUDIO_PERMISSION_DENIED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = false
) {
    override val type: ExceptionType = ExceptionType.PERMISSION
}
