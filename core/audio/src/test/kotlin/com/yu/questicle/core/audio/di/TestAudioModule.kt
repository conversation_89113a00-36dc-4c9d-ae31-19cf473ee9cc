package com.yu.questicle.core.audio.di

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import com.yu.questicle.core.common.logging.QLogger
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.hilt.testing.TestInstallIn
import io.mockk.mockk
import javax.inject.Singleton

/**
 * Audio模块的测试配置
 * 提供测试环境中AudioManager所需的依赖
 */
@Module
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [AudioModule::class]  // 替换生产环境的AudioModule
)
object TestAudioModule {

    /**
     * 提供测试环境的普通Context
     * AudioManagerTest中直接注入的context字段需要这个
     */
    @Provides
    @Singleton
    fun provideTestContext(): Context {
        return ApplicationProvider.getApplicationContext()
    }

    /**
     * 提供测试环境的QLogger
     * 使用MockK创建的relaxed mock，不需要具体实现
     */
    @Provides
    @Singleton
    fun provideTestQLogger(): QLogger {
        return mockk(relaxed = true)
    }
}

/**
 * 如果生产环境有AudioModule，这里提供一个空的占位符
 * 实际项目中应该有对应的生产AudioModule
 */
@Module
@InstallIn(SingletonComponent::class)
object AudioModule {
    // 生产环境的AudioModule配置
    // 在实际项目中，这里会有真实的依赖提供方法
}