package com.yu.questicle.core.domain.engine

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType

/**
 * Tetris-specific game engine interface
 */
interface TetrisEngine : GameEngine<TetrisGameState, TetrisAction> {
    
    override val gameType: GameType
        get() = GameType.TETRIS
    
    /**
     * Generate next piece
     */
    fun generateNextPiece(): TetrisPiece
    
    /**
     * Move current piece
     */
    suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Rotate current piece
     */
    suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Drop current piece
     */
    suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Hold current piece
     */
    suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Check for line clears
     */
    suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Calculate drop interval based on level
     */
    fun calculateDropInterval(level: Int): Long
    
    /**
     * Calculate score for line clear
     */
    fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int
    
    /**
     * Get ghost piece position
     */
    fun getGhostPiece(gameState: TetrisGameState): TetrisPiece?
    
    /**
     * Check if position is valid for piece
     */
    fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean
    
    /**
     * Get all possible rotations for current piece
     */
    fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece>
    
    /**
     * Get all possible positions for current piece
     */
    fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece>
    
    /**
     * Auto-drop piece (gravity)
     */
    suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState>
    
    /**
     * Check for T-spin
     */
    fun checkTSpin(gameState: TetrisGameState): Boolean
    
    /**
     * Calculate level progression
     */
    fun calculateLevel(lines: Int): Int
}

/**
 * Tetris AI engine interface
 */
interface TetrisAIEngine : TetrisEngine, AIGameEngine<TetrisGameState, TetrisAction> {
    
    /**
     * Evaluate board position
     */
    suspend fun evaluatePosition(gameState: TetrisGameState): Float
    
    /**
     * Find best placement for current piece
     */
    suspend fun findBestPlacement(gameState: TetrisGameState): Result<TetrisPiece>
    
    /**
     * Calculate board metrics
     */
    suspend fun calculateBoardMetrics(gameState: TetrisGameState): BoardMetrics
    
    /**
     * Simulate piece placement
     */
    suspend fun simulatePlacement(piece: TetrisPiece, gameState: TetrisGameState): TetrisGameState
    
    /**
     * Get opening book moves
     */
    suspend fun getOpeningMoves(gameState: TetrisGameState): List<TetrisAction>
    
    /**
     * Analyze piece sequence
     */
    suspend fun analyzePieceSequence(pieces: List<TetrisPieceType>): SequenceAnalysis
}

/**
 * Board evaluation metrics
 */
data class BoardMetrics(
    val height: Int,
    val holes: Int,
    val bumpiness: Int,
    val completedLines: Int,
    val aggregateHeight: Int,
    val maxHeight: Int,
    val minHeight: Int,
    val heightVariance: Float,
    val wellDepth: Int,
    val blockades: Int,
    val density: Float
)

/**
 * Piece sequence analysis
 */
data class SequenceAnalysis(
    val difficulty: Float,
    val opportunities: List<String>,
    val threats: List<String>,
    val recommendations: List<String>
)

/**
 * Tetris scoring system
 */
object TetrisScoring {
    
    /**
     * Base scores for line clears
     */
    private val baseScores = mapOf(
        1 to 100,   // Single
        2 to 300,   // Double
        3 to 500,   // Triple
        4 to 800    // Tetris
    )
    
    /**
     * T-spin bonus scores
     */
    private val tSpinScores = mapOf(
        1 to 800,   // T-spin Single
        2 to 1200,  // T-spin Double
        3 to 1600   // T-spin Triple
    )
    
    /**
     * Calculate score for line clear
     */
    fun calculateScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean = false,
        combo: Int = 0
    ): Int {
        if (linesCleared == 0) return 0
        
        val baseScore = if (isTSpin) {
            tSpinScores[linesCleared] ?: 0
        } else {
            baseScores[linesCleared] ?: 0
        }
        
        val levelMultiplier = level + 1
        val comboBonus = if (combo > 0) 50 * combo * levelMultiplier else 0
        
        return (baseScore * levelMultiplier) + comboBonus
    }
    
    /**
     * Calculate soft drop score
     */
    fun calculateSoftDropScore(cells: Int): Int = cells
    
    /**
     * Calculate hard drop score
     */
    fun calculateHardDropScore(cells: Int): Int = cells * 2
}
