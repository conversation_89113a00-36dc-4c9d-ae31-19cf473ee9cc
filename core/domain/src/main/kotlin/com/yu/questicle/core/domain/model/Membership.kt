package com.yu.questicle.core.domain.model

/**
 * 会员等级枚举
 */
enum class MembershipTier {
    FREE,      // 免费用户
    BASIC,     // 基础会员
    PREMIUM,   // 高级会员
    VIP        // VIP会员
}

/**
 * 奖励类型枚举
 */
enum class RewardType {
    COINS,              // 金币
    GEMS,               // 宝石
    ACHIEVEMENT,        // 成就
    UNLOCK,             // 解锁内容
    MEMBERSHIP_BONUS    // 会员奖励
}

/**
 * 等级升级事件
 */
data class LevelUpEvent(
    val oldLevel: Int,
    val newLevel: Int,
    val rewards: List<Reward>
)

/**
 * 奖励数据类
 */
data class Reward(
    val type: RewardType,
    val amount: Long,
    val description: String
)

/**
 * 扩展User模型以支持会员等级
 */
val User.membershipTier: MembershipTier
    get() {
        // Simple logic: determine tier based on user level
        return when {
            this.level >= 20 -> MembershipTier.VIP
            this.level >= 15 -> MembershipTier.PREMIUM
            this.level >= 10 -> MembershipTier.BASIC
            else -> MembershipTier.FREE
        }
    } 