package com.yu.questicle.core.domain.exception

import com.yu.questicle.core.common.exception.QuesticleException
import com.yu.questicle.core.common.exception.ExceptionType
import com.yu.questicle.core.common.exception.ErrorSeverity

/**
 * Tetris 游戏相关异常
 */
class TetrisException(
    message: String,
    errorCode: String = "TETRIS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(
    message = message,
    errorCode = errorCode,
    severity = severity,
    cause = cause
) {

    override val type: ExceptionType = ExceptionType.GAME_ERROR

    init {
        // 添加 Tetris 特定的上下文信息
        addContext("gameType", "Tetris")
    }
    companion object {
        /**
         * 无效移动异常
         */
        fun invalidMove(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Invalid move: $reason",
                errorCode = "TETRIS_INVALID_MOVE",
                severity = ErrorSeverity.LOW,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 无效旋转异常
         */
        fun invalidRotation(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Invalid rotation: $reason",
                errorCode = "TETRIS_INVALID_ROTATION",
                severity = ErrorSeverity.LOW,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 游戏状态异常
         */
        fun invalidGameState(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Invalid game state: $reason",
                errorCode = "TETRIS_INVALID_STATE",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 方块生成异常
         */
        fun pieceGenerationFailed(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Piece generation failed: $reason",
                errorCode = "TETRIS_PIECE_GENERATION_FAILED",
                severity = ErrorSeverity.HIGH,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 游戏引擎异常
         */
        fun engineError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Tetris engine error: $reason",
                errorCode = "TETRIS_ENGINE_ERROR",
                severity = ErrorSeverity.HIGH,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 碰撞检测异常
         */
        fun collisionDetectionFailed(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Collision detection failed: $reason",
                errorCode = "TETRIS_COLLISION_DETECTION_FAILED",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 行清除异常
         */
        fun lineClearFailed(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Line clear failed: $reason",
                errorCode = "TETRIS_LINE_CLEAR_FAILED",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 计分异常
         */
        fun scoringError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Scoring error: $reason",
                errorCode = "TETRIS_SCORING_ERROR",
                severity = ErrorSeverity.LOW,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 保存/加载异常
         */
        fun saveLoadError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Save/Load error: $reason",
                errorCode = "TETRIS_SAVE_LOAD_ERROR",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 配置异常
         */
        fun configurationError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Configuration error: $reason",
                errorCode = "TETRIS_CONFIGURATION_ERROR",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 性能监控异常
         */
        fun performanceMonitoringError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Performance monitoring error: $reason",
                errorCode = "TETRIS_PERFORMANCE_MONITORING_ERROR",
                severity = ErrorSeverity.LOW,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 网络同步异常
         */
        fun networkSyncError(reason: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Network sync error: $reason",
                errorCode = "TETRIS_NETWORK_SYNC_ERROR",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("reason", reason)
            return exception
        }

        /**
         * 超时异常
         */
        fun timeoutError(operation: String, timeoutMs: Long, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Operation '$operation' timed out after ${timeoutMs}ms",
                errorCode = "TETRIS_TIMEOUT_ERROR",
                severity = ErrorSeverity.MEDIUM,
                cause = cause
            )
            exception.addContext("operation", operation)
            exception.addContext("timeoutMs", timeoutMs)
            return exception
        }

        /**
         * 资源不足异常
         */
        fun resourceExhausted(resource: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Resource exhausted: $resource",
                errorCode = "TETRIS_RESOURCE_EXHAUSTED",
                severity = ErrorSeverity.HIGH,
                cause = cause
            )
            exception.addContext("resource", resource)
            return exception
        }

        /**
         * 版本不兼容异常
         */
        fun versionMismatch(expected: String, actual: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Version mismatch: expected $expected, got $actual",
                errorCode = "TETRIS_VERSION_MISMATCH",
                severity = ErrorSeverity.HIGH,
                cause = cause
            )
            exception.addContext("expectedVersion", expected)
            exception.addContext("actualVersion", actual)
            return exception
        }

        /**
         * 数据损坏异常
         */
        fun dataCorruption(dataType: String, cause: Throwable? = null): TetrisException {
            val exception = TetrisException(
                message = "Data corruption detected in $dataType",
                errorCode = "TETRIS_DATA_CORRUPTION",
                severity = ErrorSeverity.CRITICAL,
                cause = cause
            )
            exception.addContext("dataType", dataType)
            return exception
        }
    }
}
