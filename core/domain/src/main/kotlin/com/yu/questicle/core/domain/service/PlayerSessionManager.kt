package com.yu.questicle.core.domain.service

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.User
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * 玩家会话管理器
 * 负责管理当前玩家会话和游戏会话
 */
interface PlayerSessionManager {
    
    /**
     * 当前玩家
     */
    val currentPlayer: StateFlow<User?>
    
    /**
     * 当前玩家ID
     */
    val currentPlayerId: StateFlow<String?>
    
    /**
     * 当前游戏会话
     */
    val currentGameSession: StateFlow<GameSession?>
    
    /**
     * 获取当前玩家ID
     * @return 当前玩家ID，如果未登录则返回临时ID
     */
    suspend fun getCurrentPlayerId(): String
    
    /**
     * 创建新的游戏会话
     * @param gameType 游戏类型
     * @return 创建的游戏会话
     */
    suspend fun createGameSession(gameType: String): Result<GameSession>
    
    /**
     * 结束当前游戏会话
     * @param finalScore 最终分数
     * @param gameData 游戏数据
     * @return 结束结果
     */
    suspend fun endGameSession(
        finalScore: Int,
        gameData: Map<String, Any> = emptyMap()
    ): Result<Unit>
    
    /**
     * 更新游戏会话数据
     * @param sessionId 会话ID
     * @param data 要更新的数据
     * @return 更新结果
     */
    suspend fun updateGameSession(
        sessionId: String,
        data: Map<String, Any>
    ): Result<Unit>
    
    /**
     * 获取玩家的游戏会话历史
     * @param playerId 玩家ID
     * @param limit 限制数量
     * @return 会话历史列表
     */
    suspend fun getPlayerSessionHistory(
        playerId: String,
        limit: Int = 50
    ): Result<List<GameSession>>
    
    /**
     * 检查玩家是否已登录
     * @return 是否已登录
     */
    suspend fun isPlayerLoggedIn(): Boolean
    
    /**
     * 检查玩家是否为游客
     * @return 是否为游客
     */
    suspend fun isGuestPlayer(): Boolean
    
    /**
     * 生成临时玩家ID (用于游客模式)
     * @return 临时玩家ID
     */
    fun generateTempPlayerId(): String
    
    /**
     * 清理过期的会话数据
     * @return 清理结果
     */
    suspend fun cleanupExpiredSessions(): Result<Int>
}

/**
 * 玩家会话管理器实现
 */
@javax.inject.Singleton
class PlayerSessionManagerImpl @javax.inject.Inject constructor(
    private val userRepository: com.yu.questicle.core.domain.repository.UserRepository,
    private val gameRepository: com.yu.questicle.core.domain.repository.GameRepository
) : PlayerSessionManager {
    
    private val _currentPlayer = kotlinx.coroutines.flow.MutableStateFlow<User?>(null)
    override val currentPlayer: StateFlow<User?> = _currentPlayer
    
    private val _currentPlayerId = kotlinx.coroutines.flow.MutableStateFlow<String?>(null)
    override val currentPlayerId: StateFlow<String?> = _currentPlayerId
    
    private val _currentGameSession = kotlinx.coroutines.flow.MutableStateFlow<GameSession?>(null)
    override val currentGameSession: StateFlow<GameSession?> = _currentGameSession
    
    // 使用 lazy 初始化来避免在 init 块中使用 GlobalScope
    private val sessionScope = kotlinx.coroutines.CoroutineScope(
        kotlinx.coroutines.Dispatchers.IO + kotlinx.coroutines.SupervisorJob()
    )
    
    init {
        // 监听用户状态变化
        sessionScope.launch {
            userRepository.getCurrentUser().collect { user ->
                _currentPlayer.value = user
                _currentPlayerId.value = user?.id ?: generateTempPlayerId()
            }
        }
    }
    
    override suspend fun getCurrentPlayerId(): String {
        return currentPlayerId.value ?: generateTempPlayerId()
    }
    
    override suspend fun createGameSession(gameType: String): Result<GameSession> {
        return try {
            val playerId = getCurrentPlayerId()
            val session = GameSession(
                id = generateSessionId(),
                gameId = generateSessionId(), // 使用相同的ID作为gameId
                playerId = playerId
            )
            
            val saveResult = gameRepository.saveGameSession(session)
            if (saveResult is Result.Success) {
                _currentGameSession.value = session
                Result.Success(session)
            } else {
                saveResult as Result.Error
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun endGameSession(
        finalScore: Int,
        gameData: Map<String, Any>
    ): Result<Unit> {
        return try {
            val session = currentGameSession.value ?: return Result.Error(
                IllegalStateException("No active game session").toQuesticleException()
            )

            val endedSession = session.copy(
                endTime = System.currentTimeMillis() / 1000
            )
            
            val saveResult = gameRepository.saveGameSession(endedSession)
            if (saveResult is Result.Success) {
                _currentGameSession.value = null
                Result.Success(Unit)
            } else {
                saveResult as Result.Error
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateGameSession(
        sessionId: String,
        data: Map<String, Any>
    ): Result<Unit> {
        return try {
            val session = currentGameSession.value
            if (session?.id == sessionId) {
                // GameSession模型不支持动态数据更新，这里只是标记为成功
                // 实际的游戏数据应该通过其他方式存储
                _currentGameSession.value = session
                Result.Success(Unit)
            } else {
                Result.Error(IllegalArgumentException("Session not found").toQuesticleException())
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun getPlayerSessionHistory(
        playerId: String,
        limit: Int
    ): Result<List<GameSession>> {
        return try {
            // 这里需要GameRepository支持按玩家ID查询会话
            // 暂时返回空列表，等待GameRepository扩展
            Result.Success(emptyList())
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun isPlayerLoggedIn(): Boolean {
        return currentPlayer.value?.isRegisteredUser() == true
    }
    
    override suspend fun isGuestPlayer(): Boolean {
        return currentPlayer.value?.isGuest() == true
    }
    
    override fun generateTempPlayerId(): String {
        return "temp_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    override suspend fun cleanupExpiredSessions(): Result<Int> {
        return try {
            // 清理超过24小时的临时会话
            // 这里需要GameRepository支持批量删除
            Result.Success(0)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    private fun generateSessionId(): String {
        return "session_${System.currentTimeMillis()}_${java.util.UUID.randomUUID().toString().take(8)}"
    }
    
    /**
     * 清理资源，取消所有协程
     * 应该在适当的时候调用，比如应用关闭时
     */
    fun cleanup() {
        sessionScope.cancel()
    }
}

// 扩展函数
private fun Exception.toQuesticleException(): com.yu.questicle.core.common.exception.QuesticleException {
    return com.yu.questicle.core.common.exception.BusinessException(
        message = this.message ?: "Unknown error",
        cause = this
    )
}
