package com.yu.questicle.core.domain.model

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * 用户性别枚举
 */
@Serializable
enum class Gender(val displayName: String) {
    MALE("男"),
    FEMALE("女"),
    PREFER_NOT_TO_SAY("不透露")
}

/**
 * User model for the gaming platform
 */
@Serializable
data class User(
    val id: String = UUID.randomUUID().toString(),
    val username: String,
    val email: String? = null,
    val displayName: String = "",
    val bio: String? = null,                    // 新增：个人简介
    val birthday: String? = null,               // 新增：生日（ISO字符串格式）
    val gender: Gender? = null,                 // 新增：性别
    val avatarUrl: String? = null,
    val passwordHash: String? = null, // 加密后的密码哈希
    val level: Int = 1,
    val experience: Long = 0,
    val coins: Long = 0,
    val gems: Long = 0,
    val preferences: UserPreferences = UserPreferences(),
    val stats: UserStats = UserStats(),
    val achievements: Set<String> = emptySet(),
    val friends: Set<String> = emptySet(),
    val createdAt: Long = System.currentTimeMillis() / 1000,
    val lastLoginAt: Long = System.currentTimeMillis() / 1000,
    val isActive: Boolean = true,
    val emailVerified: Boolean = false, // 邮箱验证状态
    val accountStatus: AccountStatus = AccountStatus.ACTIVE // 账户状态
) {
    /**
     * 判断是否为游客用户
     * 统一的游客判断逻辑，避免在不同地方使用不同的判断方式
     */
    fun isGuest(): Boolean = username.startsWith("Guest_")

    /**
     * 判断是否为正式注册用户
     */
    fun isRegisteredUser(): Boolean = !isGuest()

    /**
     * 获取用户显示名称，游客显示特殊名称
     */
    fun getEffectiveDisplayName(): String = if (isGuest()) "游客用户" else displayName.ifBlank { username }

    companion object {
        fun createGuest(): User {
            return User(
                username = "Guest_${UUID.randomUUID().toString().take(8)}",
                displayName = "游客用户"
            )
        }

        /**
         * 游客用户名前缀常量
         */
        const val GUEST_USERNAME_PREFIX = "Guest_"
    }
}

/**
 * User preferences and settings
 */
@Serializable
data class UserPreferences(
    val theme: Theme = Theme.SYSTEM,
    val language: String = "zh-CN",
    val soundEnabled: Boolean = true,
    val musicEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val masterVolume: Float = 0.7f,
    val soundEffectsVolume: Float = 1.0f,
    val musicVolume: Float = 0.5f,
    val notificationsEnabled: Boolean = true,
    val autoSaveEnabled: Boolean = true,
    val showHintsEnabled: Boolean = true,
    val animationSpeed: Float = 0.5f,
    val difficulty: GameDifficulty = GameDifficulty.MEDIUM,
    val privacySettings: PrivacySettings = PrivacySettings()
)

/**
 * Theme options
 */
@Serializable
enum class Theme {
    LIGHT,
    DARK,
    SYSTEM
}

/**
 * Account status
 */
@Serializable
enum class AccountStatus {
    ACTIVE,      // 正常活跃状态
    INACTIVE,    // 非活跃状态
    SUSPENDED,   // 暂停状态
    BANNED,      // 封禁状态
    PENDING      // 待验证状态
}

/**
 * Privacy settings
 */
@Serializable
data class PrivacySettings(
    val profileVisible: Boolean = true,
    val statsVisible: Boolean = true,
    val friendRequestsEnabled: Boolean = true,
    val dataCollectionEnabled: Boolean = true
)

/**
 * User statistics across all games
 */
@Serializable
data class UserStats(
    val totalGames: Int = 0,
    val totalScore: Long = 0,
    val totalPlayTime: Long = 0, // in seconds
    val gamesWon: Int = 0,
    val gamesLost: Int = 0,
    val currentStreak: Int = 0,
    val bestStreak: Int = 0,
    val favoriteGameType: GameType? = null,
    val gameStats: Map<GameType, GameStats> = emptyMap()
) {
    val winRate: Double
        get() = if (totalGames > 0) gamesWon.toDouble() / totalGames else 0.0
    
    val averageScore: Double
        get() = if (totalGames > 0) totalScore.toDouble() / totalGames else 0.0
}
