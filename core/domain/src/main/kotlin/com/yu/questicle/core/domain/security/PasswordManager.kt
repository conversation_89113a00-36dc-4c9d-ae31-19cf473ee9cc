package com.yu.questicle.core.domain.security

import java.security.MessageDigest
import java.security.SecureRandom
import java.util.Base64
import javax.crypto.spec.PBEKeySpec
import javax.crypto.SecretKeyFactory

/**
 * 密码管理器
 * 负责密码的加密、验证和安全处理
 * 实现 REQ-SEC-001: 数据安全要求
 */
object PasswordManager {
    
    // 安全常量
    private const val PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256"
    private const val SALT_LENGTH = 32
    private const val HASH_LENGTH = 64
    private const val ITERATIONS = 100000
    
    /**
     * 生成密码哈希
     * 使用PBKDF2算法和随机盐值
     */
    fun hashPassword(password: String): String {
        val salt = generateSalt()
        val hash = pbkdf2(password, salt, ITERATIONS, HASH_LENGTH)
        
        // 格式: iterations:salt:hash
        return "$ITERATIONS:${Base64.getEncoder().encodeToString(salt)}:${Base64.getEncoder().encodeToString(hash)}"
    }
    
    /**
     * 验证密码
     * 比较输入密码与存储的哈希值
     */
    fun verifyPassword(password: String, storedHash: String): Boolean {
        if (storedHash.isBlank()) return false
        
        return try {
            val parts = storedHash.split(":")
            if (parts.size != 3) return false
            
            val iterations = parts[0].toInt()
            val salt = Base64.getDecoder().decode(parts[1])
            val hash = Base64.getDecoder().decode(parts[2])
            
            val testHash = pbkdf2(password, salt, iterations, hash.size)
            
            // 使用常量时间比较防止时序攻击
            constantTimeEquals(hash, testHash)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 生成随机盐值
     */
    private fun generateSalt(): ByteArray {
        val random = SecureRandom()
        val salt = ByteArray(SALT_LENGTH)
        random.nextBytes(salt)
        return salt
    }
    
    /**
     * PBKDF2密钥派生函数
     */
    private fun pbkdf2(password: String, salt: ByteArray, iterations: Int, keyLength: Int): ByteArray {
        val spec = PBEKeySpec(password.toCharArray(), salt, iterations, keyLength * 8)
        val factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM)
        return factory.generateSecret(spec).encoded
    }
    
    /**
     * 常量时间比较
     * 防止时序攻击
     */
    private fun constantTimeEquals(a: ByteArray, b: ByteArray): Boolean {
        if (a.size != b.size) return false
        
        var result = 0
        for (i in a.indices) {
            result = result or (a[i].toInt() xor b[i].toInt())
        }
        return result == 0
    }
    
    /**
     * 检查密码强度
     * 返回密码强度评分 (0-100)
     */
    fun checkPasswordStrength(password: String): PasswordStrength {
        var score = 0
        val feedback = mutableListOf<String>()
        
        // 长度检查
        when {
            password.length >= 12 -> score += 25
            password.length >= 8 -> score += 15
            password.length >= 6 -> score += 10
            else -> feedback.add("密码长度至少需要6个字符")
        }
        
        // 字符类型检查
        if (password.any { it.isLowerCase() }) {
            score += 10
        } else {
            feedback.add("需要包含小写字母")
        }
        
        if (password.any { it.isUpperCase() }) {
            score += 10
        } else {
            feedback.add("建议包含大写字母")
        }
        
        if (password.any { it.isDigit() }) {
            score += 15
        } else {
            feedback.add("需要包含数字")
        }
        
        if (password.any { !it.isLetterOrDigit() }) {
            score += 20
        } else {
            feedback.add("建议包含特殊字符")
        }
        
        // 复杂度检查
        val uniqueChars = password.toSet().size
        if (uniqueChars >= password.length * 0.7) {
            score += 10
        } else {
            feedback.add("避免重复字符")
        }
        
        // 常见密码检查
        if (isCommonPassword(password)) {
            score -= 30
            feedback.add("避免使用常见密码")
        }
        
        // 确保分数在0-100范围内
        score = score.coerceIn(0, 100)
        
        val level = when {
            score >= 80 -> PasswordStrengthLevel.STRONG
            score >= 60 -> PasswordStrengthLevel.MEDIUM
            score >= 40 -> PasswordStrengthLevel.WEAK
            else -> PasswordStrengthLevel.VERY_WEAK
        }
        
        return PasswordStrength(
            score = score,
            level = level,
            feedback = feedback
        )
    }
    
    /**
     * 检查是否为常见密码
     */
    private fun isCommonPassword(password: String): Boolean {
        val commonPasswords = setOf(
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "password123", "123qwe", "qwerty123", "000000"
        )
        return password.lowercase() in commonPasswords
    }
    
    /**
     * 生成安全的随机密码
     */
    fun generateSecurePassword(
        length: Int = 12,
        includeUppercase: Boolean = true,
        includeLowercase: Boolean = true,
        includeNumbers: Boolean = true,
        includeSymbols: Boolean = true
    ): String {
        require(length >= 4) { "密码长度至少需要4个字符" }
        
        val lowercase = "abcdefghijklmnopqrstuvwxyz"
        val uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val numbers = "0123456789"
        val symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        var charset = ""
        if (includeLowercase) charset += lowercase
        if (includeUppercase) charset += uppercase
        if (includeNumbers) charset += numbers
        if (includeSymbols) charset += symbols
        
        require(charset.isNotEmpty()) { "至少需要选择一种字符类型" }
        
        val random = SecureRandom()
        val password = StringBuilder()
        
        // 确保每种选中的字符类型至少包含一个
        if (includeLowercase) password.append(lowercase[random.nextInt(lowercase.length)])
        if (includeUppercase) password.append(uppercase[random.nextInt(uppercase.length)])
        if (includeNumbers) password.append(numbers[random.nextInt(numbers.length)])
        if (includeSymbols) password.append(symbols[random.nextInt(symbols.length)])
        
        // 填充剩余长度
        repeat(length - password.length) {
            password.append(charset[random.nextInt(charset.length)])
        }
        
        // 打乱字符顺序
        val chars = password.toString().toCharArray()
        for (i in chars.indices) {
            val j = random.nextInt(chars.size)
            val temp = chars[i]
            chars[i] = chars[j]
            chars[j] = temp
        }
        
        return String(chars)
    }
}

/**
 * 密码强度数据类
 */
data class PasswordStrength(
    val score: Int,
    val level: PasswordStrengthLevel,
    val feedback: List<String>
)

/**
 * 密码强度等级
 */
enum class PasswordStrengthLevel(val displayName: String, val color: String) {
    VERY_WEAK("很弱", "#F44336"),
    WEAK("弱", "#FF9800"),
    MEDIUM("中等", "#FFC107"),
    STRONG("强", "#4CAF50")
}

/**
 * 密码策略配置
 */
data class PasswordPolicy(
    val minLength: Int = 8,
    val maxLength: Int = 128,
    val requireUppercase: Boolean = false,
    val requireLowercase: Boolean = true,
    val requireNumbers: Boolean = true,
    val requireSymbols: Boolean = false,
    val preventCommonPasswords: Boolean = true,
    val preventUserInfo: Boolean = true
) {
    companion object {
        val DEFAULT = PasswordPolicy()
        val STRICT = PasswordPolicy(
            minLength = 12,
            requireUppercase = true,
            requireSymbols = true
        )
    }
}
