package com.yu.questicle.core.domain.validation

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.regex.Pattern

/**
 * 用户输入验证工具类
 * 实现 REQ-USER-005: 系统应验证用户输入的有效性
 */
object UserValidation {
    
    // 验证规则常量
    private const val MIN_USERNAME_LENGTH = 3
    private const val MAX_USERNAME_LENGTH = 20
    private const val MIN_PASSWORD_LENGTH = 6
    private const val MAX_PASSWORD_LENGTH = 128
    private const val MIN_DISPLAY_NAME_LENGTH = 1
    private const val MAX_DISPLAY_NAME_LENGTH = 30
    
    // 正则表达式模式
    private val USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+$")
    private val EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    )
    private val DISPLAY_NAME_PATTERN = Pattern.compile("^[\\p{L}\\p{N}\\p{P}\\p{Z}]+$")
    
    // 禁用的用户名列表
    private val FORBIDDEN_USERNAMES = setOf(
        "admin", "administrator", "root", "system", "guest", "user",
        "null", "undefined", "test", "demo", "api", "www", "mail",
        "support", "help", "info", "contact", "service", "official"
    )
    
    /**
     * 验证用户名
     * 规则：
     * - 长度3-20字符
     * - 只能包含字母、数字、下划线、连字符
     * - 不能是保留关键词
     * - 不能以数字开头
     */
    fun validateUsername(username: String): ValidationResult {
        val errors = mutableListOf<String>()
        
        // 检查长度
        if (username.length < MIN_USERNAME_LENGTH) {
            errors.add("用户名长度不能少于${MIN_USERNAME_LENGTH}个字符")
        }
        if (username.length > MAX_USERNAME_LENGTH) {
            errors.add("用户名长度不能超过${MAX_USERNAME_LENGTH}个字符")
        }
        
        // 检查字符格式
        if (!USERNAME_PATTERN.matcher(username).matches()) {
            errors.add("用户名只能包含字母、数字、下划线和连字符")
        }
        
        // 检查是否以数字开头
        if (username.isNotEmpty() && username[0].isDigit()) {
            errors.add("用户名不能以数字开头")
        }
        
        // 检查是否为保留关键词
        if (username.lowercase() in FORBIDDEN_USERNAMES) {
            errors.add("该用户名为系统保留，请选择其他用户名")
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(errors)
        }
    }
    
    /**
     * 验证邮箱地址
     * 规则：
     * - 符合标准邮箱格式
     * - 长度不超过254字符（RFC标准）
     */
    fun validateEmail(email: String): ValidationResult {
        val errors = mutableListOf<String>()
        
        // 检查长度
        if (email.length > 254) {
            errors.add("邮箱地址长度不能超过254个字符")
        }
        
        // 检查格式
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            errors.add("请输入有效的邮箱地址")
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(errors)
        }
    }
    
    /**
     * 验证密码
     * 规则：
     * - 长度6-128字符
     * - 至少包含一个字母
     * - 至少包含一个数字
     * - 不能包含空格
     */
    fun validatePassword(password: String): ValidationResult {
        val errors = mutableListOf<String>()
        
        // 检查长度
        if (password.length < MIN_PASSWORD_LENGTH) {
            errors.add("密码长度不能少于${MIN_PASSWORD_LENGTH}个字符")
        }
        if (password.length > MAX_PASSWORD_LENGTH) {
            errors.add("密码长度不能超过${MAX_PASSWORD_LENGTH}个字符")
        }
        
        // 检查是否包含字母
        if (!password.any { it.isLetter() }) {
            errors.add("密码必须包含至少一个字母")
        }
        
        // 检查是否包含数字
        if (!password.any { it.isDigit() }) {
            errors.add("密码必须包含至少一个数字")
        }
        
        // 检查是否包含空格
        if (password.contains(' ')) {
            errors.add("密码不能包含空格")
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(errors)
        }
    }
    
    /**
     * 验证显示名称
     * 规则：
     * - 长度1-30字符
     * - 可以包含任何Unicode字符
     * - 不能只包含空白字符
     */
    fun validateDisplayName(displayName: String): ValidationResult {
        val errors = mutableListOf<String>()
        
        // 检查长度
        if (displayName.length < MIN_DISPLAY_NAME_LENGTH) {
            errors.add("显示名称不能为空")
        }
        if (displayName.length > MAX_DISPLAY_NAME_LENGTH) {
            errors.add("显示名称长度不能超过${MAX_DISPLAY_NAME_LENGTH}个字符")
        }
        
        // 检查是否只包含空白字符
        if (displayName.isBlank()) {
            errors.add("显示名称不能只包含空白字符")
        }
        
        return if (errors.isEmpty()) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(errors)
        }
    }
    
    /**
     * 验证密码确认
     */
    fun validatePasswordConfirmation(password: String, confirmation: String): ValidationResult {
        return if (password == confirmation) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(listOf("两次输入的密码不一致"))
        }
    }
    
    /**
     * 批量验证用户注册信息
     */
    fun validateRegistration(
        username: String,
        email: String?,
        password: String,
        passwordConfirmation: String,
        displayName: String?
    ): ValidationResult {
        val allErrors = mutableListOf<String>()
        
        // 验证用户名
        when (val usernameResult = validateUsername(username)) {
            is ValidationResult.Invalid -> allErrors.addAll(usernameResult.errors)
            ValidationResult.Valid -> { /* 验证通过 */ }
        }
        
        // 验证邮箱（如果提供）
        if (!email.isNullOrBlank()) {
            when (val emailResult = validateEmail(email)) {
                is ValidationResult.Invalid -> allErrors.addAll(emailResult.errors)
                ValidationResult.Valid -> { /* 验证通过 */ }
            }
        }
        
        // 验证密码
        when (val passwordResult = validatePassword(password)) {
            is ValidationResult.Invalid -> allErrors.addAll(passwordResult.errors)
            ValidationResult.Valid -> { /* 验证通过 */ }
        }
        
        // 验证密码确认
        when (val confirmResult = validatePasswordConfirmation(password, passwordConfirmation)) {
            is ValidationResult.Invalid -> allErrors.addAll(confirmResult.errors)
            ValidationResult.Valid -> { /* 验证通过 */ }
        }
        
        // 验证显示名称（如果提供）
        if (!displayName.isNullOrBlank()) {
            when (val displayNameResult = validateDisplayName(displayName)) {
                is ValidationResult.Invalid -> allErrors.addAll(displayNameResult.errors)
                ValidationResult.Valid -> { /* 验证通过 */ }
            }
        }
        
        return if (allErrors.isEmpty()) {
            ValidationResult.Valid
        } else {
            ValidationResult.Invalid(allErrors)
        }
    }
}

/**
 * 验证结果密封类
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Invalid(val errors: List<String>) : ValidationResult()
    
    val isValid: Boolean
        get() = this is Valid
    
    val isInvalid: Boolean
        get() = this is Invalid
    
    fun getErrorMessage(): String? {
        return when (this) {
            is Invalid -> errors.joinToString("\n")
            Valid -> null
        }
    }
}

/**
 * 验证结果扩展函数
 */
fun ValidationResult.onValid(action: () -> Unit): ValidationResult {
    if (this is ValidationResult.Valid) {
        action()
    }
    return this
}

fun ValidationResult.onInvalid(action: (List<String>) -> Unit): ValidationResult {
    if (this is ValidationResult.Invalid) {
        action(errors)
    }
    return this
}

/**
 * 验证个人简介
 * 规则：
 * - 可选字段
 * - 最大100个字符
 * - 不能包含前后空格
 */
fun validateBio(bio: String?): ValidationResult {
    val errors = mutableListOf<String>()

    if (!bio.isNullOrBlank()) {
        if (bio.length > 100) {
            errors.add("个人简介不能超过100个字符")
        }
        if (bio.trim() != bio) {
            errors.add("个人简介不能包含前后空格")
        }
    }

    return if (errors.isEmpty()) {
        ValidationResult.Valid
    } else {
        ValidationResult.Invalid(errors)
    }
}

/**
 * 验证生日
 * 规则：
 * - 可选字段
 * - 必须是有效的ISO日期格式
 * - 不能是未来日期
 * - 不能超过120年前
 */
fun validateBirthday(birthday: String?): ValidationResult {
    val errors = mutableListOf<String>()

    if (!birthday.isNullOrBlank()) {
        try {
            val date = LocalDate.parse(birthday, DateTimeFormatter.ISO_LOCAL_DATE)
            val now = LocalDate.now()

            if (date.isAfter(now)) {
                errors.add("生日不能是未来日期")
            } else if (date.isBefore(now.minusYears(120))) {
                errors.add("生日不能超过120年前")
            }
        } catch (e: DateTimeParseException) {
            errors.add("生日格式不正确，请使用YYYY-MM-DD格式")
        }
    }

    return if (errors.isEmpty()) {
        ValidationResult.Valid
    } else {
        ValidationResult.Invalid(errors)
    }
}

/**
 * 验证文件类型
 * 规则：
 * - 只支持JPEG和PNG格式
 */
fun validateImageType(mimeType: String?): ValidationResult {
    val errors = mutableListOf<String>()
    val allowedTypes = setOf("image/jpeg", "image/png", "image/jpg")

    when {
        mimeType.isNullOrBlank() -> errors.add("无法识别文件类型")
        mimeType !in allowedTypes -> errors.add("只支持JPEG和PNG格式的图片")
    }

    return if (errors.isEmpty()) {
        ValidationResult.Valid
    } else {
        ValidationResult.Invalid(errors)
    }
}

/**
 * 验证文件大小
 * 规则：
 * - 不能超过指定大小（默认5MB）
 */
fun validateFileSize(fileSize: Long, maxSize: Long = 5 * 1024 * 1024): ValidationResult {
    val errors = mutableListOf<String>()

    when {
        fileSize <= 0 -> errors.add("文件大小无效")
        fileSize > maxSize -> errors.add("文件大小不能超过${maxSize / 1024 / 1024}MB")
    }

    return if (errors.isEmpty()) {
        ValidationResult.Valid
    } else {
        ValidationResult.Invalid(errors)
    }
}

/**
 * 创建验证异常
 * 遵循项目异常处理规范
 */
fun createValidationException(
    field: String,
    value: Any?,
    validationResult: ValidationResult.Invalid
): com.yu.questicle.core.domain.exception.ProfileValidationException {
    return com.yu.questicle.core.domain.exception.ProfileValidationException(
        message = "Validation failed for field '$field': ${validationResult.errors.joinToString(", ")}",
        errorCode = "VALIDATION_ERROR",
        field = field,
        value = value
    )
}
