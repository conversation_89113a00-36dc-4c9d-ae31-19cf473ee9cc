package com.yu.questicle.core.domain.repository

import android.net.Uri
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.AvatarUploadResponse
import com.yu.questicle.core.domain.model.PasswordResetToken
import com.yu.questicle.core.domain.model.ProfileUpdateRequest
import com.yu.questicle.core.domain.model.User

/**
 * 用户资料相关数据仓库接口
 */
interface ProfileRepository {
    
    /**
     * 更新用户资料
     */
    suspend fun updateProfile(
        userId: String, 
        request: ProfileUpdateRequest
    ): Result<User>
    
    /**
     * 请求密码重置
     */
    suspend fun requestPasswordReset(email: String): Result<Unit>
    
    /**
     * 确认密码重置
     */
    suspend fun confirmPasswordReset(
        token: String, 
        newPassword: String, 
        confirmPassword: String
    ): Result<Unit>
    
    /**
     * 验证密码重置令牌
     */
    suspend fun validateResetToken(token: String): Result<PasswordResetToken>
    
    /**
     * 上传头像
     */
    suspend fun uploadAvatar(
        userId: String, 
        imageUri: Uri
    ): Result<AvatarUploadResponse>
    
    /**
     * 删除头像
     */
    suspend fun deleteAvatar(userId: String): Result<Unit>
    
    /**
     * 获取用户资料
     */
    suspend fun getUserProfile(userId: String): Result<User>
}

/**
 * 文件处理相关仓库接口
 */
interface FileRepository {
    
    /**
     * 压缩图片
     */
    suspend fun compressImage(
        uri: Uri, 
        maxWidth: Int = 400, 
        maxHeight: Int = 400, 
        quality: Int = 85
    ): Result<Uri>
    
    /**
     * 裁剪图片为正方形
     */
    suspend fun cropImageToSquare(uri: Uri): Result<Uri>
    
    /**
     * 上传文件到服务器
     */
    suspend fun uploadFile(
        uri: Uri, 
        fileName: String, 
        contentType: String
    ): Result<String>
    
    /**
     * 获取文件MIME类型
     */
    fun getMimeType(uri: Uri): String?
    
    /**
     * 验证文件类型
     */
    fun validateFileType(uri: Uri, allowedTypes: Set<String>): Boolean
    
    /**
     * 验证文件大小
     */
    fun validateFileSize(uri: Uri, maxSizeBytes: Long): Boolean
}
