package com.yu.questicle.core.domain.timer

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

/**
 * 游戏定时器接口
 */
interface GameTimer {
    /**
     * 定时器状态
     */
    val isRunning: StateFlow<Boolean>
    
    /**
     * 当前间隔时间
     */
    val currentInterval: StateFlow<Duration>
    
    /**
     * 启动定时器
     */
    fun start(interval: Duration, onTick: suspend () -> Unit)
    
    /**
     * 停止定时器
     */
    fun stop()
    
    /**
     * 暂停定时器
     */
    fun pause()
    
    /**
     * 恢复定时器
     */
    fun resume()
    
    /**
     * 更新间隔时间
     */
    fun updateInterval(interval: Duration)
    
    /**
     * 立即触发一次tick
     */
    suspend fun tick()
}

/**
 * 协程定时器实现
 */
class CoroutineGameTimer(
    private val scope: CoroutineScope
) : GameTimer {
    
    private val _isRunning = MutableStateFlow(false)
    override val isRunning = _isRunning.asStateFlow()
    
    private val _currentInterval = MutableStateFlow(1000.milliseconds)
    override val currentInterval = _currentInterval.asStateFlow()
    
    private var timerJob: Job? = null
    private var onTickCallback: (suspend () -> Unit)? = null
    private var isPaused = false
    
    override fun start(interval: Duration, onTick: suspend () -> Unit) {
        stop() // 停止之前的定时器
        
        _currentInterval.value = interval
        onTickCallback = onTick
        isPaused = false
        
        timerJob = scope.launch {
            _isRunning.value = true
            
            while (isActive && !isPaused) {
                try {
                    onTick()
                    delay(_currentInterval.value)
                } catch (e: CancellationException) {
                    break
                } catch (e: Exception) {
                    // 记录错误但继续运行
                    println("Timer tick error: ${e.message}")
                }
            }
            
            _isRunning.value = false
        }
    }
    
    override fun stop() {
        timerJob?.cancel()
        timerJob = null
        onTickCallback = null
        isPaused = false
        _isRunning.value = false
    }
    
    override fun pause() {
        isPaused = true
        _isRunning.value = false
    }
    
    override fun resume() {
        if (isPaused && onTickCallback != null) {
            isPaused = false
            start(_currentInterval.value, onTickCallback!!)
        }
    }
    
    override fun updateInterval(interval: Duration) {
        _currentInterval.value = interval
        
        // 如果定时器正在运行，重启以应用新间隔
        if (_isRunning.value && onTickCallback != null) {
            start(interval, onTickCallback!!)
        }
    }
    
    override suspend fun tick() {
        onTickCallback?.invoke()
    }
}

/**
 * 俄罗斯方块游戏定时器
 * 专门为俄罗斯方块游戏设计的定时器
 */
class TetrisGameTimer(
    private val scope: CoroutineScope
) {
    private val dropTimer = CoroutineGameTimer(scope)
    private val lockTimer = CoroutineGameTimer(scope)
    
    private val _gameTime = MutableStateFlow(0L)
    val gameTime = _gameTime.asStateFlow()
    
    private var gameTimeJob: Job? = null
    private var gameStartTime = 0L
    
    /**
     * 下降定时器状态
     */
    val isDropTimerRunning = dropTimer.isRunning
    
    /**
     * 锁定定时器状态
     */
    val isLockTimerRunning = lockTimer.isRunning
    
    /**
     * 启动下降定时器
     */
    fun startDropTimer(interval: Duration, onDrop: suspend () -> Unit) {
        dropTimer.start(interval, onDrop)
    }
    
    /**
     * 停止下降定时器
     */
    fun stopDropTimer() {
        dropTimer.stop()
    }
    
    /**
     * 暂停下降定时器
     */
    fun pauseDropTimer() {
        dropTimer.pause()
    }
    
    /**
     * 恢复下降定时器
     */
    fun resumeDropTimer() {
        dropTimer.resume()
    }
    
    /**
     * 更新下降间隔
     */
    fun updateDropInterval(interval: Duration) {
        dropTimer.updateInterval(interval)
    }
    
    /**
     * 启动锁定定时器
     */
    fun startLockTimer(delay: Duration, onLock: suspend () -> Unit) {
        lockTimer.start(delay, onLock)
    }
    
    /**
     * 停止锁定定时器
     */
    fun stopLockTimer() {
        lockTimer.stop()
    }
    
    /**
     * 重置锁定定时器
     */
    fun resetLockTimer(delay: Duration, onLock: suspend () -> Unit) {
        stopLockTimer()
        startLockTimer(delay, onLock)
    }
    
    /**
     * 启动游戏时间计时
     */
    fun startGameTime() {
        gameStartTime = System.currentTimeMillis()
        gameTimeJob = scope.launch {
            while (isActive) {
                _gameTime.value = System.currentTimeMillis() - gameStartTime
                delay(100) // 每100ms更新一次
            }
        }
    }
    
    /**
     * 停止游戏时间计时
     */
    fun stopGameTime() {
        gameTimeJob?.cancel()
        gameTimeJob = null
    }
    
    /**
     * 暂停游戏时间计时
     */
    fun pauseGameTime() {
        gameTimeJob?.cancel()
    }
    
    /**
     * 恢复游戏时间计时
     */
    fun resumeGameTime() {
        if (gameStartTime > 0) {
            gameStartTime = System.currentTimeMillis() - _gameTime.value
            startGameTime()
        }
    }
    
    /**
     * 重置游戏时间
     */
    fun resetGameTime() {
        stopGameTime()
        _gameTime.value = 0L
        gameStartTime = 0L
    }
    
    /**
     * 停止所有定时器
     */
    fun stopAll() {
        stopDropTimer()
        stopLockTimer()
        stopGameTime()
    }
    
    /**
     * 暂停所有定时器
     */
    fun pauseAll() {
        pauseDropTimer()
        stopLockTimer()
        pauseGameTime()
    }
    
    /**
     * 恢复所有定时器
     */
    fun resumeAll() {
        resumeDropTimer()
        resumeGameTime()
    }
    
    /**
     * 立即触发下降
     */
    suspend fun triggerDrop() {
        dropTimer.tick()
    }
}

/**
 * 定时器配置
 */
data class TimerConfig(
    val dropInterval: Duration = 1000.milliseconds,
    val lockDelay: Duration = 500.milliseconds,
    val autoRepeatDelay: Duration = 170.milliseconds,
    val autoRepeatInterval: Duration = 50.milliseconds
)

/**
 * 定时器工厂
 */
object GameTimerFactory {
    
    /**
     * 创建协程定时器
     */
    fun createCoroutineTimer(scope: CoroutineScope): GameTimer {
        return CoroutineGameTimer(scope)
    }
    
    /**
     * 创建俄罗斯方块定时器
     */
    fun createTetrisTimer(scope: CoroutineScope): TetrisGameTimer {
        return TetrisGameTimer(scope)
    }
}

/**
 * 定时器状态
 */
data class TimerState(
    val isDropTimerRunning: Boolean,
    val isLockTimerRunning: Boolean,
    val dropInterval: Duration,
    val gameTime: Long
) {
    /**
     * 格式化游戏时间
     */
    fun formatGameTime(): String {
        val totalSeconds = gameTime / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
}
