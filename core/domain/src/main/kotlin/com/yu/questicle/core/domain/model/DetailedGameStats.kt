package com.yu.questicle.core.domain.model

import kotlinx.serialization.Serializable

/**
 * 详细游戏统计数据
 * 实现 REQ-STATS-001 到 REQ-STATS-010: 完整的数据统计功能
 */
@Serializable
data class DetailedGameStats(
    val userId: String,
    val gameType: GameType,
    
    // 基础统计 (REQ-STATS-001-005)
    val totalGames: Long = 0,
    val totalPlayTime: Long = 0,        // 秒
    val totalScore: Long = 0,
    val gamesWon: Long = 0,
    val gamesLost: Long = 0,
    val currentWinStreak: Int = 0,
    val bestWinStreak: Int = 0,
    val averageScore: Double = 0.0,
    val bestScore: Long = 0,
    val worstScore: Long = 0,
    
    // 俄罗斯方块专项统计
    val totalLinesCleared: Long = 0,
    val singleLineClears: Long = 0,
    val doubleLineClears: Long = 0,
    val tripleLineClears: Long = 0,
    val tetrisClears: Long = 0,
    val tSpinSingles: Long = 0,
    val tSpinDoubles: Long = 0,
    val tSpinTriples: Long = 0,
    val maxCombo: Int = 0,
    val totalPiecesPlaced: Long = 0,
    val totalHolds: Long = 0,
    val perfectClears: Long = 0,
    
    // 方块类型统计
    val pieceStats: Map<String, PieceStatistics> = emptyMap(),
    
    // 时间统计
    val averageGameDuration: Double = 0.0,
    val shortestGame: Long = 0,
    val longestGame: Long = 0,
    val totalSessionTime: Long = 0,
    val averageSessionDuration: Double = 0.0,
    
    // 性能统计
    val averagePiecesPerMinute: Double = 0.0,
    val averageLinesPerMinute: Double = 0.0,
    val averagePointsPerMinute: Double = 0.0,
    val efficiency: Double = 0.0,       // 分数/时间比率
    val consistency: Double = 0.0,      // 分数一致性
    
    // 等级和进度统计
    val highestLevel: Int = 0,
    val averageLevel: Double = 0.0,
    val levelUps: Long = 0,
    val experienceGained: Long = 0,
    
    // 错误和失误统计
    val mistakeCount: Long = 0,
    val averageMistakesPerGame: Double = 0.0,
    val recoveryRate: Double = 0.0,     // 从错误中恢复的比率
    
    // 时间维度统计
    val dailyStats: Map<String, DailyStats> = emptyMap(),  // YYYY-MM-DD -> DailyStats
    val weeklyStats: Map<String, WeeklyStats> = emptyMap(), // YYYY-WW -> WeeklyStats
    val monthlyStats: Map<String, MonthlyStats> = emptyMap(), // YYYY-MM -> MonthlyStats
    
    val lastUpdated: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 方块统计数据
 */
@Serializable
data class PieceStatistics(
    val type: String,
    val totalUsed: Long = 0,
    val averageTimeToPlace: Double = 0.0,
    val rotationsUsed: Long = 0,
    val averageRotations: Double = 0.0,
    val holdUsage: Long = 0,
    val efficiency: Double = 0.0,      // 该方块的使用效率
    val preferredRotation: String = "NONE"
)

/**
 * 每日统计数据
 */
@Serializable
data class DailyStats(
    val date: String,                   // YYYY-MM-DD
    val gamesPlayed: Int = 0,
    val totalScore: Long = 0,
    val totalPlayTime: Long = 0,
    val bestScore: Long = 0,
    val linesCleared: Long = 0,
    val averageGameDuration: Double = 0.0,
    val winRate: Double = 0.0
)

/**
 * 每周统计数据
 */
@Serializable
data class WeeklyStats(
    val week: String,                   // YYYY-WW
    val gamesPlayed: Int = 0,
    val totalScore: Long = 0,
    val totalPlayTime: Long = 0,
    val bestScore: Long = 0,
    val averageScore: Double = 0.0,
    val improvement: Double = 0.0,      // 与上周相比的改进
    val consistency: Double = 0.0,
    val activeDays: Int = 0
)

/**
 * 每月统计数据
 */
@Serializable
data class MonthlyStats(
    val month: String,                  // YYYY-MM
    val gamesPlayed: Int = 0,
    val totalScore: Long = 0,
    val totalPlayTime: Long = 0,
    val bestScore: Long = 0,
    val averageScore: Double = 0.0,
    val monthlyGoalProgress: Double = 0.0,
    val achievements: List<String> = emptyList(),
    val activeDays: Int = 0,
    val longestStreak: Int = 0
)

/**
 * 时间维度枚举
 */
enum class TimeFrame {
    DAILY, WEEKLY, MONTHLY, YEARLY, ALL_TIME
}

/**
 * 趋势数据点
 */
@Serializable
data class TrendDataPoint(
    val timestamp: Long,
    val value: Double,
    val label: String,
    val metadata: Map<String, String> = emptyMap()
)

/**
 * 趋势分析结果
 */
@Serializable
data class TrendAnalysis(
    val metric: StatisticMetric,
    val timeFrame: TimeFrame,
    val dataPoints: List<TrendDataPoint>,
    val trend: TrendDirection,
    val changePercentage: Double,
    val summary: String,
    val insights: List<String> = emptyList(),
    val predictions: List<TrendPrediction> = emptyList()
)

/**
 * 趋势方向
 */
enum class TrendDirection {
    INCREASING,     // 上升趋势
    DECREASING,     // 下降趋势
    STABLE,         // 稳定
    VOLATILE,       // 波动
    SEASONAL        // 季节性变化
}

/**
 * 统计指标
 */
enum class StatisticMetric {
    TOTAL_SCORE,
    AVERAGE_SCORE,
    GAMES_PLAYED,
    WIN_RATE,
    PLAY_TIME,
    EFFICIENCY,
    LINES_CLEARED,
    PIECES_PER_MINUTE,
    LEVEL_PROGRESSION,
    CONSISTENCY,
    IMPROVEMENT_RATE,
    MISTAKE_RATE
}

/**
 * 趋势预测
 */
@Serializable
data class TrendPrediction(
    val metric: StatisticMetric,
    val predictedValue: Double,
    val confidence: Double,
    val timeframe: String,
    val description: String
)

/**
 * 性能报告
 */
@Serializable
data class PerformanceReport(
    val userId: String,
    val gameType: GameType,
    val reportPeriod: TimeFrame,
    val overallStats: DetailedGameStats,
    val trends: Map<StatisticMetric, TrendAnalysis>,
    val insights: List<PerformanceInsight>,
    val recommendations: List<String>,
    val strengths: List<String>,
    val areasForImprovement: List<String>,
    val goals: List<PerformanceGoal>,
    val generatedAt: Long = System.currentTimeMillis()
)

/**
 * 性能洞察
 */
@Serializable
data class PerformanceInsight(
    val type: InsightType,
    val title: String,
    val description: String,
    val impact: InsightImpact,
    val actionable: Boolean = false,
    val action: String? = null,
    val supportingData: Map<String, Double> = emptyMap()
)

/**
 * 洞察类型
 */
enum class InsightType {
    IMPROVEMENT,        // 改进
    ACHIEVEMENT,        // 成就
    PATTERN,           // 模式
    WARNING,           // 警告
    RECOMMENDATION,    // 建议
    MILESTONE,         // 里程碑
    COMPARISON         // 对比
}

/**
 * 洞察影响程度
 */
enum class InsightImpact {
    HIGH,              // 高影响
    MEDIUM,            // 中等影响
    LOW                // 低影响
}

/**
 * 性能目标
 */
@Serializable
data class PerformanceGoal(
    val id: String,
    val title: String,
    val description: String,
    val targetValue: Double,
    val currentValue: Double,
    val metric: StatisticMetric,
    val deadline: Long?,
    val progress: Double,
    val isCompleted: Boolean = false,
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 比较分析结果
 */
@Serializable
data class ComparisonAnalysis(
    val userStats: DetailedGameStats,
    val comparisonTarget: ComparisonTarget,
    val comparisons: Map<StatisticMetric, ComparisonResult>,
    val overallRanking: Int,
    val totalPlayers: Int,
    val percentile: Double
)

/**
 * 比较目标
 */
@Serializable
sealed class ComparisonTarget {
    @Serializable
    object GlobalAverage : ComparisonTarget()
    @Serializable
    object FriendsList : ComparisonTarget()
    @Serializable
    data class SpecificUser(val userId: String, val username: String) : ComparisonTarget()
    @Serializable
    data class LevelGroup(val level: Int) : ComparisonTarget()
}

/**
 * 比较结果
 */
@Serializable
data class ComparisonResult(
    val metric: StatisticMetric,
    val userValue: Double,
    val comparisonValue: Double,
    val difference: Double,
    val percentageDifference: Double,
    val isBetter: Boolean,
    val ranking: Int?,
    val description: String
)

/**
 * 统计数据摘要
 */
@Serializable
data class StatsSummary(
    val totalGames: Long,
    val totalPlayTime: Long,
    val averageScore: Double,
    val bestScore: Long,
    val currentLevel: Int,
    val winRate: Double,
    val recentTrend: TrendDirection,
    val keyAchievements: List<String>
)
