package com.yu.questicle.core.domain.model

import kotlinx.serialization.Serializable

/**
 * Achievement model for the gaming platform
 */
@Serializable
data class Achievement(
    val id: String,
    val title: String,
    val description: String,
    val iconUrl: String? = null,
    val category: AchievementCategory,
    val type: AchievementType,
    val requirement: AchievementRequirement,
    val reward: AchievementReward? = null,
    val isHidden: Boolean = false,
    val sortOrder: Int = 0,
    val createdAt: Long = System.currentTimeMillis() / 1000
)

/**
 * User's achievement progress and unlock status
 */
@Serializable
data class UserAchievement(
    val achievementId: String,
    val userId: String,
    val isUnlocked: Boolean = false,
    val progress: Float = 0f, // 0.0 to 1.0
    val currentValue: Long = 0L,
    val unlockedAt: Long? = null,
    val lastUpdatedAt: Long = System.currentTimeMillis() / 1000
) {
    val isCompleted: Boolean get() = progress >= 1.0f
    val progressPercentage: Int get() = (progress * 100).toInt()
}

/**
 * Achievement categories
 */
@Serializable
enum class AchievementCategory {
    GAMEPLAY,      // 游戏玩法相关
    PROGRESSION,   // 进度相关
    SKILL,         // 技能相关
    SOCIAL,        // 社交相关
    COLLECTION,    // 收集相关
    SPECIAL        // 特殊成就
}

/**
 * Achievement types
 */
@Serializable
enum class AchievementType {
    SINGLE,        // 一次性成就
    INCREMENTAL,   // 累积型成就
    STREAK,        // 连续型成就
    MILESTONE      // 里程碑成就
}

/**
 * Achievement requirements
 */
@Serializable
sealed class AchievementRequirement {
    @Serializable
    data class ScoreRequirement(
        val targetScore: Long,
        val gameType: GameType? = null // null means any game type
    ) : AchievementRequirement()
    
    @Serializable
    data class GamesPlayedRequirement(
        val targetGames: Int,
        val gameType: GameType? = null
    ) : AchievementRequirement()
    
    @Serializable
    data class WinStreakRequirement(
        val targetStreak: Int,
        val gameType: GameType? = null
    ) : AchievementRequirement()
    
    @Serializable
    data class LevelRequirement(
        val targetLevel: Int
    ) : AchievementRequirement()
    
    @Serializable
    data class TetrisSpecificRequirement(
        val type: TetrisAchievementType,
        val targetValue: Long
    ) : AchievementRequirement()
    
    @Serializable
    data class TimeBasedRequirement(
        val targetTimeSeconds: Long,
        val condition: TimeCondition
    ) : AchievementRequirement()
    
    @Serializable
    data class ComboRequirement(
        val requirements: List<AchievementRequirement>,
        val requireAll: Boolean = true // true = AND, false = OR
    ) : AchievementRequirement()
}

/**
 * Tetris-specific achievement types
 */
@Serializable
enum class TetrisAchievementType {
    LINES_CLEARED,
    TETRISES,
    T_SPINS,
    PERFECT_CLEARS,
    BACK_TO_BACK,
    MAX_LEVEL_REACHED,
    PIECES_PER_MINUTE,
    EFFICIENCY_RATE
}

/**
 * Time-based conditions
 */
@Serializable
enum class TimeCondition {
    TOTAL_PLAY_TIME,    // 总游戏时间
    SINGLE_SESSION,     // 单次游戏时间
    DAILY_PLAY_TIME,    // 每日游戏时间
    WEEKLY_PLAY_TIME    // 每周游戏时间
}

/**
 * Achievement rewards
 */
@Serializable
data class AchievementReward(
    val experience: Long = 0L,
    val coins: Long = 0L,
    val gems: Long = 0L,
    val title: String? = null,
    val avatar: String? = null,
    val specialItems: List<String> = emptyList()
)

/**
 * Achievement progress update event
 */
data class AchievementProgressEvent(
    val userId: String,
    val gameScore: Long = 0L,
    val gameDuration: Long = 0L,
    val gameType: GameType? = null,
    val userStats: UserStats? = null,
    val customData: Map<String, Any> = emptyMap()
)

/**
 * Achievement progress result
 */
data class AchievementProgress(
    val currentProgress: Float,
    val isUnlocked: Boolean
)



/**
 * Achievement unlock result
 */
data class AchievementUnlockResult(
    val newlyUnlocked: List<Achievement>,
    val progressUpdated: List<UserAchievement>,
    val totalRewards: AchievementReward
)

/**
 * Predefined achievements for Tetris game
 */
object PredefinedAchievements {
    
    val FIRST_GAME = Achievement(
        id = "first_game",
        title = "First Steps",
        description = "Play your first game",
        category = AchievementCategory.PROGRESSION,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.GamesPlayedRequirement(1),
        reward = AchievementReward(experience = 100L, coins = 50L)
    )
    
    val SCORE_1000 = Achievement(
        id = "score_1000",
        title = "Getting Started",
        description = "Score 1,000 points in a single game",
        category = AchievementCategory.SKILL,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.ScoreRequirement(1000L, GameType.TETRIS),
        reward = AchievementReward(experience = 200L, coins = 100L)
    )
    
    val SCORE_10000 = Achievement(
        id = "score_10000",
        title = "Rising Star",
        description = "Score 10,000 points in a single game",
        category = AchievementCategory.SKILL,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.ScoreRequirement(10000L, GameType.TETRIS),
        reward = AchievementReward(experience = 500L, coins = 250L)
    )
    
    val FIRST_TETRIS = Achievement(
        id = "first_tetris",
        title = "Tetris Master",
        description = "Clear 4 lines at once (Tetris)",
        category = AchievementCategory.SKILL,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.TetrisSpecificRequirement(
            TetrisAchievementType.TETRISES, 1L
        ),
        reward = AchievementReward(experience = 300L, coins = 150L)
    )
    
    val T_SPIN_MASTER = Achievement(
        id = "t_spin_master",
        title = "T-Spin Expert",
        description = "Perform 10 T-Spins",
        category = AchievementCategory.SKILL,
        type = AchievementType.INCREMENTAL,
        requirement = AchievementRequirement.TetrisSpecificRequirement(
            TetrisAchievementType.T_SPINS, 10L
        ),
        reward = AchievementReward(experience = 800L, coins = 400L)
    )
    
    val LEVEL_10 = Achievement(
        id = "level_10",
        title = "Experienced Player",
        description = "Reach player level 10",
        category = AchievementCategory.PROGRESSION,
        type = AchievementType.MILESTONE,
        requirement = AchievementRequirement.LevelRequirement(10),
        reward = AchievementReward(experience = 1000L, coins = 500L, gems = 10L)
    )
    
    val GAMES_100 = Achievement(
        id = "games_100",
        title = "Dedicated Player",
        description = "Play 100 games",
        category = AchievementCategory.PROGRESSION,
        type = AchievementType.INCREMENTAL,
        requirement = AchievementRequirement.GamesPlayedRequirement(100),
        reward = AchievementReward(experience = 1500L, coins = 750L, gems = 15L)
    )
    
    val WIN_STREAK_5 = Achievement(
        id = "win_streak_5",
        title = "On Fire",
        description = "Win 5 games in a row",
        category = AchievementCategory.SKILL,
        type = AchievementType.STREAK,
        requirement = AchievementRequirement.WinStreakRequirement(5),
        reward = AchievementReward(experience = 600L, coins = 300L)
    )
    
    val PERFECT_CLEAR = Achievement(
        id = "perfect_clear",
        title = "Perfect Clear",
        description = "Achieve a perfect clear",
        category = AchievementCategory.SKILL,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.TetrisSpecificRequirement(
            TetrisAchievementType.PERFECT_CLEARS, 1L
        ),
        reward = AchievementReward(experience = 1000L, coins = 500L, gems = 20L)
    )
    
    val SPEED_DEMON = Achievement(
        id = "speed_demon",
        title = "Speed Demon",
        description = "Place 60 pieces per minute",
        category = AchievementCategory.SKILL,
        type = AchievementType.SINGLE,
        requirement = AchievementRequirement.TetrisSpecificRequirement(
            TetrisAchievementType.PIECES_PER_MINUTE, 60L
        ),
        reward = AchievementReward(experience = 800L, coins = 400L, gems = 15L)
    )
    
    /**
     * Get all predefined achievements
     */
    fun getAllAchievements(): List<Achievement> = listOf(
        FIRST_GAME,
        SCORE_1000,
        SCORE_10000,
        FIRST_TETRIS,
        T_SPIN_MASTER,
        LEVEL_10,
        GAMES_100,
        WIN_STREAK_5,
        PERFECT_CLEAR,
        SPEED_DEMON
    )
}
