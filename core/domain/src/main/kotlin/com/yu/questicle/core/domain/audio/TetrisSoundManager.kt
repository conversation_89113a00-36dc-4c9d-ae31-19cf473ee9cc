package com.yu.questicle.core.domain.audio

import kotlinx.coroutines.flow.StateFlow

/**
 * 俄罗斯方块音效管理器接口
 */
interface TetrisSoundManager {
    /**
     * 音效是否启用
     */
    val isSoundEnabled: StateFlow<Boolean>
    
    /**
     * 音量设置 (0.0 - 1.0)
     */
    val volume: StateFlow<Float>
    
    /**
     * 播放方块移动音效
     */
    fun playMoveSound()
    
    /**
     * 播放方块旋转音效
     */
    fun playRotateSound()
    
    /**
     * 播放方块下降音效
     */
    fun playDropSound()
    
    /**
     * 播放硬降音效
     */
    fun playHardDropSound()
    
    /**
     * 播放方块锁定音效
     */
    fun playLockSound()
    
    /**
     * 播放行消除音效
     */
    fun playLineClearSound(lines: Int)
    
    /**
     * 播放四行消除音效 (Tetris)
     */
    fun playTetrisSound()
    
    /**
     * 播放T-Spin音效
     */
    fun playTSpinSound()
    
    /**
     * 播放等级提升音效
     */
    fun playLevelUpSound()
    
    /**
     * 播放游戏结束音效
     */
    fun playGameOverSound()
    
    /**
     * 播放暂停音效
     */
    fun playPauseSound()
    
    /**
     * 播放方块保持音效
     */
    fun playHoldSound()
    
    /**
     * 播放连击音效
     */
    fun playComboSound(combo: Int)
    
    /**
     * 播放完美消除音效
     */
    fun playPerfectClearSound()
    
    /**
     * 设置音效开关
     */
    fun setSoundEnabled(enabled: Boolean)
    
    /**
     * 设置音量
     */
    fun setVolume(volume: Float)
    
    /**
     * 停止所有音效
     */
    fun stopAllSounds()
    
    /**
     * 预加载音效资源
     */
    suspend fun preloadSounds()
    
    /**
     * 释放音效资源
     */
    fun releaseSounds()
}

/**
 * 音效类型枚举
 */
enum class TetrisSoundType {
    MOVE,           // 移动
    ROTATE,         // 旋转
    DROP,           // 下降
    HARD_DROP,      // 硬降
    LOCK,           // 锁定
    LINE_CLEAR_1,   // 单行消除
    LINE_CLEAR_2,   // 双行消除
    LINE_CLEAR_3,   // 三行消除
    TETRIS,         // 四行消除
    T_SPIN,         // T-Spin
    LEVEL_UP,       // 等级提升
    GAME_OVER,      // 游戏结束
    PAUSE,          // 暂停
    HOLD,           // 保持
    COMBO,          // 连击
    PERFECT_CLEAR   // 完美消除
}

/**
 * 音效配置
 */
data class SoundConfig(
    val enabled: Boolean = true,
    val volume: Float = 0.7f,
    val enableVibration: Boolean = true,
    val vibrationIntensity: Float = 0.5f
)

/**
 * 触觉反馈管理器接口
 */
interface TetrisHapticManager {
    /**
     * 触觉反馈是否启用
     */
    val isHapticEnabled: StateFlow<Boolean>
    
    /**
     * 触觉反馈强度 (0.0 - 1.0)
     */
    val intensity: StateFlow<Float>
    
    /**
     * 轻微触觉反馈 (移动、旋转)
     */
    fun performLightHaptic()
    
    /**
     * 中等触觉反馈 (下降、锁定)
     */
    fun performMediumHaptic()
    
    /**
     * 强烈触觉反馈 (行消除、游戏结束)
     */
    fun performHeavyHaptic()
    
    /**
     * 成功触觉反馈 (Tetris、T-Spin)
     */
    fun performSuccessHaptic()
    
    /**
     * 警告触觉反馈 (游戏即将结束)
     */
    fun performWarningHaptic()
    
    /**
     * 设置触觉反馈开关
     */
    fun setHapticEnabled(enabled: Boolean)
    
    /**
     * 设置触觉反馈强度
     */
    fun setIntensity(intensity: Float)
}

/**
 * 触觉反馈类型
 */
enum class TetrisHapticType {
    LIGHT,      // 轻微
    MEDIUM,     // 中等
    HEAVY,      // 强烈
    SUCCESS,    // 成功
    WARNING     // 警告
}

/**
 * 音效和触觉反馈的统一管理器
 */
interface TetrisAudioManager {
    val soundManager: TetrisSoundManager
    val hapticManager: TetrisHapticManager
    
    /**
     * 播放动作反馈 (音效 + 触觉)
     */
    fun playActionFeedback(action: TetrisActionFeedback)
    
    /**
     * 播放事件反馈
     */
    fun playEventFeedback(event: TetrisEventFeedback)
    
    /**
     * 设置全局配置
     */
    fun updateConfig(config: SoundConfig)
}

/**
 * 动作反馈类型
 */
enum class TetrisActionFeedback {
    MOVE,
    ROTATE,
    DROP,
    HARD_DROP,
    HOLD,
    LOCK
}

/**
 * 事件反馈类型
 */
enum class TetrisEventFeedback {
    LINE_CLEAR_SINGLE,
    LINE_CLEAR_DOUBLE,
    LINE_CLEAR_TRIPLE,
    TETRIS,
    T_SPIN,
    LEVEL_UP,
    GAME_OVER,
    PAUSE,
    COMBO,
    PERFECT_CLEAR
}

/**
 * 音效资源路径配置
 */
object TetrisSoundPaths {
    const val MOVE = "sounds/tetris/move.wav"
    const val ROTATE = "sounds/tetris/rotate.wav"
    const val DROP = "sounds/tetris/drop.wav"
    const val HARD_DROP = "sounds/tetris/hard_drop.wav"
    const val LOCK = "sounds/tetris/lock.wav"
    const val LINE_CLEAR_1 = "sounds/tetris/line_clear_1.wav"
    const val LINE_CLEAR_2 = "sounds/tetris/line_clear_2.wav"
    const val LINE_CLEAR_3 = "sounds/tetris/line_clear_3.wav"
    const val TETRIS = "sounds/tetris/tetris.wav"
    const val T_SPIN = "sounds/tetris/t_spin.wav"
    const val LEVEL_UP = "sounds/tetris/level_up.wav"
    const val GAME_OVER = "sounds/tetris/game_over.wav"
    const val PAUSE = "sounds/tetris/pause.wav"
    const val HOLD = "sounds/tetris/hold.wav"
    const val COMBO = "sounds/tetris/combo.wav"
    const val PERFECT_CLEAR = "sounds/tetris/perfect_clear.wav"
}

/**
 * 音效播放优先级
 */
enum class SoundPriority {
    LOW,        // 低优先级 (移动、旋转)
    MEDIUM,     // 中等优先级 (下降、锁定)
    HIGH,       // 高优先级 (行消除、等级提升)
    CRITICAL    // 关键优先级 (游戏结束、Tetris)
}
