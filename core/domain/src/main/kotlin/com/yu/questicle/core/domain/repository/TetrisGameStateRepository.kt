package com.yu.questicle.core.domain.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for Tetris game state persistence
 * 专门用于Tetris游戏状态的保存和加载
 */
interface TetrisGameStateRepository {
    
    /**
     * 保存Tetris游戏状态
     * @param gameState 要保存的游戏状态
     * @param playerId 玩家ID
     * @param slotId 存档槽位ID (支持多个存档)
     * @return 保存结果
     */
    suspend fun saveGameState(
        gameState: TetrisGameState,
        playerId: String,
        slotId: String = "default"
    ): Result<Unit>
    
    /**
     * 加载Tetris游戏状态
     * @param playerId 玩家ID
     * @param slotId 存档槽位ID
     * @return 加载的游戏状态
     */
    suspend fun loadGameState(
        playerId: String,
        slotId: String = "default"
    ): Result<TetrisGameState>
    
    /**
     * 获取玩家的所有存档
     * @param playerId 玩家ID
     * @return 存档列表
     */
    fun getGameStateSaves(playerId: String): Flow<List<TetrisGameStateSave>>
    
    /**
     * 删除游戏状态存档
     * @param playerId 玩家ID
     * @param slotId 存档槽位ID
     * @return 删除结果
     */
    suspend fun deleteGameState(
        playerId: String,
        slotId: String
    ): Result<Unit>
    
    /**
     * 检查存档是否存在
     * @param playerId 玩家ID
     * @param slotId 存档槽位ID
     * @return 是否存在
     */
    suspend fun hasGameState(
        playerId: String,
        slotId: String
    ): Boolean
    
    /**
     * 获取存档的元数据
     * @param playerId 玩家ID
     * @param slotId 存档槽位ID
     * @return 存档元数据
     */
    suspend fun getGameStateMetadata(
        playerId: String,
        slotId: String
    ): Result<TetrisGameStateMetadata>
}

/**
 * Tetris游戏状态存档信息
 */
data class TetrisGameStateSave(
    val slotId: String,
    val playerId: String,
    val saveTime: Long,
    val gameState: TetrisGameState,
    val metadata: TetrisGameStateMetadata
)

/**
 * Tetris游戏状态元数据
 */
data class TetrisGameStateMetadata(
    val level: Int,
    val score: Int,
    val lines: Int,
    val playTime: Long,
    val saveTime: Long,
    val gameVersion: String = "1.0.0"
)
