package com.yu.questicle.core.domain.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.model.UserStats
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for user data operations
 */
interface UserRepository {
    
    /**
     * Get current user
     */
    fun getCurrentUser(): Flow<User?>
    
    /**
     * Save user data
     */
    suspend fun saveUser(user: User): Result<Unit>
    
    /**
     * Load user by ID
     */
    suspend fun loadUser(userId: String): Result<User>

    /**
     * Get user by ID (alias for loadUser)
     */
    suspend fun getUserById(userId: String): Result<User> = loadUser(userId)

    /**
     * Get user by username
     */
    suspend fun getUserByUsername(username: String): Result<User>

    /**
     * Get user by email
     */
    suspend fun getUserByEmail(email: String): Result<User>

    /**
     * Set current user
     */
    suspend fun setCurrentUser(user: User): Result<Unit>

    /**
     * Clear current user
     */
    suspend fun clearCurrentUser(): Result<Unit>

    /**
     * Create user with full data
     */
    suspend fun createUser(user: User): Result<User>

    /**
     * Update user
     */
    suspend fun updateUser(user: User): Result<User>
    
    /**
     * Create a new user with basic info
     */
    suspend fun createUserWithBasicInfo(username: String, email: String? = null, passwordHash: String? = null): Result<User>
    
    /**
     * Create guest user
     */
    suspend fun createGuestUser(): Result<User>
    
    /**
     * Update user preferences
     */
    suspend fun updatePreferences(userId: String, preferences: UserPreferences): Result<Unit>
    
    /**
     * Update user statistics
     */
    suspend fun updateStats(userId: String, stats: UserStats): Result<Unit>
    
    suspend fun findUsersByLevel(level: Int): Result<List<User>>

    /**
     * Add coins
     */
    suspend fun addCoins(userId: String, coins: Long): Result<User>
    
    /**
     * Add gems
     */
    suspend fun addGems(userId: String, gems: Long): Result<User>
    
    /**
     * Add achievement
     */
    suspend fun addAchievement(userId: String, achievementId: String): Result<Unit>
    
    /**
     * Add friend
     */
    suspend fun addFriend(userId: String, friendId: String): Result<Unit>
    
    /**
     * Remove friend
     */
    suspend fun removeFriend(userId: String, friendId: String): Result<Unit>
    
    /**
     * Get friends list
     */
    suspend fun getFriends(userId: String): Result<List<User>>
    
    /**
     * Search users by username
     */
    suspend fun searchUsers(query: String): Result<List<User>>
    
    /**
     * Get user leaderboard
     */
    suspend fun getUserLeaderboard(limit: Int = 10): Result<List<User>>
    
    /**
     * Delete user
     */
    suspend fun deleteUser(userId: String): Result<Unit>
}
