package com.yu.questicle.core.domain.model

/**
 * Base interface for all game actions
 */
sealed interface GameAction {
    val playerId: String
    val timestamp: Long
}

/**
 * Tetris game actions
 */
sealed class TetrisAction : GameAction {
    override val timestamp: Long = System.currentTimeMillis() / 1000

    data class Move(
        val direction: Direction,
        override val playerId: String
    ) : TetrisAction()

    data class Rotate(
        val clockwise: <PERSON><PERSON>an = true,
        override val playerId: String
    ) : TetrisAction()

    data class Drop(
        val hard: Boolean = false,
        override val playerId: String
    ) : TetrisAction()

    data class Hold(
        override val playerId: String
    ) : TetrisAction()

    data class Pause(
        override val playerId: String
    ) : TetrisAction()
}

/**
 * Movement directions
 */
enum class Direction {
    LEFT, RIGHT, DOWN
}
