package com.yu.questicle.core.domain.usecase.user

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.model.LevelUpEvent
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.membershipTier
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.service.MembershipService
import com.yu.questicle.core.domain.usecase.UseCase
import com.yu.questicle.core.domain.usecase.UseCaseValidation
import javax.inject.Inject

/**
 * Use case for adding experience to a user and handling level-up events
 * 
 * This use case coordinates between the UserRepository and MembershipService
 * to properly handle experience addition and level progression.
 */
class AddExperienceUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val membershipService: MembershipService
) : UseCase<AddExperienceUseCase.Input, AddExperienceUseCase.Output> {

    data class Input(
        val userId: String,
        val experience: Long,
        val source: String? = null // Optional source tracking
    )

    data class Output(
        val levelUpEvent: LevelUpEvent,
        val updatedUser: User
    )

    override suspend fun execute(input: Input): Result<Output> {
        return try {
            // Validate input
            UseCaseValidation.validateNotBlank(input.userId, "userId")
            UseCaseValidation.validateNonNegative(input.experience, "experience")

            // Get current user
            val userResult = userRepository.getUserById(input.userId)
            if (userResult is Result.Error) {
                return userResult
            }
            val currentUser = (userResult as Result.Success).data

            // Calculate experience with multiplier
            val actualExperience = membershipService.calculateExperienceWithMultiplier(
                input.experience, 
                currentUser
            )

            // Calculate new level and experience
            val oldLevel = currentUser.level
            val newTotalExperience = currentUser.experience + actualExperience
            val newLevel = membershipService.calculateLevel(newTotalExperience)

            // Update user with new experience and level
            val updatedUser = currentUser.copy(
                experience = newTotalExperience,
                level = newLevel,
                lastLoginAt = System.currentTimeMillis() / 1000
            )

            // Save updated user
            val updateResult = userRepository.updateUser(updatedUser)
            if (updateResult is Result.Error) {
                return updateResult
            }

            // Handle level-up rewards
            var finalUser = updatedUser
            val levelUpEvent = if (newLevel > oldLevel) {
                val rewards = membershipService.getLevelRewards(newLevel)
                
                // Apply rewards to user
                finalUser = applyRewardsToUser(finalUser, rewards)
                
                // Save user with rewards
                val rewardUpdateResult = userRepository.updateUser(finalUser)
                if (rewardUpdateResult is Result.Error) {
                    return rewardUpdateResult
                }

                LevelUpEvent(oldLevel, newLevel, rewards)
            } else {
                LevelUpEvent(oldLevel, oldLevel, emptyList())
            }

            Result.Success(
                Output(
                    levelUpEvent = levelUpEvent,
                    updatedUser = finalUser
                )
            )
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    private fun applyRewardsToUser(user: User, rewards: List<com.yu.questicle.core.domain.model.Reward>): User {
        var updatedUser = user
        
        rewards.forEach { reward ->
            when (reward.type) {
                com.yu.questicle.core.domain.model.RewardType.COINS -> {
                    updatedUser = updatedUser.copy(
                        coins = updatedUser.coins + reward.amount
                    )
                }
                com.yu.questicle.core.domain.model.RewardType.GEMS -> {
                    updatedUser = updatedUser.copy(
                        gems = updatedUser.gems + reward.amount
                    )
                }
                com.yu.questicle.core.domain.model.RewardType.ACHIEVEMENT -> {
                    // Handle achievement rewards if needed
                }
                else -> {
                    // Handle other reward types
                }
            }
        }
        
        return updatedUser
    }
}

/**
 * Use case for getting user level information
 */
class GetUserLevelInfoUseCase @Inject constructor(
    private val userRepository: UserRepository,
    private val membershipService: MembershipService
) : UseCase<GetUserLevelInfoUseCase.Input, GetUserLevelInfoUseCase.Output> {

    data class Input(
        val userId: String
    )

    data class Output(
        val currentLevel: Int,
        val currentExperience: Long,
        val experienceToNextLevel: Long,
        val levelProgress: Float,
        val membershipTier: com.yu.questicle.core.domain.model.MembershipTier
    )

    override suspend fun execute(input: Input): Result<Output> {
        return try {
            UseCaseValidation.validateNotBlank(input.userId, "userId")

            val userResult = userRepository.getUserById(input.userId)
            if (userResult is Result.Error) {
                return userResult
            }
            val user = (userResult as Result.Success).data

            Result.Success(
                Output(
                    currentLevel = user.level,
                    currentExperience = user.experience,
                    experienceToNextLevel = membershipService.getExperienceToNextLevel(user.experience),
                    levelProgress = membershipService.calculateLevelProgress(user.experience),
                    membershipTier = user.membershipTier
                )
            )
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
} 