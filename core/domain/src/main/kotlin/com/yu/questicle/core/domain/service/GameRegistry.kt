package com.yu.questicle.core.domain.service

import com.yu.questicle.core.domain.model.GameDifficulty
import com.yu.questicle.core.domain.model.GameType
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 游戏注册系统
 * 实现 REQ-GAME-001 到 REQ-GAME-005: 游戏列表和选择功能
 */
@Singleton
class GameRegistry @Inject constructor() {
    
    private val registeredGames = mutableMapOf<GameType, GameInfo>()
    
    init {
        // 注册默认游戏
        registerDefaultGames()
    }
    
    /**
     * 注册游戏信息
     */
    fun registerGame(gameInfo: GameInfo) {
        registeredGames[gameInfo.type] = gameInfo
    }
    
    /**
     * 获取所有可用游戏
     */
    fun getAvailableGames(): List<GameInfo> {
        return registeredGames.values
            .filter { it.isAvailable }
            .sortedBy { it.displayOrder }
    }
    
    /**
     * 根据用户等级获取可用游戏
     */
    fun getAvailableGamesForLevel(userLevel: Int): List<GameInfo> {
        return getAvailableGames()
            .filter { it.minLevel <= userLevel }
    }
    
    /**
     * 获取特定游戏信息
     */
    fun getGameInfo(gameType: GameType): GameInfo? {
        return registeredGames[gameType]
    }
    
    /**
     * 检查游戏是否支持
     */
    fun isGameSupported(gameType: GameType): Boolean {
        return registeredGames.containsKey(gameType) && 
               registeredGames[gameType]?.isAvailable == true
    }
    
    /**
     * 获取推荐游戏（基于用户等级和偏好）
     */
    fun getRecommendedGames(userLevel: Int, playedGames: Set<GameType>): List<GameInfo> {
        return getAvailableGamesForLevel(userLevel)
            .filter { gameInfo ->
                // 优先推荐未玩过的游戏
                gameInfo.type !in playedGames || gameInfo.isPopular
            }
            .sortedWith(compareByDescending<GameInfo> { it.isPopular }
                .thenBy { if (it.type in playedGames) 1 else 0 }
                .thenBy { it.displayOrder })
            .take(5)
    }
    
    /**
     * 根据分类获取游戏
     */
    fun getGamesByCategory(category: GameCategory): List<GameInfo> {
        return getAvailableGames()
            .filter { it.category == category }
    }
    
    /**
     * 搜索游戏
     */
    fun searchGames(query: String): List<GameInfo> {
        val lowercaseQuery = query.lowercase()
        return getAvailableGames()
            .filter { gameInfo ->
                gameInfo.name.lowercase().contains(lowercaseQuery) ||
                gameInfo.description.lowercase().contains(lowercaseQuery) ||
                gameInfo.tags.any { it.lowercase().contains(lowercaseQuery) }
            }
    }
    
    /**
     * 注册默认游戏
     */
    private fun registerDefaultGames() {
        // 俄罗斯方块
        registerGame(
            GameInfo(
                type = GameType.TETRIS,
                name = "俄罗斯方块",
                description = "经典的俄罗斯方块游戏，考验你的空间思维和反应速度",
                category = GameCategory.PUZZLE,
                previewImageRes = "tetris_preview",
                iconRes = "tetris_icon",
                isAvailable = true,
                isPopular = true,
                minLevel = 1,
                maxPlayers = 1,
                estimatedDuration = 300, // 5分钟
                difficulty = GameDifficulty.MEDIUM,
                features = listOf(
                    GameFeature.SINGLE_PLAYER,
                    GameFeature.SCORE_BASED,
                    GameFeature.LEVEL_PROGRESSION,
                    GameFeature.ACHIEVEMENTS
                ),
                tags = listOf("经典", "益智", "方块", "消除"),
                displayOrder = 1
            )
        )
        
        // 拼图游戏（暂未实现）
        registerGame(
            GameInfo(
                type = GameType.PUZZLE,
                name = "拼图挑战",
                description = "各种有趣的拼图游戏，锻炼逻辑思维",
                category = GameCategory.PUZZLE,
                previewImageRes = "puzzle_preview",
                iconRes = "puzzle_icon",
                isAvailable = false, // 暂未实现
                isPopular = false,
                minLevel = 3,
                maxPlayers = 1,
                estimatedDuration = 600, // 10分钟
                difficulty = GameDifficulty.HARD,
                features = listOf(
                    GameFeature.SINGLE_PLAYER,
                    GameFeature.TIME_BASED,
                    GameFeature.ACHIEVEMENTS
                ),
                tags = listOf("拼图", "逻辑", "思维"),
                displayOrder = 2
            )
        )
        
        // 卡牌游戏（暂未实现）
        registerGame(
            GameInfo(
                type = GameType.CARD,
                name = "卡牌大师",
                description = "策略卡牌游戏，考验你的战术思维",
                category = GameCategory.STRATEGY,
                previewImageRes = "card_preview",
                iconRes = "card_icon",
                isAvailable = false, // 暂未实现
                isPopular = false,
                minLevel = 5,
                maxPlayers = 2,
                estimatedDuration = 900, // 15分钟
                difficulty = GameDifficulty.HARD,
                features = listOf(
                    GameFeature.MULTIPLAYER,
                    GameFeature.TURN_BASED,
                    GameFeature.ACHIEVEMENTS
                ),
                tags = listOf("卡牌", "策略", "对战"),
                displayOrder = 3
            )
        )
        
        // 策略游戏（暂未实现）
        registerGame(
            GameInfo(
                type = GameType.STRATEGY,
                name = "策略征服",
                description = "深度策略游戏，建设你的帝国",
                category = GameCategory.STRATEGY,
                previewImageRes = "strategy_preview",
                iconRes = "strategy_icon",
                isAvailable = false, // 暂未实现
                isPopular = false,
                minLevel = 10,
                maxPlayers = 4,
                estimatedDuration = 1800, // 30分钟
                difficulty = GameDifficulty.EXPERT,
                features = listOf(
                    GameFeature.MULTIPLAYER,
                    GameFeature.TURN_BASED,
                    GameFeature.RESOURCE_MANAGEMENT,
                    GameFeature.ACHIEVEMENTS
                ),
                tags = listOf("策略", "建设", "征服"),
                displayOrder = 4
            )
        )
        
        // 动作游戏（暂未实现）
        registerGame(
            GameInfo(
                type = GameType.ACTION,
                name = "反应大师",
                description = "快节奏动作游戏，挑战你的反应极限",
                category = GameCategory.ACTION,
                previewImageRes = "action_preview",
                iconRes = "action_icon",
                isAvailable = false, // 暂未实现
                isPopular = false,
                minLevel = 7,
                maxPlayers = 1,
                estimatedDuration = 180, // 3分钟
                difficulty = GameDifficulty.HARD,
                features = listOf(
                    GameFeature.SINGLE_PLAYER,
                    GameFeature.REAL_TIME,
                    GameFeature.SCORE_BASED,
                    GameFeature.ACHIEVEMENTS
                ),
                tags = listOf("动作", "反应", "快节奏"),
                displayOrder = 5
            )
        )
    }
}

/**
 * 游戏信息数据类
 */
data class GameInfo(
    val type: GameType,
    val name: String,
    val description: String,
    val category: GameCategory,
    val previewImageRes: String,
    val iconRes: String,
    val isAvailable: Boolean = true,
    val isPopular: Boolean = false,
    val minLevel: Int = 1,
    val maxPlayers: Int = 1,
    val estimatedDuration: Int, // 预估游戏时长（秒）
    val difficulty: GameDifficulty = GameDifficulty.MEDIUM,
    val features: List<GameFeature> = emptyList(),
    val tags: List<String> = emptyList(),
    val displayOrder: Int = 0
)

/**
 * 游戏分类枚举
 */
enum class GameCategory(val displayName: String) {
    PUZZLE("益智游戏"),
    STRATEGY("策略游戏"),
    ACTION("动作游戏"),
    CARD("卡牌游戏"),
    CASUAL("休闲游戏"),
    ARCADE("街机游戏")
}

/**
 * 游戏特性枚举
 */
enum class GameFeature(val displayName: String) {
    SINGLE_PLAYER("单人游戏"),
    MULTIPLAYER("多人游戏"),
    REAL_TIME("实时游戏"),
    TURN_BASED("回合制"),
    SCORE_BASED("计分制"),
    TIME_BASED("计时制"),
    LEVEL_PROGRESSION("等级进阶"),
    RESOURCE_MANAGEMENT("资源管理"),
    ACHIEVEMENTS("成就系统"),
    LEADERBOARD("排行榜"),
    OFFLINE_PLAY("离线游戏"),
    CLOUD_SAVE("云端存档")
}

/**
 * 游戏状态枚举
 */
enum class GameAvailability {
    AVAILABLE,      // 可用
    COMING_SOON,    // 即将推出
    MAINTENANCE,    // 维护中
    DEPRECATED,     // 已弃用
    LEVEL_LOCKED    // 等级锁定
}
