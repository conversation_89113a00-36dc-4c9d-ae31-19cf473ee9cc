package com.yu.questicle.core.domain.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Achievement
import com.yu.questicle.core.domain.model.UserAchievement
import kotlinx.coroutines.flow.Flow

/**
 * Achievement repository interface
 * 成就数据仓库接口
 */
interface AchievementRepository {
    
    /**
     * Get all available achievements
     * 获取所有可用成就
     */
    suspend fun getAllAchievements(): Result<List<Achievement>>
    
    /**
     * Get specific achievement by ID
     * 根据ID获取特定成就
     */
    suspend fun getAchievement(achievementId: String): Result<Achievement?>
    
    /**
     * Get user's achievement progress
     * 获取用户成就进度
     */
    suspend fun getUserAchievements(userId: String): Result<List<UserAchievement>>
    
    /**
     * Get specific user achievement
     * 获取特定用户成就
     */
    suspend fun getUserAchievement(userId: String, achievementId: String): Result<UserAchievement?>
    
    /**
     * Get user's achievement progress as flow
     * 获取用户成就进度流
     */
    fun getUserAchievementsFlow(userId: String): Flow<List<UserAchievement>>
    
    /**
     * Save or update user achievement progress
     * 保存或更新用户成就进度
     */
    suspend fun saveUserAchievement(userAchievement: UserAchievement): Result<Unit>
    
    /**
     * Save multiple user achievements
     * 保存多个用户成就
     */
    suspend fun saveUserAchievements(userAchievements: List<UserAchievement>): Result<Unit>
    
    /**
     * Reset user achievements (for testing)
     * 重置用户成就（用于测试）
     */
    suspend fun resetUserAchievements(userId: String): Result<Unit>
    
    /**
     * Initialize predefined achievements
     * 初始化预定义成就
     */
    suspend fun initializePredefinedAchievements(): Result<Unit>
    
    /**
     * Check if achievements are initialized
     * 检查成就是否已初始化
     */
    suspend fun areAchievementsInitialized(): Result<Boolean>
}
