package com.yu.questicle.core.domain.service

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.*
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Achievement service interface
 * 成就系统服务接口
 */
interface AchievementService {
    
    /**
     * Get all available achievements
     * 获取所有可用成就
     */
    suspend fun getAllAchievements(): Result<List<Achievement>>
    
    /**
     * Get user's achievement progress
     * 获取用户成就进度
     */
    suspend fun getUserAchievements(userId: String): Result<List<UserAchievement>>
    
    /**
     * Get user's unlocked achievements
     * 获取用户已解锁的成就
     */
    suspend fun getUnlockedAchievements(userId: String): Result<List<Achievement>>
    
    /**
     * Get user's achievement progress as flow
     * 获取用户成就进度流
     */
    fun getUserAchievementsFlow(userId: String): Flow<List<UserAchievement>>
    
    /**
     * Check and update achievement progress based on game result
     * 根据游戏结果检查并更新成就进度
     */
    suspend fun checkAchievements(event: AchievementProgressEvent): Result<AchievementUnlockResult>
    
    /**
     * Get achievement progress for specific achievement
     * 获取特定成就的进度
     */
    suspend fun getAchievementProgress(userId: String, achievementId: String): Result<UserAchievement?>
    
    /**
     * Manually unlock achievement (for testing or special events)
     * 手动解锁成就（用于测试或特殊事件）
     */
    suspend fun unlockAchievement(userId: String, achievementId: String): Result<AchievementUnlockResult>
    
    /**
     * Get achievement statistics
     * 获取成就统计信息
     */
    suspend fun getAchievementStats(userId: String): Result<AchievementStats>
    
    /**
     * Reset user achievements (for testing)
     * 重置用户成就（用于测试）
     */
    suspend fun resetUserAchievements(userId: String): Result<Unit>
}

/**
 * Achievement statistics
 * 成就统计信息
 */
data class AchievementStats(
    val totalAchievements: Int,
    val unlockedAchievements: Int,
    val totalExperienceEarned: Long,
    val totalCoinsEarned: Long,
    val totalGemsEarned: Long,
    val completionPercentage: Float,
    val categoriesProgress: Map<AchievementCategory, CategoryProgress>
) {
    val unlockedPercentage: Float get() = if (totalAchievements > 0) unlockedAchievements.toFloat() / totalAchievements else 0f
}

/**
 * Category progress information
 * 分类进度信息
 */
data class CategoryProgress(
    val total: Int,
    val unlocked: Int,
    val averageProgress: Float
) {
    val completionPercentage: Float get() = if (total > 0) unlocked.toFloat() / total else 0f
}

/**
 * Achievement service implementation
 * 成就系统服务实现
 */
class AchievementServiceImpl @Inject constructor(
    private val membershipService: MembershipService
) : AchievementService {
    
    override suspend fun getAllAchievements(): Result<List<Achievement>> {
        return try {
            // For now, return predefined achievements
            Result.Success(PredefinedAchievements.getAllAchievements())
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to get achievements: ${e.message}",
                cause = e
            ))
        }
    }

    override suspend fun getUserAchievements(userId: String): Result<List<UserAchievement>> {
        return try {
            // For now, return empty list - will be implemented with repository
            Result.Success(emptyList())
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to get user achievements: ${e.message}",
                cause = e
            ))
        }
    }
    
    override suspend fun getUnlockedAchievements(userId: String): Result<List<Achievement>> {
        return when (val userAchievementsResult = getUserAchievements(userId)) {
            is Result.Success -> {
                val unlockedIds = userAchievementsResult.data
                    .filter { it.isUnlocked }
                    .map { it.achievementId }
                    .toSet()
                
                when (val allAchievementsResult = getAllAchievements()) {
                    is Result.Success -> {
                        val unlockedAchievements = allAchievementsResult.data
                            .filter { it.id in unlockedIds }
                        Result.Success(unlockedAchievements)
                    }
                    is Result.Error -> allAchievementsResult
                    is Result.Loading -> allAchievementsResult
                }
            }
            is Result.Error -> userAchievementsResult
            is Result.Loading -> userAchievementsResult
        }
    }
    
    override fun getUserAchievementsFlow(userId: String): Flow<List<UserAchievement>> {
        return kotlinx.coroutines.flow.flowOf(emptyList())
    }
    
    override suspend fun checkAchievements(event: AchievementProgressEvent): Result<AchievementUnlockResult> {
        return try {
            val newlyUnlocked = mutableListOf<Achievement>()
            val progressUpdated = mutableListOf<UserAchievement>()
            var totalExperienceReward = 0L

            // 获取所有成就
            val allAchievements = when (val result = getAllAchievements()) {
                is Result.Success -> result.data
                else -> emptyList()
            }

            // 检查每个成就的进度
            for (achievement in allAchievements) {
                val progress = checkAchievementProgress(achievement, event)

                if (progress.isUnlocked && !isAchievementUnlocked(event.userId, achievement.id)) {
                    // 新解锁的成就
                    newlyUnlocked.add(achievement)
                    totalExperienceReward += achievement.reward?.experience ?: 0L

                    // 记录用户成就进度
                    val userAchievement = UserAchievement(
                        achievementId = achievement.id,
                        userId = event.userId,
                        unlockedAt = System.currentTimeMillis(),
                        progress = 1.0f
                    )
                    progressUpdated.add(userAchievement)
                } else if (progress.currentProgress > 0f && progress.currentProgress < 1f) {
                    // 进度更新的成就
                    val userAchievement = UserAchievement(
                        achievementId = achievement.id,
                        userId = event.userId,
                        unlockedAt = 0L,
                        progress = progress.currentProgress
                    )
                    progressUpdated.add(userAchievement)
                }
            }

            Result.Success(
                AchievementUnlockResult(
                    newlyUnlocked = newlyUnlocked,
                    progressUpdated = progressUpdated,
                    totalRewards = AchievementReward(
                        experience = totalExperienceReward,
                        coins = totalExperienceReward / 10, // 1经验值 = 0.1金币
                        gems = if (newlyUnlocked.isNotEmpty()) 1L else 0L
                    )
                )
            )
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to check achievements: ${e.message}",
                cause = e
            ))
        }
    }
    
    override suspend fun getAchievementProgress(userId: String, achievementId: String): Result<UserAchievement?> {
        return try {
            Result.Success(null)
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to get achievement progress: ${e.message}",
                cause = e
            ))
        }
    }

    override suspend fun unlockAchievement(userId: String, achievementId: String): Result<AchievementUnlockResult> {
        return try {
            Result.Success(
                AchievementUnlockResult(
                    newlyUnlocked = emptyList(),
                    progressUpdated = emptyList(),
                    totalRewards = AchievementReward()
                )
            )
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to unlock achievement: ${e.message}",
                cause = e
            ))
        }
    }
    
    override suspend fun getAchievementStats(userId: String): Result<AchievementStats> {
        return try {
            Result.Success(
                AchievementStats(
                    totalAchievements = 10,
                    unlockedAchievements = 0,
                    totalExperienceEarned = 0L,
                    totalCoinsEarned = 0L,
                    totalGemsEarned = 0L,
                    completionPercentage = 0f,
                    categoriesProgress = emptyMap()
                )
            )
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to get achievement stats: ${e.message}",
                cause = e
            ))
        }
    }
    
    override suspend fun resetUserAchievements(userId: String): Result<Unit> {
        return try {
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(com.yu.questicle.core.common.exception.BusinessException(
                message = "Failed to reset user achievements: ${e.message}",
                cause = e
            ))
        }
    }

    /**
     * 检查单个成就的进度
     */
    private fun checkAchievementProgress(achievement: Achievement, event: AchievementProgressEvent): AchievementProgress {
        return when (achievement.type) {
            AchievementType.SINGLE -> checkSingleAchievement(achievement, event)
            AchievementType.INCREMENTAL -> checkIncrementalAchievement(achievement, event)
            AchievementType.STREAK -> checkStreakAchievement(achievement, event)
            AchievementType.MILESTONE -> checkMilestoneAchievement(achievement, event)
        }
    }

    /**
     * 检查一次性成就
     */
    private fun checkSingleAchievement(achievement: Achievement, event: AchievementProgressEvent): AchievementProgress {
        return when (val requirement = achievement.requirement) {
            is AchievementRequirement.ScoreRequirement -> {
                val isUnlocked = event.gameScore >= requirement.targetScore &&
                    (requirement.gameType == null || requirement.gameType == event.gameType)
                AchievementProgress(
                    currentProgress = if (isUnlocked) 1f else 0f,
                    isUnlocked = isUnlocked
                )
            }
            is AchievementRequirement.TetrisSpecificRequirement -> {
                val customValue = event.customData[requirement.type.name.lowercase()] as? Long ?: 0L
                val isUnlocked = customValue >= requirement.targetValue
                AchievementProgress(
                    currentProgress = if (isUnlocked) 1f else 0f,
                    isUnlocked = isUnlocked
                )
            }
            else -> AchievementProgress(currentProgress = 0f, isUnlocked = false)
        }
    }

    /**
     * 检查累积型成就
     */
    private fun checkIncrementalAchievement(achievement: Achievement, event: AchievementProgressEvent): AchievementProgress {
        return when (val requirement = achievement.requirement) {
            is AchievementRequirement.GamesPlayedRequirement -> {
                val currentGames = event.userStats?.totalGames ?: 0
                val progress = (currentGames.toFloat() / requirement.targetGames).coerceIn(0f, 1f)
                AchievementProgress(
                    currentProgress = progress,
                    isUnlocked = currentGames >= requirement.targetGames
                )
            }
            is AchievementRequirement.ScoreRequirement -> {
                val totalScore = event.userStats?.totalScore ?: 0L
                val progress = (totalScore.toFloat() / requirement.targetScore).coerceIn(0f, 1f)
                AchievementProgress(
                    currentProgress = progress,
                    isUnlocked = totalScore >= requirement.targetScore
                )
            }
            else -> AchievementProgress(currentProgress = 0f, isUnlocked = false)
        }
    }

    /**
     * 检查连续型成就
     */
    private fun checkStreakAchievement(achievement: Achievement, event: AchievementProgressEvent): AchievementProgress {
        return when (val requirement = achievement.requirement) {
            is AchievementRequirement.WinStreakRequirement -> {
                val currentStreak = event.userStats?.currentStreak ?: 0
                val progress = (currentStreak.toFloat() / requirement.targetStreak).coerceIn(0f, 1f)
                AchievementProgress(
                    currentProgress = progress,
                    isUnlocked = currentStreak >= requirement.targetStreak
                )
            }
            else -> AchievementProgress(currentProgress = 0f, isUnlocked = false)
        }
    }

    /**
     * 检查里程碑成就
     */
    private fun checkMilestoneAchievement(achievement: Achievement, event: AchievementProgressEvent): AchievementProgress {
        return when (val requirement = achievement.requirement) {
            is AchievementRequirement.LevelRequirement -> {
                val userLevel = membershipService.calculateLevel(event.userStats?.totalScore ?: 0L)
                val progress = (userLevel.toFloat() / requirement.targetLevel).coerceIn(0f, 1f)
                AchievementProgress(
                    currentProgress = progress,
                    isUnlocked = userLevel >= requirement.targetLevel
                )
            }
            is AchievementRequirement.TimeBasedRequirement -> {
                val totalPlayTime = event.userStats?.totalPlayTime ?: 0L
                val targetTimeMs = requirement.targetTimeSeconds * 1000L
                val progress = (totalPlayTime.toFloat() / targetTimeMs).coerceIn(0f, 1f)
                AchievementProgress(
                    currentProgress = progress,
                    isUnlocked = totalPlayTime >= targetTimeMs
                )
            }
            else -> AchievementProgress(currentProgress = 0f, isUnlocked = false)
        }
    }

    /**
     * 检查成就是否已解锁
     */
    private suspend fun isAchievementUnlocked(userId: String, achievementId: String): Boolean {
        // 简化实现：假设都未解锁
        return false
    }
}

/**
 * Extension function to combine achievement rewards
 */
private fun AchievementReward.combine(other: AchievementReward): AchievementReward {
    return AchievementReward(
        experience = this.experience + other.experience,
        coins = this.coins + other.coins,
        gems = this.gems + other.gems,
        title = other.title ?: this.title,
        avatar = other.avatar ?: this.avatar,
        specialItems = this.specialItems + other.specialItems
    )
}
