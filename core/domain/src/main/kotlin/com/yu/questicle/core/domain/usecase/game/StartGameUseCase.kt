package com.yu.questicle.core.domain.usecase.game

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.engine.GameEngine
import com.yu.questicle.core.domain.engine.GameEngineFactory
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameAction
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.service.MembershipService
import com.yu.questicle.core.domain.usecase.user.AddExperienceUseCase
import javax.inject.Inject

/**
 * Use case for starting a new game
 */
class StartGameUseCase @Inject constructor(
    private val gameRepository: GameRepository,
    private val userRepository: UserRepository,
    private val gameEngineFactory: GameEngineFactory
) {
    
    suspend operator fun invoke(
        gameType: GameType,
        playerId: String
    ): Result<Game> {
        return try {
            // Validate game type is supported
            if (!gameEngineFactory.isSupported(gameType)) {
                return Result.Error(IllegalArgumentException("Game type $gameType is not supported").toQuesticleException())
            }

            // Validate player exists
            when (val userResult = userRepository.loadUser(playerId)) {
                is Result.Error -> return Result.Error(userResult.exception)
                is Result.Loading -> return Result.Error(IllegalStateException("User loading").toQuesticleException())
                is Result.Success -> {
                    // User exists, continue
                }
            }

            // Create new game
            val game = Game.createNew(gameType, playerId)

            // Save game
            when (val saveResult = gameRepository.saveGame(game)) {
                is Result.Error -> return Result.Error(saveResult.exception)
                is Result.Loading -> return Result.Error(IllegalStateException("Save in progress").toQuesticleException())
                is Result.Success -> {
                    // Game saved successfully
                }
            }

            // Initialize game engine
            val engine = gameEngineFactory.createEngine(gameType)
                ?: return Result.Error(IllegalStateException("Failed to create game engine").toQuesticleException())

            // Initialize game state
            when (val initResult = engine.initializeGame(playerId)) {
                is Result.Error -> return Result.Error(initResult.exception)
                is Result.Loading -> return Result.Error(IllegalStateException("Initialization in progress").toQuesticleException())
                is Result.Success -> {
                    // Game initialized successfully
                }
            }

            Result.Success(game)

        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}

/**
 * Use case for resuming an existing game
 */
class ResumeGameUseCase @Inject constructor(
    private val gameRepository: GameRepository,
    private val gameEngineFactory: GameEngineFactory
) {
    
    suspend operator fun invoke(gameId: String): Result<Game> {
        return try {
            // Load game
            when (val gameResult = gameRepository.loadGame(gameId)) {
                is Result.Error -> return Result.Error(gameResult.exception)
                is Result.Loading -> return Result.Error(IllegalStateException("Game loading").toQuesticleException())
                is Result.Success -> {
                    val game = gameResult.data

                    // Get appropriate engine
                    val engine = gameEngineFactory.createEngine(game.type)
                        ?: return Result.Error(IllegalStateException("Failed to create game engine").toQuesticleException())

                    // Load game state
                    when (val stateResult = engine.loadGameState(gameId)) {
                        is Result.Error -> return Result.Error(stateResult.exception)
                        is Result.Loading -> return Result.Error(IllegalStateException("State loading").toQuesticleException())
                        is Result.Success -> {
                            // Resume game - cast to avoid star projection issue
                            @Suppress("UNCHECKED_CAST")
                            val typedEngine = engine as GameEngine<Any, GameAction>
                            when (val resumeResult = typedEngine.resumeGame(stateResult.data)) {
                                is Result.Error -> return Result.Error(resumeResult.exception)
                                is Result.Loading -> return Result.Error(IllegalStateException("Resume in progress").toQuesticleException())
                                is Result.Success -> {
                                    return Result.Success(game)
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}

/**
 * Use case for ending a game
 */
class EndGameUseCase @Inject constructor(
    private val gameRepository: GameRepository,
    private val userRepository: UserRepository,
    private val membershipService: MembershipService,
    private val gameEngineFactory: GameEngineFactory,
    private val addExperienceUseCase: AddExperienceUseCase
) {
    
    suspend operator fun invoke(
        gameId: String,
        finalScore: Int
    ): Result<Game> {
        return try {
            // Load game
            when (val gameResult = gameRepository.loadGame(gameId)) {
                is Result.Error -> return Result.Error(gameResult.exception)
                is Result.Loading -> return Result.Error(IllegalStateException("Game loading").toQuesticleException())
                is Result.Success -> {
                    val game = gameResult.data

                    // Get appropriate engine
                    val engine = gameEngineFactory.createEngine(game.type)
                        ?: return Result.Error(IllegalStateException("Failed to create game engine").toQuesticleException())

                    // Load current game state
                    when (val stateResult = engine.loadGameState(gameId)) {
                        is Result.Error -> return Result.Error(stateResult.exception)
                        is Result.Loading -> return Result.Error(IllegalStateException("State loading").toQuesticleException())
                        is Result.Success -> {
                            // End game - cast to avoid star projection issue
                            @Suppress("UNCHECKED_CAST")
                            val typedEngine = engine as GameEngine<Any, GameAction>
                            when (val endResult = typedEngine.endGame(stateResult.data)) {
                                is Result.Error -> return Result.Error(endResult.exception)
                                is Result.Loading -> return Result.Error(IllegalStateException("End game in progress").toQuesticleException())
                                is Result.Success -> {
                                    val finalGame = endResult.data.copy(score = finalScore)

                                    // Save final game state
                                    when (val saveResult = gameRepository.saveGame(finalGame)) {
                                        is Result.Error -> return Result.Error(saveResult.exception)
                                        is Result.Loading -> return Result.Error(IllegalStateException("Save in progress").toQuesticleException())
                                        is Result.Success -> {
                                            // Update user statistics
                                            updateUserStats(finalGame)
                                            return Result.Success(finalGame)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    private suspend fun updateUserStats(game: Game) {
        try {
            when (val userResult = userRepository.loadUser(game.playerId)) {
                is Result.Success -> {
                    val user = userResult.data
                    val updatedStats = user.stats.copy(
                        totalGames = user.stats.totalGames + 1,
                        totalScore = user.stats.totalScore + game.score,
                        gamesWon = if (game.score > 0) user.stats.gamesWon + 1 else user.stats.gamesWon,
                        totalPlayTime = user.stats.totalPlayTime + game.duration
                    )
                    
                    userRepository.updateStats(game.playerId, updatedStats)
                    
                    // Use the new UseCase for adding experience
                    val experience = calculateExperience(game.score, game.type)
                    val addExperienceResult = addExperienceUseCase.execute(
                        AddExperienceUseCase.Input(
                            userId = game.playerId,
                            experience = experience,
                            source = "game_completion_${game.type.name.lowercase()}"
                        )
                    )
                    
                    // Handle experience result if needed
                    if (addExperienceResult is Result.Error) {
                        // Log error but don't fail the game completion
                        // This is silently handled to avoid breaking the game flow
                    }
                }
                else -> {
                    // Handle error silently for stats update
                }
            }
        } catch (e: Exception) {
            // Handle error silently for stats update
        }
    }
    
    private fun calculateExperience(score: Int, gameType: GameType): Long {
        return when (gameType) {
            GameType.TETRIS -> (score / 100).toLong()
            GameType.PUZZLE -> (score / 50).toLong()
            GameType.CARD -> (score / 200).toLong()
            GameType.STRATEGY -> (score / 150).toLong()
            GameType.ACTION -> (score / 80).toLong()
        }
    }
}
