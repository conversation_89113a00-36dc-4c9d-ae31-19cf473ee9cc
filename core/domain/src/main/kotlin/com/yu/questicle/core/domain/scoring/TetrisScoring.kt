package com.yu.questicle.core.domain.scoring

/**
 * 俄罗斯方块计分系统
 * 实现标准的俄罗斯方块计分规则
 */
object TetrisScoring {
    
    // 基础分数表
    private val baseScores = mapOf(
        1 to 40,    // 单行消除
        2 to 100,   // 双行消除
        3 to 300,   // 三行消除
        4 to 1200   // 四行消除 (Tetris)
    )
    
    // T-Spin分数表
    private val tSpinScores = mapOf(
        0 to 400,   // T-Spin Mini
        1 to 800,   // T-Spin Single
        2 to 1200,  // T-Spin Double
        3 to 1600   // T-Spin Triple
    )
    
    // 软降分数
    const val SOFT_DROP_SCORE = 1
    
    // 硬降分数倍数
    const val HARD_DROP_MULTIPLIER = 2
    
    /**
     * 计算行消除分数
     */
    fun calculateLineScore(
        linesCleared: Int,
        level: Int,
        isTSpin: Boolean = false,
        combo: Int = 0
    ): Int {
        if (linesCleared == 0) return 0
        
        val baseScore = if (isTSpin) {
            tSpinScores[linesCleared] ?: 0
        } else {
            baseScores[linesCleared] ?: 0
        }
        
        val levelMultiplier = level + 1
        val comboBonus = calculateComboBonus(combo, level)
        
        return (baseScore * levelMultiplier) + comboBonus
    }
    
    /**
     * 计算连击奖励分数
     */
    fun calculateComboBonus(combo: Int, level: Int): Int {
        return if (combo > 0) {
            50 * combo * level
        } else {
            0
        }
    }
    
    /**
     * 计算软降分数
     */
    fun calculateSoftDropScore(cells: Int): Int = cells * SOFT_DROP_SCORE
    
    /**
     * 计算硬降分数
     */
    fun calculateHardDropScore(cells: Int): Int = cells * HARD_DROP_MULTIPLIER
    
    /**
     * 计算完美消除奖励 (Perfect Clear)
     */
    fun calculatePerfectClearBonus(level: Int, linesCleared: Int): Int {
        val baseBonus = when (linesCleared) {
            1 -> 800
            2 -> 1200
            3 -> 1800
            4 -> 2000
            else -> 0
        }
        return baseBonus * level
    }
    
    /**
     * 计算等级提升所需行数
     */
    fun getLinesForNextLevel(currentLevel: Int): Int {
        return when {
            currentLevel <= 9 -> (currentLevel + 1) * 10
            currentLevel <= 15 -> 100 + (currentLevel - 9) * 10
            else -> 200 + (currentLevel - 15) * 10
        }
    }
    
    /**
     * 根据消除行数计算新等级
     */
    fun calculateLevel(totalLines: Int): Int {
        return when {
            totalLines < 10 -> 1
            totalLines < 20 -> 2
            totalLines < 30 -> 3
            totalLines < 40 -> 4
            totalLines < 50 -> 5
            totalLines < 60 -> 6
            totalLines < 70 -> 7
            totalLines < 80 -> 8
            totalLines < 90 -> 9
            totalLines < 100 -> 10
            else -> 10 + (totalLines - 100) / 10
        }
    }
    
    /**
     * 计算下降间隔时间（毫秒）
     */
    fun calculateDropInterval(level: Int): Long {
        return when (level) {
            1 -> 1000L
            2 -> 793L
            3 -> 618L
            4 -> 473L
            5 -> 355L
            6 -> 262L
            7 -> 190L
            8 -> 135L
            9 -> 94L
            10 -> 64L
            11 -> 43L
            12 -> 28L
            13 -> 18L
            14 -> 11L
            15 -> 7L
            16 -> 5L
            17 -> 3L
            18 -> 2L
            19 -> 1L
            else -> 1L
        }
    }
    
    /**
     * 计算总分数
     */
    fun calculateTotalScore(
        lineScore: Int,
        softDropScore: Int,
        hardDropScore: Int,
        perfectClearBonus: Int = 0
    ): Int {
        return lineScore + softDropScore + hardDropScore + perfectClearBonus
    }
    
    /**
     * 获取分数等级称号
     */
    fun getScoreRank(score: Int): String {
        return when {
            score < 1000 -> "新手"
            score < 5000 -> "初级"
            score < 10000 -> "中级"
            score < 25000 -> "高级"
            score < 50000 -> "专家"
            score < 100000 -> "大师"
            score < 200000 -> "宗师"
            else -> "传奇"
        }
    }
    
    /**
     * 计算统计数据
     */
    data class ScoreBreakdown(
        val lineScore: Int,
        val softDropScore: Int,
        val hardDropScore: Int,
        val comboBonus: Int,
        val perfectClearBonus: Int,
        val totalScore: Int
    )
    
    /**
     * 获取详细的分数分解
     */
    fun getScoreBreakdown(
        linesCleared: Int,
        level: Int,
        combo: Int,
        softDropCells: Int,
        hardDropCells: Int,
        isPerfectClear: Boolean = false,
        isTSpin: Boolean = false
    ): ScoreBreakdown {
        val lineScore = calculateLineScore(linesCleared, level, isTSpin, combo)
        val softDropScore = calculateSoftDropScore(softDropCells)
        val hardDropScore = calculateHardDropScore(hardDropCells)
        val comboBonus = calculateComboBonus(combo, level)
        val perfectClearBonus = if (isPerfectClear) {
            calculatePerfectClearBonus(level, linesCleared)
        } else {
            0
        }
        
        val totalScore = lineScore + softDropScore + hardDropScore + perfectClearBonus
        
        return ScoreBreakdown(
            lineScore = lineScore,
            softDropScore = softDropScore,
            hardDropScore = hardDropScore,
            comboBonus = comboBonus,
            perfectClearBonus = perfectClearBonus,
            totalScore = totalScore
        )
    }
}
