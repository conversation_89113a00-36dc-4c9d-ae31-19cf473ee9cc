package com.yu.questicle.core.domain.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for game data operations
 */
interface GameRepository {
    
    /**
     * Save a game state
     */
    suspend fun saveGame(game: Game): Result<Unit>
    
    /**
     * Load a game by ID
     */
    suspend fun loadGame(gameId: String): Result<Game>
    
    /**
     * Get all games for a player
     */
    fun getGamesForPlayer(playerId: String): Flow<List<Game>>
    
    /**
     * Get games by type for a player
     */
    fun getGamesByType(playerId: String, gameType: GameType): Flow<List<Game>>
    
    /**
     * Delete a game
     */
    suspend fun deleteGame(gameId: String): Result<Unit>
    
    /**
     * Save game session
     */
    suspend fun saveGameSession(session: GameSession): Result<Unit>
    
    /**
     * Get game sessions for a game
     */
    fun getGameSessions(gameId: String): Flow<List<GameSession>>
    
    /**
     * Get game statistics for a player
     */
    suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats>
    
    /**
     * Update game statistics
     */
    suspend fun updateGameStats(stats: GameStats): Result<Unit>
    
    /**
     * Get leaderboard for a game type
     */
    suspend fun getLeaderboard(gameType: GameType, limit: Int = 10): Result<List<GameStats>>
    
    /**
     * Search games by criteria
     */
    suspend fun searchGames(
        playerId: String? = null,
        gameType: GameType? = null,
        minScore: Int? = null,
        maxScore: Int? = null,
        limit: Int = 50
    ): Result<List<Game>>
}
