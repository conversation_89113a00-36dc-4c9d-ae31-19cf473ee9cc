package com.yu.questicle.core.domain.model.tetris

import kotlinx.serialization.Serializable

/**
 * 游戏统计数据
 * 记录游戏过程中的各种统计信息
 */
@Serializable
data class GameStatistics(
    // 基础统计
    val piecesPlaced: Int = 0,
    val linesCleared: Int = 0,
    val singles: Int = 0,
    val doubles: Int = 0,
    val triples: Int = 0,
    val tetrises: Int = 0,
    val tSpins: Int = 0,
    val tSpinSingles: Int = 0,
    val tSpinDoubles: Int = 0,
    val tSpinTriples: Int = 0,
    val maxCombo: Int = 0,
    val perfectClears: Int = 0,
    val backToBackCount: Int = 0,
    val maxBackToBack: Int = 0,
    
    // 游戏时间统计（毫秒）
    val gameTime: Long = 0,
    
    // 效率指标
    val piecesPerMinute: Double = 0.0,
    val linesPerMinute: Double = 0.0,
    val efficiency: Double = 0.0,
    
    // 方块统计
    val pieceStats: Map<TetrisPieceType, Int> = emptyMap()
) {
    /**
     * 计算游戏效率指标
     */
    fun calculateEfficiency(): GameStatistics {
        val gameTimeMinutes = gameTime / 60000.0
        val newPiecesPerMinute = if (gameTimeMinutes > 0) piecesPlaced / gameTimeMinutes else 0.0
        val newLinesPerMinute = if (gameTimeMinutes > 0) linesCleared / gameTimeMinutes else 0.0
        val newEfficiency = if (piecesPlaced > 0) linesCleared.toDouble() / piecesPlaced else 0.0

        return copy(
            piecesPerMinute = newPiecesPerMinute,
            linesPerMinute = newLinesPerMinute,
            efficiency = newEfficiency
        )
    }

    /**
     * 更新方块统计
     */
    fun updatePieceStats(pieceType: TetrisPieceType): GameStatistics {
        val newPieceStats = pieceStats.toMutableMap()
        newPieceStats[pieceType] = (newPieceStats[pieceType] ?: 0) + 1
        return copy(pieceStats = newPieceStats)
    }

    /**
     * 更新消除行统计
     */
    fun updateLineClearStats(linesCleared: Int, isTSpin: Boolean = false, isBackToBack: Boolean = false): GameStatistics {
        val newStats = when (linesCleared) {
            1 -> if (isTSpin) copy(tSpinSingles = tSpinSingles + 1) else copy(singles = singles + 1)
            2 -> if (isTSpin) copy(tSpinDoubles = tSpinDoubles + 1) else copy(doubles = doubles + 1)
            3 -> if (isTSpin) copy(tSpinTriples = tSpinTriples + 1) else copy(triples = triples + 1)
            4 -> copy(tetrises = tetrises + 1)
            else -> this
        }

        val newTSpins = if (isTSpin) tSpins + 1 else tSpins
        val newBackToBackCount = if (isBackToBack) backToBackCount + 1 else 0
        val newMaxBackToBack = maxOf(maxBackToBack, newBackToBackCount)

        return newStats.copy(
            tSpins = newTSpins,
            backToBackCount = newBackToBackCount,
            maxBackToBack = newMaxBackToBack
        )
    }

    /**
     * 获取总特殊消除次数（Tetris + T-Spin）
     */
    fun getSpecialClears(): Int = tetrises + tSpins

    /**
     * 获取攻击力（用于多人游戏）
     */
    fun getAttackPower(): Int {
        return singles * 0 + doubles * 1 + triples * 2 + tetrises * 4 +
               tSpinSingles * 2 + tSpinDoubles * 4 + tSpinTriples * 6
    }
    
    /**
     * 创建副本，更新总行数和方块数
     */
    fun copy(additionalLines: Int = 0, additionalPieces: Int = 0): GameStatistics {
        return copy(
            linesCleared = linesCleared + additionalLines,
            piecesPlaced = piecesPlaced + additionalPieces
        )
    }
}