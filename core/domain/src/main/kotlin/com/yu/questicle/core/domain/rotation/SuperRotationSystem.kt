package com.yu.questicle.core.domain.rotation

import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*

/**
 * Super Rotation System (SRS) 实现
 * 标准的俄罗斯方块旋转系统，包含踢墙算法
 */
object SuperRotationSystem {
    
    /**
     * 踢墙测试偏移表 - JLSTZ方块
     */
    private val jlstzKickTable = mapOf(
        // 0->1 (0度到90度)
        (0 to 1) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            -1 to 1,  // 左移1格，上移1格
            0 to -2,  // 下移2格
            -1 to -2  // 左移1格，下移2格
        ),
        // 1->0 (90度到0度)
        (1 to 0) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            1 to -1,  // 右移1格，下移1格
            0 to 2,   // 上移2格
            1 to 2    // 右移1格，上移2格
        ),
        // 1->2 (90度到180度)
        (1 to 2) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            1 to -1,  // 右移1格，下移1格
            0 to 2,   // 上移2格
            1 to 2    // 右移1格，上移2格
        ),
        // 2->1 (180度到90度)
        (2 to 1) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            -1 to 1,  // 左移1格，上移1格
            0 to -2,  // 下移2格
            -1 to -2  // 左移1格，下移2格
        ),
        // 2->3 (180度到270度)
        (2 to 3) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            1 to 1,   // 右移1格，上移1格
            0 to -2,  // 下移2格
            1 to -2   // 右移1格，下移2格
        ),
        // 3->2 (270度到180度)
        (3 to 2) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            -1 to -1, // 左移1格，下移1格
            0 to 2,   // 上移2格
            -1 to 2   // 左移1格，上移2格
        ),
        // 3->0 (270度到0度)
        (3 to 0) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            -1 to -1, // 左移1格，下移1格
            0 to 2,   // 上移2格
            -1 to 2   // 左移1格，上移2格
        ),
        // 0->3 (0度到270度)
        (0 to 3) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            1 to 1,   // 右移1格，上移1格
            0 to -2,  // 下移2格
            1 to -2   // 右移1格，下移2格
        )
    )
    
    /**
     * 踢墙测试偏移表 - I方块
     */
    private val iKickTable = mapOf(
        // 0->1
        (0 to 1) to listOf(
            0 to 0,   // 无偏移
            -2 to 0,  // 左移2格
            1 to 0,   // 右移1格
            -2 to -1, // 左移2格，下移1格
            1 to 2    // 右移1格，上移2格
        ),
        // 1->0
        (1 to 0) to listOf(
            0 to 0,   // 无偏移
            2 to 0,   // 右移2格
            -1 to 0,  // 左移1格
            2 to 1,   // 右移2格，上移1格
            -1 to -2  // 左移1格，下移2格
        ),
        // 1->2
        (1 to 2) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            2 to 0,   // 右移2格
            -1 to 2,  // 左移1格，上移2格
            2 to -1   // 右移2格，下移1格
        ),
        // 2->1
        (2 to 1) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            -2 to 0,  // 左移2格
            1 to -2,  // 右移1格，下移2格
            -2 to 1   // 左移2格，上移1格
        ),
        // 2->3
        (2 to 3) to listOf(
            0 to 0,   // 无偏移
            2 to 0,   // 右移2格
            -1 to 0,  // 左移1格
            2 to 1,   // 右移2格，上移1格
            -1 to -2  // 左移1格，下移2格
        ),
        // 3->2
        (3 to 2) to listOf(
            0 to 0,   // 无偏移
            -2 to 0,  // 左移2格
            1 to 0,   // 右移1格
            -2 to -1, // 左移2格，下移1格
            1 to 2    // 右移1格，上移2格
        ),
        // 3->0
        (3 to 0) to listOf(
            0 to 0,   // 无偏移
            1 to 0,   // 右移1格
            -2 to 0,  // 左移2格
            1 to -2,  // 右移1格，下移2格
            -2 to 1   // 左移2格，上移1格
        ),
        // 0->3
        (0 to 3) to listOf(
            0 to 0,   // 无偏移
            -1 to 0,  // 左移1格
            2 to 0,   // 右移2格
            -1 to 2,  // 左移1格，上移2格
            2 to -1   // 右移2格，下移1格
        )
    )
    
    /**
     * 尝试旋转方块
     * @param piece 当前方块
     * @param clockwise 是否顺时针旋转
     * @param board 游戏板
     * @return 旋转后的方块，如果无法旋转则返回null
     */
    fun tryRotate(
        piece: TetrisPiece,
        clockwise: Boolean,
        board: TetrisBoard
    ): TetrisPiece? {
        val currentRotation = piece.rotation % 4
        val newRotation = if (clockwise) {
            (currentRotation + 1) % 4
        } else {
            (currentRotation + 3) % 4
        }
        
        // O方块不需要旋转
        if (piece.type == TetrisPieceType.O) {
            return piece
        }
        
        // 获取踢墙测试偏移
        val kickOffsets = getKickOffsets(piece.type, currentRotation, newRotation)
        
        // 尝试每个踢墙偏移
        for ((dx, dy) in kickOffsets) {
            val testPiece = piece.copy(
                x = piece.x + dx,
                y = piece.y + dy,
                rotation = newRotation
            )
            
            if (board.isValidPosition(testPiece)) {
                return testPiece
            }
        }
        
        return null // 无法旋转
    }
    
    /**
     * 获取踢墙测试偏移
     */
    private fun getKickOffsets(
        pieceType: TetrisPieceType,
        fromRotation: Int,
        toRotation: Int
    ): List<Pair<Int, Int>> {
        val key = fromRotation to toRotation
        
        return when (pieceType) {
            TetrisPieceType.I -> iKickTable[key] ?: listOf(0 to 0)
            TetrisPieceType.O -> listOf(0 to 0) // O方块不旋转
            else -> jlstzKickTable[key] ?: listOf(0 to 0)
        }
    }
    
    /**
     * 检测T-Spin
     * @param piece T方块
     * @param board 游戏板
     * @param lastAction 最后一个动作是否为旋转
     * @return T-Spin类型
     */
    fun detectTSpin(
        piece: TetrisPiece,
        board: TetrisBoard,
        lastAction: TetrisAction
    ): TSpinType {
        if (piece.type != TetrisPieceType.T || lastAction !is TetrisAction.Rotate) {
            return TSpinType.NONE
        }
        
        // 检查T方块的四个角落
        val corners = getTCorners(piece)
        var filledCorners = 0
        
        for ((x, y) in corners) {
            if (x < 0 || x >= board.width || y < 0 || y >= board.height ||
                board.cells[y][x] != TetrisCellType.EMPTY) {
                filledCorners++
            }
        }
        
        // T-Spin需要至少3个角落被填充
        if (filledCorners >= 3) {
            // 检查是否为T-Spin Mini
            val frontCorners = getFrontTCorners(piece)
            var filledFrontCorners = 0
            
            for ((x, y) in frontCorners) {
                if (x < 0 || x >= board.width || y < 0 || y >= board.height ||
                    board.cells[y][x] != TetrisCellType.EMPTY) {
                    filledFrontCorners++
                }
            }
            
            return if (filledFrontCorners == 2) {
                TSpinType.FULL
            } else {
                TSpinType.MINI
            }
        }
        
        return TSpinType.NONE
    }
    
    /**
     * 获取T方块的四个角落位置
     */
    private fun getTCorners(piece: TetrisPiece): List<Pair<Int, Int>> {
        val centerX = piece.x
        val centerY = piece.y
        
        return when (piece.rotation % 4) {
            0 -> listOf( // T方块朝上
                centerX - 1 to centerY - 1, // 左上
                centerX + 1 to centerY - 1, // 右上
                centerX - 1 to centerY + 1, // 左下
                centerX + 1 to centerY + 1  // 右下
            )
            1 -> listOf( // T方块朝右
                centerX - 1 to centerY - 1, // 左上
                centerX + 1 to centerY - 1, // 右上
                centerX - 1 to centerY + 1, // 左下
                centerX + 1 to centerY + 1  // 右下
            )
            2 -> listOf( // T方块朝下
                centerX - 1 to centerY - 1, // 左上
                centerX + 1 to centerY - 1, // 右上
                centerX - 1 to centerY + 1, // 左下
                centerX + 1 to centerY + 1  // 右下
            )
            3 -> listOf( // T方块朝左
                centerX - 1 to centerY - 1, // 左上
                centerX + 1 to centerY - 1, // 右上
                centerX - 1 to centerY + 1, // 左下
                centerX + 1 to centerY + 1  // 右下
            )
            else -> emptyList()
        }
    }
    
    /**
     * 获取T方块前方的两个角落位置
     */
    private fun getFrontTCorners(piece: TetrisPiece): List<Pair<Int, Int>> {
        val centerX = piece.x
        val centerY = piece.y
        
        return when (piece.rotation % 4) {
            0 -> listOf( // T方块朝上，前方是上方
                centerX - 1 to centerY - 1, // 左上
                centerX + 1 to centerY - 1  // 右上
            )
            1 -> listOf( // T方块朝右，前方是右方
                centerX + 1 to centerY - 1, // 右上
                centerX + 1 to centerY + 1  // 右下
            )
            2 -> listOf( // T方块朝下，前方是下方
                centerX - 1 to centerY + 1, // 左下
                centerX + 1 to centerY + 1  // 右下
            )
            3 -> listOf( // T方块朝左，前方是左方
                centerX - 1 to centerY - 1, // 左上
                centerX - 1 to centerY + 1  // 左下
            )
            else -> emptyList()
        }
    }
}

/**
 * T-Spin类型
 */
enum class TSpinType {
    NONE,   // 不是T-Spin
    MINI,   // T-Spin Mini
    FULL    // 完整T-Spin
}
