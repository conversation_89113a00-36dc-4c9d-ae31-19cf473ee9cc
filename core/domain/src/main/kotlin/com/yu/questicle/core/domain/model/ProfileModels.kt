package com.yu.questicle.core.domain.model

import kotlinx.serialization.Serializable

/**
 * 用户资料更新请求
 */
@Serializable
data class ProfileUpdateRequest(
    val displayName: String?,
    val email: String?,
    val bio: String?,
    val birthday: String?, // ISO字符串格式
    val gender: Gender?
)

/**
 * 密码重置请求
 */
@Serializable
data class PasswordResetRequest(
    val email: String
)

/**
 * 密码重置确认请求
 */
@Serializable
data class PasswordResetConfirm(
    val token: String,
    val newPassword: String,
    val confirmPassword: String
)

/**
 * 头像上传响应
 */
@Serializable
data class AvatarUploadResponse(
    val avatarUrl: String,
    val thumbnailUrl: String? = null
)

/**
 * 密码重置令牌实体
 */
@Serializable
data class PasswordResetToken(
    val token: String,
    val userId: String,
    val email: String,
    val expiresAt: Long,
    val used: Boolean = false,
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 文件上传记录
 */
@Serializable
data class FileUpload(
    val id: String,
    val userId: String,
    val fileName: String,
    val fileType: String,
    val fileSize: Long,
    val localPath: String?,
    val remoteUrl: String?,
    val uploadStatus: FileUploadStatus,
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 文件上传状态
 */
@Serializable
enum class FileUploadStatus {
    PENDING,
    UPLOADING,
    COMPLETED,
    FAILED
}

/**
 * 表单验证错误
 */
data class ValidationError(
    val field: String,
    val message: String
)

/**
 * 表单验证结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<ValidationError> = emptyList()
) {
    fun getErrorForField(field: String): String? {
        return errors.find { it.field == field }?.message
    }
}
