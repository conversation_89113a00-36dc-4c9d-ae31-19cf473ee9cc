package com.yu.questicle.core.domain.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.model.Theme
import com.yu.questicle.core.domain.model.GameDifficulty
import kotlinx.coroutines.flow.Flow

/**
 * 用户偏好设置仓库接口
 * 负责用户偏好设置的持久化和管理
 * 实现 REQ-SETTINGS-001 到 REQ-SETTINGS-010
 */
interface UserPreferencesRepository {
    
    /**
     * 获取用户偏好设置流
     */
    fun getUserPreferences(): Flow<UserPreferences>
    
    /**
     * 获取当前用户偏好设置
     */
    suspend fun getCurrentUserPreferences(): Result<UserPreferences>
    
    /**
     * 更新用户偏好设置
     */
    suspend fun updateUserPreferences(preferences: UserPreferences): Result<UserPreferences>
    
    /**
     * 更新主题设置
     */
    suspend fun updateTheme(theme: Theme): Result<Unit>
    
    /**
     * 更新语言设置
     */
    suspend fun updateLanguage(language: String): Result<Unit>
    
    /**
     * 更新音效设置
     */
    suspend fun updateSoundEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * 更新音乐设置
     */
    suspend fun updateMusicEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * 更新震动设置
     */
    suspend fun updateVibrationEnabled(enabled: Boolean): Result<Unit>

    /**
     * 更新主音量
     */
    suspend fun updateMasterVolume(volume: Float): Result<Unit>

    /**
     * 更新音效音量
     */
    suspend fun updateSoundEffectsVolume(volume: Float): Result<Unit>

    /**
     * 更新音乐音量
     */
    suspend fun updateMusicVolume(volume: Float): Result<Unit>

    /**
     * 更新通知设置
     */
    suspend fun updateNotificationsEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * 更新自动保存设置
     */
    suspend fun updateAutoSaveEnabled(enabled: Boolean): Result<Unit>
    
    /**
     * 更新游戏难度
     */
    suspend fun updateGameDifficulty(difficulty: GameDifficulty): Result<Unit>
    
    /**
     * 更新游戏速度
     */
    suspend fun updateGameSpeed(speed: Float): Result<Unit>
    
    /**
     * 更新控制灵敏度
     */
    suspend fun updateControlSensitivity(sensitivity: Float): Result<Unit>
    
    /**
     * 更新显示提示设置
     */
    suspend fun updateShowHintsEnabled(enabled: Boolean): Result<Unit>

    /**
     * 更新动画速度
     */
    suspend fun updateAnimationSpeed(speed: Float): Result<Unit>

    /**
     * 更新显示教程设置
     */
    suspend fun updateShowTutorial(show: Boolean): Result<Unit>
    
    /**
     * 重置为默认设置
     */
    suspend fun resetToDefaults(): Result<UserPreferences>
    
    /**
     * 导出用户数据
     */
    suspend fun exportUserData(): Result<String>
    
    /**
     * 清除所有数据
     */
    suspend fun clearAllData(): Result<Unit>
    
    /**
     * 批量更新设置
     */
    suspend fun batchUpdatePreferences(
        updates: Map<String, Any>
    ): Result<UserPreferences>
    
    /**
     * 验证设置值
     */
    suspend fun validatePreferences(preferences: UserPreferences): Result<Unit>
    
    /**
     * 获取设置历史记录
     */
    suspend fun getPreferencesHistory(): Result<List<UserPreferences>>
    
    /**
     * 备份设置
     */
    suspend fun backupPreferences(): Result<String>
    
    /**
     * 恢复设置
     */
    suspend fun restorePreferences(backup: String): Result<UserPreferences>
}
