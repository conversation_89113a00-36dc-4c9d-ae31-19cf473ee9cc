package com.yu.questicle.core.domain.model.tetris

import kotlinx.serialization.Serializable

/**
 * 游戏移动操作结果
 */
@Serializable
sealed class GameMoveResult {
    /**
     * 移动成功
     */
    @Serializable
    data class Success(val newPiece: TetrisPiece) : GameMoveResult()
    
    /**
     * 移动失败 - 碰撞
     */
    @Serializable
    object Collision : GameMoveResult()
    
    /**
     * 移动失败 - 方块已锁定
     */
    @Serializable
    object PieceLocked : GameMoveResult()
}

/**
 * 游戏旋转操作结果
 */
@Serializable
sealed class GameRotationResult {
    /**
     * 旋转成功
     */
    @Serializable
    data class Success(val rotatedPiece: TetrisPiece) : GameRotationResult()
    
    /**
     * 旋转成功（通过踢墙）
     */
    @Serializable
    data class SuccessWithKick(val kickedPiece: TetrisPiece) : GameRotationResult()
    
    /**
     * 旋转失败
     */
    @Serializable
    object Failed : GameRotationResult()
}

/**
 * 行清除操作结果
 */
@Serializable
sealed class LineClearResult {
    /**
     * 清除成功
     */
    @Serializable
    data class Success(
        val clearedBoard: TetrisBoard,
        val linesCleared: Int,
        val scoreIncrease: Int,
        val isTSpin: Boolean = false,
        val isPerfectClear: Boolean = false,
        val isBackToBack: Boolean = false
    ) : LineClearResult()
    
    /**
     * 无行可清除
     */
    @Serializable
    object NoLines : LineClearResult()
}

/**
 * 方块放置结果
 */
@Serializable
data class PiecePlacementResult(
    val board: TetrisBoard,
    val lineClearResult: LineClearResult,
    val gameOver: Boolean = false
)

/**
 * 游戏操作类型
 */
@Serializable
enum class TetrisAction {
    MOVE_LEFT,
    MOVE_RIGHT,
    MOVE_DOWN,
    ROTATE_CLOCKWISE,
    ROTATE_COUNTERCLOCKWISE,
    HARD_DROP,
    SOFT_DROP,
    HOLD,
    PAUSE,
    RESUME
}

/**
 * 游戏操作结果
 */
@Serializable
data class TetrisActionResult(
    val action: TetrisAction,
    val success: Boolean,
    val newGameState: TetrisGameState? = null,
    val scoreIncrease: Int = 0,
    val linesCleared: Int = 0,
    val message: String? = null
)

/**
 * 碰撞检测结果
 */
@Serializable
data class CollisionResult(
    val hasCollision: Boolean,
    val collisionType: CollisionType = CollisionType.NONE,
    val collisionPoints: List<Pair<Int, Int>> = emptyList()
)

/**
 * 碰撞类型
 */
@Serializable
enum class CollisionType {
    NONE,
    WALL,
    FLOOR,
    PIECE,
    OUT_OF_BOUNDS
}

/**
 * 方块生成结果
 */
@Serializable
data class PieceGenerationResult(
    val piece: TetrisPiece,
    val nextPiece: TetrisPiece,
    val bagEmpty: Boolean = false
)

/**
 * 游戏初始化结果
 */
@Serializable
data class GameInitializationResult(
    val gameState: TetrisGameState,
    val success: Boolean,
    val errorMessage: String? = null
)

/**
 * 游戏保存结果
 */
@Serializable
data class GameSaveResult(
    val success: Boolean,
    val saveId: String? = null,
    val errorMessage: String? = null
)

/**
 * 游戏加载结果
 */
@Serializable
data class GameLoadResult(
    val gameState: TetrisGameState? = null,
    val success: Boolean,
    val errorMessage: String? = null
)

/**
 * 统计更新结果
 */
@Serializable
data class StatisticsUpdateResult(
    val statistics: GameStatistics,
    val achievements: List<String> = emptyList(),
    val newRecords: List<String> = emptyList()
)

/**
 * 性能测试结果
 */
@Serializable
data class PerformanceTestResult(
    val operationName: String,
    val executionTimeMs: Double,
    val memoryUsedBytes: Long,
    val success: Boolean,
    val errorMessage: String? = null
)

/**
 * 游戏验证结果
 */
@Serializable
data class GameValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)

/**
 * 网络同步结果
 */
@Serializable
data class NetworkSyncResult(
    val success: Boolean,
    val syncedGameState: TetrisGameState? = null,
    val conflictResolution: String? = null,
    val errorMessage: String? = null
)

/**
 * 配置验证结果
 */
@Serializable
data class ConfigValidationResult(
    val isValid: Boolean,
    val configName: String,
    val errors: List<String> = emptyList(),
    val appliedDefaults: Map<String, String> = emptyMap()
)

/**
 * 缓存操作结果
 */
@Serializable
data class CacheOperationResult(
    val success: Boolean,
    val operation: String,
    val cacheHit: Boolean = false,
    val dataSize: Long = 0,
    val errorMessage: String? = null
)
