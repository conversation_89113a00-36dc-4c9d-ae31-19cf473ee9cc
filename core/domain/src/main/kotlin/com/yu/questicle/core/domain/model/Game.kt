package com.yu.questicle.core.domain.model

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * Core game model that supports multiple game types
 */
@Serializable
data class Game(
    val id: String = UUID.randomUUID().toString(),
    val type: GameType,
    val status: GameStatus,
    val playerId: String,
    val score: Int = 0,
    val level: Int = 1,
    val startTime: Long = System.currentTimeMillis() / 1000,
    val endTime: Long? = null,
    val duration: Long = 0, // in seconds
    val metadata: Map<String, String> = emptyMap()
) {
    companion object {
        fun createNew(type: GameType, playerId: String): Game {
            return Game(
                type = type,
                status = GameStatus.READY,
                playerId = playerId
            )
        }
    }
}

/**
 * Supported game types - extensible for future games
 */
@Serializable
enum class GameType(val displayName: String) {
    TETRIS("俄罗斯方块"),
    PUZZLE("拼图游戏"),
    CARD("卡牌游戏"),
    STRATEGY("策略游戏"),
    ACTION("动作游戏");
    
    companion object {
        fun fromString(value: String): GameType? {
            return entries.find { it.name.equals(value, ignoreCase = true) }
        }
    }
}

/**
 * Game status lifecycle
 */
@Serializable
enum class GameStatus {
    READY,      // Game is ready to start
    PLAYING,    // Game is in progress
    PAUSED,     // Game is paused
    COMPLETED,  // Game completed successfully
    FAILED,     // Game ended in failure
    ABANDONED   // Game was abandoned by player
}

/**
 * Game difficulty levels
 */
@Serializable
enum class GameDifficulty(val multiplier: Float) {
    EASY(1.0f),
    MEDIUM(1.5f),
    HARD(2.0f),
    EXPERT(3.0f),
    MASTER(5.0f)
}

/**
 * Game session for tracking gameplay
 */
@Serializable
data class GameSession(
    val id: String = UUID.randomUUID().toString(),
    val gameId: String,
    val playerId: String,
    val startTime: Long = System.currentTimeMillis() / 1000,
    val endTime: Long? = null,
    val actions: List<GameAction> = emptyList(),
    val achievements: List<String> = emptyList()
)



/**
 * Game statistics for analytics
 */
@Serializable
data class GameStats(
    val gameType: GameType,
    val playerId: String,
    val totalGames: Int = 0,
    val totalScore: Long = 0,
    val bestScore: Int = 0,
    val averageScore: Double = 0.0,
    val totalPlayTime: Long = 0, // in seconds
    val achievements: Set<String> = emptySet(),
    val lastPlayed: Long? = null
) {
    companion object {
        fun createEmpty(gameType: GameType = GameType.TETRIS, playerId: String = "guest"): GameStats {
            return GameStats(
                gameType = gameType,
                playerId = playerId
            )
        }
    }
}
