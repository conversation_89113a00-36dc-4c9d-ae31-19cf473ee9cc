package com.yu.questicle.core.domain.usecase.user

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.security.PasswordManager
import com.yu.questicle.core.domain.validation.UserValidation
import com.yu.questicle.core.domain.validation.ValidationResult
import kotlinx.coroutines.flow.first
import java.time.Instant
import javax.inject.Inject

/**
 * 用户认证用例
 * 实现 REQ-USER-001 到 REQ-USER-004: 用户注册、登录和游客模式
 */
class AuthUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    
    /**
     * 游客登录
     * 实现 REQ-USER-004: 系统应支持游客模式
     */
    suspend fun loginAsGuest(): Result<User> {
        return try {
            userRepository.createGuestUser()
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 用户注册
     * 实现 REQ-USER-001: 用户应能够注册新账户
     */
    suspend fun registerUser(
        username: String,
        email: String?,
        password: String,
        passwordConfirmation: String,
        displayName: String? = null
    ): Result<User> {
        return try {
            // 验证输入
            val validationResult = UserValidation.validateRegistration(
                username = username,
                email = email,
                password = password,
                passwordConfirmation = passwordConfirmation,
                displayName = displayName
            )
            
            when (validationResult) {
                is ValidationResult.Invalid -> {
                    return Result.Error(
                        IllegalArgumentException(validationResult.getErrorMessage())
                            .toQuesticleException()
                    )
                }
                ValidationResult.Valid -> {
                    // 验证通过，创建用户
                    val passwordHash = PasswordManager.hashPassword(password)
                    val newUser = User(
                        username = username,
                        email = email,
                        passwordHash = passwordHash,
                        displayName = username
                    )
                    userRepository.createUser(newUser)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 用户名登录
     * 实现 REQ-USER-002: 用户应能够使用用户名和密码登录
     */
    suspend fun loginWithUsername(
        username: String,
        password: String
    ): Result<User> {
        return try {
            // 验证输入
            val usernameValidation = UserValidation.validateUsername(username)
            if (usernameValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("用户名格式不正确")
                        .toQuesticleException()
                )
            }
            
            val passwordValidation = UserValidation.validatePassword(password)
            if (passwordValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("密码格式不正确")
                        .toQuesticleException()
                )
            }
            
            // 实现密码验证逻辑
            when (val searchResult = userRepository.searchUsers(username)) {
                is Result.Success -> {
                    val users = searchResult.data
                    val user = users.find { it.username == username }
                    if (user != null) {
                        // 验证密码（实际应用中应使用加密的密码）
                        val isPasswordValid = PasswordManager.verifyPassword(password, user.passwordHash ?: "")
                        if (isPasswordValid) {
                            // 更新最后登录时间
                            val updatedUser = user.copy(lastLoginAt = System.currentTimeMillis() / 1000)
                            userRepository.saveUser(updatedUser)
                            Result.Success(updatedUser)
                        } else {
                            Result.Error(
                                IllegalArgumentException("用户名或密码错误")
                                    .toQuesticleException()
                            )
                        }
                    } else {
                        Result.Error(
                            IllegalArgumentException("用户名或密码错误")
                                .toQuesticleException()
                        )
                    }
                }
                is Result.Error -> searchResult
                is Result.Loading -> Result.Error(
                    IllegalStateException("登录处理中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 邮箱登录
     * 实现 REQ-USER-003: 用户应能够使用邮箱和密码登录
     */
    suspend fun loginWithEmail(
        email: String,
        password: String
    ): Result<User> {
        return try {
            // 验证输入
            val emailValidation = UserValidation.validateEmail(email)
            if (emailValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("邮箱格式不正确")
                        .toQuesticleException()
                )
            }
            
            val passwordValidation = UserValidation.validatePassword(password)
            if (passwordValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("密码格式不正确")
                        .toQuesticleException()
                )
            }
            
            // 实现密码验证逻辑
            when (val searchResult = userRepository.searchUsers(email)) {
                is Result.Success -> {
                    val users = searchResult.data
                    val user = users.find { it.email == email }
                    if (user != null) {
                        // 验证密码
                        val userPasswordHash = user.passwordHash
                        if (userPasswordHash != null && PasswordManager.verifyPassword(password, userPasswordHash)) {
                            Result.Success(user)
                        } else {
                            Result.Error(
                                IllegalArgumentException("邮箱或密码错误")
                                    .toQuesticleException()
                            )
                        }
                    } else {
                        Result.Error(
                            IllegalArgumentException("邮箱或密码错误")
                                .toQuesticleException()
                        )
                    }
                }
                is Result.Error -> searchResult
                is Result.Loading -> Result.Error(
                    IllegalStateException("登录处理中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 重置密码
     */
    suspend fun resetPassword(email: String): Result<Unit> {
        return try {
            // 验证邮箱格式
            val emailValidation = UserValidation.validateEmail(email)
            if (emailValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("邮箱格式不正确")
                        .toQuesticleException()
                )
            }

            // 查找用户
            when (val searchResult = userRepository.searchUsers(email)) {
                is Result.Success -> {
                    val users = searchResult.data
                    val user = users.find { it.email == email }
                    if (user != null) {
                        // 生成临时密码
                        val tempPassword = PasswordManager.generateSecurePassword(12)
                        val hashedPassword = PasswordManager.hashPassword(tempPassword)

                        // 更新用户密码
                        val updatedUser = user.copy(passwordHash = hashedPassword)
                        when (val saveResult = userRepository.saveUser(updatedUser)) {
                            is Result.Success -> {
                                // 在实际应用中，这里应该发送邮件
                                // 目前简化实现，直接返回成功
                                Result.Success(Unit)
                            }
                            is Result.Error -> saveResult
                            is Result.Loading -> Result.Error(
                                IllegalStateException("重置密码处理中").toQuesticleException()
                            )
                        }
                    } else {
                        // 为了安全，即使用户不存在也返回成功
                        Result.Success(Unit)
                    }
                }
                is Result.Error -> Result.Success(Unit) // 为了安全，不暴露用户是否存在
                is Result.Loading -> Result.Error(
                    IllegalStateException("重置密码处理中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 登出
     */
    suspend fun logout(): Result<Unit> {
        return try {
            // 清理用户会话
            when (val result = userRepository.clearCurrentUser()) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("登出处理中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 获取当前用户
     */
    suspend fun getCurrentUser(): Result<User?> {
        return try {
            val user = userRepository.getCurrentUser().first()
            Result.Success(user)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 验证用户是否已登录
     * 使用统一的游客判断逻辑
     */
    suspend fun isLoggedIn(): Boolean {
        return try {
            val user = userRepository.getCurrentUser().first()
            user != null && user.isRegisteredUser()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 验证用户是否为游客
     * 使用统一的游客判断逻辑
     */
    suspend fun isGuest(): Boolean {
        return try {
            val user = userRepository.getCurrentUser().first()
            user?.isGuest() == true
        } catch (e: Exception) {
            true
        }
    }
    
    /**
     * 更新用户密码
     */
    suspend fun updatePassword(
        userId: String,
        currentPassword: String,
        newPassword: String
    ): Result<Unit> {
        return try {
            // 验证输入
            val passwordValidation = UserValidation.validatePassword(newPassword)
            if (passwordValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("新密码格式不正确")
                        .toQuesticleException()
                )
            }

            // 获取用户
            when (val userResult = userRepository.getUserById(userId)) {
                is Result.Success -> {
                    val user = userResult.data

                    // 验证当前密码
                    val currentPasswordHash = user.passwordHash
                    if (currentPasswordHash == null || !PasswordManager.verifyPassword(currentPassword, currentPasswordHash)) {
                        return Result.Error(
                            IllegalArgumentException("当前密码错误")
                                .toQuesticleException()
                        )
                    }

                    // 生成新密码哈希
                    val newPasswordHash = PasswordManager.hashPassword(newPassword)

                    // 更新用户密码
                    val updatedUser = user.copy(passwordHash = newPasswordHash)
                    when (val saveResult = userRepository.saveUser(updatedUser)) {
                        is Result.Success -> Result.Success(Unit)
                        is Result.Error -> saveResult
                        is Result.Loading -> Result.Error(
                            IllegalStateException("更新密码处理中").toQuesticleException()
                        )
                    }
                }
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(
                    IllegalStateException("获取用户信息中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * 从游客升级为正式用户
     */
    suspend fun upgradeFromGuest(
        username: String,
        email: String?,
        password: String,
        passwordConfirmation: String
    ): Result<User> {
        return try {
            val currentUser = userRepository.getCurrentUser().first()
                ?: return Result.Error(
                    IllegalStateException("没有当前用户").toQuesticleException()
                )

            if (!currentUser.isGuest()) {
                return Result.Error(
                    IllegalStateException("当前用户不是游客").toQuesticleException()
                )
            }

            // 验证新用户信息
            val validationResult = UserValidation.validateRegistration(
                username = username,
                email = email,
                password = password,
                passwordConfirmation = passwordConfirmation,
                displayName = username
            )

            when (validationResult) {
                is ValidationResult.Invalid -> {
                    return Result.Error(
                        IllegalArgumentException(validationResult.getErrorMessage())
                            .toQuesticleException()
                    )
                }
                ValidationResult.Valid -> {
                    // 创建新用户，保留游客的游戏数据
                    val upgradedUser = currentUser.copy(
                        username = username,
                        email = email,
                        displayName = username
                    )

                    when (val saveResult = userRepository.saveUser(upgradedUser)) {
                        is Result.Success -> Result.Success(upgradedUser)
                        is Result.Error -> saveResult
                        is Result.Loading -> Result.Error(
                            IllegalStateException("保存用户中").toQuesticleException()
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}
