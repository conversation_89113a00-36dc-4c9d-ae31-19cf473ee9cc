package com.yu.questicle.core.domain.exception

import com.yu.questicle.core.common.exception.*

/**
 * 用户资料相关异常
 */
class ProfileUpdateException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PROFILE_UPDATE_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val profileUserId: String? = null,
    val field: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    userMessage = userMessage,
    context = context + buildMap {
        profileUserId?.let { put("userId", it) }
        field?.let { put("field", it) }
    },
    userId = profileUserId
) {
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException = ProfileUpdateException(
        message = message,
        cause = cause,
        errorCode = errorCode,
        userMessage = userMessage,
        context = context,
        profileUserId = context["userId"] as? String,
        field = context["field"] as? String
    )
}

/**
 * 密码重置异常
 */
class PasswordResetException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PASSWORD_RESET_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val email: String? = null,
    val token: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    userMessage = userMessage,
    context = context + buildMap {
        email?.let { put("email", it.take(3) + "***") } // 脱敏处理
        token?.let { put("token", it.take(8) + "***") }
    }
) {
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException = PasswordResetException(
        message = message,
        cause = cause,
        errorCode = errorCode,
        userMessage = userMessage,
        context = context,
        email = context["email"] as? String,
        token = context["token"] as? String
    )
}

/**
 * 头像上传异常
 */
class AvatarUploadException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AVATAR_UPLOAD_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val avatarUserId: String? = null,
    val fileSize: Long? = null,
    val fileType: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    userMessage = userMessage,
    context = context + buildMap {
        avatarUserId?.let { put("userId", it) }
        fileSize?.let { put("fileSize", it) }
        fileType?.let { put("fileType", it) }
    },
    userId = avatarUserId
) {
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException = AvatarUploadException(
        message = message,
        cause = cause,
        errorCode = errorCode,
        userMessage = userMessage,
        context = context,
        avatarUserId = context["userId"] as? String,
        fileSize = context["fileSize"] as? Long,
        fileType = context["fileType"] as? String
    )
}

/**
 * 用户资料验证异常
 */
class ProfileValidationException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PROFILE_VALIDATION_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val field: String? = null,
    val value: Any? = null
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.VALIDATION,
    severity = ErrorSeverity.LOW,
    userMessage = userMessage,
    context = context + buildMap {
        field?.let { put("field", it) }
        value?.let { put("value", it) }
    },
    retryable = false
) {
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException = ProfileValidationException(
        message = message,
        cause = cause,
        errorCode = errorCode,
        userMessage = userMessage,
        context = context,
        field = context["field"] as? String,
        value = context["value"]
    )

    override fun getDefaultUserMessage(): String {
        return field?.let { "输入的 $it 不正确，请检查后重试" } ?: "输入信息不正确，请检查后重试"
    }
}
