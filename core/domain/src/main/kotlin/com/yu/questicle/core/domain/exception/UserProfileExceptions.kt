package com.yu.questicle.core.domain.exception

import com.yu.questicle.core.common.exception.*

/**
 * 用户相关异常基类
 */
abstract class UserException(
    message: String,
    cause: Throwable? = null,
    errorCode: String,
    val userId: String? = null
) : BusinessException(
    message = message,
    errorCode = errorCode,
    severity = ErrorSeverity.MEDIUM,
    cause = cause
)

/**
 * 用户资料相关异常
 */
class ProfileUpdateException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PROFILE_UPDATE_ERROR",
    val profileUserId: String? = null,
    val field: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    userId = profileUserId
)

/**
 * 密码重置异常
 */
class PasswordResetException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PASSWORD_RESET_ERROR",
    val email: String? = null,
    val token: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode
)

/**
 * 头像上传异常
 */
class AvatarUploadException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AVATAR_UPLOAD_ERROR",
    val avatarUserId: String? = null,
    val fileSize: Long? = null,
    val fileType: String? = null
) : UserException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    userId = avatarUserId
)

/**
 * 用户资料验证异常
 */
class ProfileValidationException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PROFILE_VALIDATION_ERROR",
    val field: String? = null,
    val value: Any? = null
) : BusinessException(
    message = message,
    errorCode = errorCode,
    severity = ErrorSeverity.LOW,
    cause = cause
) {
    override fun getDefaultUserMessage(): String {
        return field?.let { "输入的 $it 不正确，请检查后重试" } ?: "输入信息不正确，请检查后重试"
    }
}
