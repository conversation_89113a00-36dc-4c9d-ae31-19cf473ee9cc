package com.yu.questicle.core.domain.service

import com.yu.questicle.core.domain.model.LevelUpEvent
import com.yu.questicle.core.domain.model.MembershipTier
import com.yu.questicle.core.domain.model.Reward
import com.yu.questicle.core.domain.model.RewardType
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.membershipTier
import javax.inject.Inject

/**
 * 会员服务接口
 * 
 * Pure business logic service for membership and level calculations.
 * This service no longer depends on repositories directly, following Clean Architecture principles.
 */
interface MembershipService {
    fun calculateLevel(experience: Long): Int
    fun calculateLevelProgress(experience: Long): Float
    fun getExperienceForLevel(level: Int): Long
    fun getExperienceToNextLevel(experience: Long): Long
    fun getLevelRewards(level: Int): List<Reward>
    fun calculateExperienceWithMultiplier(baseExperience: Long, user: User): Long
    fun getNextLevelInfo(currentExperience: Long): LevelInfo
    fun calculateLevelUpInfo(oldLevel: Int, newLevel: Int): LevelUpInfo
}

/**
 * Level information data class
 */
data class LevelInfo(
    val currentLevel: Int,
    val experienceToNextLevel: Long,
    val levelProgress: Float,
    val nextLevelExperience: Long
)

/**
 * Level up information data class
 */
data class LevelUpInfo(
    val levelsGained: Int,
    val totalRewards: List<Reward>,
    val isSignificantLevelUp: Boolean // Level 5, 10, 15, 20 etc.
)

/**
 * 会员服务实现
 * 
 * Pure business logic implementation without external dependencies.
 * All I/O operations are handled by UseCases.
 */
class MembershipServiceImpl @Inject constructor() : MembershipService {

    private val levelThresholds = listOf(
        0L, 100L, 300L, 600L, 1000L, 1500L, 2100L, 2800L, 3600L, 4500L,
        5500L, 6600L, 7800L, 9100L, 10500L, 12000L, 13600L, 15300L, 17100L, 19000L
    )

    override fun calculateLevel(experience: Long): Int {
        if (experience < 0) return 1

        val index = levelThresholds.indexOfLast { it <= experience }
        return if (index == -1) {
            1  // If no threshold found, return level 1
        } else {
            minOf(index + 1, 20)  // index + 1 because levels are 1-based
        }
    }

    override fun calculateLevelProgress(experience: Long): Float {
        val currentLevel = calculateLevel(experience)
        if (currentLevel >= 20) return 1.0f

        val currentLevelStartExp = if (currentLevel == 1) 0L else levelThresholds[currentLevel - 1]
        val nextLevelStartExp = if (currentLevel < levelThresholds.size) levelThresholds[currentLevel] else levelThresholds.last()

        return if (nextLevelStartExp > currentLevelStartExp) {
            ((experience - currentLevelStartExp).toFloat() / (nextLevelStartExp - currentLevelStartExp)).coerceIn(0f, 1f)
        } else {
            1.0f
        }
    }

    override fun getExperienceForLevel(level: Int): Long {
        if (level <= 1) return 0L
        if (level > levelThresholds.size) return levelThresholds.last()
        return levelThresholds.getOrElse(level - 2) { levelThresholds.last() }
    }

    override fun getExperienceToNextLevel(experience: Long): Long {
        val currentLevel = calculateLevel(experience)
        if (currentLevel >= 20) return 0L
        
        // Get the experience required for the next level
        val nextLevelIndex = currentLevel  // currentLevel is 1-based, array is 0-based
        if (nextLevelIndex >= levelThresholds.size) return 0L
        
        val nextLevelExp = levelThresholds[nextLevelIndex]
        return (nextLevelExp - experience).coerceAtLeast(0L)
    }

    override fun getLevelRewards(level: Int): List<Reward> {
        val rewards = mutableListOf<Reward>()

        // 基础金币奖励
        rewards.add(Reward(RewardType.COINS, level * 50L, "Level $level coins reward"))

        // 特殊等级奖励
        when {
            level % 10 == 0 -> {
                rewards.add(Reward(RewardType.GEMS, level / 10 * 5L, "Level $level gems milestone"))
            }
            level % 5 == 0 -> {
                rewards.add(Reward(RewardType.ACHIEVEMENT, level.toLong(), "Level $level Achievement"))
            }
        }

        return rewards
    }

    override fun calculateExperienceWithMultiplier(baseExperience: Long, user: User): Long {
        val multiplier = when (user.membershipTier) {
            MembershipTier.FREE -> 1.0f
            MembershipTier.BASIC -> 1.2f
            MembershipTier.PREMIUM -> 1.5f
            MembershipTier.VIP -> 2.0f
        }
        return (baseExperience * multiplier).toLong()
    }

    override fun getNextLevelInfo(currentExperience: Long): LevelInfo {
        val currentLevel = calculateLevel(currentExperience)
        val experienceToNextLevel = getExperienceToNextLevel(currentExperience)
        val levelProgress = calculateLevelProgress(currentExperience)
        val nextLevelExperience = if (currentLevel < 20) {
            levelThresholds[currentLevel]
        } else {
            levelThresholds.last()
        }

        return LevelInfo(
            currentLevel = currentLevel,
            experienceToNextLevel = experienceToNextLevel,
            levelProgress = levelProgress,
            nextLevelExperience = nextLevelExperience
        )
    }

    override fun calculateLevelUpInfo(oldLevel: Int, newLevel: Int): LevelUpInfo {
        val levelsGained = newLevel - oldLevel
        val totalRewards = mutableListOf<Reward>()

        // Collect rewards for all levels gained
        for (level in (oldLevel + 1)..newLevel) {
            totalRewards.addAll(getLevelRewards(level))
        }

        // Check if this is a significant level up
        val isSignificantLevelUp = (newLevel % 5 == 0) || (newLevel % 10 == 0)

        return LevelUpInfo(
            levelsGained = levelsGained,
            totalRewards = totalRewards,
            isSignificantLevelUp = isSignificantLevelUp
        )
    }
} 