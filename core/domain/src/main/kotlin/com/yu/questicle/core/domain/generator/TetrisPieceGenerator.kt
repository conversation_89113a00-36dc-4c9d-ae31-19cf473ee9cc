package com.yu.questicle.core.domain.generator

import com.yu.questicle.core.domain.model.tetris.TetrisPiece
import com.yu.questicle.core.domain.model.tetris.TetrisPieceType
import kotlin.random.Random

/**
 * 俄罗斯方块生成器接口
 */
interface TetrisPieceGenerator {
    /**
     * 生成下一个方块
     */
    fun generateNext(): TetrisPiece
    
    /**
     * 预览接下来的几个方块
     */
    fun preview(count: Int): List<TetrisPiece>
    
    /**
     * 重置生成器
     */
    fun reset()
    
    /**
     * 获取当前bag的剩余方块数
     */
    fun getRemainingInBag(): Int
}

/**
 * 7-bag随机生成器实现
 * 确保每7个方块中包含所有7种类型的方块各一个
 */
class SevenBagGenerator(
    private val random: Random = Random.Default,
    private val startX: Int = 4,
    private val startY: Int = 0
) : TetrisPieceGenerator {
    
    private val allPieceTypes = TetrisPieceType.entries.toList()
    private var currentBag = mutableListOf<TetrisPieceType>()
    private var nextBag = mutableListOf<TetrisPieceType>()
    
    init {
        // 初始化两个bag
        refillBag(currentBag)
        refillBag(nextBag)
    }
    
    override fun generateNext(): TetrisPiece {
        // 如果当前bag为空，使用下一个bag并生成新的下一个bag
        if (currentBag.isEmpty()) {
            currentBag.addAll(nextBag)
            nextBag.clear()
            refillBag(nextBag)
        }
        
        // 从当前bag中随机取出一个方块类型
        val randomIndex = random.nextInt(currentBag.size)
        val pieceType = currentBag.removeAt(randomIndex)
        
        // 使用正确的初始位置
        val correctStartX = when (pieceType) {
            TetrisPieceType.I -> 3  // I方块在位置3-6
            TetrisPieceType.O -> 4  // O方块在位置4-5
            else -> 3               // 其他方块在位置3-5
        }

        return TetrisPiece(
            type = pieceType,
            x = correctStartX,
            y = startY,
            rotation = 0
        )
    }
    
    override fun preview(count: Int): List<TetrisPiece> {
        val preview = mutableListOf<TetrisPiece>()
        val tempCurrentBag = currentBag.toMutableList()
        val tempNextBag = nextBag.toMutableList()
        val tempRandom = Random(random.nextLong())
        
        repeat(count) {
            if (tempCurrentBag.isEmpty()) {
                tempCurrentBag.addAll(tempNextBag)
                tempNextBag.clear()
                refillBag(tempNextBag, tempRandom)
            }
            
            val randomIndex = tempRandom.nextInt(tempCurrentBag.size)
            val pieceType = tempCurrentBag.removeAt(randomIndex)
            
            // 使用正确的初始位置
            val correctStartX = when (pieceType) {
                TetrisPieceType.I -> 3  // I方块在位置3-6
                TetrisPieceType.O -> 4  // O方块在位置4-5
                else -> 3               // 其他方块在位置3-5
            }

            preview.add(
                TetrisPiece(
                    type = pieceType,
                    x = correctStartX,
                    y = startY,
                    rotation = 0
                )
            )
        }
        
        return preview
    }
    
    override fun reset() {
        currentBag.clear()
        nextBag.clear()
        refillBag(currentBag)
        refillBag(nextBag)
    }
    
    override fun getRemainingInBag(): Int = currentBag.size
    
    /**
     * 重新填充bag
     */
    private fun refillBag(bag: MutableList<TetrisPieceType>, rng: Random = random) {
        bag.clear()
        bag.addAll(allPieceTypes)
    }
}

/**
 * 纯随机生成器（用于测试或特殊模式）
 */
class RandomGenerator(
    private val random: Random = Random.Default,
    private val startX: Int = 4,
    private val startY: Int = 0
) : TetrisPieceGenerator {
    
    private val allPieceTypes = TetrisPieceType.entries.toList()
    
    override fun generateNext(): TetrisPiece {
        val pieceType = allPieceTypes.random(random)
        return TetrisPiece(
            type = pieceType,
            x = startX,
            y = startY,
            rotation = 0
        )
    }
    
    override fun preview(count: Int): List<TetrisPiece> {
        return (1..count).map {
            val pieceType = allPieceTypes.random(random)
            TetrisPiece(
                type = pieceType,
                x = startX,
                y = startY,
                rotation = 0
            )
        }
    }
    
    override fun reset() {
        // 纯随机生成器不需要重置状态
    }
    
    override fun getRemainingInBag(): Int = -1 // 纯随机模式没有bag概念
}

/**
 * 方块生成器工厂
 */
object TetrisPieceGeneratorFactory {
    
    /**
     * 创建7-bag生成器
     */
    fun createSevenBag(
        random: Random = Random.Default,
        startX: Int = 4,
        startY: Int = 0
    ): TetrisPieceGenerator = SevenBagGenerator(random, startX, startY)
    
    /**
     * 创建纯随机生成器
     */
    fun createRandom(
        random: Random = Random.Default,
        startX: Int = 4,
        startY: Int = 0
    ): TetrisPieceGenerator = RandomGenerator(random, startX, startY)
    
    /**
     * 根据类型创建生成器
     */
    fun create(
        type: GeneratorType,
        random: Random = Random.Default,
        startX: Int = 4,
        startY: Int = 0
    ): TetrisPieceGenerator = when (type) {
        GeneratorType.SEVEN_BAG -> createSevenBag(random, startX, startY)
        GeneratorType.RANDOM -> createRandom(random, startX, startY)
    }
}

/**
 * 生成器类型
 */
enum class GeneratorType {
    SEVEN_BAG,  // 7-bag生成器
    RANDOM      // 纯随机生成器
}

/**
 * 方块生成统计
 */
data class GeneratorStats(
    val totalGenerated: Int,
    val pieceCount: Map<TetrisPieceType, Int>,
    val currentBagRemaining: Int
) {
    /**
     * 获取方块类型的生成百分比
     */
    fun getPercentage(pieceType: TetrisPieceType): Double {
        return if (totalGenerated > 0) {
            (pieceCount[pieceType] ?: 0).toDouble() / totalGenerated * 100
        } else {
            0.0
        }
    }
    
    /**
     * 检查分布是否均匀（用于7-bag验证）
     */
    fun isDistributionEven(tolerance: Double = 5.0): Boolean {
        val expectedPercentage = 100.0 / TetrisPieceType.entries.size
        return TetrisPieceType.entries.all { pieceType ->
            val percentage = getPercentage(pieceType)
            kotlin.math.abs(percentage - expectedPercentage) <= tolerance
        }
    }
}

/**
 * 带统计功能的生成器装饰器
 */
class StatisticalGenerator(
    private val delegate: TetrisPieceGenerator
) : TetrisPieceGenerator by delegate {
    
    private var totalGenerated = 0
    private val pieceCount = mutableMapOf<TetrisPieceType, Int>()
    
    override fun generateNext(): TetrisPiece {
        val piece = delegate.generateNext()
        totalGenerated++
        pieceCount[piece.type] = pieceCount.getOrDefault(piece.type, 0) + 1
        return piece
    }
    
    override fun reset() {
        delegate.reset()
        totalGenerated = 0
        pieceCount.clear()
    }
    
    /**
     * 获取生成统计
     */
    fun getStats(): GeneratorStats {
        return GeneratorStats(
            totalGenerated = totalGenerated,
            pieceCount = pieceCount.toMap(),
            currentBagRemaining = delegate.getRemainingInBag()
        )
    }
}
