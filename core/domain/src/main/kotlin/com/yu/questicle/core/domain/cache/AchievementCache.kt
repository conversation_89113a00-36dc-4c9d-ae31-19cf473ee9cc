package com.yu.questicle.core.domain.cache

import com.yu.questicle.core.domain.model.Achievement
import com.yu.questicle.core.domain.model.UserAchievement
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 成就数据缓存管理器
 * Achievement data cache manager
 */
@Singleton
class AchievementCache @Inject constructor() {
    
    private val mutex = Mutex()
    
    // 缓存数据
    private var allAchievements: List<Achievement>? = null
    private var userAchievementsCache = mutableMapOf<String, List<UserAchievement>>()
    private var achievementStatsCache = mutableMapOf<String, Any>()
    
    // 缓存时间戳
    private var allAchievementsTimestamp = 0L
    private var userAchievementsTimestamp = mutableMapOf<String, Long>()
    private var achievementStatsTimestamp = mutableMapOf<String, Long>()
    
    // 缓存有效期（毫秒）
    private val cacheValidityDuration = 5 * 60 * 1000L // 5分钟
    private val userDataValidityDuration = 2 * 60 * 1000L // 2分钟
    
    /**
     * 获取所有成就（带缓存）
     */
    suspend fun getAllAchievements(): List<Achievement>? = mutex.withLock {
        if (isAllAchievementsCacheValid()) {
            allAchievements
        } else {
            null
        }
    }
    
    /**
     * 缓存所有成就
     */
    suspend fun cacheAllAchievements(achievements: List<Achievement>) = mutex.withLock {
        allAchievements = achievements
        allAchievementsTimestamp = System.currentTimeMillis()
    }
    
    /**
     * 获取用户成就（带缓存）
     */
    suspend fun getUserAchievements(userId: String): List<UserAchievement>? = mutex.withLock {
        if (isUserAchievementsCacheValid(userId)) {
            userAchievementsCache[userId]
        } else {
            null
        }
    }
    
    /**
     * 缓存用户成就
     */
    suspend fun cacheUserAchievements(userId: String, achievements: List<UserAchievement>) = mutex.withLock {
        userAchievementsCache[userId] = achievements
        userAchievementsTimestamp[userId] = System.currentTimeMillis()
    }
    
    /**
     * 获取成就统计（带缓存）
     */
    suspend fun getAchievementStats(userId: String): Any? = mutex.withLock {
        if (isAchievementStatsCacheValid(userId)) {
            achievementStatsCache[userId]
        } else {
            null
        }
    }
    
    /**
     * 缓存成就统计
     */
    suspend fun cacheAchievementStats(userId: String, stats: Any) = mutex.withLock {
        achievementStatsCache[userId] = stats
        achievementStatsTimestamp[userId] = System.currentTimeMillis()
    }
    
    /**
     * 使用户成就缓存失效
     */
    suspend fun invalidateUserAchievements(userId: String) = mutex.withLock {
        userAchievementsCache.remove(userId)
        userAchievementsTimestamp.remove(userId)
        achievementStatsCache.remove(userId)
        achievementStatsTimestamp.remove(userId)
    }
    
    /**
     * 使所有缓存失效
     */
    suspend fun invalidateAll() = mutex.withLock {
        allAchievements = null
        allAchievementsTimestamp = 0L
        userAchievementsCache.clear()
        userAchievementsTimestamp.clear()
        achievementStatsCache.clear()
        achievementStatsTimestamp.clear()
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun cleanupExpiredCache() = mutex.withLock {
        val currentTime = System.currentTimeMillis()
        
        // 清理过期的用户成就缓存
        val expiredUserIds = userAchievementsTimestamp.filter { (_, timestamp) ->
            currentTime - timestamp > userDataValidityDuration
        }.keys
        
        expiredUserIds.forEach { userId ->
            userAchievementsCache.remove(userId)
            userAchievementsTimestamp.remove(userId)
        }
        
        // 清理过期的成就统计缓存
        val expiredStatsUserIds = achievementStatsTimestamp.filter { (_, timestamp) ->
            currentTime - timestamp > userDataValidityDuration
        }.keys
        
        expiredStatsUserIds.forEach { userId ->
            achievementStatsCache.remove(userId)
            achievementStatsTimestamp.remove(userId)
        }
        
        // 清理过期的所有成就缓存
        if (currentTime - allAchievementsTimestamp > cacheValidityDuration) {
            allAchievements = null
            allAchievementsTimestamp = 0L
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStats(): CacheStats = mutex.withLock {
        CacheStats(
            allAchievementsCached = allAchievements != null,
            userAchievementsCacheSize = userAchievementsCache.size,
            achievementStatsCacheSize = achievementStatsCache.size,
            totalMemoryUsage = estimateMemoryUsage()
        )
    }
    
    /**
     * 检查所有成就缓存是否有效
     */
    private fun isAllAchievementsCacheValid(): Boolean {
        return allAchievements != null && 
               System.currentTimeMillis() - allAchievementsTimestamp < cacheValidityDuration
    }
    
    /**
     * 检查用户成就缓存是否有效
     */
    private fun isUserAchievementsCacheValid(userId: String): Boolean {
        val timestamp = userAchievementsTimestamp[userId] ?: return false
        return userAchievementsCache.containsKey(userId) &&
               System.currentTimeMillis() - timestamp < userDataValidityDuration
    }
    
    /**
     * 检查成就统计缓存是否有效
     */
    private fun isAchievementStatsCacheValid(userId: String): Boolean {
        val timestamp = achievementStatsTimestamp[userId] ?: return false
        return achievementStatsCache.containsKey(userId) &&
               System.currentTimeMillis() - timestamp < userDataValidityDuration
    }
    
    /**
     * 估算内存使用量（简化版）
     */
    private fun estimateMemoryUsage(): Long {
        var usage = 0L
        
        // 估算所有成就的内存使用
        allAchievements?.let { achievements ->
            usage += achievements.size * 500L // 假设每个成就约500字节
        }
        
        // 估算用户成就的内存使用
        userAchievementsCache.values.forEach { achievements ->
            usage += achievements.size * 200L // 假设每个用户成就约200字节
        }
        
        // 估算成就统计的内存使用
        usage += achievementStatsCache.size * 300L // 假设每个统计约300字节
        
        return usage
    }
}

/**
 * 缓存统计信息
 */
data class CacheStats(
    val allAchievementsCached: Boolean,
    val userAchievementsCacheSize: Int,
    val achievementStatsCacheSize: Int,
    val totalMemoryUsage: Long
)
