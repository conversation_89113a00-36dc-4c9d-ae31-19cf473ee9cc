package com.yu.questicle.core.domain.usecase

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.toQuesticleException

/**
 * Base interface for all use cases
 * 
 * This interface establishes the foundation for Clean Architecture's Application Layer.
 * All business logic should be implemented through UseCases that:
 * - Are independent of frameworks
 * - Are testable
 * - Are reusable
 * - Have single responsibility
 */
interface UseCase<Input, Output> {
    /**
     * Execute the use case with the given input
     * 
     * @param input The input parameters for the use case
     * @return Result containing the output or error
     */
    suspend fun execute(input: Input): Result<Output>
}

/**
 * Use case with no input parameters
 */
interface NoParamUseCase<Output> {
    /**
     * Execute the use case without parameters
     * 
     * @return Result containing the output or error
     */
    suspend fun execute(): Result<Output>
}

/**
 * Use case with no output (side effect only)
 */
interface NoOutputUseCase<Input> {
    /**
     * Execute the use case with the given input
     * 
     * @param input The input parameters for the use case
     * @return Result indicating success or failure
     */
    suspend fun execute(input: Input): Result<Unit>
}

/**
 * Use case with no input and no output (side effect only)
 */
interface NoParamNoOutputUseCase {
    /**
     * Execute the use case
     * 
     * @return Result indicating success or failure
     */
    suspend fun execute(): Result<Unit>
}

/**
 * Base class for use cases with input and output
 */
abstract class BaseUseCase<Input, Output> : UseCase<Input, Output> {
    /**
     * Template method that handles common concerns
     */
    final override suspend fun execute(input: Input): Result<Output> {
        return try {
            validateInput(input)
            executeInternal(input)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * Validate input parameters
     * Override this method to add input validation
     */
    protected open fun validateInput(input: Input) {
        // Default implementation does nothing
    }

    /**
     * Execute the actual business logic
     */
    protected abstract suspend fun executeInternal(input: Input): Result<Output>
}

/**
 * Base class for use cases with no input parameters
 */
abstract class BaseNoParamUseCase<Output> : NoParamUseCase<Output> {
    /**
     * Template method that handles common concerns
     */
    final override suspend fun execute(): Result<Output> {
        return try {
            validatePreconditions()
            executeInternal()
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * Validate preconditions
     * Override this method to add precondition validation
     */
    protected open fun validatePreconditions() {
        // Default implementation does nothing
    }

    /**
     * Execute the actual business logic
     */
    protected abstract suspend fun executeInternal(): Result<Output>
}

/**
 * Common input validation utilities
 */
object UseCaseValidation {
    fun validateNotNull(value: Any?, paramName: String) {
        if (value == null) {
            throw IllegalArgumentException("$paramName cannot be null")
        }
    }

    fun validateNotEmpty(value: String?, paramName: String) {
        if (value.isNullOrEmpty()) {
            throw IllegalArgumentException("$paramName cannot be empty")
        }
    }

    fun validateNotBlank(value: String?, paramName: String) {
        if (value.isNullOrBlank()) {
            throw IllegalArgumentException("$paramName cannot be blank")
        }
    }

    fun validatePositive(value: Long, paramName: String) {
        if (value <= 0) {
            throw IllegalArgumentException("$paramName must be positive")
        }
    }

    fun validateNonNegative(value: Long, paramName: String) {
        if (value < 0) {
            throw IllegalArgumentException("$paramName must be non-negative")
        }
    }
} 