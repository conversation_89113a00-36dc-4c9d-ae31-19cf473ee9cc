package com.yu.questicle.core.domain.usecase

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.common.exception.ErrorSeverity

/**
 * Base interface for all use cases
 * 
 * This interface establishes the foundation for Clean Architecture's Application Layer.
 * All business logic should be implemented through UseCases that:
 * - Are independent of frameworks
 * - Are testable
 * - Are reusable
 * - Have single responsibility
 */
interface UseCase<Input, Output> {
    /**
     * Execute the use case with the given input
     * 
     * @param input The input parameters for the use case
     * @return Result containing the output or error
     */
    suspend fun execute(input: Input): Result<Output>
}

/**
 * Use case with no input parameters
 */
interface NoParamUseCase<Output> {
    /**
     * Execute the use case without parameters
     * 
     * @return Result containing the output or error
     */
    suspend fun execute(): Result<Output>
}

/**
 * Use case with no output (side effect only)
 */
interface NoOutputUseCase<Input> {
    /**
     * Execute the use case with the given input
     * 
     * @param input The input parameters for the use case
     * @return Result indicating success or failure
     */
    suspend fun execute(input: Input): Result<Unit>
}

/**
 * Use case validation utilities
 */
object UseCaseValidation {
    
    /**
     * Validate that a string is not blank
     */
    fun validateNotBlank(value: String?, fieldName: String): Result<Unit> {
        return if (value.isNullOrBlank()) {
            Result.Error(
                BusinessException(
                    message = "Field '$fieldName' cannot be blank",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        } else {
            Result.Success(Unit)
        }
    }
    
    /**
     * Validate that a value is not null
     */
    fun <T> validateNotNull(value: T?, fieldName: String): Result<Unit> {
        return if (value == null) {
            Result.Error(
                BusinessException(
                    message = "Field '$fieldName' cannot be null",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        } else {
            Result.Success(Unit)
        }
    }
    
    /**
     * Validate numeric range
     */
    fun validateRange(value: Long, min: Long, max: Long, fieldName: String): Result<Unit> {
        return if (value < min || value > max) {
            Result.Error(
                BusinessException(
                    message = "Field '$fieldName' must be between $min and $max, but was $value",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        } else {
            Result.Success(Unit)
        }
    }
    
    /**
     * Validate that a collection is not empty
     */
    fun <T> validateNotEmpty(collection: Collection<T>?, fieldName: String): Result<Unit> {
        return if (collection.isNullOrEmpty()) {
            Result.Error(
                BusinessException(
                    message = "Field '$fieldName' cannot be empty",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        } else {
            Result.Success(Unit)
        }
    }
    
    /**
     * Validate email format
     */
    fun validateEmail(email: String?, fieldName: String = "email"): Result<Unit> {
        if (email.isNullOrBlank()) {
            return Result.Error(
                BusinessException(
                    message = "Email cannot be blank",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        }
        
        val emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$".toRegex()
        return if (email.matches(emailPattern)) {
            Result.Success(Unit)
        } else {
            Result.Error(
                BusinessException(
                    message = "Invalid email format: $email",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        }
    }
    
    /**
     * Validate user ID format
     */
    fun validateUserId(userId: String?, fieldName: String = "userId"): Result<Unit> {
        return validateNotBlank(userId, fieldName)
    }

    /**
     * Validate that a number is non-negative
     */
    fun validateNonNegative(value: Long, fieldName: String): Result<Unit> {
        return if (value < 0) {
            Result.Error(
                BusinessException(
                    message = "Field '$fieldName' must be non-negative, but was $value",
                    errorCode = "VALIDATION_ERROR"
                )
            )
        } else {
            Result.Success(Unit)
        }
    }
    
    /**
     * Combine multiple validation results
     */
    fun combineValidations(vararg validations: Result<Unit>): Result<Unit> {
        val errors = validations.filterIsInstance<Result.Error>()
        return if (errors.isEmpty()) {
            Result.Success(Unit)
        } else {
            // Return the first error
            errors.first()
        }
    }
}

/**
 * Use case exception
 */
class UseCaseException(
    message: String,
    errorCode: String = "USE_CASE_ERROR",
    cause: Throwable? = null
) : BusinessException(
    message = message,
    errorCode = errorCode,
    severity = ErrorSeverity.MEDIUM,
    cause = cause
) {
    override fun getDefaultUserMessage(): String {
        return "操作执行失败，请稍后重试"
    }
}

/**
 * Use case input validation exception
 */
class UseCaseValidationException(
    message: String,
    val field: String,
    val value: Any?,
    errorCode: String = "USE_CASE_VALIDATION_ERROR",
    cause: Throwable? = null
) : BusinessException(
    message = message,
    errorCode = errorCode,
    severity = ErrorSeverity.LOW,
    cause = cause
) {
    override fun getDefaultUserMessage(): String {
        return "输入参数 '$field' 不正确，请检查后重试"
    }
}

/**
 * Abstract base use case with common functionality
 */
abstract class BaseUseCase<Input, Output> : UseCase<Input, Output> {
    
    /**
     * Template method that handles common concerns
     */
    final override suspend fun execute(input: Input): Result<Output> {
        return try {
            // Validate input
            val validationResult = validateInput(input)
            if (validationResult is Result.Error) {
                return validationResult
            }
            
            // Execute the use case
            doExecute(input)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * Validate the input parameters
     */
    protected open suspend fun validateInput(input: Input): Result<Unit> {
        return Result.Success(Unit)
    }
    
    /**
     * Execute the actual use case logic
     */
    protected abstract suspend fun doExecute(input: Input): Result<Output>
}

/**
 * Abstract base use case for no-parameter use cases
 */
abstract class BaseNoParamUseCase<Output> : NoParamUseCase<Output> {
    
    final override suspend fun execute(): Result<Output> {
        return try {
            doExecute()
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    protected abstract suspend fun doExecute(): Result<Output>
}

/**
 * Abstract base use case for no-output use cases
 */
abstract class BaseNoOutputUseCase<Input> : NoOutputUseCase<Input> {
    
    final override suspend fun execute(input: Input): Result<Unit> {
        return try {
            val validationResult = validateInput(input)
            if (validationResult is Result.Error) {
                return validationResult
            }
            
            doExecute(input)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    protected open suspend fun validateInput(input: Input): Result<Unit> {
        return Result.Success(Unit)
    }
    
    protected abstract suspend fun doExecute(input: Input): Result<Unit>
}
