package com.yu.questicle.core.domain.di

import com.yu.questicle.core.domain.service.AchievementService
import com.yu.questicle.core.domain.service.AchievementServiceImpl
import com.yu.questicle.core.domain.service.MembershipService
import com.yu.questicle.core.domain.service.MembershipServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for domain layer services
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class DomainModule {
    
    @Binds
    @Singleton
    abstract fun bindAchievementService(
        achievementServiceImpl: AchievementServiceImpl
    ): AchievementService
    
    @Binds
    @Singleton
    abstract fun bindMembershipService(
        membershipServiceImpl: MembershipServiceImpl
    ): MembershipService
}
