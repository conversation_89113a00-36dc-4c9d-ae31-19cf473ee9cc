package com.yu.questicle.core.domain.service

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.usecase.user.AddExperienceUseCase
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 游戏会话管理器
 * 
 * Simplified version focusing on core UseCase architecture
 */
@Singleton
class GameSessionManager @Inject constructor(
    private val gameRepository: GameRepository,
    private val userRepository: UserRepository,
    private val membershipService: MembershipService,
    private val addExperienceUseCase: AddExperienceUseCase
) {
    
    /**
     * 更新用户统计信息
     */
    suspend fun updateUserStats(playerId: String, experienceGained: Long): Result<Unit> {
        return try {
            val userResult = userRepository.getUserById(playerId)
            if (userResult is Result.Success) {
                val user = userResult.data
                val updatedStats = user.stats.copy(
                    totalGames = user.stats.totalGames + 1,
                    totalPlayTime = user.stats.totalPlayTime + (System.currentTimeMillis() / 1000 - user.lastLoginAt)
                )
                
                userRepository.updateStats(playerId, updatedStats)
                
                // Use the new UseCase for adding experience
                val addExperienceResult = addExperienceUseCase.execute(
                    AddExperienceUseCase.Input(
                        userId = playerId,
                        experience = experienceGained,
                        source = "game_completion"
                    )
                )
                
                when (addExperienceResult) {
                    is Result.Success -> Result.Success(Unit)
                    is Result.Error -> Result.Error(addExperienceResult.exception)
                    is Result.Loading -> Result.Error(Exception("Experience addition in progress").toQuesticleException())
                }
            } else {
                when (userResult) {
                    is Result.Error -> Result.Error(userResult.exception)
                    else -> Result.Error(Exception("Unknown error").toQuesticleException())
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 计算游戏经验值
     */
    fun calculateExperience(score: Int, gameType: GameType): Long {
        return when (gameType) {
            GameType.TETRIS -> (score / 100).toLong()
            GameType.PUZZLE -> (score / 50).toLong()
            GameType.CARD -> (score / 200).toLong()
            GameType.STRATEGY -> (score / 150).toLong()
            GameType.ACTION -> (score / 80).toLong()
        }
    }
}




