package com.yu.questicle.core.domain.model.tetris

import com.yu.questicle.core.domain.model.GameAction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.Direction
import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.UUID

/**
 * Tetris game state
 */
@Serializable
data class TetrisGameState(
    val id: String = UUID.randomUUID().toString(),
    val board: TetrisBoard,
    val currentPiece: TetrisPiece?,
    val nextPiece: TetrisPiece?,
    val holdPiece: TetrisPiece? = null,
    val score: Int = 0,
    val level: Int = 1,
    val lines: Int = 0,
    val status: TetrisStatus = TetrisStatus.READY,
    val dropInterval: Long = 1000L, // milliseconds
    val lastDropTime: Long = 0L,
    val canHold: Boolean = true,
    val combo: Int = 0,
    val statistics: GameStatistics = GameStatistics()
) {
    companion object {
        /**
         * 创建初始游戏状态
         * 注意：这是一个空状态，需要通过TetrisEngine.initializeGame()来生成方块
         */
        fun initial(): TetrisGameState {
            return TetrisGameState(
                board = TetrisBoard.empty(),
                currentPiece = null,  // 初始状态为null，等待引擎初始化
                nextPiece = null      // 初始状态为null，等待引擎初始化
            )
        }

        /**
         * 创建已初始化的游戏状态（用于测试）
         */
        fun initialized(
            currentPiece: TetrisPiece,
            nextPiece: TetrisPiece
        ): TetrisGameState {
            return TetrisGameState(
                board = TetrisBoard.empty(),
                currentPiece = currentPiece,
                nextPiece = nextPiece,
                status = TetrisStatus.READY
            )
        }
    }
}

/**
 * Tetris game status
 * 专门用于Tetris游戏内部状态管理，与通用GameStatus有映射关系
 */
@Serializable
enum class TetrisStatus {
    READY,
    PLAYING,
    PAUSED,
    GAME_OVER,
    COMPLETED;

    /**
     * 转换为通用游戏状态
     * 用于与其他游戏类型保持一致的状态表示
     */
    fun toGameStatus(): com.yu.questicle.core.domain.model.GameStatus {
        return when (this) {
            READY -> com.yu.questicle.core.domain.model.GameStatus.READY
            PLAYING -> com.yu.questicle.core.domain.model.GameStatus.PLAYING
            PAUSED -> com.yu.questicle.core.domain.model.GameStatus.PAUSED
            GAME_OVER -> com.yu.questicle.core.domain.model.GameStatus.FAILED
            COMPLETED -> com.yu.questicle.core.domain.model.GameStatus.COMPLETED
        }
    }

    companion object {
        /**
         * 从通用游戏状态转换为Tetris状态
         */
        fun fromGameStatus(gameStatus: com.yu.questicle.core.domain.model.GameStatus): TetrisStatus {
            return when (gameStatus) {
                com.yu.questicle.core.domain.model.GameStatus.READY -> READY
                com.yu.questicle.core.domain.model.GameStatus.PLAYING -> PLAYING
                com.yu.questicle.core.domain.model.GameStatus.PAUSED -> PAUSED
                com.yu.questicle.core.domain.model.GameStatus.FAILED -> GAME_OVER
                com.yu.questicle.core.domain.model.GameStatus.COMPLETED -> COMPLETED
                com.yu.questicle.core.domain.model.GameStatus.ABANDONED -> GAME_OVER
            }
        }
    }
}

/**
 * Tetris board representation
 */
@Serializable
data class TetrisBoard(
    val width: Int = STANDARD_WIDTH,
    val height: Int = STANDARD_HEIGHT,
    val cells: List<List<TetrisCellType>> = List(height) { List(width) { TetrisCellType.EMPTY } }
) {
    companion object {
        const val STANDARD_WIDTH = 10
        const val STANDARD_HEIGHT = 20
        
        fun empty(): TetrisBoard = TetrisBoard()
    }
    
    fun isValidPosition(piece: TetrisPiece): Boolean {
        return piece.getOccupiedPositions().all { (x, y) ->
            x in 0 until width && 
            y in 0 until height && 
            cells[y][x] == TetrisCellType.EMPTY
        }
    }
    
    fun placePiece(piece: TetrisPiece): TetrisBoard {
        val newCells = cells.map { it.toMutableList() }.toMutableList()
        piece.getOccupiedPositions().forEach { (x, y) ->
            if (x in 0 until width && y in 0 until height) {
                newCells[y][x] = piece.type.toCellType()
            }
        }
        return copy(cells = newCells)
    }
    
    fun clearLines(): Pair<TetrisBoard, Int> {
        val fullLines = cells.indices.filter { y ->
            cells[y].all { it != TetrisCellType.EMPTY }
        }
        
        if (fullLines.isEmpty()) {
            return this to 0
        }
        
        val newCells = cells.toMutableList()
        fullLines.reversed().forEach { lineIndex ->
            newCells.removeAt(lineIndex)
            newCells.add(0, List(width) { TetrisCellType.EMPTY })
        }
        
        return copy(cells = newCells) to fullLines.size
    }
}

/**
 * Tetris piece types
 */
@Serializable
enum class TetrisPieceType(val displayName: String) {
    I("I型"),
    O("O型"),
    T("T型"),
    S("S型"),
    Z("Z型"),
    J("J型"),
    L("L型");
    
    fun toCellType(): TetrisCellType = when (this) {
        I -> TetrisCellType.I_PIECE
        O -> TetrisCellType.O_PIECE
        T -> TetrisCellType.T_PIECE
        S -> TetrisCellType.S_PIECE
        Z -> TetrisCellType.Z_PIECE
        J -> TetrisCellType.J_PIECE
        L -> TetrisCellType.L_PIECE
    }
    
    fun getShape(): List<List<Boolean>> = when (this) {
        I -> listOf(
            listOf(true, true, true, true)
        )
        O -> listOf(
            listOf(true, true),
            listOf(true, true)
        )
        T -> listOf(
            listOf(false, true, false),
            listOf(true, true, true)
        )
        S -> listOf(
            listOf(false, true, true),
            listOf(true, true, false)
        )
        Z -> listOf(
            listOf(true, true, false),
            listOf(false, true, true)
        )
        J -> listOf(
            listOf(true, false, false),
            listOf(true, true, true)
        )
        L -> listOf(
            listOf(false, false, true),
            listOf(true, true, true)
        )
    }
}

/**
 * Cell types for the board
 */
@Serializable
enum class TetrisCellType {
    EMPTY,
    I_PIECE,
    O_PIECE,
    T_PIECE,
    S_PIECE,
    Z_PIECE,
    J_PIECE,
    L_PIECE,
    GHOST // For ghost piece preview
}

/**
 * Tetris piece with position and rotation
 */
@Serializable
data class TetrisPiece(
    val type: TetrisPieceType,
    val x: Int,
    val y: Int,
    val rotation: Int = 0
) {
    fun getOccupiedPositions(): List<Pair<Int, Int>> {
        val shape = getRotatedShape()
        val positions = mutableListOf<Pair<Int, Int>>()
        
        shape.forEachIndexed { rowIndex, row ->
            row.forEachIndexed { colIndex, cell ->
                if (cell) {
                    positions.add(x + colIndex to y + rowIndex)
                }
            }
        }
        
        return positions
    }
    
    private fun getRotatedShape(): List<List<Boolean>> {
        var shape = type.getShape()
        repeat(rotation % 4) {
            shape = rotateClockwise(shape)
        }
        return shape
    }
    
    private fun rotateClockwise(matrix: List<List<Boolean>>): List<List<Boolean>> {
        val rows = matrix.size
        val cols = matrix[0].size
        return List(cols) { col ->
            List(rows) { row ->
                matrix[rows - 1 - row][col]
            }
        }
    }
    
    fun move(dx: Int, dy: Int): TetrisPiece = copy(x = x + dx, y = y + dy)
    fun rotate(): TetrisPiece = copy(rotation = rotation + 1)
}



// TetrisStatistics 已移至 GameStatistics.kt
