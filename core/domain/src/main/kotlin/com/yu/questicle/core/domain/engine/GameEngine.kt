package com.yu.questicle.core.domain.engine

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameAction
import com.yu.questicle.core.domain.model.GameType
import kotlinx.coroutines.flow.Flow

/**
 * Base interface for all game engines
 */
interface GameEngine<TState : Any, TAction : GameAction> {
    
    /**
     * Game type this engine handles
     */
    val gameType: GameType
    
    /**
     * Initialize a new game
     */
    suspend fun initializeGame(playerId: String): Result<TState>
    
    /**
     * Start the game
     */
    suspend fun startGame(gameState: TState): Result<TState>
    
    /**
     * Process a game action
     */
    suspend fun processAction(action: TAction, currentState: TState): Result<TState>
    
    /**
     * Pause the game
     */
    suspend fun pauseGame(gameState: TState): Result<TState>
    
    /**
     * Resume the game
     */
    suspend fun resumeGame(gameState: TState): Result<TState>
    
    /**
     * End the game
     */
    suspend fun endGame(gameState: TState): Result<Game>
    
    /**
     * Get current game state
     */
    fun observeGameState(): Flow<TState>
    
    /**
     * Validate if an action is legal in the current state
     */
    fun isValidAction(action: TAction, currentState: TState): Boolean
    
    /**
     * Calculate score for the current state
     */
    fun calculateScore(gameState: TState): Int
    
    /**
     * Check if game is over
     */
    fun isGameOver(gameState: TState): Boolean
    
    /**
     * Get game statistics
     */
    fun getGameStatistics(gameState: TState): Map<String, Any>
    
    /**
     * Save game state
     */
    suspend fun saveGameState(gameState: TState): Result<Unit>
    
    /**
     * Load game state
     */
    suspend fun loadGameState(gameId: String): Result<TState>
}

/**
 * Game engine factory for creating appropriate engines
 */
interface GameEngineFactory {
    
    /**
     * Create a game engine for the specified game type
     */
    fun createEngine(gameType: GameType): GameEngine<*, *>?
    
    /**
     * Get all supported game types
     */
    fun getSupportedGameTypes(): List<GameType>
    
    /**
     * Check if a game type is supported
     */
    fun isSupported(gameType: GameType): Boolean
}

/**
 * Game engine registry for managing multiple engines
 */
interface GameEngineRegistry {
    
    /**
     * Register a game engine
     */
    fun registerEngine(gameType: GameType, engine: GameEngine<*, *>)
    
    /**
     * Get engine for game type
     */
    fun getEngine(gameType: GameType): GameEngine<*, *>?
    
    /**
     * Get all registered engines
     */
    fun getAllEngines(): Map<GameType, GameEngine<*, *>>
    
    /**
     * Unregister an engine
     */
    fun unregisterEngine(gameType: GameType)
}

/**
 * AI game engine interface for AI-powered games
 */
interface AIGameEngine<TState : Any, TAction : GameAction> : GameEngine<TState, TAction> {
    
    /**
     * Get AI suggestion for next move
     */
    suspend fun getAISuggestion(gameState: TState): Result<TAction>
    
    /**
     * Get AI analysis of current position
     */
    suspend fun getAIAnalysis(gameState: TState): Result<AIAnalysis>
    
    /**
     * Set AI difficulty level
     */
    suspend fun setAIDifficulty(level: AIDifficulty): Result<Unit>
    
    /**
     * Enable/disable AI assistance
     */
    suspend fun setAIAssistance(enabled: Boolean): Result<Unit>
}

/**
 * AI analysis result
 */
data class AIAnalysis(
    val score: Float,
    val bestMoves: List<GameAction>,
    val analysis: String,
    val confidence: Float
)

/**
 * AI difficulty levels
 */
enum class AIDifficulty(val level: Int) {
    BEGINNER(1),
    EASY(2),
    MEDIUM(3),
    HARD(4),
    EXPERT(5),
    MASTER(6)
}
