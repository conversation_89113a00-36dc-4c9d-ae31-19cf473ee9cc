package com.yu.questicle.core.domain.cache

import com.yu.questicle.core.domain.model.*
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Assertions.*
import kotlin.system.measureTimeMillis

/**
 * AchievementCache性能和功能测试
 */
@DisplayName("AchievementCache Tests")
class AchievementCacheTest {
    
    private lateinit var cache: AchievementCache
    
    private val testAchievements = listOf(
        Achievement(
            id = "test1",
            title = "Test Achievement 1",
            description = "Test description 1",
            category = AchievementCategory.SKILL,
            type = AchievementType.SINGLE,
            requirement = AchievementRequirement.ScoreRequirement(targetScore = 1000L),
            reward = AchievementReward(experience = 100L),
            sortOrder = 1
        ),
        Achievement(
            id = "test2",
            title = "Test Achievement 2",
            description = "Test description 2",
            category = AchievementCategory.PROGRESSION,
            type = AchievementType.INCREMENTAL,
            requirement = AchievementRequirement.GamesPlayedRequirement(targetGames = 10),
            reward = AchievementReward(experience = 50L),
            sortOrder = 2
        )
    )
    
    private val testUserAchievements = listOf(
        UserAchievement(
            achievementId = "test1",
            userId = "user1",
            isUnlocked = true,
            unlockedAt = System.currentTimeMillis() / 1000,
            progress = 1.0f
        ),
        UserAchievement(
            achievementId = "test2",
            userId = "user1",
            isUnlocked = false,
            unlockedAt = null,
            progress = 0.5f
        )
    )
    
    @BeforeEach
    fun setUp() {
        cache = AchievementCache()
    }
    
    @Nested
    @DisplayName("基本缓存功能")
    inner class BasicCachingTests {
        
        @Test
        @DisplayName("应该能够缓存和获取所有成就")
        fun `should cache and retrieve all achievements`() = runTest {
            // When
            cache.cacheAllAchievements(testAchievements)
            val cached = cache.getAllAchievements()
            
            // Then
            assertNotNull(cached)
            assertEquals(2, cached!!.size)
            assertEquals("test1", cached[0].id)
            assertEquals("test2", cached[1].id)
        }
        
        @Test
        @DisplayName("应该能够缓存和获取用户成就")
        fun `should cache and retrieve user achievements`() = runTest {
            // When
            cache.cacheUserAchievements("user1", testUserAchievements)
            val cached = cache.getUserAchievements("user1")
            
            // Then
            assertNotNull(cached)
            assertEquals(2, cached!!.size)
            assertEquals("test1", cached[0].achievementId)
            assertEquals(1.0f, cached[0].progress)
        }
        
        @Test
        @DisplayName("未缓存的数据应该返回null")
        fun `uncached data should return null`() = runTest {
            // When
            val achievements = cache.getAllAchievements()
            val userAchievements = cache.getUserAchievements("nonexistent")
            
            // Then
            assertNull(achievements)
            assertNull(userAchievements)
        }
    }
    
    @Nested
    @DisplayName("缓存失效")
    inner class CacheInvalidationTests {
        
        @Test
        @DisplayName("应该能够使用户缓存失效")
        fun `should invalidate user cache`() = runTest {
            // Given
            cache.cacheUserAchievements("user1", testUserAchievements)
            
            // When
            cache.invalidateUserAchievements("user1")
            val cached = cache.getUserAchievements("user1")
            
            // Then
            assertNull(cached)
        }
        
        @Test
        @DisplayName("应该能够使所有缓存失效")
        fun `should invalidate all cache`() = runTest {
            // Given
            cache.cacheAllAchievements(testAchievements)
            cache.cacheUserAchievements("user1", testUserAchievements)
            
            // When
            cache.invalidateAll()
            
            // Then
            assertNull(cache.getAllAchievements())
            assertNull(cache.getUserAchievements("user1"))
        }
    }
    
    @Nested
    @DisplayName("缓存统计")
    inner class CacheStatsTests {
        
        @Test
        @DisplayName("应该提供正确的缓存统计")
        fun `should provide correct cache stats`() = runTest {
            // Given
            cache.cacheAllAchievements(testAchievements)
            cache.cacheUserAchievements("user1", testUserAchievements)
            cache.cacheUserAchievements("user2", emptyList())
            
            // When
            val stats = cache.getCacheStats()
            
            // Then
            assertTrue(stats.allAchievementsCached)
            assertEquals(2, stats.userAchievementsCacheSize)
            assertEquals(0, stats.achievementStatsCacheSize)
            assertTrue(stats.totalMemoryUsage > 0)
        }
    }
    
    @Nested
    @DisplayName("性能测试")
    inner class PerformanceTests {
        
        @Test
        @DisplayName("大量数据缓存性能测试")
        fun `large data caching performance test`() = runTest {
            // Given
            val largeAchievementList = (1..1000).map { index ->
                Achievement(
                    id = "achievement_$index",
                    title = "Achievement $index",
                    description = "Description $index",
                    category = AchievementCategory.SKILL,
                    type = AchievementType.SINGLE,
                    requirement = AchievementRequirement.ScoreRequirement(targetScore = index * 100L),
                    reward = AchievementReward(experience = index * 10L),
                    sortOrder = index
                )
            }
            
            // When
            val cacheTime = measureTimeMillis {
                cache.cacheAllAchievements(largeAchievementList)
            }
            
            val retrieveTime = measureTimeMillis {
                cache.getAllAchievements()
            }
            
            // Then
            assertTrue(cacheTime < 100) // 缓存应该在100ms内完成
            assertTrue(retrieveTime < 10) // 检索应该在10ms内完成
        }
        
        @Test
        @DisplayName("用户成就缓存性能测试")
        fun `user achievements caching performance test`() = runTest {
            // Given
            val largeUserAchievementList = (1..1000).map { index ->
                UserAchievement(
                    achievementId = "achievement_$index",
                    userId = "user1",
                    isUnlocked = index % 2 == 0,
                    progress = (index % 100) / 100.0f,
                    currentValue = index.toLong()
                )
            }
            
            // When
            val cacheTime = measureTimeMillis {
                cache.cacheUserAchievements("user1", largeUserAchievementList)
            }
            
            val retrieveTime = measureTimeMillis {
                cache.getUserAchievements("user1")
            }
            
            // Then
            assertTrue(cacheTime < 50) // 缓存应该在50ms内完成
            assertTrue(retrieveTime < 5) // 检索应该在5ms内完成
        }
    }
    
    @Nested
    @DisplayName("缓存清理")
    inner class CacheCleanupTests {
        
        @Test
        @DisplayName("应该能够清理过期的缓存条目")
        fun `should clean up expired cache entries`() = runTest {
            // Given
            cache.cacheAllAchievements(testAchievements)
            cache.cacheUserAchievements("user1", testUserAchievements)
            cache.cacheUserAchievements("user2", emptyList())
            
            // When
            cache.cleanupExpiredCache()
            
            // Then - 由于没有设置过期时间，所有缓存应该仍然存在
            assertNotNull(cache.getAllAchievements())
            assertNotNull(cache.getUserAchievements("user1"))
            assertNotNull(cache.getUserAchievements("user2"))
        }
        
        @Test
        @DisplayName("应该能够清理内存使用")
        fun `should cleanup memory usage`() = runTest {
            // Given
            val largeData = (1..500).map { index ->
                Achievement(
                    id = "achievement_$index",
                    title = "Achievement $index",
                    description = "Description $index",
                    category = AchievementCategory.SKILL,
                    type = AchievementType.SINGLE,
                    requirement = AchievementRequirement.ScoreRequirement(targetScore = index * 100L),
                    reward = AchievementReward(experience = index * 10L),
                    sortOrder = index
                )
            }
            
            // When
            cache.cacheAllAchievements(largeData)
            val beforeCleanup = cache.getCacheStats().totalMemoryUsage
            
            cache.cleanupExpiredCache()
            val afterCleanup = cache.getCacheStats().totalMemoryUsage
            
            // Then - 内存使用应该减少或保持不变
            assertTrue(afterCleanup <= beforeCleanup)
        }
    }
    
    @Nested
    @DisplayName("并发测试")
    inner class ConcurrencyTests {
        
        @Test
        @DisplayName("并发读写应该是线程安全的")
        fun `concurrent read write should be thread safe`() = runTest {
            // Given
            val achievements = testAchievements
            
            // When - 模拟并发访问
            cache.cacheAllAchievements(achievements)
            
            // 并发读取
            val results = (1..10).map {
                cache.getAllAchievements()
            }
            
            // Then
            results.forEach { result ->
                assertNotNull(result)
                assertEquals(2, result!!.size)
            }
        }
    }
}
