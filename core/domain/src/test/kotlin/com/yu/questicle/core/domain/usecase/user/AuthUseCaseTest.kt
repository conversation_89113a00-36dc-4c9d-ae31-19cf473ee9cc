package com.yu.questicle.core.domain.usecase.user

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.UserStats
import com.yu.questicle.core.domain.repository.UserRepository
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import com.yu.questicle.core.domain.security.PasswordManager

/**
 * AuthUseCase 测试类
 * 测试用户认证用例的所有功能
 */
@DisplayName("AuthUseCase 测试")
class AuthUseCaseTest {

    private lateinit var userRepository: UserRepository
    private lateinit var authUseCase: AuthUseCase

    @BeforeEach
    fun setUp() {
        userRepository = mockk()
        authUseCase = AuthUseCase(userRepository)
    }

    @Nested
    @DisplayName("游客登录测试")
    inner class GuestLoginTests {

        @Test
        @DisplayName("应该能够成功创建游客用户")
        fun shouldCreateGuestUserSuccessfully() = runTest {
            // Given
            val guestUser = User.createGuest()
            coEvery { userRepository.createGuestUser() } returns Result.Success(guestUser)

            // When
            val result = authUseCase.loginAsGuest()

            // Then
            assertTrue(result is Result.Success)
            val user = (result as Result.Success).data
            assertTrue(user.username.startsWith("Guest_"))
            assertEquals("游客用户", user.displayName)
            coVerify { userRepository.createGuestUser() }
        }

        @Test
        @DisplayName("游客用户创建失败时应该返回错误")
        fun shouldReturnErrorWhenGuestCreationFails() = runTest {
            // Given
            val exception = BusinessException("创建游客用户失败")
            coEvery { userRepository.createGuestUser() } returns Result.Error(exception)

            // When
            val result = authUseCase.loginAsGuest()

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertTrue(error.exception is BusinessException)
        }
    }

    @Nested
    @DisplayName("用户注册测试")
    inner class UserRegistrationTests {

        @Test
        @DisplayName("应该能够成功注册新用户")
        fun shouldRegisterNewUserSuccessfully() = runTest {
            // Given
            val username = "testuser"
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"
            
            val newUser = User(
                username = username,
                email = email,
                passwordHash = "hashed_password",
                displayName = username
            )
            
            coEvery { userRepository.createUser(any()) } returns Result.Success(newUser)

            // When
            val result = authUseCase.registerUser(username, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Success)
            val user = (result as Result.Success).data
            assertEquals(username, user.username)
            assertEquals(email, user.email)
            assertEquals(username, user.displayName)
            coVerify { userRepository.createUser(any()) }
        }

        @Test
        @DisplayName("用户名验证失败时应该返回错误")
        fun shouldReturnErrorWhenUsernameValidationFails() = runTest {
            // Given
            val invalidUsername = "ab" // 太短
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"

            // When
            val result = authUseCase.registerUser(invalidUsername, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertNotNull(error.exception.message)
        }

        @Test
        @DisplayName("密码不匹配时应该返回错误")
        fun shouldReturnErrorWhenPasswordsDoNotMatch() = runTest {
            // Given
            val username = "testuser"
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "different_password"

            // When
            val result = authUseCase.registerUser(username, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertNotNull(error.exception.message)
        }

        @Test
        @DisplayName("邮箱格式无效时应该返回错误")
        fun shouldReturnErrorWhenEmailFormatIsInvalid() = runTest {
            // Given
            val username = "testuser"
            val invalidEmail = "invalid_email"
            val password = "password123"
            val passwordConfirmation = "password123"

            // When
            val result = authUseCase.registerUser(username, invalidEmail, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertNotNull(error.exception.message)
        }

        @Test
        @DisplayName("用户创建失败时应该返回错误")
        fun shouldReturnErrorWhenUserCreationFails() = runTest {
            // Given
            val username = "testuser"
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"
            
            val exception = BusinessException("用户名已存在")
            coEvery { userRepository.createUser(any()) } returns Result.Error(exception)

            // When
            val result = authUseCase.registerUser(username, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertTrue(error.exception is BusinessException)
        }
    }

    @Nested
    @DisplayName("用户名登录测试")
    inner class UsernameLoginTests {

        @Test
        @DisplayName("应该能够使用用户名成功登录")
        fun shouldLoginWithUsernameSuccessfully() = runTest {
            // Given
            val username = "testuser"
            val password = "password123"
            val hashedPassword = PasswordManager.hashPassword(password) // Use actual hash
            val user = User(
                username = username,
                passwordHash = hashedPassword,
                displayName = username
            )
            
            coEvery { userRepository.searchUsers(username) } returns Result.Success(listOf(user))
            coEvery { userRepository.saveUser(any()) } returns Result.Success(Unit)

            // When
            val result = authUseCase.loginWithUsername(username, password)

            // Then
            assertTrue(result is Result.Success)
            val loggedInUser = (result as Result.Success).data
            assertEquals(username, loggedInUser.username)
            coVerify { userRepository.searchUsers(username) }
        }

        @Test
        @DisplayName("用户名不存在时应该返回错误")
        fun shouldReturnErrorWhenUsernameNotFound() = runTest {
            // Given
            val username = "nonexistent"
            val password = "password123"
            
            coEvery { userRepository.searchUsers(username) } returns Result.Success(emptyList())

            // When
            val result = authUseCase.loginWithUsername(username, password)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertTrue(error.exception.message?.contains("用户名或密码错误") == true)
        }

        @Test
        @DisplayName("密码错误时应该返回错误")
        fun shouldReturnErrorWhenPasswordIsIncorrect() = runTest {
            // Given
            val username = "testuser"
            val wrongPassword = "wrongpassword"
            val user = User(
                username = username,
                passwordHash = "correct_hashed_password",
                displayName = username
            )
            
            coEvery { userRepository.searchUsers(username) } returns Result.Success(listOf(user))

            // When
            val result = authUseCase.loginWithUsername(username, wrongPassword)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertNotNull(error.exception.message)
        }
    }

    @Nested
    @DisplayName("邮箱登录测试")
    inner class EmailLoginTests {

        @Test
        @DisplayName("应该能够使用邮箱成功登录")
        fun shouldLoginWithEmailSuccessfully() = runTest {
            // Given
            val email = "<EMAIL>"
            val password = "password123"
            val hashedPassword = PasswordManager.hashPassword(password) // Use actual hash
            val user = User(
                username = "testuser",
                email = email,
                passwordHash = hashedPassword,
                displayName = "testuser"
            )
            
            coEvery { userRepository.searchUsers(email) } returns Result.Success(listOf(user))

            // When
            val result = authUseCase.loginWithEmail(email, password)

            // Then
            assertTrue(result is Result.Success)
            val loggedInUser = (result as Result.Success).data
            assertEquals(email, loggedInUser.email)
            coVerify { userRepository.searchUsers(email) }
        }

        @Test
        @DisplayName("邮箱不存在时应该返回错误")
        fun shouldReturnErrorWhenEmailNotFound() = runTest {
            // Given
            val email = "<EMAIL>"
            val password = "password123"
            
            coEvery { userRepository.searchUsers(email) } returns Result.Success(emptyList())

            // When
            val result = authUseCase.loginWithEmail(email, password)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertTrue(error.exception.message?.contains("邮箱或密码错误") == true)
        }
    }

    @Nested
    @DisplayName("游客升级测试")
    inner class GuestUpgradeTests {

        @Test
        @DisplayName("应该能够将游客升级为正式用户")
        fun shouldUpgradeGuestToRegularUser() = runTest {
            // Given
            val guestUser = User.createGuest()
            val username = "newuser"
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"
            
            every { userRepository.getCurrentUser() } returns flowOf(guestUser)
            coEvery { userRepository.saveUser(any()) } returns Result.Success(Unit)

            // When
            val result = authUseCase.upgradeFromGuest(username, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Success)
            val user = (result as Result.Success).data
            assertEquals(username, user.username)
            assertEquals(email, user.email)
            coVerify { userRepository.saveUser(any()) }
        }

        @Test
        @DisplayName("非游客用户尝试升级时应该返回错误")
        fun shouldReturnErrorWhenNonGuestTriesToUpgrade() = runTest {
            // Given
            val regularUser = User(
                username = "regularuser",
                displayName = "Regular User"
            )
            val username = "newuser"
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"
            
            every { userRepository.getCurrentUser() } returns flowOf(regularUser)

            // When
            val result = authUseCase.upgradeFromGuest(username, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertTrue(error.exception.message?.contains("当前用户不是游客") == true)
        }

        @Test
        @DisplayName("升级验证失败时应该返回错误")
        fun shouldReturnErrorWhenUpgradeValidationFails() = runTest {
            // Given
            val guestUser = User.createGuest()
            val invalidUsername = "ab" // 太短
            val email = "<EMAIL>"
            val password = "password123"
            val passwordConfirmation = "password123"
            
            every { userRepository.getCurrentUser() } returns flowOf(guestUser)

            // When
            val result = authUseCase.upgradeFromGuest(invalidUsername, email, password, passwordConfirmation)

            // Then
            assertTrue(result is Result.Error)
            val error = result as Result.Error
            assertNotNull(error.exception.message)
        }
    }

    @Nested
    @DisplayName("密码重置测试")
    inner class PasswordResetTests {

        @Test
        @DisplayName("应该能够发送密码重置邮件")
        fun shouldSendPasswordResetEmail() = runTest {
            // Given
            val email = "<EMAIL>"
            val user = User(
                username = "testuser",
                email = email,
                displayName = "testuser"
            )
            
            coEvery { userRepository.searchUsers(email) } returns Result.Success(listOf(user))
            coEvery { userRepository.saveUser(any()) } returns Result.Success(Unit)

            // When
            val result = authUseCase.resetPassword(email)

            // Then
            assertTrue(result is Result.Success)
            coVerify { userRepository.searchUsers(email) }
        }

        @Test
        @DisplayName("邮箱不存在时密码重置应该返回错误")
        fun shouldReturnErrorWhenEmailNotFoundForReset() = runTest {
            // Given
            val email = "<EMAIL>"
            
            coEvery { userRepository.searchUsers(email) } returns Result.Success(emptyList())

            // When
            val result = authUseCase.resetPassword(email)

            // Then
            // 为了安全，即使用户不存在也返回成功
            assertTrue(result is Result.Success)
        }
    }
}
