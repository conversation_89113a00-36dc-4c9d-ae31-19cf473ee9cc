package com.yu.questicle.core.domain.usecase.user

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.isSuccess
import com.yu.questicle.core.common.result.isError
import com.yu.questicle.core.common.result.getOrNull
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.service.MembershipService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * AddExperienceUseCase 单元测试
 */
@DisplayName("添加经验用例测试")
class AddExperienceUseCaseTest {

    private lateinit var userRepository: UserRepository
    private lateinit var membershipService: MembershipService
    private lateinit var addExperienceUseCase: AddExperienceUseCase

    private val testUserId = "test-user-001"
    private val baseTestUser = User(
        id = testUserId,
        username = "testuser",
        email = "<EMAIL>",
        level = 1,
        experience = 0L
    )

    @BeforeEach
    fun setup() {
        userRepository = mockk()
        membershipService = mockk()
        addExperienceUseCase = AddExperienceUseCase(userRepository, membershipService)
    }

    @Test
    @DisplayName("正常经验增加 - 无升级情况")
    fun `should add experience without level up`() = runTest {
        // Given
        val user = baseTestUser.copy(level = 2, experience = 150L)
        val updatedUser = user.copy(experience = 200L)
        
        coEvery { userRepository.getUserById(testUserId) } returns Result.Success(user)
        coEvery { membershipService.calculateExperienceWithMultiplier(50L, user) } returns 50L
        coEvery { membershipService.calculateLevel(200L) } returns 2
        coEvery { userRepository.updateUser(any()) } returns Result.Success(updatedUser)

        // When
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input(testUserId, 50L))

        // Then
        assertTrue(result.isSuccess)
        val output = result.getOrNull()!!
        assertEquals(2, output.levelUpEvent.oldLevel)
        assertEquals(2, output.levelUpEvent.newLevel) // 未升级
        assertTrue(output.levelUpEvent.rewards.isEmpty())
    }

    @Test
    @DisplayName("单级升级 - 应正确处理升级逻辑")
    fun `should handle single level up correctly`() = runTest {
        // Given - 用户接近升级
        val user = baseTestUser.copy(level = 1, experience = 90L)
        val updatedUser = user.copy(level = 2, experience = 110L)
        val rewards = listOf(Reward(RewardType.COINS, 100L, "Level 2 coins reward"))
        
        coEvery { userRepository.getUserById(testUserId) } returns Result.Success(user)
        coEvery { membershipService.calculateExperienceWithMultiplier(20L, user) } returns 20L
        coEvery { membershipService.calculateLevel(110L) } returns 2
        coEvery { membershipService.getLevelRewards(2) } returns rewards
        coEvery { userRepository.updateUser(any()) } returns Result.Success(updatedUser)

        // When - 添加足够经验升级
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input(testUserId, 20L))

        // Then
        assertTrue(result.isSuccess)
        val output = result.getOrNull()!!
        assertEquals(1, output.levelUpEvent.oldLevel)
        assertEquals(2, output.levelUpEvent.newLevel)
        assertTrue(output.levelUpEvent.rewards.isNotEmpty())
    }

    @Test
    @DisplayName("用户不存在时应返回错误")
    fun `should return error when user not found`() = runTest {
        // Given
        coEvery { userRepository.getUserById(testUserId) } returns Result.Error(
            com.yu.questicle.core.common.exception.BusinessException("User not found")
        )

        // When
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input(testUserId, 100L))

        // Then
        assertTrue(result.isError)
    }

    @Test
    @DisplayName("负经验值应返回错误")
    fun `should return error for negative experience`() = runTest {
        // When
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input(testUserId, -100L))

        // Then
        assertTrue(result.isError)
    }

    @Test
    @DisplayName("零经验值应成功处理")
    fun `should handle zero experience successfully`() = runTest {
        // Given
        coEvery { userRepository.getUserById(testUserId) } returns Result.Success(baseTestUser)
        coEvery { membershipService.calculateExperienceWithMultiplier(0L, baseTestUser) } returns 0L
        coEvery { membershipService.calculateLevel(0L) } returns 1
        coEvery { userRepository.updateUser(any()) } returns Result.Success(baseTestUser)

        // When
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input(testUserId, 0L))

        // Then
        assertTrue(result.isSuccess)
        val output = result.getOrNull()!!
        assertEquals(1, output.levelUpEvent.oldLevel)
        assertEquals(1, output.levelUpEvent.newLevel)
    }

    @Test
    @DisplayName("空用户ID应返回错误")
    fun `should return error for empty user id`() = runTest {
        // When
        val result = addExperienceUseCase.execute(AddExperienceUseCase.Input("", 100L))

        // Then
        assertTrue(result.isError)
    }
} 