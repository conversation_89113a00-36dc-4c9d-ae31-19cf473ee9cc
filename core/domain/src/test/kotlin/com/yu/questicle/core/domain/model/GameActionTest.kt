package com.yu.questicle.core.domain.model

import com.yu.questicle.core.testing.factory.TestDataFactory
import com.yu.questicle.core.testing.util.*
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeInstanceOf
import org.junit.jupiter.api.*
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource

/**
 * GameAction和TetrisAction测试
 * 测试游戏动作的创建、属性和行为
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("GameAction 和 TetrisAction 测试")
class GameActionTest {

    @Nested
    @DisplayName("TetrisAction.Move 测试")
    inner class TetrisActionMoveTests {

        @ParameterizedTest
        @EnumSource(Direction::class)
        @DisplayName("应该正确创建移动动作")
        fun `should create move action correctly`(direction: Direction) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val moveAction = TetrisAction.Move(direction, playerId)

            // Then
            moveAction.apply {
                this.direction shouldBe direction
                this.playerId shouldBe playerId
                timestamp shouldNotBe 0L
            }
            moveAction.shouldBeInstanceOf<TetrisAction.Move>()
            moveAction.shouldBeInstanceOf<GameAction>()
        }

        @Test
        @DisplayName("应该为不同的移动动作生成不同的时间戳")
        fun `should generate different timestamps for different move actions`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val action1 = TetrisAction.Move(Direction.LEFT, playerId)
            val timestamp1 = action1.timestamp

            // 确保时间戳不同的更可靠方法
            var action2: TetrisAction.Move
            do {
                Thread.sleep(1)
                action2 = TetrisAction.Move(Direction.RIGHT, playerId)
            } while (action2.timestamp == timestamp1)

            // Then
            action1.timestamp shouldNotBe action2.timestamp
        }

        @Test
        @DisplayName("应该正确处理所有方向的移动")
        fun `should handle all direction movements correctly`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val leftMove = TetrisAction.Move(Direction.LEFT, playerId)
            val rightMove = TetrisAction.Move(Direction.RIGHT, playerId)
            val downMove = TetrisAction.Move(Direction.DOWN, playerId)

            // Then
            leftMove.direction shouldBe Direction.LEFT
            rightMove.direction shouldBe Direction.RIGHT
            downMove.direction shouldBe Direction.DOWN
        }
    }

    @Nested
    @DisplayName("TetrisAction.Rotate 测试")
    inner class TetrisActionRotateTests {

        @ParameterizedTest
        @ValueSource(booleans = [true, false])
        @DisplayName("应该正确创建旋转动作")
        fun `should create rotate action correctly`(clockwise: Boolean) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val rotateAction = TetrisAction.Rotate(clockwise, playerId)

            // Then
            rotateAction.apply {
                this.clockwise shouldBe clockwise
                this.playerId shouldBe playerId
                timestamp shouldNotBe 0L
            }
            rotateAction.shouldBeInstanceOf<TetrisAction.Rotate>()
            rotateAction.shouldBeInstanceOf<GameAction>()
        }

        @Test
        @DisplayName("应该默认为顺时针旋转")
        fun `should default to clockwise rotation`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val rotateAction = TetrisAction.Rotate(playerId = playerId)

            // Then
            rotateAction.clockwise shouldBe true
        }

        @Test
        @DisplayName("应该能够创建逆时针旋转")
        fun `should be able to create counter-clockwise rotation`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val rotateAction = TetrisAction.Rotate(clockwise = false, playerId = playerId)

            // Then
            rotateAction.clockwise shouldBe false
        }
    }

    @Nested
    @DisplayName("TetrisAction.Drop 测试")
    inner class TetrisActionDropTests {

        @ParameterizedTest
        @ValueSource(booleans = [true, false])
        @DisplayName("应该正确创建下降动作")
        fun `should create drop action correctly`(hard: Boolean) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val dropAction = TetrisAction.Drop(hard, playerId)

            // Then
            dropAction.apply {
                this.hard shouldBe hard
                this.playerId shouldBe playerId
                timestamp shouldNotBe 0L
            }
            dropAction.shouldBeInstanceOf<TetrisAction.Drop>()
            dropAction.shouldBeInstanceOf<GameAction>()
        }

        @Test
        @DisplayName("应该默认为软降")
        fun `should default to soft drop`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val dropAction = TetrisAction.Drop(playerId = playerId)

            // Then
            dropAction.hard shouldBe false
        }

        @Test
        @DisplayName("应该能够创建硬降动作")
        fun `should be able to create hard drop action`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val dropAction = TetrisAction.Drop(hard = true, playerId = playerId)

            // Then
            dropAction.hard shouldBe true
        }
    }

    @Nested
    @DisplayName("TetrisAction.Hold 测试")
    inner class TetrisActionHoldTests {

        @Test
        @DisplayName("应该正确创建保持动作")
        fun `should create hold action correctly`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val holdAction = TetrisAction.Hold(playerId)

            // Then
            holdAction.apply {
                this.playerId shouldBe playerId
                timestamp shouldNotBe 0L
            }
            holdAction.shouldBeInstanceOf<TetrisAction.Hold>()
            holdAction.shouldBeInstanceOf<GameAction>()
        }

        @Test
        @DisplayName("应该为不同的保持动作生成不同的时间戳")
        fun `should generate different timestamps for different hold actions`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val action1 = TetrisAction.Hold(playerId)
            val timestamp1 = action1.timestamp

            // 确保时间戳不同的更可靠方法
            var action2: TetrisAction.Hold
            do {
                Thread.sleep(1)
                action2 = TetrisAction.Hold(playerId)
            } while (action2.timestamp == timestamp1)

            // Then
            action1.timestamp shouldNotBe action2.timestamp
        }
    }

    @Nested
    @DisplayName("TetrisAction.Pause 测试")
    inner class TetrisActionPauseTests {

        @Test
        @DisplayName("应该正确创建暂停动作")
        fun `should create pause action correctly`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val pauseAction = TetrisAction.Pause(playerId)

            // Then
            pauseAction.apply {
                this.playerId shouldBe playerId
                timestamp shouldNotBe 0L
            }
            pauseAction.shouldBeInstanceOf<TetrisAction.Pause>()
            pauseAction.shouldBeInstanceOf<GameAction>()
        }
    }

    @Nested
    @DisplayName("Direction 枚举测试")
    inner class DirectionTests {

        @Test
        @DisplayName("应该包含所有必要的方向")
        fun `should contain all necessary directions`() {
            // When
            val directions = Direction.entries

            // Then
            directions.shouldContain(Direction.LEFT)
            directions.shouldContain(Direction.RIGHT)
            directions.shouldContain(Direction.DOWN)
        }

        @Test
        @DisplayName("应该有正确的方向数量")
        fun `should have correct number of directions`() {
            // When
            val directions = Direction.entries

            // Then
            directions.shouldHaveSize(3)
        }

        @ParameterizedTest
        @EnumSource(Direction::class)
        @DisplayName("应该能够在移动动作中使用所有方向")
        fun `should be able to use all directions in move actions`(direction: Direction) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val moveAction = TetrisAction.Move(direction, playerId)

            // Then
            moveAction.direction shouldBe direction
        }
    }

    @Nested
    @DisplayName("GameAction 接口测试")
    inner class GameActionInterfaceTests {

        @Test
        @DisplayName("所有TetrisAction应该实现GameAction接口")
        fun `all TetrisAction should implement GameAction interface`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val actions: List<GameAction> = listOf(
                TetrisAction.Move(Direction.LEFT, playerId),
                TetrisAction.Rotate(true, playerId),
                TetrisAction.Drop(false, playerId),
                TetrisAction.Hold(playerId),
                TetrisAction.Pause(playerId)
            )

            // Then
            actions.forEach { action ->
                action.shouldBeInstanceOf<GameAction>()
                action.playerId shouldBe playerId
                action.timestamp shouldNotBe 0L
            }
        }

        @Test
        @DisplayName("应该能够通过GameAction接口访问公共属性")
        fun `should be able to access common properties through GameAction interface`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val action: GameAction = TetrisAction.Move(Direction.LEFT, playerId)

            // When & Then
            action.playerId shouldBe playerId
            action.timestamp shouldNotBe 0L
        }

        @Test
        @DisplayName("不同动作应该有不同的时间戳")
        fun `different actions should have different timestamps`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val action1: GameAction = TetrisAction.Move(Direction.LEFT, playerId)
            val timestamp1 = action1.timestamp

            // 确保时间戳不同的更可靠方法
            var action2: GameAction
            do {
                Thread.sleep(1)
                action2 = TetrisAction.Rotate(true, playerId)
            } while (action2.timestamp == timestamp1)

            // Then
            action1.timestamp shouldNotBe action2.timestamp
        }
    }

    @Nested
    @DisplayName("动作序列化测试")
    inner class ActionSerializationTests {

        @Test
        @DisplayName("应该能够创建动作的字符串表示")
        fun `should be able to create string representation of actions`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val moveAction = TetrisAction.Move(Direction.LEFT, playerId)
            val rotateAction = TetrisAction.Rotate(true, playerId)
            val dropAction = TetrisAction.Drop(true, playerId)
            val holdAction = TetrisAction.Hold(playerId)
            val pauseAction = TetrisAction.Pause(playerId)

            // Then
            moveAction.toString().shouldContain("Move")
            moveAction.toString().shouldContain("LEFT")
            
            rotateAction.toString().shouldContain("Rotate")
            rotateAction.toString().shouldContain("true")
            
            dropAction.toString().shouldContain("Drop")
            dropAction.toString().shouldContain("true")
            
            holdAction.toString().shouldContain("Hold")
            pauseAction.toString().shouldContain("Pause")
        }

        @Test
        @DisplayName("相同参数的动作应该相等")
        fun `actions with same parameters should be equal`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID

            // When
            val action1 = TetrisAction.Move(Direction.LEFT, playerId)
            val action2 = TetrisAction.Move(Direction.LEFT, playerId)

            // Then
            // 注意：由于时间戳不同，这些动作实际上不会相等
            // 但我们可以验证其他属性
            action1.direction shouldBe action2.direction
            action1.playerId shouldBe action2.playerId
        }
    }

    @Nested
    @DisplayName("动作验证测试")
    inner class ActionValidationTests {

        @Test
        @DisplayName("应该拒绝空的玩家ID")
        fun `should reject empty player id`() {
            // Given
            val emptyPlayerId = ""

            // When
            val action = TetrisAction.Move(Direction.LEFT, emptyPlayerId)

            // Then
            action.playerId shouldBe emptyPlayerId
            // 注意：当前实现不验证玩家ID，这可能是未来的改进点
        }

        @Test
        @DisplayName("应该接受有效的玩家ID")
        fun `should accept valid player id`() {
            // Given
            val validPlayerId = "player_123"

            // When
            val action = TetrisAction.Move(Direction.LEFT, validPlayerId)

            // Then
            action.playerId shouldBe validPlayerId
        }

        @Test
        @DisplayName("时间戳应该是合理的值")
        fun `timestamp should be reasonable value`() {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val beforeTime = System.currentTimeMillis() / 1000 - 1
            val afterTime = System.currentTimeMillis() / 1000 + 1

            // When
            val action = TetrisAction.Move(Direction.LEFT, playerId)

            // Then
            action.timestamp.shouldBeGreaterThanOrEqual(beforeTime)
            action.timestamp.shouldBeLessThanOrEqual(afterTime)
        }
    }
}
