package com.yu.questicle.core.domain.service

import com.yu.questicle.core.domain.model.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * MembershipService 单元测试
 * 
 * 测试纯业务逻辑，不涉及外部依赖
 */
@DisplayName("会员体系服务测试")
class MembershipServiceTest {

    private lateinit var membershipService: MembershipService

    private val testUser = User(
        id = "test-user-001",
        username = "testuser",
        email = "<EMAIL>",
        level = 1,
        experience = 0L
    )

    @BeforeEach
    fun setup() {
        membershipService = MembershipServiceImpl()
    }

    @Test
    @DisplayName("应正确处理边界值")
    fun `should handle boundary values correctly`() {
        // 测试最小值
        assertEquals(1, membershipService.calculateLevel(0L))
        assertEquals(1, membershipService.calculateLevel(-1L))

        // 测试高值
        assertTrue(membershipService.calculateLevel(100000L) > 1)
    }

    @Test
    @DisplayName("应准确计算等级阈值")
    fun `should calculate levels at exact thresholds`() {
        // 1级边界
        assertEquals(1, membershipService.calculateLevel(0L))
        assertEquals(1, membershipService.calculateLevel(99L))

        // 2级边界
        assertEquals(2, membershipService.calculateLevel(100L))
        assertEquals(2, membershipService.calculateLevel(299L))

        // 3级边界  
        assertEquals(3, membershipService.calculateLevel(300L))
    }

    @Test
    @DisplayName("应正确计算等级内进度")
    fun `should calculate level progress accurately`() {
        // 1级50%进度
        assertEquals(0.5f, membershipService.calculateLevelProgress(50L), 0.01f)

        // 边界情况
        assertEquals(0.0f, membershipService.calculateLevelProgress(0L), 0.01f)
        assertEquals(0.0f, membershipService.calculateLevelProgress(100L), 0.01f) // 刚升级到2级
    }

    @Test
    @DisplayName("应准确计算到下级所需经验")
    fun `should calculate experience to next level correctly`() {
        // 1级用户还需多少经验到2级
        assertEquals(50L, membershipService.getExperienceToNextLevel(50L))

        // 边界情况
        assertTrue(membershipService.getExperienceToNextLevel(0L) > 0)
    }

    @Test
    @DisplayName("应正确计算等级奖励")
    fun `should calculate level rewards correctly`() {
        // 测试1级奖励
        val level1Rewards = membershipService.getLevelRewards(1)
        assertTrue(level1Rewards.isNotEmpty())
        assertTrue(level1Rewards.any { it.type == RewardType.COINS })

        // 测试5级奖励（特殊等级）
        val level5Rewards = membershipService.getLevelRewards(5)
        assertTrue(level5Rewards.any { it.type == RewardType.ACHIEVEMENT })

        // 测试10级奖励（里程碑等级）
        val level10Rewards = membershipService.getLevelRewards(10)
        assertTrue(level10Rewards.any { it.type == RewardType.GEMS })
    }

    @Test
    @DisplayName("应正确计算经验倍数")
    fun `should calculate experience multiplier correctly`() {
        val baseExperience = 100L

        // 免费用户
        val freeUser = testUser.copy(membershipTier = MembershipTier.FREE)
        assertEquals(100L, membershipService.calculateExperienceWithMultiplier(baseExperience, freeUser))

        // 基础会员
        val basicUser = testUser.copy(membershipTier = MembershipTier.BASIC)
        assertEquals(120L, membershipService.calculateExperienceWithMultiplier(baseExperience, basicUser))

        // 高级会员
        val premiumUser = testUser.copy(membershipTier = MembershipTier.PREMIUM)
        assertEquals(150L, membershipService.calculateExperienceWithMultiplier(baseExperience, premiumUser))

        // VIP会员
        val vipUser = testUser.copy(membershipTier = MembershipTier.VIP)
        assertEquals(200L, membershipService.calculateExperienceWithMultiplier(baseExperience, vipUser))
    }

    @Test
    @DisplayName("应正确获取下一级信息")
    fun `should get next level info correctly`() {
        val currentExperience = 50L
        val levelInfo = membershipService.getNextLevelInfo(currentExperience)

        assertEquals(1, levelInfo.currentLevel)
        assertEquals(50L, levelInfo.experienceToNextLevel)
        assertEquals(0.5f, levelInfo.levelProgress, 0.01f)
        assertEquals(100L, levelInfo.nextLevelExperience)
    }

    @Test
    @DisplayName("应正确计算升级信息")
    fun `should calculate level up info correctly`() {
        // 从1级升级到2级
        val levelUpInfo = membershipService.calculateLevelUpInfo(1, 2)
        
        assertEquals(1, levelUpInfo.levelsGained)
        assertTrue(levelUpInfo.totalRewards.isNotEmpty())
        assertFalse(levelUpInfo.isSignificantLevelUp)

        // 从4级升级到5级（重要升级）
        val significantLevelUpInfo = membershipService.calculateLevelUpInfo(4, 5)
        assertTrue(significantLevelUpInfo.isSignificantLevelUp)
    }
} 