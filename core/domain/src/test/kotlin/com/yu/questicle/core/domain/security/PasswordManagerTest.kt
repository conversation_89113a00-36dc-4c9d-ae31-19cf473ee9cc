package com.yu.questicle.core.domain.security

import com.google.common.truth.Truth.assertThat
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.*

/**
 * PasswordManager的全面测试套件
 * 测试密码加密、验证和安全功能
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("PasswordManager 测试")
class PasswordManagerTest {

    @Nested
    @DisplayName("密码哈希测试")
    inner class PasswordHashingTests {

        @Test
        @DisplayName("应该生成有效的密码哈希")
        fun `should generate valid password hash`() {
            // Given
            val password = "SecurePassword123!"

            // When
            val hash = PasswordManager.hashPassword(password)

            // Then
            assertThat(hash).isNotEmpty()
            assertThat(hash.split(":")).hasSize(3) // iterations:salt:hash
            
            val parts = hash.split(":")
            assertThat(parts[0].toInt()).isGreaterThan(0) // iterations
            assertThat(parts[1]).isNotEmpty() // salt
            assertThat(parts[2]).isNotEmpty() // hash
        }

        @Test
        @DisplayName("相同密码应该生成不同的哈希")
        fun `same password should generate different hashes`() {
            // Given
            val password = "SecurePassword123!"

            // When
            val hash1 = PasswordManager.hashPassword(password)
            val hash2 = PasswordManager.hashPassword(password)

            // Then
            hash1 shouldNotBe hash2 // 因为使用了随机盐值
        }

        @Test
        @DisplayName("空密码应该生成有效哈希")
        fun `empty password should generate valid hash`() {
            // Given
            val password = ""

            // When
            val hash = PasswordManager.hashPassword(password)

            // Then
            assertThat(hash).isNotEmpty()
            assertThat(hash.split(":")).hasSize(3)
        }
    }

    @Nested
    @DisplayName("密码验证测试")
    inner class PasswordVerificationTests {

        @Test
        @DisplayName("正确密码应该验证成功")
        fun `correct password should verify successfully`() {
            // Given
            val password = "SecurePassword123!"
            val hash = PasswordManager.hashPassword(password)

            // When
            val isValid = PasswordManager.verifyPassword(password, hash)

            // Then
            isValid shouldBe true
        }

        @Test
        @DisplayName("错误密码应该验证失败")
        fun `wrong password should fail verification`() {
            // Given
            val correctPassword = "SecurePassword123!"
            val wrongPassword = "WrongPassword123!"
            val hash = PasswordManager.hashPassword(correctPassword)

            // When
            val isValid = PasswordManager.verifyPassword(wrongPassword, hash)

            // Then
            isValid shouldBe false
        }

        @Test
        @DisplayName("空哈希应该验证失败")
        fun `empty hash should fail verification`() {
            // Given
            val password = "SecurePassword123!"
            val emptyHash = ""

            // When
            val isValid = PasswordManager.verifyPassword(password, emptyHash)

            // Then
            isValid shouldBe false
        }

        @Test
        @DisplayName("无效哈希格式应该验证失败")
        fun `invalid hash format should fail verification`() {
            // Given
            val password = "SecurePassword123!"
            val invalidHash = "invalid:hash"

            // When
            val isValid = PasswordManager.verifyPassword(password, invalidHash)

            // Then
            isValid shouldBe false
        }

        @Test
        @DisplayName("空密码应该能正确验证")
        fun `empty password should verify correctly`() {
            // Given
            val password = ""
            val hash = PasswordManager.hashPassword(password)

            // When
            val isValid = PasswordManager.verifyPassword(password, hash)

            // Then
            isValid shouldBe true
        }
    }

    @Nested
    @DisplayName("密码强度检查测试")
    inner class PasswordStrengthTests {

        @Test
        @DisplayName("强密码应该获得高分")
        fun `strong password should get high score`() {
            // Given
            val strongPassword = "MyVerySecure123!Password"

            // When
            val strength = PasswordManager.checkPasswordStrength(strongPassword)

            // Then
            assertThat(strength.score).isAtLeast(80)
            assertThat(strength.level).isEqualTo(PasswordStrengthLevel.STRONG)
            assertThat(strength.feedback).isEmpty()
        }

        @Test
        @DisplayName("弱密码应该获得低分")
        fun `weak password should get low score`() {
            // Given
            val weakPassword = "123"

            // When
            val strength = PasswordManager.checkPasswordStrength(weakPassword)

            // Then
            assertThat(strength.score).isLessThan(40)
            assertThat(strength.level).isEqualTo(PasswordStrengthLevel.VERY_WEAK)
            assertThat(strength.feedback).isNotEmpty()
        }

        @Test
        @DisplayName("中等密码应该获得中等分数")
        fun `medium password should get medium score`() {
            // Given
            val mediumPassword = "MySecret456"  // 不在常见密码列表中的中等强度密码

            // When
            val strength = PasswordManager.checkPasswordStrength(mediumPassword)

            // Then
            assertThat(strength.score).isAtLeast(40)
            assertThat(strength.score).isLessThan(80)
            assertThat(strength.level).isAnyOf(
                PasswordStrengthLevel.WEAK,
                PasswordStrengthLevel.MEDIUM
            )
        }

        @Test
        @DisplayName("常见密码应该被检测出来")
        fun `common passwords should be detected`() {
            // Given
            val commonPasswords = listOf("123456", "password", "qwerty", "admin")

            commonPasswords.forEach { password ->
                // When
                val strength = PasswordManager.checkPasswordStrength(password)

                // Then
                assertThat(strength.feedback).contains("避免使用常见密码")
            }
        }

        @Test
        @DisplayName("应该检查各种字符类型")
        fun `should check various character types`() {
            // Given
            val passwordWithoutUppercase = "lowercase123!"
            val passwordWithoutLowercase = "UPPERCASE123!"
            val passwordWithoutNumbers = "Password!"
            val passwordWithoutSymbols = "Password123"

            // When & Then
            val strength1 = PasswordManager.checkPasswordStrength(passwordWithoutUppercase)
            assertThat(strength1.feedback).contains("建议包含大写字母")

            val strength2 = PasswordManager.checkPasswordStrength(passwordWithoutLowercase)
            assertThat(strength2.feedback).contains("需要包含小写字母")

            val strength3 = PasswordManager.checkPasswordStrength(passwordWithoutNumbers)
            assertThat(strength3.feedback).contains("需要包含数字")

            val strength4 = PasswordManager.checkPasswordStrength(passwordWithoutSymbols)
            assertThat(strength4.feedback).contains("建议包含特殊字符")
        }
    }

    @Nested
    @DisplayName("安全密码生成测试")
    inner class SecurePasswordGenerationTests {

        @Test
        @DisplayName("应该生成指定长度的密码")
        fun `should generate password of specified length`() {
            // Given
            val length = 16

            // When
            val password = PasswordManager.generateSecurePassword(length = length)

            // Then
            assertThat(password).hasLength(length)
        }

        @Test
        @DisplayName("生成的密码应该包含指定的字符类型")
        fun `generated password should contain specified character types`() {
            // When
            val password = PasswordManager.generateSecurePassword(
                length = 20,
                includeUppercase = true,
                includeLowercase = true,
                includeNumbers = true,
                includeSymbols = true
            )

            // Then
            assertThat(password).hasLength(20)
            assertThat(password.any { it.isUpperCase() }).isTrue()
            assertThat(password.any { it.isLowerCase() }).isTrue()
            assertThat(password.any { it.isDigit() }).isTrue()
            assertThat(password.any { !it.isLetterOrDigit() }).isTrue()
        }

        @Test
        @DisplayName("应该能生成只包含字母的密码")
        fun `should generate letters only password`() {
            // When
            val password = PasswordManager.generateSecurePassword(
                length = 12,
                includeUppercase = true,
                includeLowercase = true,
                includeNumbers = false,
                includeSymbols = false
            )

            // Then
            assertThat(password).hasLength(12)
            assertThat(password.all { it.isLetter() }).isTrue()
        }

        @Test
        @DisplayName("生成的密码应该是强密码")
        fun `generated password should be strong`() {
            // When
            val password = PasswordManager.generateSecurePassword(length = 16)
            val strength = PasswordManager.checkPasswordStrength(password)

            // Then
            assertThat(strength.score).isAtLeast(70)
            assertThat(strength.level).isAnyOf(
                PasswordStrengthLevel.MEDIUM,
                PasswordStrengthLevel.STRONG
            )
        }

        @Test
        @DisplayName("多次生成的密码应该不同")
        fun `multiple generated passwords should be different`() {
            // When
            val passwords = (1..10).map { 
                PasswordManager.generateSecurePassword(length = 12) 
            }

            // Then
            val uniquePasswords = passwords.toSet()
            assertThat(uniquePasswords).hasSize(passwords.size)
        }

        @Test
        @DisplayName("最小长度限制应该被强制执行")
        fun `minimum length constraint should be enforced`() {
            // When & Then
            assertThrows<IllegalArgumentException> {
                PasswordManager.generateSecurePassword(length = 3)
            }
        }

        @Test
        @DisplayName("至少需要选择一种字符类型")
        fun `at least one character type should be selected`() {
            // When & Then
            assertThrows<IllegalArgumentException> {
                PasswordManager.generateSecurePassword(
                    length = 12,
                    includeUppercase = false,
                    includeLowercase = false,
                    includeNumbers = false,
                    includeSymbols = false
                )
            }
        }
    }

    @Nested
    @DisplayName("性能测试")
    inner class PerformanceTests {

        @Test
        @DisplayName("密码哈希应该在合理时间内完成")
        fun `password hashing should complete in reasonable time`() {
            // Given
            val password = "TestPassword123!"
            val startTime = System.currentTimeMillis()

            // When
            repeat(5) { // 减少重复次数以适应CI环境
                PasswordManager.hashPassword(password)
            }
            val endTime = System.currentTimeMillis()

            // Then
            val duration = endTime - startTime
            // 检测是否在CI环境中运行
            val isCI = System.getenv("CI") != null || System.getProperty("questicle.test.mode") == "true"
            val threshold = if (isCI) 15000 else 5000 // CI环境使用更宽松的阈值
            assertThat(duration).isLessThan(threshold)
        }

        @Test
        @DisplayName("密码验证应该在合理时间内完成")
        fun `password verification should complete in reasonable time`() {
            // Given
            val password = "TestPassword123!"
            val hash = PasswordManager.hashPassword(password)
            val startTime = System.currentTimeMillis()

            // When
            repeat(10) { // 大幅减少重复次数以适应不同环境
                PasswordManager.verifyPassword(password, hash)
            }
            val endTime = System.currentTimeMillis()

            // Then
            val duration = endTime - startTime
            // 使用更宽松的阈值，适应不同的测试环境
            val threshold = 20000L // 20秒的宽松阈值
            assertThat(duration).isLessThan(threshold)
        }
    }
}
