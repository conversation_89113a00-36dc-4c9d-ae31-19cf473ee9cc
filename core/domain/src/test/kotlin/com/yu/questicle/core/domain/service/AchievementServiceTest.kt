package com.yu.questicle.core.domain.service

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.isSuccess
import com.yu.questicle.core.common.result.getOrNull
import com.yu.questicle.core.domain.model.*
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Assertions.*

/**
 * AchievementService单元测试
 * 测试成就系统的核心功能
 */
@DisplayName("AchievementService Tests")
class AchievementServiceTest {
    
    private lateinit var achievementService: AchievementService
    private lateinit var membershipService: MembershipService
    
    @BeforeEach
    fun setUp() {
        membershipService = mockk<MembershipService>(relaxed = true)
        achievementService = AchievementServiceImpl(membershipService)
    }
    
    @Test
    @DisplayName("应该返回预定义的成就列表")
    fun `should return predefined achievements`() = runTest {
        // When
        val result = achievementService.getAllAchievements()
        
        // Then
        assertTrue(result.isSuccess)
        val achievements = result.getOrNull()!!
        assertTrue(achievements.size >= 0)
    }
    
    @Test
    @DisplayName("新用户应该返回空的成就列表")
    fun `new user should return empty achievements list`() = runTest {
        // Given
        val userId = "test_user_123"
        
        // When
        val result = achievementService.getUserAchievements(userId)
        
        // Then
        assertTrue(result.isSuccess)
        val userAchievements = result.getOrNull()!!
        assertTrue(userAchievements.isEmpty())
    }
    
    @Test
    @DisplayName("空事件应该返回空结果")
    fun `empty event should return empty result`() = runTest {
        // Given
        val event = AchievementProgressEvent(
            userId = "test_user",
            gameScore = 0L,
            gameDuration = 0L
        )
        
        // When
        val result = achievementService.checkAchievements(event)
        
        // Then
        assertTrue(result.isSuccess)
        val unlockResult = result.getOrNull()!!
        assertTrue(unlockResult.newlyUnlocked.isEmpty())
        assertTrue(unlockResult.progressUpdated.isEmpty())
        assertEquals(0L, unlockResult.totalRewards.experience)
    }
    
    @Test
    @DisplayName("应该返回正确的统计信息")
    fun `should return correct statistics`() = runTest {
        // Given
        val userId = "test_user"
        
        // When
        val result = achievementService.getAchievementStats(userId)
        
        // Then
        assertTrue(result.isSuccess)
        val stats = result.getOrNull()!!
        assertTrue(stats.totalAchievements >= 0)
        assertTrue(stats.unlockedAchievements >= 0)
        assertTrue(stats.completionPercentage >= 0f)
        assertTrue(stats.completionPercentage <= 1f)
        assertTrue(stats.totalExperienceEarned >= 0L)
    }
}
