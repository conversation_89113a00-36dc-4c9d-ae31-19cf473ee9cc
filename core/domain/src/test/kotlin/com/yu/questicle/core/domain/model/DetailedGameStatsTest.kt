package com.yu.questicle.core.domain.model

import com.google.common.truth.Truth.assertThat
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested

/**
 * DetailedGameStats单元测试
 * 验证统计数据模型的所有功能
 */
@DisplayName("详细游戏统计数据测试")
class DetailedGameStatsTest {

    @Nested
    @DisplayName("数据模型测试")
    inner class DataModelTests {

        @Test
        @DisplayName("默认统计数据应该初始化正确")
        fun `default stats should be initialized correctly`() {
            val stats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS
            )
            
            assertThat(stats.userId).isEqualTo("test-user")
            assertThat(stats.gameType).isEqualTo(GameType.TETRIS)
            assertThat(stats.totalGames).isEqualTo(0)
            assertThat(stats.totalScore).isEqualTo(0)
            assertThat(stats.averageScore).isEqualTo(0.0)
            assertThat(stats.gamesWon).isEqualTo(0)
            assertThat(stats.gamesLost).isEqualTo(0)
        }

        @Test
        @DisplayName("俄罗斯方块专项统计应该初始化正确")
        fun `tetris specific stats should be initialized correctly`() {
            val stats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS
            )
            
            assertThat(stats.totalLinesCleared).isEqualTo(0)
            assertThat(stats.singleLineClears).isEqualTo(0)
            assertThat(stats.tetrisClears).isEqualTo(0)
            assertThat(stats.tSpinDoubles).isEqualTo(0)
            assertThat(stats.maxCombo).isEqualTo(0)
            assertThat(stats.totalPiecesPlaced).isEqualTo(0)
        }

        @Test
        @DisplayName("时间统计应该初始化正确")
        fun `time stats should be initialized correctly`() {
            val stats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS
            )
            
            assertThat(stats.totalPlayTime).isEqualTo(0)
            assertThat(stats.averageGameDuration).isEqualTo(0.0)
            assertThat(stats.shortestGame).isEqualTo(0)
            assertThat(stats.longestGame).isEqualTo(0)
        }

        @Test
        @DisplayName("性能统计应该初始化正确")
        fun `performance stats should be initialized correctly`() {
            val stats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS
            )
            
            assertThat(stats.averagePiecesPerMinute).isEqualTo(0.0)
            assertThat(stats.averageLinesPerMinute).isEqualTo(0.0)
            assertThat(stats.efficiency).isEqualTo(0.0)
            assertThat(stats.consistency).isEqualTo(0.0)
        }
    }

    @Nested
    @DisplayName("序列化测试")
    inner class SerializationTests {

        @Test
        @DisplayName("统计数据应该能够序列化和反序列化")
        fun `stats should be serializable and deserializable`() {
            val originalStats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS,
                totalGames = 100,
                totalScore = 50000,
                averageScore = 500.0,
                totalLinesCleared = 1000,
                tetrisClears = 25
            )
            
            val json = Json.encodeToString(DetailedGameStats.serializer(), originalStats)
            val deserializedStats = Json.decodeFromString(DetailedGameStats.serializer(), json)
            
            assertThat(deserializedStats.userId).isEqualTo(originalStats.userId)
            assertThat(deserializedStats.gameType).isEqualTo(originalStats.gameType)
            assertThat(deserializedStats.totalGames).isEqualTo(originalStats.totalGames)
            assertThat(deserializedStats.totalScore).isEqualTo(originalStats.totalScore)
            assertThat(deserializedStats.averageScore).isEqualTo(originalStats.averageScore)
        }

        @Test
        @DisplayName("复杂统计数据应该能够序列化")
        fun `complex stats should be serializable`() {
            val pieceStats = mapOf(
                "I" to PieceStatistics("I", 100, 2.5, 50, 0.5),
                "T" to PieceStatistics("T", 80, 3.0, 120, 1.5)
            )
            
            val dailyStats = mapOf(
                "2025-06-20" to DailyStats("2025-06-20", 10, 5000, 3600, 800, 50)
            )
            
            val stats = DetailedGameStats(
                userId = "test-user",
                gameType = GameType.TETRIS,
                pieceStats = pieceStats,
                dailyStats = dailyStats
            )
            
            val json = Json.encodeToString(DetailedGameStats.serializer(), stats)
            val deserializedStats = Json.decodeFromString(DetailedGameStats.serializer(), json)
            
            assertThat(deserializedStats.pieceStats).hasSize(2)
            assertThat(deserializedStats.dailyStats).hasSize(1)
            assertThat(deserializedStats.pieceStats["I"]?.totalUsed).isEqualTo(100)
            assertThat(deserializedStats.dailyStats["2025-06-20"]?.gamesPlayed).isEqualTo(10)
        }
    }

    @Nested
    @DisplayName("方块统计测试")
    inner class PieceStatisticsTests {

        @Test
        @DisplayName("方块统计应该正确初始化")
        fun `piece statistics should initialize correctly`() {
            val pieceStats = PieceStatistics(
                type = "I",
                totalUsed = 50,
                averageTimeToPlace = 2.5,
                rotationsUsed = 25,
                averageRotations = 0.5
            )
            
            assertThat(pieceStats.type).isEqualTo("I")
            assertThat(pieceStats.totalUsed).isEqualTo(50)
            assertThat(pieceStats.averageTimeToPlace).isEqualTo(2.5)
            assertThat(pieceStats.rotationsUsed).isEqualTo(25)
            assertThat(pieceStats.averageRotations).isEqualTo(0.5)
        }

        @Test
        @DisplayName("方块统计应该能够序列化")
        fun `piece statistics should be serializable`() {
            val pieceStats = PieceStatistics("T", 30, 3.0, 45, 1.5, 5, 0.8)
            
            val json = Json.encodeToString(PieceStatistics.serializer(), pieceStats)
            val deserialized = Json.decodeFromString(PieceStatistics.serializer(), json)
            
            assertThat(deserialized.type).isEqualTo("T")
            assertThat(deserialized.totalUsed).isEqualTo(30)
            assertThat(deserialized.efficiency).isEqualTo(0.8)
        }
    }

    @Nested
    @DisplayName("时间维度统计测试")
    inner class TimeBasedStatsTests {

        @Test
        @DisplayName("每日统计应该正确初始化")
        fun `daily stats should initialize correctly`() {
            val dailyStats = DailyStats(
                date = "2025-06-20",
                gamesPlayed = 15,
                totalScore = 7500,
                totalPlayTime = 5400,
                bestScore = 800,
                linesCleared = 150
            )
            
            assertThat(dailyStats.date).isEqualTo("2025-06-20")
            assertThat(dailyStats.gamesPlayed).isEqualTo(15)
            assertThat(dailyStats.totalScore).isEqualTo(7500)
            assertThat(dailyStats.bestScore).isEqualTo(800)
        }

        @Test
        @DisplayName("每周统计应该正确初始化")
        fun `weekly stats should initialize correctly`() {
            val weeklyStats = WeeklyStats(
                week = "2025-25",
                gamesPlayed = 100,
                totalScore = 50000,
                averageScore = 500.0,
                improvement = 15.5,
                activeDays = 5
            )
            
            assertThat(weeklyStats.week).isEqualTo("2025-25")
            assertThat(weeklyStats.gamesPlayed).isEqualTo(100)
            assertThat(weeklyStats.improvement).isEqualTo(15.5)
            assertThat(weeklyStats.activeDays).isEqualTo(5)
        }

        @Test
        @DisplayName("每月统计应该正确初始化")
        fun `monthly stats should initialize correctly`() {
            val monthlyStats = MonthlyStats(
                month = "2025-06",
                gamesPlayed = 400,
                totalScore = 200000,
                achievements = listOf("first_tetris", "speed_demon"),
                activeDays = 20,
                longestStreak = 7
            )
            
            assertThat(monthlyStats.month).isEqualTo("2025-06")
            assertThat(monthlyStats.gamesPlayed).isEqualTo(400)
            assertThat(monthlyStats.achievements).hasSize(2)
            assertThat(monthlyStats.longestStreak).isEqualTo(7)
        }
    }

    @Nested
    @DisplayName("趋势分析测试")
    inner class TrendAnalysisTests {

        @Test
        @DisplayName("趋势数据点应该正确创建")
        fun `trend data point should be created correctly`() {
            val dataPoint = TrendDataPoint(
                timestamp = 1640995200000L, // 2022-01-01
                value = 500.0,
                label = "Average Score",
                metadata = mapOf("games" to "10", "improvement" to "5%")
            )
            
            assertThat(dataPoint.timestamp).isEqualTo(1640995200000L)
            assertThat(dataPoint.value).isEqualTo(500.0)
            assertThat(dataPoint.label).isEqualTo("Average Score")
            assertThat(dataPoint.metadata).hasSize(2)
        }

        @Test
        @DisplayName("趋势分析应该正确创建")
        fun `trend analysis should be created correctly`() {
            val dataPoints = listOf(
                TrendDataPoint(1640995200000L, 400.0, "Day 1"),
                TrendDataPoint(1641081600000L, 450.0, "Day 2"),
                TrendDataPoint(1641168000000L, 500.0, "Day 3")
            )
            
            val trendAnalysis = TrendAnalysis(
                metric = StatisticMetric.AVERAGE_SCORE,
                timeFrame = TimeFrame.DAILY,
                dataPoints = dataPoints,
                trend = TrendDirection.INCREASING,
                changePercentage = 25.0,
                summary = "Score improving steadily"
            )
            
            assertThat(trendAnalysis.metric).isEqualTo(StatisticMetric.AVERAGE_SCORE)
            assertThat(trendAnalysis.trend).isEqualTo(TrendDirection.INCREASING)
            assertThat(trendAnalysis.changePercentage).isEqualTo(25.0)
            assertThat(trendAnalysis.dataPoints).hasSize(3)
        }

        @Test
        @DisplayName("趋势预测应该正确创建")
        fun `trend prediction should be created correctly`() {
            val prediction = TrendPrediction(
                metric = StatisticMetric.AVERAGE_SCORE,
                predictedValue = 600.0,
                confidence = 0.85,
                timeframe = "next_week",
                description = "Expected to reach 600 points average"
            )
            
            assertThat(prediction.metric).isEqualTo(StatisticMetric.AVERAGE_SCORE)
            assertThat(prediction.predictedValue).isEqualTo(600.0)
            assertThat(prediction.confidence).isEqualTo(0.85)
            assertThat(prediction.timeframe).isEqualTo("next_week")
        }
    }

    @Nested
    @DisplayName("性能报告测试")
    inner class PerformanceReportTests {

        @Test
        @DisplayName("性能报告应该正确创建")
        fun `performance report should be created correctly`() {
            val stats = DetailedGameStats("user1", GameType.TETRIS, totalGames = 100)
            val trends = mapOf(
                StatisticMetric.AVERAGE_SCORE to TrendAnalysis(
                    StatisticMetric.AVERAGE_SCORE,
                    TimeFrame.WEEKLY,
                    emptyList(),
                    TrendDirection.INCREASING,
                    15.0,
                    "Improving"
                )
            )
            val insights = listOf(
                PerformanceInsight(
                    InsightType.IMPROVEMENT,
                    "Score Improvement",
                    "Your average score has improved by 15%",
                    InsightImpact.HIGH
                )
            )
            
            val report = PerformanceReport(
                userId = "user1",
                gameType = GameType.TETRIS,
                reportPeriod = TimeFrame.MONTHLY,
                overallStats = stats,
                trends = trends,
                insights = insights,
                recommendations = listOf("Focus on T-spins"),
                strengths = listOf("Good line clearing"),
                areasForImprovement = listOf("Speed improvement needed"),
                goals = emptyList()
            )
            
            assertThat(report.userId).isEqualTo("user1")
            assertThat(report.gameType).isEqualTo(GameType.TETRIS)
            assertThat(report.trends).hasSize(1)
            assertThat(report.insights).hasSize(1)
            assertThat(report.recommendations).hasSize(1)
        }

        @Test
        @DisplayName("性能洞察应该正确创建")
        fun `performance insight should be created correctly`() {
            val insight = PerformanceInsight(
                type = InsightType.ACHIEVEMENT,
                title = "First Tetris",
                description = "You achieved your first Tetris clear!",
                impact = InsightImpact.MEDIUM,
                actionable = true,
                action = "Try to get more Tetris clears",
                supportingData = mapOf("tetris_count" to 1.0, "total_clears" to 100.0)
            )
            
            assertThat(insight.type).isEqualTo(InsightType.ACHIEVEMENT)
            assertThat(insight.title).isEqualTo("First Tetris")
            assertThat(insight.impact).isEqualTo(InsightImpact.MEDIUM)
            assertThat(insight.actionable).isTrue()
            assertThat(insight.supportingData).hasSize(2)
        }

        @Test
        @DisplayName("性能目标应该正确创建")
        fun `performance goal should be created correctly`() {
            val goal = PerformanceGoal(
                id = "goal1",
                title = "Reach 1000 Average Score",
                description = "Improve your average score to 1000 points",
                targetValue = 1000.0,
                currentValue = 750.0,
                metric = StatisticMetric.AVERAGE_SCORE,
                deadline = System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000L, // 30 days
                progress = 0.75
            )
            
            assertThat(goal.id).isEqualTo("goal1")
            assertThat(goal.targetValue).isEqualTo(1000.0)
            assertThat(goal.currentValue).isEqualTo(750.0)
            assertThat(goal.progress).isEqualTo(0.75)
            assertThat(goal.isCompleted).isFalse()
        }
    }

    @Nested
    @DisplayName("比较分析测试")
    inner class ComparisonAnalysisTests {

        @Test
        @DisplayName("比较结果应该正确创建")
        fun `comparison result should be created correctly`() {
            val result = ComparisonResult(
                metric = StatisticMetric.AVERAGE_SCORE,
                userValue = 750.0,
                comparisonValue = 600.0,
                difference = 150.0,
                percentageDifference = 25.0,
                isBetter = true,
                ranking = 15,
                description = "You score 25% higher than average"
            )
            
            assertThat(result.metric).isEqualTo(StatisticMetric.AVERAGE_SCORE)
            assertThat(result.userValue).isEqualTo(750.0)
            assertThat(result.comparisonValue).isEqualTo(600.0)
            assertThat(result.isBetter).isTrue()
            assertThat(result.ranking).isEqualTo(15)
        }

        @Test
        @DisplayName("比较分析应该正确创建")
        fun `comparison analysis should be created correctly`() {
            val userStats = DetailedGameStats("user1", GameType.TETRIS, averageScore = 750.0)
            val comparisons = mapOf(
                StatisticMetric.AVERAGE_SCORE to ComparisonResult(
                    StatisticMetric.AVERAGE_SCORE, 750.0, 600.0, 150.0, 25.0, true, 15,
                    "Above average"
                )
            )
            
            val analysis = ComparisonAnalysis(
                userStats = userStats,
                comparisonTarget = ComparisonTarget.GlobalAverage,
                comparisons = comparisons,
                overallRanking = 15,
                totalPlayers = 1000,
                percentile = 85.0
            )
            
            assertThat(analysis.userStats.averageScore).isEqualTo(750.0)
            assertThat(analysis.comparisonTarget).isEqualTo(ComparisonTarget.GlobalAverage)
            assertThat(analysis.comparisons).hasSize(1)
            assertThat(analysis.overallRanking).isEqualTo(15)
            assertThat(analysis.percentile).isEqualTo(85.0)
        }
    }

    @Nested
    @DisplayName("统计摘要测试")
    inner class StatsSummaryTests {

        @Test
        @DisplayName("统计摘要应该正确创建")
        fun `stats summary should be created correctly`() {
            val summary = StatsSummary(
                totalGames = 500,
                totalPlayTime = 50000,
                averageScore = 650.0,
                bestScore = 1200,
                currentLevel = 15,
                winRate = 0.75,
                recentTrend = TrendDirection.INCREASING,
                keyAchievements = listOf("First Tetris", "Speed Demon", "Line Master")
            )
            
            assertThat(summary.totalGames).isEqualTo(500)
            assertThat(summary.averageScore).isEqualTo(650.0)
            assertThat(summary.winRate).isEqualTo(0.75)
            assertThat(summary.recentTrend).isEqualTo(TrendDirection.INCREASING)
            assertThat(summary.keyAchievements).hasSize(3)
        }
    }
}
