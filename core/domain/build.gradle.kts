plugins {
    id("questicle.android.library")
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
}

android {
    namespace = "com.yu.questicle.core.domain"

    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            all {
                it.useJUnitPlatform()
            }
        }
    }
}

dependencies {
    implementation(project(":core:common"))
    implementation(libs.androidx.compose.animation)

    api(libs.kotlinx.serialization.json)

    // Coroutines - 使用版本目录
    api(libs.kotlinx.coroutines.core)
    api(libs.kotlinx.coroutines.android)

    // Hilt - 使用版本目录
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    // 标准化测试依赖
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)
    testImplementation(libs.junit5.params)
    
    // 修复缺失的JUnit5依赖
    testImplementation(libs.junit5.api)
    testRuntimeOnly(libs.junit5.engine)
}
