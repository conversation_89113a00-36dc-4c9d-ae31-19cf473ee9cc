package com.yu.questicle.core.data.repository

import app.cash.turbine.test
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.data.datasource.local.GameLocalDataSource
import com.yu.questicle.core.data.datasource.remote.GameRemoteDataSource
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.testing.factory.TestDataFactory
import com.yu.questicle.core.testing.mock.MockFactory
import com.yu.questicle.core.testing.rules.TetrisTestRule
import com.yu.questicle.core.testing.util.*
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.collections.shouldHaveSize
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.*

/**
 * GameRepositoryImpl集成测试
 * 测试Repository层与数据源的集成
 */
@ExperimentalCoroutinesApi
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("GameRepositoryImpl 集成测试")
class GameRepositoryImplIntegrationTest {

    private val testDispatcher: TestDispatcher = StandardTestDispatcher()

    private lateinit var gameRepository: GameRepositoryImpl
    private lateinit var mockLocalDataSource: GameLocalDataSource
    private lateinit var mockRemoteDataSource: GameRemoteDataSource

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        mockLocalDataSource = mockk<GameLocalDataSource>(relaxed = true)
        mockRemoteDataSource = mockk<GameRemoteDataSource>(relaxed = true)
        
        gameRepository = GameRepositoryImpl(
            localDataSource = mockLocalDataSource,
            remoteDataSource = mockRemoteDataSource,
            ioDispatcher = testDispatcher
        )
    }

    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }

    @Nested
    @DisplayName("游戏保存测试")
    inner class GameSaveTests {

        @Test
        @DisplayName("应该成功保存游戏到本地和远程")
        fun `should save game to local and remote successfully`() = runTest(testDispatcher) {
            // Given
            val game = TestDataFactory.createGame()
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.saveGame(game) } returns Result.Success(Unit)

            // When
            val result = gameRepository.saveGame(game)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>()
            coVerify { mockLocalDataSource.saveGame(game) }
            coVerify { mockRemoteDataSource.saveGame(game) }
        }

        @Test
        @DisplayName("本地保存失败时应该返回错误")
        fun `should return error when local save fails`() = runTest(testDispatcher) {
            // Given
            val game = TestDataFactory.createGame()
            val error = RuntimeException("Local save failed")
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Error(error.toQuesticleException())

            // When
            val result = gameRepository.saveGame(game)

            // Then
            result.shouldBeInstanceOf<Result.Error>()
            coVerify { mockLocalDataSource.saveGame(game) }
            coVerify(exactly = 0) { mockRemoteDataSource.saveGame(any()) }
        }

        @Test
        @DisplayName("远程保存失败时本地保存应该成功")
        fun `should succeed locally even when remote save fails`() = runTest(testDispatcher) {
            // Given
            val game = TestDataFactory.createGame()
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.saveGame(game) } throws RuntimeException("Remote save failed")

            // When
            val result = gameRepository.saveGame(game)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>()
            coVerify { mockLocalDataSource.saveGame(game) }
            coVerify { mockRemoteDataSource.saveGame(game) }
        }
    }

    @Nested
    @DisplayName("游戏加载测试")
    inner class GameLoadTests {

        @Test
        @DisplayName("应该优先从本地加载游戏")
        fun `should load game from local first`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val game = TestDataFactory.createGame(id = gameId)
            coEvery { mockLocalDataSource.loadGame(gameId) } returns Result.Success(game)

            // When
            val result = gameRepository.loadGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Success<Game>>()
            result.data shouldBe game
            coVerify { mockLocalDataSource.loadGame(gameId) }
            coVerify(exactly = 0) { mockRemoteDataSource.loadGame(any()) }
        }

        @Test
        @DisplayName("本地加载失败时应该尝试远程加载")
        fun `should try remote when local load fails`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val game = TestDataFactory.createGame(id = gameId)
            val localError = RuntimeException("Local load failed")
            
            coEvery { mockLocalDataSource.loadGame(gameId) } returns Result.Error(localError.toQuesticleException())
            coEvery { mockRemoteDataSource.loadGame(gameId) } returns Result.Success(game)
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Success(Unit)

            // When
            val result = gameRepository.loadGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Success<Game>>()
            result.data shouldBe game
            coVerify { mockLocalDataSource.loadGame(gameId) }
            coVerify { mockRemoteDataSource.loadGame(gameId) }
            coVerify { mockLocalDataSource.saveGame(game) } // 应该缓存到本地
        }

        @Test
        @DisplayName("本地和远程都失败时应该返回错误")
        fun `should return error when both local and remote fail`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val localError = RuntimeException("Local load failed")
            val remoteError = RuntimeException("Remote load failed")
            
            coEvery { mockLocalDataSource.loadGame(gameId) } returns Result.Error(localError.toQuesticleException())
            coEvery { mockRemoteDataSource.loadGame(gameId) } returns Result.Error(remoteError.toQuesticleException())

            // When
            val result = gameRepository.loadGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Error>()
            coVerify { mockLocalDataSource.loadGame(gameId) }
            coVerify { mockRemoteDataSource.loadGame(gameId) }
        }
    }

    @Nested
    @DisplayName("游戏删除测试")
    inner class GameDeleteTests {

        @Test
        @DisplayName("应该从本地和远程删除游戏")
        fun `should delete game from both local and remote`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            coEvery { mockLocalDataSource.deleteGame(gameId) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.deleteGame(gameId) } returns Result.Success(Unit)

            // When
            val result = gameRepository.deleteGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>()
            coVerify { mockLocalDataSource.deleteGame(gameId) }
            coVerify { mockRemoteDataSource.deleteGame(gameId) }
        }

        @Test
        @DisplayName("本地删除失败时应该返回错误")
        fun `should return error when local delete fails`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val error = RuntimeException("Local delete failed")
            coEvery { mockLocalDataSource.deleteGame(gameId) } returns Result.Error(error.toQuesticleException())

            // When
            val result = gameRepository.deleteGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Error>()
            coVerify { mockLocalDataSource.deleteGame(gameId) }
            coVerify(exactly = 0) { mockRemoteDataSource.deleteGame(any()) }
        }
    }

    @Nested
    @DisplayName("游戏查询测试")
    inner class GameQueryTests {

        @Test
        @DisplayName("应该返回玩家的游戏列表")
        fun `should return games for player`() = runTest(testDispatcher) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val games = listOf(
                TestDataFactory.createGame(playerId = playerId),
                TestDataFactory.createGame(playerId = playerId)
            )
            every { mockLocalDataSource.getGamesForPlayer(playerId) } returns flowOf(games)

            // When
            gameRepository.getGamesForPlayer(playerId).test {
                val result = awaitItem()
                
                // Then
                result shouldBe games
                result.shouldHaveSize(2)
                result.forEach { game ->
                    game.playerId shouldBe playerId
                }
                
                awaitComplete()
            }
        }

        @Test
        @DisplayName("应该按类型返回游戏列表")
        fun `should return games by type`() = runTest(testDispatcher) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val gameType = GameType.TETRIS
            val games = listOf(
                TestDataFactory.createGame(playerId = playerId, type = gameType),
                TestDataFactory.createGame(playerId = playerId, type = gameType)
            )
            every { mockLocalDataSource.getGamesByType(playerId, gameType) } returns flowOf(games)

            // When
            gameRepository.getGamesByType(playerId, gameType).test {
                val result = awaitItem()
                
                // Then
                result shouldBe games
                result.forEach { game ->
                    game.type shouldBe gameType
                    game.playerId shouldBe playerId
                }
                
                awaitComplete()
            }
        }

        @Test
        @DisplayName("应该支持游戏搜索")
        fun `should support game search`() = runTest(testDispatcher) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val gameType = GameType.TETRIS
            val minScore = 1000
            val maxScore = 5000
            val limit = 10
            val games = listOf(
                TestDataFactory.createGame(playerId = playerId, type = gameType, score = 2000),
                TestDataFactory.createGame(playerId = playerId, type = gameType, score = 3000)
            )
            
            coEvery { 
                mockLocalDataSource.searchGames(playerId, gameType, minScore, maxScore, limit) 
            } returns Result.Success(games)

            // When
            val result = gameRepository.searchGames(playerId, gameType, minScore, maxScore, limit)

            // Then
            result.shouldBeInstanceOf<Result.Success<List<Game>>>()
            result.data shouldBe games
            coVerify { mockLocalDataSource.searchGames(playerId, gameType, minScore, maxScore, limit) }
        }
    }

    @Nested
    @DisplayName("游戏会话测试")
    inner class GameSessionTests {

        @Test
        @DisplayName("应该成功保存游戏会话")
        fun `should save game session successfully`() = runTest(testDispatcher) {
            // Given
            val session = TestDataFactory.createGameSession()
            coEvery { mockLocalDataSource.saveGameSession(session) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.saveGameSession(session) } returns Result.Success(Unit)

            // When
            val result = gameRepository.saveGameSession(session)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>()
            coVerify { mockLocalDataSource.saveGameSession(session) }
            coVerify { mockRemoteDataSource.saveGameSession(session) }
        }

        @Test
        @DisplayName("应该返回游戏会话列表")
        fun `should return game sessions`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val sessions = listOf(
                TestDataFactory.createGameSession(gameId = gameId),
                TestDataFactory.createGameSession(gameId = gameId)
            )
            every { mockLocalDataSource.getGameSessions(gameId) } returns flowOf(sessions)

            // When
            gameRepository.getGameSessions(gameId).test {
                val result = awaitItem()
                
                // Then
                result shouldBe sessions
                result.forEach { session ->
                    session.gameId shouldBe gameId
                }
                
                awaitComplete()
            }
        }
    }

    @Nested
    @DisplayName("游戏统计测试")
    inner class GameStatsTests {

        @Test
        @DisplayName("应该返回游戏统计")
        fun `should return game statistics`() = runTest(testDispatcher) {
            // Given
            val playerId = TestDataFactory.Constants.TEST_PLAYER_ID
            val gameType = GameType.TETRIS
            val stats = TestDataFactory.createGameStats(playerId = playerId, gameType = gameType)
            
            coEvery { mockLocalDataSource.getGameStats(playerId, gameType) } returns Result.Success(stats)

            // When
            val result = gameRepository.getGameStats(playerId, gameType)

            // Then
            result.shouldBeInstanceOf<Result.Success<GameStats>>()
            result.data shouldBe stats
            coVerify { mockLocalDataSource.getGameStats(playerId, gameType) }
        }

        @Test
        @DisplayName("应该保存游戏统计")
        fun `should save game statistics`() = runTest(testDispatcher) {
            // Given
            val stats = TestDataFactory.createGameStats()
            coEvery { mockLocalDataSource.saveGameStats(stats) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.updateGameStats(stats) } returns Result.Success(Unit)

            // When
            val result = gameRepository.updateGameStats(stats)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>()
            coVerify { mockLocalDataSource.saveGameStats(stats) }
            coVerify { mockRemoteDataSource.updateGameStats(stats) }
        }

        @Test
        @DisplayName("应该返回排行榜")
        fun `should return leaderboard`() = runTest(testDispatcher) {
            // Given
            val gameType = GameType.TETRIS
            val limit = 10
            val leaderboard = listOf(
                TestDataFactory.createGameStats(bestScore = 5000),
                TestDataFactory.createGameStats(bestScore = 4000),
                TestDataFactory.createGameStats(bestScore = 3000)
            )
            
            coEvery { mockRemoteDataSource.getLeaderboard(gameType, limit) } returns Result.Success(leaderboard)

            // When
            val result = gameRepository.getLeaderboard(gameType, limit)

            // Then
            result.shouldBeInstanceOf<Result.Success<List<GameStats>>>()
            result.data shouldBe leaderboard
            coVerify { mockRemoteDataSource.getLeaderboard(gameType, limit) }
        }

        @Test
        @DisplayName("远程排行榜失败时应该使用本地排行榜")
        fun `should use local leaderboard when remote fails`() = runTest(testDispatcher) {
            // Given
            val gameType = GameType.TETRIS
            val limit = 10
            val localLeaderboard = listOf(
                TestDataFactory.createGameStats(bestScore = 2000),
                TestDataFactory.createGameStats(bestScore = 1500)
            )
            
            coEvery { mockRemoteDataSource.getLeaderboard(gameType, limit) } throws RuntimeException("Remote failed")
            coEvery { mockLocalDataSource.getLeaderboard(gameType, limit) } returns Result.Success(localLeaderboard)

            // When
            val result = gameRepository.getLeaderboard(gameType, limit)

            // Then
            result.shouldBeInstanceOf<Result.Success<List<GameStats>>>()
            result.data shouldBe localLeaderboard
            coVerify { mockRemoteDataSource.getLeaderboard(gameType, limit) }
            coVerify { mockLocalDataSource.getLeaderboard(gameType, limit) }
        }
    }

    @Nested
    @DisplayName("错误处理测试")
    inner class ErrorHandlingTests {

        @Test
        @DisplayName("应该正确处理网络错误")
        fun `should handle network errors correctly`() = runTest(testDispatcher) {
            // Given
            val game = TestDataFactory.createGame()
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Success(Unit)
            coEvery { mockRemoteDataSource.saveGame(game) } throws RuntimeException("Network error")

            // When
            val result = gameRepository.saveGame(game)

            // Then
            result.shouldBeInstanceOf<Result.Success<Unit>>() // 本地保存成功
        }

        @Test
        @DisplayName("应该正确处理数据库错误")
        fun `should handle database errors correctly`() = runTest(testDispatcher) {
            // Given
            val game = TestDataFactory.createGame()
            val dbError = RuntimeException("Database error")
            coEvery { mockLocalDataSource.saveGame(game) } returns Result.Error(dbError.toQuesticleException())

            // When
            val result = gameRepository.saveGame(game)

            // Then
            result.shouldBeInstanceOf<Result.Error>()
        }

        @Test
        @DisplayName("应该正确处理序列化错误")
        fun `should handle serialization errors correctly`() = runTest(testDispatcher) {
            // Given
            val gameId = TestDataFactory.Constants.TEST_GAME_ID
            val serializationError = RuntimeException("Serialization error")
            coEvery { mockLocalDataSource.loadGame(gameId) } returns Result.Error(serializationError.toQuesticleException())
            coEvery { mockRemoteDataSource.loadGame(gameId) } returns Result.Error(serializationError.toQuesticleException())

            // When
            val result = gameRepository.loadGame(gameId)

            // Then
            result.shouldBeInstanceOf<Result.Error>()
        }
    }
}
