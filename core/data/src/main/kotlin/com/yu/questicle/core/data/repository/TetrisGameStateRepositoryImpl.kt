package com.yu.questicle.core.data.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.tetris.TetrisGameState
import com.yu.questicle.core.domain.repository.TetrisGameStateRepository
import com.yu.questicle.core.domain.repository.TetrisGameStateSave
import com.yu.questicle.core.domain.repository.TetrisGameStateMetadata
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Tetris游戏状态仓库实现
 * 目前使用内存存储，后续可以扩展为数据库存储
 */
@Singleton
class TetrisGameStateRepositoryImpl @Inject constructor() : TetrisGameStateRepository {
    
    // 临时使用内存存储，后续可以替换为Room数据库
    private val gameStates = mutableMapOf<String, TetrisGameStateSave>()
    
    override suspend fun saveGameState(
        gameState: TetrisGameState,
        playerId: String,
        slotId: String
    ): Result<Unit> {
        return try {
            val key = "$playerId:$slotId"
            val metadata = TetrisGameStateMetadata(
                level = gameState.level,
                score = gameState.score,
                lines = gameState.lines,
                playTime = calculatePlayTime(gameState),
                saveTime = System.currentTimeMillis()
            )
            
            val save = TetrisGameStateSave(
                slotId = slotId,
                playerId = playerId,
                saveTime = System.currentTimeMillis(),
                gameState = gameState,
                metadata = metadata
            )
            
            gameStates[key] = save
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(
                com.yu.questicle.core.common.exception.BusinessException(
                    message = "Failed to save game state: ${e.message}",
                    cause = e
                )
            )
        }
    }
    
    override suspend fun loadGameState(
        playerId: String,
        slotId: String
    ): Result<TetrisGameState> {
        return try {
            val key = "$playerId:$slotId"
            val save = gameStates[key]
            
            if (save != null) {
                Result.Success(save.gameState)
            } else {
                Result.Error(
                    com.yu.questicle.core.common.exception.BusinessException(
                        message = "Game state not found for player $playerId, slot $slotId"
                    )
                )
            }
        } catch (e: Exception) {
            Result.Error(
                com.yu.questicle.core.common.exception.BusinessException(
                    message = "Failed to load game state: ${e.message}",
                    cause = e
                )
            )
        }
    }
    
    override fun getGameStateSaves(playerId: String): Flow<List<TetrisGameStateSave>> {
        return try {
            val playerSaves = gameStates.values.filter { it.playerId == playerId }
            flowOf(playerSaves)
        } catch (e: Exception) {
            flowOf(emptyList())
        }
    }
    
    override suspend fun deleteGameState(
        playerId: String,
        slotId: String
    ): Result<Unit> {
        return try {
            val key = "$playerId:$slotId"
            gameStates.remove(key)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(
                com.yu.questicle.core.common.exception.BusinessException(
                    message = "Failed to delete game state: ${e.message}",
                    cause = e
                )
            )
        }
    }
    
    override suspend fun hasGameState(
        playerId: String,
        slotId: String
    ): Boolean {
        return try {
            val key = "$playerId:$slotId"
            gameStates.containsKey(key)
        } catch (e: Exception) {
            false
        }
    }
    
    override suspend fun getGameStateMetadata(
        playerId: String,
        slotId: String
    ): Result<TetrisGameStateMetadata> {
        return try {
            val key = "$playerId:$slotId"
            val save = gameStates[key]
            
            if (save != null) {
                Result.Success(save.metadata)
            } else {
                Result.Error(
                    com.yu.questicle.core.common.exception.BusinessException(
                        message = "Game state metadata not found for player $playerId, slot $slotId"
                    )
                )
            }
        } catch (e: Exception) {
            Result.Error(
                com.yu.questicle.core.common.exception.BusinessException(
                    message = "Failed to get game state metadata: ${e.message}",
                    cause = e
                )
            )
        }
    }

    /**
     * 计算游戏时间
     * 基于游戏统计信息或估算
     */
    private fun calculatePlayTime(gameState: TetrisGameState): Long {
        return try {
            // 优先使用统计信息中的游戏时间
            if (gameState.statistics.gameTime > 0) {
                gameState.statistics.gameTime
            } else {
                // 基于等级和分数估算游戏时间
                // 假设每个等级需要约2分钟，每1000分需要约1分钟
                val levelTime = (gameState.level - 1) * 120000L // 2分钟/等级
                val scoreTime = (gameState.score / 1000) * 60000L // 1分钟/1000分
                maxOf(levelTime, scoreTime, 60000L) // 最少1分钟
            }
        } catch (e: Exception) {
            // 如果计算失败，返回默认值
            60000L // 1分钟
        }
    }
}
