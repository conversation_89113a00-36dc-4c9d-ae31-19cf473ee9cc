package com.yu.questicle.core.data.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.toQuesticleException
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.model.Theme
import com.yu.questicle.core.domain.model.GameDifficulty
import com.yu.questicle.core.domain.repository.UserPreferencesRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户偏好设置仓库实现
 * 负责用户偏好设置的持久化和管理
 */
@Singleton
class UserPreferencesRepositoryImpl @Inject constructor() : UserPreferencesRepository {
    
    private val mutex = Mutex()
    private val _userPreferences = MutableStateFlow(UserPreferences())
    private val preferencesHistory = mutableListOf<UserPreferences>()
    
    override fun getUserPreferences(): Flow<UserPreferences> = _userPreferences.asStateFlow()
    
    override suspend fun getCurrentUserPreferences(): Result<UserPreferences> = mutex.withLock {
        return try {
            Result.Success(_userPreferences.value)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateUserPreferences(preferences: UserPreferences): Result<UserPreferences> = mutex.withLock {
        return try {
            // 验证设置
            when (val validationResult = validatePreferences(preferences)) {
                is Result.Error -> return validationResult
                is Result.Success -> {
                    // 保存历史记录
                    preferencesHistory.add(_userPreferences.value)
                    if (preferencesHistory.size > 10) {
                        preferencesHistory.removeAt(0)
                    }
                    
                    _userPreferences.value = preferences
                    Result.Success(preferences)
                }
                is Result.Loading -> Result.Error(
                    IllegalStateException("验证中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateTheme(theme: Theme): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(theme = theme)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateLanguage(language: String): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(language = language)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateSoundEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(soundEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateMusicEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(musicEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateVibrationEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(vibrationEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateMasterVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            val clampedVolume = volume.coerceIn(0f, 1f)
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(masterVolume = clampedVolume)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateSoundEffectsVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            val clampedVolume = volume.coerceIn(0f, 1f)
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(soundEffectsVolume = clampedVolume)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateMusicVolume(volume: Float): Result<Unit> = mutex.withLock {
        return try {
            val clampedVolume = volume.coerceIn(0f, 1f)
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(musicVolume = clampedVolume)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateNotificationsEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(notificationsEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateAutoSaveEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(autoSaveEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateGameDifficulty(difficulty: GameDifficulty): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(difficulty = difficulty)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateGameSpeed(speed: Float): Result<Unit> = mutex.withLock {
        return try {
            // 游戏速度验证
            if (speed < 0.1f || speed > 10.0f) {
                return Result.Error(
                    IllegalArgumentException("游戏速度必须在0.1到10.0之间")
                        .toQuesticleException()
                )
            }
            
            // 注意：UserPreferences中没有gameSpeed字段，这里需要扩展模型
            // 暂时返回成功，等待模型扩展
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateControlSensitivity(sensitivity: Float): Result<Unit> = mutex.withLock {
        return try {
            // 控制灵敏度验证
            if (sensitivity < 0.1f || sensitivity > 5.0f) {
                return Result.Error(
                    IllegalArgumentException("控制灵敏度必须在0.1到5.0之间")
                        .toQuesticleException()
                )
            }
            
            // 注意：UserPreferences中没有controlSensitivity字段，这里需要扩展模型
            // 暂时返回成功，等待模型扩展
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updateShowHintsEnabled(enabled: Boolean): Result<Unit> = mutex.withLock {
        return try {
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(showHintsEnabled = enabled)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateAnimationSpeed(speed: Float): Result<Unit> = mutex.withLock {
        return try {
            val clampedSpeed = speed.coerceIn(0.1f, 2.0f)
            val currentPrefs = _userPreferences.value
            val updatedPrefs = currentPrefs.copy(animationSpeed = clampedSpeed)
            when (val result = updateUserPreferences(updatedPrefs)) {
                is Result.Success -> Result.Success(Unit)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateShowTutorial(show: Boolean): Result<Unit> = mutex.withLock {
        return try {
            // 注意：UserPreferences中没有showTutorial字段，这里需要扩展模型
            // 暂时返回成功，等待模型扩展
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun resetToDefaults(): Result<UserPreferences> = mutex.withLock {
        return try {
            val defaultPrefs = UserPreferences()
            when (val result = updateUserPreferences(defaultPrefs)) {
                is Result.Success -> Result.Success(defaultPrefs)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("重置中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun exportUserData(): Result<String> = mutex.withLock {
        return try {
            val json = Json { prettyPrint = true }
            val exportData = json.encodeToString(_userPreferences.value)
            Result.Success(exportData)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun clearAllData(): Result<Unit> = mutex.withLock {
        return try {
            preferencesHistory.clear()
            _userPreferences.value = UserPreferences()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun batchUpdatePreferences(updates: Map<String, Any>): Result<UserPreferences> = mutex.withLock {
        return try {
            var currentPrefs = _userPreferences.value
            
            updates.forEach { (key, value) ->
                currentPrefs = when (key) {
                    "theme" -> currentPrefs.copy(theme = value as Theme)
                    "language" -> currentPrefs.copy(language = value as String)
                    "soundEnabled" -> currentPrefs.copy(soundEnabled = value as Boolean)
                    "musicEnabled" -> currentPrefs.copy(musicEnabled = value as Boolean)
                    "vibrationEnabled" -> currentPrefs.copy(vibrationEnabled = value as Boolean)
                    "notificationsEnabled" -> currentPrefs.copy(notificationsEnabled = value as Boolean)
                    "autoSaveEnabled" -> currentPrefs.copy(autoSaveEnabled = value as Boolean)
                    "difficulty" -> currentPrefs.copy(difficulty = value as GameDifficulty)
                    else -> currentPrefs // 忽略未知字段
                }
            }
            
            when (val result = updateUserPreferences(currentPrefs)) {
                is Result.Success -> Result.Success(currentPrefs)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("批量更新中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun validatePreferences(preferences: UserPreferences): Result<Unit> {
        return try {
            // 验证语言代码
            if (preferences.language.isBlank()) {
                return Result.Error(
                    IllegalArgumentException("语言代码不能为空")
                        .toQuesticleException()
                )
            }
            
            // 验证语言代码格式
            if (!preferences.language.matches(Regex("^[a-z]{2}(-[A-Z]{2})?$"))) {
                return Result.Error(
                    IllegalArgumentException("语言代码格式不正确")
                        .toQuesticleException()
                )
            }
            
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun getPreferencesHistory(): Result<List<UserPreferences>> = mutex.withLock {
        return try {
            Result.Success(preferencesHistory.toList())
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun backupPreferences(): Result<String> = mutex.withLock {
        return try {
            val json = Json { prettyPrint = true }
            val backup = mapOf(
                "preferences" to _userPreferences.value,
                "history" to preferencesHistory,
                "timestamp" to System.currentTimeMillis()
            )
            val backupData = json.encodeToString(backup)
            Result.Success(backupData)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun restorePreferences(backup: String): Result<UserPreferences> = mutex.withLock {
        return try {
            val json = Json { ignoreUnknownKeys = true }
            val backupData = json.decodeFromString<Map<String, Any>>(backup)
            
            // 这里需要更复杂的反序列化逻辑
            // 暂时返回默认设置
            val defaultPrefs = UserPreferences()
            when (val result = updateUserPreferences(defaultPrefs)) {
                is Result.Success -> Result.Success(defaultPrefs)
                is Result.Error -> result
                is Result.Loading -> Result.Error(
                    IllegalStateException("恢复中").toQuesticleException()
                )
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}
