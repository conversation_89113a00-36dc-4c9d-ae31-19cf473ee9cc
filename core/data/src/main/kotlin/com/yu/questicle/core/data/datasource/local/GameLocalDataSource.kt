package com.yu.questicle.core.data.datasource.local

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType
import kotlinx.coroutines.flow.Flow

/**
 * Local data source interface for game data
 */
interface GameLocalDataSource {
    
    /**
     * Save game to local storage
     */
    suspend fun saveGame(game: Game): Result<Unit>
    
    /**
     * Load game from local storage
     */
    suspend fun loadGame(gameId: String): Result<Game>
    
    /**
     * Get all games for a player
     */
    fun getGamesForPlayer(playerId: String): Flow<List<Game>>
    
    /**
     * Get games by type for a player
     */
    fun getGamesByType(playerId: String, gameType: GameType): Flow<List<Game>>
    
    /**
     * Delete game from local storage
     */
    suspend fun deleteGame(gameId: String): Result<Unit>
    
    /**
     * Save game session
     */
    suspend fun saveGameSession(session: GameSession): Result<Unit>
    
    /**
     * Get game sessions for a game
     */
    fun getGameSessions(gameId: String): Flow<List<GameSession>>
    
    /**
     * Get game statistics
     */
    suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats>
    
    /**
     * Save game statistics
     */
    suspend fun saveGameStats(stats: GameStats): Result<Unit>
    
    /**
     * Get local leaderboard
     */
    suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>>
    
    /**
     * Search games by criteria
     */
    suspend fun searchGames(
        playerId: String? = null,
        gameType: GameType? = null,
        minScore: Int? = null,
        maxScore: Int? = null,
        limit: Int = 50
    ): Result<List<Game>>
    
    /**
     * Clear all local game data
     */
    suspend fun clearAllData(): Result<Unit>
    
    /**
     * Get total storage size
     */
    suspend fun getStorageSize(): Long
    
    /**
     * Optimize storage (cleanup old data)
     */
    suspend fun optimizeStorage(): Result<Unit>
}
