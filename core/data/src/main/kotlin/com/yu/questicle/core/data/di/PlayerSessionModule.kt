package com.yu.questicle.core.data.di

import com.yu.questicle.core.domain.service.PlayerSessionManager
import com.yu.questicle.core.domain.service.PlayerSessionManagerImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块：提供玩家会话管理器的依赖注入绑定
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class PlayerSessionModule {
    
    @Binds
    @Singleton
    abstract fun bindPlayerSessionManager(
        playerSessionManagerImpl: PlayerSessionManagerImpl
    ): PlayerSessionManager
}
