package com.yu.questicle.core.data.di

import com.yu.questicle.core.data.repository.TetrisGameStateRepositoryImpl
import com.yu.questicle.core.domain.repository.TetrisGameStateRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块：提供Tetris游戏状态仓库的依赖注入绑定
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class TetrisGameStateModule {
    
    @Binds
    @Singleton
    abstract fun bindTetrisGameStateRepository(
        tetrisGameStateRepositoryImpl: TetrisGameStateRepositoryImpl
    ): TetrisGameStateRepository
}
