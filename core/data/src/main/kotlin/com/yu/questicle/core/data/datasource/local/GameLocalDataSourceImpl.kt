package com.yu.questicle.core.data.datasource.local

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Local data source implementation for games
 * 实现本地游戏数据的存储和检索
 */
@Singleton
class GameLocalDataSourceImpl @Inject constructor() : GameLocalDataSource {

    // 内存存储，实际应用中应该使用数据库
    private val games = mutableMapOf<String, Game>()
    private val gameSessions = mutableMapOf<String, MutableList<GameSession>>()
    private val gameStats = mutableMapOf<String, GameStats>()
    
    override suspend fun saveGame(game: Game): Result<Unit> {
        return try {
            games[game.id] = game
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun loadGame(gameId: String): Result<Game> {
        return try {
            val game = games[gameId]
            if (game != null) {
                Result.Success(game)
            } else {
                Result.Error(Exception("Game not found: $gameId").toQuesticleException())
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun deleteGame(gameId: String): Result<Unit> {
        return try {
            games.remove(gameId)
            gameSessions.remove(gameId)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override fun getGamesForPlayer(playerId: String): Flow<List<Game>> {
        return flowOf(
            games.values.filter { game ->
                // 简化实现：假设所有游戏都属于该玩家
                true
            }.toList()
        )
    }

    override fun getGamesByType(playerId: String, gameType: GameType): Flow<List<Game>> {
        return flowOf(
            games.values.filter { game ->
                game.type == gameType
            }.toList()
        )
    }

    override suspend fun searchGames(
        playerId: String?,
        gameType: GameType?,
        minScore: Int?,
        maxScore: Int?,
        limit: Int
    ): Result<List<Game>> {
        return try {
            var filteredGames = games.values.asSequence()

            // 按游戏类型过滤
            if (gameType != null) {
                filteredGames = filteredGames.filter { it.type == gameType }
            }

            // 简化实现：不考虑分数过滤，因为Game模型中没有分数字段

            val result = filteredGames.take(limit).toList()
            Result.Success(result)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun saveGameSession(session: GameSession): Result<Unit> {
        return try {
            val sessions = gameSessions.getOrPut(session.gameId) { mutableListOf() }
            sessions.add(session)
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override fun getGameSessions(gameId: String): Flow<List<GameSession>> {
        return flowOf(gameSessions[gameId]?.toList() ?: emptyList())
    }

    override suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats> {
        return try {
            val statsKey = "${playerId}_${gameType.name}"
            val stats = gameStats[statsKey] ?: GameStats.createEmpty()
            Result.Success(stats)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun saveGameStats(stats: GameStats): Result<Unit> {
        return try {
            val statsKey = "${stats.playerId}_${stats.gameType.name}"
            gameStats[statsKey] = stats
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>> {
        return try {
            val leaderboard = gameStats.values
                .filter { it.gameType == gameType }
                .sortedByDescending { it.bestScore }
                .take(limit)
            Result.Success(leaderboard)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun clearAllData(): Result<Unit> {
        return try {
            games.clear()
            gameSessions.clear()
            gameStats.clear()
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getStorageSize(): Long {
        return try {
            // 简化计算：估算存储大小
            val gamesSize = games.size * 1024L // 假设每个游戏1KB
            val sessionsSize = gameSessions.values.sumOf { it.size } * 512L // 假设每个会话512B
            val statsSize = gameStats.size * 256L // 假设每个统计256B
            gamesSize + sessionsSize + statsSize
        } catch (e: Exception) {
            0L
        }
    }

    override suspend fun optimizeStorage(): Result<Unit> {
        return try {
            // 简化实现：清理过期的游戏会话（超过100个的旧会话）
            gameSessions.values.forEach { sessions ->
                if (sessions.size > 100) {
                    val toRemove = sessions.size - 100
                    repeat(toRemove) {
                        sessions.removeAt(0)
                    }
                }
            }
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}
