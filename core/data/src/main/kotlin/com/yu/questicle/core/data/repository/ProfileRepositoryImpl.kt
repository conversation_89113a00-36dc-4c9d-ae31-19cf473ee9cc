package com.yu.questicle.core.data.repository

import android.net.Uri
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.domain.model.AvatarUploadResponse
import com.yu.questicle.core.domain.model.Gender
import com.yu.questicle.core.domain.model.PasswordResetToken
import com.yu.questicle.core.domain.model.ProfileUpdateRequest
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.repository.ProfileRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.validation.UserValidation
import com.yu.questicle.core.domain.validation.ValidationResult
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ProfileRepository实现类
 * 负责用户资料相关的数据操作
 */
@Singleton
class ProfileRepositoryImpl @Inject constructor(
    private val userRepository: UserRepository
) : ProfileRepository {

    private val logger: QLogger = logger()
    private val mutex = Mutex()
    
    // 内存存储密码重置令牌（生产环境应使用数据库）
    private val passwordResetTokens = mutableMapOf<String, PasswordResetToken>()

    override suspend fun updateProfile(
        userId: String, 
        request: ProfileUpdateRequest
    ): Result<User> = mutex.withLock {
        return try {
            logger.i("开始更新用户资料", mapOf("userId" to userId))
            
            // 验证输入数据
            validateProfileUpdateRequest(request)
            
            // 获取当前用户
            val currentUser = when (val result = userRepository.loadUser(userId)) {
                is Result.Success -> result.data
                is Result.Error -> {
                    logger.e("用户不存在", mapOf("userId" to userId))
                    return Result.Error(result.exception)
                }
                is Result.Loading -> {
                    return Result.Error(IllegalStateException("用户加载中").toQuesticleException())
                }
            }
            
            // 如果邮箱发生变化，检查新邮箱是否已被使用
            val requestEmail = request.email
            if (requestEmail != null && requestEmail != currentUser.email) {
                when (val emailCheckResult = userRepository.getUserByEmail(requestEmail)) {
                    is Result.Success -> {
                        // 邮箱已存在且不是当前用户
                        if (emailCheckResult.data.id != userId) {
                            logger.w("邮箱已被其他用户使用", mapOf("email" to requestEmail))
                            return Result.Error(
                                IllegalArgumentException("邮箱已被其他用户使用")
                                    .toQuesticleException()
                            )
                        }
                    }
                    is Result.Error -> {
                        // 邮箱不存在，可以使用
                    }
                    is Result.Loading -> {
                        return Result.Error(IllegalStateException("邮箱检查中").toQuesticleException())
                    }
                }
            }
            
            // 更新用户信息
            val updatedUser = currentUser.copy(
                displayName = request.displayName ?: currentUser.displayName,
                email = request.email ?: currentUser.email,
                bio = request.bio ?: currentUser.bio,
                birthday = request.birthday ?: currentUser.birthday,
                gender = request.gender ?: currentUser.gender
            )
            
            // 保存更新后的用户
            when (val saveResult = userRepository.updateUser(updatedUser)) {
                is Result.Success -> {
                    logger.i("用户资料更新成功", mapOf("userId" to userId))
                    Result.Success(saveResult.data)
                }
                is Result.Error -> {
                    logger.e("用户资料保存失败", mapOf("userId" to userId))
                    Result.Error(saveResult.exception)
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("用户保存中").toQuesticleException())
                }
            }
            
        } catch (e: Exception) {
            logger.e(e, "更新用户资料失败", mapOf("userId" to userId))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun requestPasswordReset(email: String): Result<Unit> = mutex.withLock {
        return try {
            logger.i("开始密码重置请求", mapOf("email" to email.take(3) + "***"))
            
            // 验证邮箱格式
            val validationResult = UserValidation.validateEmail(email)
            if (validationResult is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("邮箱格式不正确")
                        .toQuesticleException()
                )
            }
            
            // 检查用户是否存在
            when (val userResult = userRepository.getUserByEmail(email)) {
                is Result.Success -> {
                    val user = userResult.data
                    
                    // 生成重置令牌
                    val token = generateResetToken()
                    val resetToken = PasswordResetToken(
                        token = token,
                        userId = user.id,
                        email = email,
                        expiresAt = System.currentTimeMillis() + (15 * 60 * 1000), // 15分钟过期
                        used = false
                    )
                    
                    passwordResetTokens[token] = resetToken
                    
                    // 这里应该发送邮件，目前只是模拟
                    logger.i("密码重置邮件已发送", mapOf("email" to email.take(3) + "***"))
                    
                    Result.Success(Unit)
                }
                is Result.Error -> {
                    logger.w("重置请求的邮箱不存在", mapOf("email" to email.take(3) + "***"))
                    // 出于安全考虑，不暴露邮箱是否存在
                    Result.Success(Unit)
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("用户查询中").toQuesticleException())
                }
            }
            
        } catch (e: Exception) {
            logger.e(e, "密码重置请求失败", mapOf("email" to email.take(3) + "***"))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun confirmPasswordReset(
        token: String, 
        newPassword: String, 
        confirmPassword: String
    ): Result<Unit> = mutex.withLock {
        return try {
            logger.i("开始确认密码重置", mapOf("token" to token.take(8) + "***"))
            
            // 验证密码
            if (newPassword != confirmPassword) {
                return Result.Error(
                    IllegalArgumentException("两次输入的密码不一致")
                        .toQuesticleException()
                )
            }
            
            val passwordValidation = UserValidation.validatePassword(newPassword)
            if (passwordValidation is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException("密码格式不正确: ${passwordValidation.getErrorMessage()}")
                        .toQuesticleException()
                )
            }
            
            // 验证令牌
            when (val tokenResult = validateResetToken(token)) {
                is Result.Success -> {
                    val resetToken = tokenResult.data
                    
                    // 获取用户并更新密码
                    when (val userResult = userRepository.loadUser(resetToken.userId)) {
                        is Result.Success -> {
                            val user = userResult.data
                            
                            // 这里应该对密码进行哈希处理
                            val hashedPassword = hashPassword(newPassword)
                            val updatedUser = user.copy(passwordHash = hashedPassword)
                            
                            when (val saveResult = userRepository.updateUser(updatedUser)) {
                                is Result.Success -> {
                                    // 标记令牌为已使用
                                    passwordResetTokens[token] = resetToken.copy(used = true)
                                    
                                    logger.i("密码重置成功", mapOf("userId" to resetToken.userId))
                                    Result.Success(Unit)
                                }
                                is Result.Error -> {
                                    logger.e("密码更新失败", mapOf("userId" to resetToken.userId))
                                    Result.Error(saveResult.exception)
                                }
                                is Result.Loading -> {
                                    Result.Error(IllegalStateException("用户保存中").toQuesticleException())
                                }
                            }
                        }
                        is Result.Error -> {
                            logger.e("用户不存在", mapOf("userId" to resetToken.userId))
                            Result.Error(userResult.exception)
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("用户加载中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    logger.w("无效的重置令牌", mapOf("token" to token.take(8) + "***"))
                    Result.Error(tokenResult.exception)
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("令牌验证中").toQuesticleException())
                }
            }
            
        } catch (e: Exception) {
            logger.e(e, "密码重置确认失败", mapOf("token" to token.take(8) + "***"))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun validateResetToken(token: String): Result<PasswordResetToken> {
        return try {
            val resetToken = passwordResetTokens[token]
            
            if (resetToken == null) {
                return Result.Error(
                    IllegalArgumentException("无效的重置令牌")
                        .toQuesticleException()
                )
            }
            
            if (resetToken.used) {
                return Result.Error(
                    IllegalArgumentException("重置令牌已被使用")
                        .toQuesticleException()
                )
            }
            
            if (System.currentTimeMillis() > resetToken.expiresAt) {
                return Result.Error(
                    IllegalArgumentException("重置令牌已过期")
                        .toQuesticleException()
                )
            }
            
            Result.Success(resetToken)
            
        } catch (e: Exception) {
            logger.e(e, "令牌验证失败", mapOf("token" to token.take(8) + "***"))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun uploadAvatar(
        userId: String, 
        imageUri: Uri
    ): Result<AvatarUploadResponse> = mutex.withLock {
        return try {
            logger.i("开始上传头像", mapOf("userId" to userId))
            
            // 验证用户存在
            when (val userResult = userRepository.loadUser(userId)) {
                is Result.Success -> {
                    val user = userResult.data
                    
                    // 这里应该实现真正的文件上传逻辑
                    // 目前模拟上传成功
                    val avatarUrl = generateAvatarUrl(userId)
                    val thumbnailUrl = generateThumbnailUrl(userId)
                    
                    // 更新用户头像URL
                    val updatedUser = user.copy(avatarUrl = avatarUrl)
                    when (val saveResult = userRepository.updateUser(updatedUser)) {
                        is Result.Success -> {
                            logger.i("头像上传成功", mapOf("userId" to userId))
                            Result.Success(
                                AvatarUploadResponse(
                                    avatarUrl = avatarUrl,
                                    thumbnailUrl = thumbnailUrl
                                )
                            )
                        }
                        is Result.Error -> {
                            logger.e("头像URL保存失败", mapOf("userId" to userId))
                            Result.Error(saveResult.exception)
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("用户保存中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    logger.e("用户不存在", mapOf("userId" to userId))
                    Result.Error(userResult.exception)
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("用户加载中").toQuesticleException())
                }
            }
            
        } catch (e: Exception) {
            logger.e(e, "头像上传失败", mapOf("userId" to userId))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun deleteAvatar(userId: String): Result<Unit> = mutex.withLock {
        return try {
            logger.i("开始删除头像", mapOf("userId" to userId))
            
            // 验证用户存在
            when (val userResult = userRepository.loadUser(userId)) {
                is Result.Success -> {
                    val user = userResult.data
                    
                    // 删除头像URL
                    val updatedUser = user.copy(avatarUrl = null)
                    when (val saveResult = userRepository.updateUser(updatedUser)) {
                        is Result.Success -> {
                            logger.i("头像删除成功", mapOf("userId" to userId))
                            Result.Success(Unit)
                        }
                        is Result.Error -> {
                            logger.e("头像删除保存失败", mapOf("userId" to userId))
                            Result.Error(saveResult.exception)
                        }
                        is Result.Loading -> {
                            Result.Error(IllegalStateException("用户保存中").toQuesticleException())
                        }
                    }
                }
                is Result.Error -> {
                    logger.e("用户不存在", mapOf("userId" to userId))
                    Result.Error(userResult.exception)
                }
                is Result.Loading -> {
                    Result.Error(IllegalStateException("用户加载中").toQuesticleException())
                }
            }
            
        } catch (e: Exception) {
            logger.e(e, "头像删除失败", mapOf("userId" to userId))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getUserProfile(userId: String): Result<User> {
        return try {
            logger.i("获取用户资料", mapOf("userId" to userId))
            userRepository.loadUser(userId)
        } catch (e: Exception) {
            logger.e(e, "获取用户资料失败", mapOf("userId" to userId))
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * 验证资料更新请求
     */
    private fun validateProfileUpdateRequest(request: ProfileUpdateRequest) {
        // 验证显示名称
        request.displayName?.let { displayName ->
            if (displayName.trim().length < 2) {
                throw IllegalArgumentException("显示名称至少需要2个字符")
            }
            if (displayName.trim().length > 50) {
                throw IllegalArgumentException("显示名称不能超过50个字符")
            }
        }

        // 验证邮箱
        request.email?.let { email ->
            if (email.isNotBlank()) {
                val validationResult = UserValidation.validateEmail(email)
                if (validationResult is ValidationResult.Invalid) {
                    throw IllegalArgumentException("邮箱格式不正确")
                }
            }
        }

        // 验证个人简介
        request.bio?.let { bio ->
            if (bio.trim().length > 500) {
                throw IllegalArgumentException("个人简介不能超过500个字符")
            }
        }

        // 验证生日
        request.birthday?.let { birthday ->
            if (birthday.isNotBlank()) {
                try {
                    val date = LocalDate.parse(birthday, DateTimeFormatter.ISO_LOCAL_DATE)
                    val today = LocalDate.now()
                    if (date.isAfter(today)) {
                        throw IllegalArgumentException("生日不能是未来日期")
                    }
                    if (date.isBefore(today.minusYears(120))) {
                        throw IllegalArgumentException("生日日期不合理")
                    }
                } catch (e: DateTimeParseException) {
                    throw IllegalArgumentException("生日格式不正确，请使用YYYY-MM-DD格式")
                }
            }
        }
    }

    /**
     * 生成重置令牌
     */
    private fun generateResetToken(): String {
        return UUID.randomUUID().toString().replace("-", "")
    }

    /**
     * 密码哈希（简化实现，生产环境应使用BCrypt等安全哈希）
     */
    private fun hashPassword(password: String): String {
        return "hashed_$password" // 简化实现
    }

    /**
     * 生成头像URL（模拟实现）
     */
    private fun generateAvatarUrl(userId: String): String {
        return "https://example.com/avatars/$userId/avatar.jpg"
    }

    /**
     * 生成缩略图URL（模拟实现）
     */
    private fun generateThumbnailUrl(userId: String): String {
        return "https://example.com/avatars/$userId/thumbnail.jpg"
    }
} 