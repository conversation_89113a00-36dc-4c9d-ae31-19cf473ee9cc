package com.yu.questicle.core.data.repository

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.model.UserStats
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.validation.UserValidation
import com.yu.questicle.core.domain.validation.ValidationResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.time.Instant
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 完整的UserRepository实现
 * 实现 REQ-USER-001 到 REQ-USER-015 的所有用户管理功能
 */
@Singleton
class UserRepositoryImpl @Inject constructor() : UserRepository {

    private val logger: QLogger = logger()

    // 内存存储（生产环境应使用数据库）
    private val users = mutableMapOf<String, User>()
    private val userMutex = Mutex()

    // 当前用户状态
    private val _currentUser = MutableStateFlow<User?>(null)

    init {
        // 初始化默认游客用户
        val guestUser = User.createGuest()
        users[guestUser.id] = guestUser
        _currentUser.value = guestUser
    }

    override fun getCurrentUser(): Flow<User?> {
        return _currentUser.asStateFlow()
    }
    
    override suspend fun saveUser(user: User): Result<Unit> = userMutex.withLock {
        return try {
            users[user.id] = user
            if (_currentUser.value?.id == user.id) {
                _currentUser.value = user
            }
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun loadUser(userId: String): Result<User> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())
            Result.Success(user)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun createUserWithBasicInfo(username: String, email: String?, passwordHash: String?): Result<User> = userMutex.withLock {
        return try {
            // 验证输入
            val validationResult = UserValidation.validateRegistration(
                username = username,
                email = email,
                password = "temp123", // 临时密码，实际应用中需要密码参数
                passwordConfirmation = "temp123",
                displayName = username
            )

            if (validationResult is ValidationResult.Invalid) {
                return Result.Error(
                    IllegalArgumentException(validationResult.getErrorMessage())
                        .toQuesticleException()
                )
            }

            // 检查用户名是否已存在
            if (users.values.any { it.username == username }) {
                return Result.Error(
                    IllegalArgumentException("用户名已存在: $username")
                        .toQuesticleException()
                )
            }

            // 检查邮箱是否已存在
            if (!email.isNullOrBlank() && users.values.any { it.email == email }) {
                return Result.Error(
                    IllegalArgumentException("邮箱已存在: $email")
                        .toQuesticleException()
                )
            }

            // 创建新用户
            val newUser = User(
                username = username,
                email = email,
                displayName = username,
                passwordHash = passwordHash,
                level = 1,
                experience = 0,
                coins = 100, // 新用户奖励
                gems = 5,    // 新用户奖励
                createdAt = System.currentTimeMillis() / 1000,
                lastLoginAt = System.currentTimeMillis() / 1000
            )

            users[newUser.id] = newUser
            _currentUser.value = newUser

            Result.Success(newUser)

        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun createGuestUser(): Result<User> = userMutex.withLock {
        return try {
            val guestUser = User.createGuest()
            users[guestUser.id] = guestUser
            _currentUser.value = guestUser
            Result.Success(guestUser)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
    
    override suspend fun updatePreferences(userId: String, preferences: UserPreferences): Result<Unit> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val updatedUser = user.copy(preferences = preferences)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateStats(userId: String, stats: UserStats): Result<Unit> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val updatedUser = user.copy(stats = stats)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun addCoins(userId: String, coins: Long): Result<User> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val updatedUser = user.copy(coins = user.coins + coins)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(updatedUser)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun addGems(userId: String, gems: Long): Result<User> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val updatedUser = user.copy(gems = user.gems + gems)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(updatedUser)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun addAchievement(userId: String, achievementId: String): Result<Unit> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            if (achievementId in user.achievements) {
                return Result.Success(Unit) // 已有该成就
            }

            val updatedAchievements = user.achievements + achievementId
            val updatedUser = user.copy(achievements = updatedAchievements)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun addFriend(userId: String, friendId: String): Result<Unit> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val friend = users[friendId]
                ?: return Result.Error(Exception("好友不存在: $friendId").toQuesticleException())

            if (friendId in user.friends) {
                return Result.Success(Unit) // 已是好友
            }

            if (userId == friendId) {
                return Result.Error(Exception("不能添加自己为好友").toQuesticleException())
            }

            val updatedFriends = user.friends + friendId
            val updatedUser = user.copy(friends = updatedFriends)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun removeFriend(userId: String, friendId: String): Result<Unit> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val updatedFriends = user.friends - friendId
            val updatedUser = user.copy(friends = updatedFriends)
            users[userId] = updatedUser

            if (_currentUser.value?.id == userId) {
                _currentUser.value = updatedUser
            }

            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getFriends(userId: String): Result<List<User>> = userMutex.withLock {
        return try {
            val user = users[userId]
                ?: return Result.Error(Exception("用户不存在: $userId").toQuesticleException())

            val friends = user.friends.mapNotNull { friendId ->
                users[friendId]
            }

            Result.Success(friends)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun findUsersByLevel(level: Int): Result<List<User>> = userMutex.withLock {
        return try {
            val matchingUsers = users.values.filter { it.level == level }.toList()
            Result.Success(matchingUsers)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun searchUsers(query: String): Result<List<User>> = userMutex.withLock {
        return try {
            val lowercaseQuery = query.lowercase()
            val matchingUsers = users.values
                .filter { user ->
                    user.username.lowercase().contains(lowercaseQuery) ||
                    user.displayName.lowercase().contains(lowercaseQuery) ||
                    user.email?.lowercase()?.contains(lowercaseQuery) == true
                }
                .take(10) // Using a default limit
                .toList()

            Result.Success(matchingUsers)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getUserLeaderboard(limit: Int): Result<List<User>> = userMutex.withLock {
        return try {
            val topUsers = users.values
                .sortedWith(compareByDescending<User> { it.level }
                    .thenByDescending { it.experience }
                    .thenByDescending { it.stats.totalScore })
                .take(limit)
                .toList()

            Result.Success(topUsers)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun deleteUser(userId: String): Result<Unit> = userMutex.withLock {
        return try {
            if (users.remove(userId) != null) {
                if (_currentUser.value?.id == userId) {
                    _currentUser.value = null
                }
                Result.Success(Unit)
            } else {
                Result.Error(Exception("用户不存在: $userId").toQuesticleException())
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    // 新增的抽象方法实现
    override suspend fun createUser(user: User): Result<User> = userMutex.withLock {
        return try {
            logger.withContext(mapOf(
                "userId" to user.id,
                "username" to user.username,
                "email" to user.email
            )).i("开始创建用户")

            // 检查用户名是否已存在
            if (users.values.any { it.username == user.username }) {
                logger.w("用户名已存在", mapOf("username" to user.username))
                return Result.Error(
                    IllegalArgumentException("用户名已存在: ${user.username}")
                        .toQuesticleException()
                )
            }

            // 检查邮箱是否已存在
            if (!user.email.isNullOrBlank() && users.values.any { it.email == user.email }) {
                logger.w("邮箱已存在", mapOf("email" to user.email))
                return Result.Error(
                    IllegalArgumentException("邮箱已存在: ${user.email}")
                        .toQuesticleException()
                )
            }

            users[user.id] = user
            logger.i("用户创建成功", mapOf("userId" to user.id))
            Result.Success(user)
        } catch (e: Exception) {
            logger.e(e, "用户创建失败", mapOf("userId" to user.id))
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getUserByUsername(username: String): Result<User> = userMutex.withLock {
        return try {
            val user = users.values.find { it.username == username }
                ?: return Result.Error(Exception("用户不存在: $username").toQuesticleException())
            Result.Success(user)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getUserByEmail(email: String): Result<User> = userMutex.withLock {
        return try {
            val user = users.values.find { it.email == email }
                ?: return Result.Error(Exception("邮箱不存在: $email").toQuesticleException())
            Result.Success(user)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun setCurrentUser(user: User): Result<Unit> = userMutex.withLock {
        return try {
            _currentUser.value = user
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun clearCurrentUser(): Result<Unit> = userMutex.withLock {
        return try {
            _currentUser.value = null
            Result.Success(Unit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateUser(user: User): Result<User> = userMutex.withLock {
        return try {
            users[user.id] = user
            if (_currentUser.value?.id == user.id) {
                _currentUser.value = user
            }
            Result.Success(user)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}
