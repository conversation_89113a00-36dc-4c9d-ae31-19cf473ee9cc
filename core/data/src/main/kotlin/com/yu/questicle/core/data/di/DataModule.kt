package com.yu.questicle.core.data.di

import com.yu.questicle.core.data.datasource.local.GameLocalDataSource
import com.yu.questicle.core.data.datasource.local.GameLocalDataSourceImpl
import com.yu.questicle.core.data.datasource.remote.GameRemoteDataSource
import com.yu.questicle.core.data.datasource.remote.GameRemoteDataSourceImpl
import com.yu.questicle.core.data.repository.GameRepositoryImpl
import com.yu.questicle.core.data.repository.UserRepositoryImpl
import com.yu.questicle.core.data.repository.UserPreferencesRepositoryImpl
import com.yu.questicle.core.data.repository.ProfileRepositoryImpl
import com.yu.questicle.core.data.repository.FileRepositoryImpl
import com.yu.questicle.core.domain.repository.GameRepository
import com.yu.questicle.core.domain.repository.UserRepository
import com.yu.questicle.core.domain.repository.UserPreferencesRepository
import com.yu.questicle.core.domain.repository.ProfileRepository
import com.yu.questicle.core.domain.repository.FileRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {

    @Binds
    @Singleton
    abstract fun bindGameRepository(
        gameRepositoryImpl: GameRepositoryImpl
    ): GameRepository

    @Binds
    @Singleton
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository

    @Binds
    @Singleton
    abstract fun bindUserPreferencesRepository(
        userPreferencesRepositoryImpl: UserPreferencesRepositoryImpl
    ): UserPreferencesRepository

    @Binds
    @Singleton
    abstract fun bindProfileRepository(
        profileRepositoryImpl: ProfileRepositoryImpl
    ): ProfileRepository

    @Binds
    @Singleton
    abstract fun bindFileRepository(
        fileRepositoryImpl: FileRepositoryImpl
    ): FileRepository

    @Binds
    @Singleton
    abstract fun bindGameLocalDataSource(
        gameLocalDataSourceImpl: GameLocalDataSourceImpl
    ): GameLocalDataSource

    @Binds
    @Singleton
    abstract fun bindGameRemoteDataSource(
        gameRemoteDataSourceImpl: GameRemoteDataSourceImpl
    ): GameRemoteDataSource
}
