package com.yu.questicle.core.data.datasource.remote

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType

/**
 * Remote data source interface for game data
 */
interface GameRemoteDataSource {
    
    /**
     * Save game to remote server
     */
    suspend fun saveGame(game: Game): Result<Unit>
    
    /**
     * Load game from remote server
     */
    suspend fun loadGame(gameId: String): Result<Game>
    
    /**
     * Delete game from remote server
     */
    suspend fun deleteGame(gameId: String): Result<Unit>
    
    /**
     * Save game session to remote server
     */
    suspend fun saveGameSession(session: GameSession): Result<Unit>
    
    /**
     * Get game statistics from remote server
     */
    suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats>
    
    /**
     * Update game statistics on remote server
     */
    suspend fun updateGameStats(stats: GameStats): Result<Unit>
    
    /**
     * Get global leaderboard from remote server
     */
    suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>>
    
    /**
     * Sync local data to remote server
     */
    suspend fun syncToRemote(games: List<Game>): Result<Unit>
    
    /**
     * Sync remote data to local
     */
    suspend fun syncFromRemote(playerId: String): Result<List<Game>>
    
    /**
     * Check server connectivity
     */
    suspend fun checkConnectivity(): Boolean
    
    /**
     * Get server status
     */
    suspend fun getServerStatus(): Result<ServerStatus>
}

/**
 * Server status information
 */
data class ServerStatus(
    val isOnline: Boolean,
    val version: String,
    val maintenanceMode: Boolean,
    val message: String? = null
)
