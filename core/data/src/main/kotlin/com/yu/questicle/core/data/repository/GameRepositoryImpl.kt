package com.yu.questicle.core.data.repository

import com.yu.questicle.core.common.di.Dispatcher
import com.yu.questicle.core.common.di.QuesticleDispatchers
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.logger
import com.yu.questicle.core.data.datasource.local.GameLocalDataSource
import com.yu.questicle.core.data.datasource.remote.GameRemoteDataSource
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.repository.GameRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GameRepositoryImpl @Inject constructor(
    private val localDataSource: GameLocalDataSource,
    private val remoteDataSource: GameRemoteDataSource,
    @Dispatcher(QuesticleDispatchers.IO) private val ioDispatcher: CoroutineDispatcher
) : GameRepository {

    private val logger: QLogger = logger()

    override suspend fun saveGame(game: Game): Result<Unit> = withContext(ioDispatcher) {
        try {
            logger.withContext(mapOf(
                "gameId" to game.id,
                "gameType" to game.type.name,
                "playerId" to game.playerId
            )).i("开始保存游戏")

            // Save locally first
            when (val localResult = localDataSource.saveGame(game)) {
                is Result.Error -> return@withContext Result.Error(localResult.exception)
                is Result.Loading -> return@withContext Result.Error(IllegalStateException("Local save in progress").toQuesticleException())
                is Result.Success -> {
                    // Try to sync to remote (best effort)
                    try {
                        remoteDataSource.saveGame(game)
                    } catch (e: Exception) {
                        // Remote sync failed, but local save succeeded
                        // This is acceptable for offline-first approach
                    }
                    Result.Success(Unit)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun loadGame(gameId: String): Result<Game> = withContext(ioDispatcher) {
        try {
            // Try local first
            when (val localResult = localDataSource.loadGame(gameId)) {
                is Result.Success -> Result.Success(localResult.data)
                is Result.Error -> {
                    // Try remote if local fails
                    when (val remoteResult = remoteDataSource.loadGame(gameId)) {
                        is Result.Success -> {
                            // Cache locally
                            localDataSource.saveGame(remoteResult.data)
                            Result.Success(remoteResult.data)
                        }
                        is Result.Error -> Result.Error(remoteResult.exception)
                        is Result.Loading -> Result.Error(IllegalStateException("Remote load in progress").toQuesticleException())
                    }
                }
                is Result.Loading -> Result.Error(IllegalStateException("Local load in progress").toQuesticleException())
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override fun getGamesForPlayer(playerId: String): Flow<List<Game>> {
        return localDataSource.getGamesForPlayer(playerId)
            .flowOn(ioDispatcher)
    }

    override fun getGamesByType(playerId: String, gameType: GameType): Flow<List<Game>> {
        return localDataSource.getGamesByType(playerId, gameType)
            .flowOn(ioDispatcher)
    }

    override suspend fun deleteGame(gameId: String): Result<Unit> = withContext(ioDispatcher) {
        try {
            // Delete locally
            when (val localResult = localDataSource.deleteGame(gameId)) {
                is Result.Error -> return@withContext Result.Error(localResult.exception)
                is Result.Loading -> return@withContext Result.Error(IllegalStateException("Local delete in progress").toQuesticleException())
                is Result.Success -> {
                    // Try to delete from remote (best effort)
                    try {
                        remoteDataSource.deleteGame(gameId)
                    } catch (e: Exception) {
                        // Remote delete failed, but local delete succeeded
                    }
                    Result.Success(Unit)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun saveGameSession(session: GameSession): Result<Unit> = withContext(ioDispatcher) {
        try {
            when (val localResult = localDataSource.saveGameSession(session)) {
                is Result.Error -> return@withContext Result.Error(localResult.exception)
                is Result.Loading -> return@withContext Result.Error(IllegalStateException("Local save in progress").toQuesticleException())
                is Result.Success -> {
                    // Try to sync to remote (best effort)
                    try {
                        remoteDataSource.saveGameSession(session)
                    } catch (e: Exception) {
                        // Remote sync failed, but local save succeeded
                    }
                    Result.Success(Unit)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override fun getGameSessions(gameId: String): Flow<List<GameSession>> {
        return localDataSource.getGameSessions(gameId)
            .flowOn(ioDispatcher)
    }

    override suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats> = withContext(ioDispatcher) {
        try {
            // Try local first
            when (val localResult = localDataSource.getGameStats(playerId, gameType)) {
                is Result.Success -> Result.Success(localResult.data)
                is Result.Error -> {
                    // Try remote if local fails
                    when (val remoteResult = remoteDataSource.getGameStats(playerId, gameType)) {
                        is Result.Success -> {
                            // Cache locally
                            localDataSource.saveGameStats(remoteResult.data)
                            Result.Success(remoteResult.data)
                        }
                        is Result.Error -> Result.Error(remoteResult.exception)
                        is Result.Loading -> Result.Error(IllegalStateException("Remote load in progress").toQuesticleException())
                    }
                }
                is Result.Loading -> Result.Error(IllegalStateException("Local load in progress").toQuesticleException())
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun updateGameStats(stats: GameStats): Result<Unit> = withContext(ioDispatcher) {
        try {
            when (val localResult = localDataSource.saveGameStats(stats)) {
                is Result.Error -> return@withContext Result.Error(localResult.exception)
                is Result.Loading -> return@withContext Result.Error(IllegalStateException("Local save in progress").toQuesticleException())
                is Result.Success -> {
                    // Try to sync to remote (best effort)
                    try {
                        remoteDataSource.updateGameStats(stats)
                    } catch (e: Exception) {
                        // Remote sync failed, but local save succeeded
                    }
                    Result.Success(Unit)
                }
            }
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }

    override suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>> = withContext(ioDispatcher) {
        try {
            // Leaderboard should come from remote
            remoteDataSource.getLeaderboard(gameType, limit)
        } catch (e: Exception) {
            // Fallback to local leaderboard
            localDataSource.getLeaderboard(gameType, limit)
        }
    }

    override suspend fun searchGames(
        playerId: String?,
        gameType: GameType?,
        minScore: Int?,
        maxScore: Int?,
        limit: Int
    ): Result<List<Game>> = withContext(ioDispatcher) {
        try {
            localDataSource.searchGames(playerId, gameType, minScore, maxScore, limit)
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
    }
}
