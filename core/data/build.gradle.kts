plugins {
    id("questicle.android.library")
    id("questicle.hilt")
    kotlin("plugin.serialization")
}

android {
    namespace = "com.yu.questicle.core.data"
}

dependencies {
    implementation(project(":core:common"))
    implementation(project(":core:domain"))
    implementation(project(":core:database"))
    // 移除不必要的designsystem依赖 - data层不应该依赖UI层
    // implementation(project(":core:designsystem"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.kotlinx.serialization.json)

    // 标准化测试依赖
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)
}
