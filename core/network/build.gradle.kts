plugins {
    id("questicle.android.library")
    id("questicle.hilt")
    kotlin("plugin.serialization")
}

android {
    namespace = "com.yu.questicle.core.network"
}

dependencies {
    implementation(project(":core:common"))
    implementation(project(":core:domain"))

    // 网络依赖 - 使用bundle
    implementation(libs.bundles.network)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.kotlinx.coroutines.android)

    // 标准化测试依赖
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)
    testImplementation(libs.okhttp.mockwebserver)
}
