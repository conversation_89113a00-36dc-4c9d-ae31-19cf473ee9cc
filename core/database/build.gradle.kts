plugins {
    id("questicle.android.library")
    id("questicle.hilt")
    id("questicle.android.room")
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.android.junit5)
}

android {
    namespace = "com.yu.questicle.core.database"
}

dependencies {
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // 标准化测试依赖 - 由 convention plugin 统一管理
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)
    testImplementation(libs.androidx.room.testing)

    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    implementation(libs.kotlinx.serialization.json)

    // 网络依赖
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
}
