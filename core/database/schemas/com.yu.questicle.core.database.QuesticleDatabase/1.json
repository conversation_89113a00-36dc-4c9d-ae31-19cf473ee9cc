{"formatVersion": 1, "database": {"version": 1, "identityHash": "296a4e0735ecd836acb441ba54dcdfb4", "entities": [{"tableName": "games", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `type` TEXT NOT NULL, `status` TEXT NOT NULL, `player_id` TEXT NOT NULL, `score` INTEGER NOT NULL, `level` INTEGER NOT NULL, `start_time` INTEGER NOT NULL, `end_time` INTEGER, `duration` INTEGER NOT NULL, `metadata` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "playerId", "columnName": "player_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "score", "columnName": "score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startTime", "columnName": "start_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "end_time", "affinity": "INTEGER"}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_games_player_id", "unique": false, "columnNames": ["player_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_games_player_id` ON `${TABLE_NAME}` (`player_id`)"}, {"name": "index_games_type", "unique": false, "columnNames": ["type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_games_type` ON `${TABLE_NAME}` (`type`)"}, {"name": "index_games_status", "unique": false, "columnNames": ["status"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_games_status` ON `${TABLE_NAME}` (`status`)"}, {"name": "index_games_start_time", "unique": false, "columnNames": ["start_time"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_games_start_time` ON `${TABLE_NAME}` (`start_time`)"}, {"name": "index_games_score", "unique": false, "columnNames": ["score"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_games_score` ON `${TABLE_NAME}` (`score`)"}]}, {"tableName": "game_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `game_id` TEXT NOT NULL, `player_id` TEXT NOT NULL, `start_time` INTEGER NOT NULL, `end_time` INTEGER, `actions` TEXT NOT NULL, `achievements` TEXT NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`game_id`) REFERENCES `games`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameId", "columnName": "game_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "playerId", "columnName": "player_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startTime", "columnName": "start_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "end_time", "affinity": "INTEGER"}, {"fieldPath": "actions", "columnName": "actions", "affinity": "TEXT", "notNull": true}, {"fieldPath": "achievements", "columnName": "achievements", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_game_sessions_game_id", "unique": false, "columnNames": ["game_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_sessions_game_id` ON `${TABLE_NAME}` (`game_id`)"}, {"name": "index_game_sessions_player_id", "unique": false, "columnNames": ["player_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_sessions_player_id` ON `${TABLE_NAME}` (`player_id`)"}, {"name": "index_game_sessions_start_time", "unique": false, "columnNames": ["start_time"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_sessions_start_time` ON `${TABLE_NAME}` (`start_time`)"}], "foreignKeys": [{"table": "games", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["game_id"], "referencedColumns": ["id"]}]}, {"tableName": "game_stats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `game_type` TEXT NOT NULL, `player_id` TEXT NOT NULL, `total_games` INTEGER NOT NULL, `total_score` INTEGER NOT NULL, `best_score` INTEGER NOT NULL, `average_score` REAL NOT NULL, `total_play_time` INTEGER NOT NULL, `achievements` TEXT NOT NULL, `last_played` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameType", "columnName": "game_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "playerId", "columnName": "player_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalGames", "columnName": "total_games", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalScore", "columnName": "total_score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bestScore", "columnName": "best_score", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "averageScore", "columnName": "average_score", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalPlayTime", "columnName": "total_play_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "achievements", "columnName": "achievements", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastPlayed", "columnName": "last_played", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_game_stats_player_id_game_type", "unique": true, "columnNames": ["player_id", "game_type"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_game_stats_player_id_game_type` ON `${TABLE_NAME}` (`player_id`, `game_type`)"}, {"name": "index_game_stats_best_score", "unique": false, "columnNames": ["best_score"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_stats_best_score` ON `${TABLE_NAME}` (`best_score`)"}, {"name": "index_game_stats_total_score", "unique": false, "columnNames": ["total_score"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_stats_total_score` ON `${TABLE_NAME}` (`total_score`)"}, {"name": "index_game_stats_last_played", "unique": false, "columnNames": ["last_played"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_game_stats_last_played` ON `${TABLE_NAME}` (`last_played`)"}]}, {"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `username` TEXT NOT NULL, `email` TEXT, `display_name` TEXT NOT NULL, `avatar_url` TEXT, `level` INTEGER NOT NULL, `experience` INTEGER NOT NULL, `coins` INTEGER NOT NULL, `gems` INTEGER NOT NULL, `preferences` TEXT NOT NULL, `stats` TEXT NOT NULL, `achievements` TEXT NOT NULL, `friends` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `last_login_at` INTEGER NOT NULL, `is_active` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT"}, {"fieldPath": "displayName", "columnName": "display_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatarUrl", "columnName": "avatar_url", "affinity": "TEXT"}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "experience", "columnName": "experience", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "coins", "columnName": "coins", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gems", "columnName": "gems", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "preferences", "columnName": "preferences", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stats", "columnName": "stats", "affinity": "TEXT", "notNull": true}, {"fieldPath": "achievements", "columnName": "achievements", "affinity": "TEXT", "notNull": true}, {"fieldPath": "friends", "columnName": "friends", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastLoginAt", "columnName": "last_login_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActive", "columnName": "is_active", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_users_username", "unique": true, "columnNames": ["username"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_users_username` ON `${TABLE_NAME}` (`username`)"}, {"name": "index_users_email", "unique": true, "columnNames": ["email"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_users_email` ON `${TABLE_NAME}` (`email`)"}, {"name": "index_users_level", "unique": false, "columnNames": ["level"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_level` ON `${TABLE_NAME}` (`level`)"}, {"name": "index_users_experience", "unique": false, "columnNames": ["experience"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_experience` ON `${TABLE_NAME}` (`experience`)"}, {"name": "index_users_last_login_at", "unique": false, "columnNames": ["last_login_at"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_users_last_login_at` ON `${TABLE_NAME}` (`last_login_at`)"}]}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '296a4e0735ecd836acb441ba54dcdfb4')"]}}