package com.yu.questicle.core.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.yu.questicle.core.database.dao.GameDao
import com.yu.questicle.core.database.dao.GameSessionDao
import com.yu.questicle.core.database.dao.GameStatsDao
import com.yu.questicle.core.database.dao.UserDao
import com.yu.questicle.core.database.entity.GameEntity
import com.yu.questicle.core.database.entity.GameSessionEntity
import com.yu.questicle.core.database.entity.GameStatsEntity
import com.yu.questicle.core.database.entity.UserEntity
import com.yu.questicle.core.database.util.Converters

@Database(
    entities = [
        GameEntity::class,
        GameSessionEntity::class,
        GameStatsEntity::class,
        UserEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class QuesticleDatabase : RoomDatabase() {
    
    abstract fun gameDao(): GameDao
    abstract fun gameSessionDao(): GameSessionDao
    abstract fun gameStatsDao(): GameStatsDao
    abstract fun userDao(): UserDao
    
    companion object {
        const val DATABASE_NAME = "questicle_database"
        
        fun create(context: Context): QuesticleDatabase {
            return Room.databaseBuilder(
                context,
                QuesticleDatabase::class.java,
                DATABASE_NAME
            )
                .fallbackToDestructiveMigration(false)
            .build()
        }
    }
}
