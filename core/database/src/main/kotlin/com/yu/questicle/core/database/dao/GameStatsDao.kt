package com.yu.questicle.core.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.yu.questicle.core.database.entity.GameStatsEntity

@Dao
interface GameStatsDao {
    
    @Query("SELECT * FROM game_stats WHERE player_id = :playerId AND game_type = :gameType")
    suspend fun getStatsForPlayerAndType(playerId: String, gameType: String): GameStatsEntity?
    
    @Query("SELECT * FROM game_stats WHERE player_id = :playerId")
    suspend fun getStatsForPlayer(playerId: String): List<GameStatsEntity>
    
    @Query("SELECT * FROM game_stats WHERE game_type = :gameType ORDER BY best_score DESC LIMIT :limit")
    suspend fun getLeaderboardForType(gameType: String, limit: Int): List<GameStatsEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStats(stats: GameStatsEntity)
    
    @Update
    suspend fun updateStats(stats: GameStatsEntity)
    
    @Query("DELETE FROM game_stats WHERE player_id = :playerId")
    suspend fun deleteStatsForPlayer(playerId: String)
}
