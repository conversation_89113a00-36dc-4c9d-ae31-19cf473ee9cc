package com.yu.questicle.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameStatus
import com.yu.questicle.core.domain.model.GameType

@Entity(
    tableName = "games",
    indices = [
        Index(value = ["player_id"]),
        Index(value = ["type"]),
        Index(value = ["status"]),
        Index(value = ["start_time"]),
        Index(value = ["score"])
    ]
)
data class GameEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "type")
    val type: String,
    
    @ColumnInfo(name = "status")
    val status: String,
    
    @ColumnInfo(name = "player_id")
    val playerId: String,
    
    @ColumnInfo(name = "score")
    val score: Int = 0,
    
    @ColumnInfo(name = "level")
    val level: Int = 1,
    
    @ColumnInfo(name = "start_time")
    val startTime: Long,
    
    @ColumnInfo(name = "end_time")
    val endTime: Long? = null,
    
    @ColumnInfo(name = "duration")
    val duration: Long = 0,
    
    @ColumnInfo(name = "metadata")
    val metadata: String = "{}" // JSON string
)

fun GameEntity.toDomain(): Game {
    return Game(
        id = id,
        type = GameType.fromString(type) ?: GameType.TETRIS,
        status = GameStatus.valueOf(status),
        playerId = playerId,
        score = score,
        level = level,
        startTime = startTime,
        endTime = endTime,
        duration = duration,
        metadata = parseMetadata(metadata)
    )
}

fun Game.toEntity(): GameEntity {
    return GameEntity(
        id = id,
        type = type.name,
        status = status.name,
        playerId = playerId,
        score = score,
        level = level,
        startTime = startTime,
        endTime = endTime,
        duration = duration,
        metadata = serializeMetadata(metadata)
    )
}

private fun parseMetadata(json: String): Map<String, String> {
    return try {
        if (json.isBlank() || json == "{}") {
            emptyMap()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<Map<String, String>>(json)
        }
    } catch (e: Exception) {
        emptyMap()
    }
}

private fun serializeMetadata(metadata: Map<String, String>): String {
    return try {
        if (metadata.isEmpty()) {
            "{}"
        } else {
            // 简化实现：手动构建JSON
            val jsonEntries = metadata.entries.joinToString(",") { (key, value) ->
                "\"$key\":\"$value\""
            }
            "{$jsonEntries}"
        }
    } catch (e: Exception) {
        "{}"
    }
}
