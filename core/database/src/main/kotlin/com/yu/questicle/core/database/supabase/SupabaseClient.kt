package com.yu.questicle.core.database.supabase

import android.content.Context
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.database.model.SupabasePerformanceRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Supabase 客户端
 * 
 * 负责与 Supabase 后端服务的通信
 */
@Singleton
class SupabaseClient @Inject constructor(
    private val context: Context
) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(SupabaseClient::class)
    
    // Supabase 配置
    private var config = SupabaseConfig()
    
    // HTTP 客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .addInterceptor(AuthInterceptor())
        .addInterceptor(LoggingInterceptor())
        .build()
    
    // JSON 序列化器
    private val json = Json {
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    init {
        loadConfig()
    }
    
    /**
     * 加载配置
     */
    private fun loadConfig() {
        // 从 BuildConfig 或配置文件中读取
        config = SupabaseConfig(
            url = getConfigValue("SUPABASE_URL", "https://whwbfadkzbmkgxlxxjiv.supabase.co"),
            anonKey = getConfigValue("SUPABASE_ANON_KEY", ""),
            serviceRoleKey = getConfigValue("SUPABASE_SERVICE_ROLE_KEY", "")
        )
        
        logger.i("Supabase client configured with URL: ${config.url}")
    }
    
    /**
     * 插入崩溃报告
     */
    suspend fun insertCrashReport(crashReport: SupabaseCrashReport): Result<String> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/crash_reports"
            val requestBody = json.encodeToString(crashReport)
            
            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                logger.d("Crash report inserted successfully: ${crashReport.id}")
                Result.Success(crashReport.id)
            } else {
                val errorMessage = "Failed to insert crash report: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.Error(Exception(errorMessage).toQuesticleException())
            }

        } catch (e: Exception) {
            logger.e("Error inserting crash report", e)
            Result.Error(e.toQuesticleException())
        }
    }
    
    /**
     * 插入性能指标
     */
    suspend fun insertPerformanceMetric(performanceRecord: SupabasePerformanceRecord): Result<String> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/performance_metrics"
            val requestBody = json.encodeToString(performanceRecord)

            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                logger.d("Performance metric inserted successfully: ${performanceRecord.id}")
                Result.Success(performanceRecord.id)
            } else {
                val errorMessage = "Failed to insert performance metric: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.Error(Exception(errorMessage).toQuesticleException())
            }

        } catch (e: Exception) {
            logger.e("Error inserting performance metric", e)
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * 批量插入性能指标
     */
    suspend fun insertPerformanceMetrics(performanceRecords: List<SupabasePerformanceRecord>): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/performance_metrics"
            val requestBody = json.encodeToString(performanceRecords)

            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                val ids = performanceRecords.map { it.id }
                logger.d("Batch performance metrics inserted successfully: ${ids.size} records")
                Result.Success(ids)
            } else {
                val errorMessage = "Failed to insert batch performance metrics: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.Error(Exception(errorMessage).toQuesticleException())
            }

        } catch (e: Exception) {
            logger.e("Error inserting batch performance metrics", e)
            Result.Error(e.toQuesticleException())
        }
    }

    /**
     * 设置配置
     */
    fun setConfig(newConfig: SupabaseConfig) {
        this.config = newConfig
        logger.i("Supabase config updated")
    }
    
    /**
     * 获取配置值
     */
    private fun getConfigValue(key: String, defaultValue: String): String {
        // 这里应该从 BuildConfig、SharedPreferences 或其他安全存储中读取
        return defaultValue
    }
    
    /**
     * 认证拦截器
     */
    private inner class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            val authenticatedRequest = originalRequest.newBuilder()
                .addHeader("apikey", config.anonKey)
                .addHeader("Authorization", "Bearer ${config.anonKey}")
                .build()
            
            return chain.proceed(authenticatedRequest)
        }
    }
    
    /**
     * 日志拦截器
     */
    private inner class LoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val startTime = System.currentTimeMillis()
            
            logger.d("Supabase API request: ${request.method} ${request.url}")
            
            val response = chain.proceed(request)
            val endTime = System.currentTimeMillis()
            
            logger.d("Supabase API response: ${response.code} in ${endTime - startTime}ms")
            
            return response
        }
    }
}

/**
 * Supabase 配置
 */
data class SupabaseConfig(
    val url: String = "",
    val anonKey: String = "",
    val serviceRoleKey: String = ""
)

/**
 * Supabase 崩溃报告数据结构
 */
@Serializable
data class SupabaseCrashReport(
    val id: String,
    val timestamp: Long,
    val app_version: String,
    val user_id: String?,
    val exception_type: String,
    val exception_message: String,
    val stack_trace: String,
    val error_code: String,
    val severity: String,
    val context: String, // JSON 字符串
    val thread: String,
    val is_fatal: Boolean,
    val device_info: String, // JSON 字符串
    val created_at: String // ISO 8601 格式
)

/**
 * 用户操作日志
 */
@Serializable
data class UserActionLog(
    val id: String,
    val crash_report_id: String?,
    val user_id: String?,
    val action: String,
    val parameters: String, // JSON 字符串
    val timestamp: Long
)

/**
 * API 响应数据类
 */
@Serializable
data class CrashStatisticsResponse(
    val total_crashes: Long = 0,
    val fatal_crashes: Long = 0,
    val unique_users: Long = 0,
    val top_exception_type: String? = null,
    val crash_free_rate: Double = 100.0
)
