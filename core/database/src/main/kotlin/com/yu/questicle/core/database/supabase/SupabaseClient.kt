package com.yu.questicle.core.database.supabase

import android.content.Context
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import com.yu.questicle.core.database.model.SupabasePerformanceRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Supabase 客户端
 * 
 * 负责与 Supabase 后端服务的通信
 */
@Singleton
class SupabaseClient @Inject constructor(
    private val context: Context
) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(SupabaseClient::class)
    
    // Supabase 配置
    private var config = SupabaseConfig()
    
    // HTTP 客户端
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .addInterceptor(AuthInterceptor())
        .addInterceptor(LoggingInterceptor())
        .build()
    
    // JSON 序列化器
    private val json = Json {
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    init {
        loadConfig()
    }
    
    /**
     * 加载配置
     */
    private fun loadConfig() {
        // 从 BuildConfig 或配置文件中读取
        config = SupabaseConfig(
            url = getConfigValue("SUPABASE_URL", "https://whwbfadkzbmkgxlxxjiv.supabase.co"),
            anonKey = getConfigValue("SUPABASE_ANON_KEY", ""),
            serviceRoleKey = getConfigValue("SUPABASE_SERVICE_ROLE_KEY", "")
        )
        
        logger.i("Supabase client configured with URL: ${config.url}")
    }
    
    /**
     * 插入崩溃报告
     */
    suspend fun insertCrashReport(crashReport: SupabaseCrashReport): Result<String> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/crash_reports"
            val requestBody = json.encodeToString(crashReport)
            
            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                logger.d("Crash report inserted successfully: ${crashReport.id}")
                Result.success(crashReport.id)
            } else {
                val errorMessage = "Failed to insert crash report: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }
            
        } catch (e: Exception) {
            logger.e("Error inserting crash report", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量插入崩溃报告
     */
    suspend fun insertCrashReports(crashReports: List<SupabaseCrashReport>): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/crash_reports"
            val requestBody = json.encodeToString(crashReports)
            
            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val ids = crashReports.map { it.id }
                logger.d("Batch crash reports inserted successfully: ${ids.size} reports")
                Result.success(ids)
            } else {
                val errorMessage = "Failed to insert batch crash reports: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }
            
        } catch (e: Exception) {
            logger.e("Error inserting batch crash reports", e)
            Result.failure(e)
        }
    }
    
    /**
     * 查询崩溃统计
     */
    suspend fun getCrashStatistics(
        startDate: String? = null,
        endDate: String? = null
    ): Result<CrashStatisticsResponse> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/rpc/get_crash_statistics"
            val params = mutableMapOf<String, String>()
            
            startDate?.let { params["p_start_date"] = it }
            endDate?.let { params["p_end_date"] = it }
            
            val httpUrl = url.toHttpUrl()
            val urlBuilder = httpUrl.newBuilder()
            params.forEach { (key, value) ->
                urlBuilder.addQueryParameter(key, value)
            }

            val request = Request.Builder()
                .url(urlBuilder.build())
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string() ?: "[]"
                val statistics = json.decodeFromString<List<CrashStatisticsResponse>>(responseBody)
                Result.success(statistics.firstOrNull() ?: CrashStatisticsResponse())
            } else {
                val errorMessage = "Failed to get crash statistics: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }
            
        } catch (e: Exception) {
            logger.e("Error getting crash statistics", e)
            Result.failure(e)
        }
    }
    
    /**
     * 记录用户操作
     */
    suspend fun logUserAction(
        crashReportId: String?,
        userId: String?,
        action: String,
        parameters: Map<String, Any?>,
        timestamp: Long
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val userActionLog = UserActionLog(
                id = java.util.UUID.randomUUID().toString(),
                crash_report_id = crashReportId,
                user_id = userId,
                action = action,
                parameters = json.encodeToString(parameters),
                timestamp = timestamp
            )
            
            val url = "${config.url}/rest/v1/user_action_logs"
            val requestBody = json.encodeToString(userActionLog)
            
            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                logger.d("User action logged successfully: $action")
                Result.success(userActionLog.id)
            } else {
                val errorMessage = "Failed to log user action: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }
            
        } catch (e: Exception) {
            logger.e("Error logging user action", e)
            Result.failure(e)
        }
    }
    
    /**
     * 插入性能指标
     */
    suspend fun insertPerformanceMetric(performanceRecord: SupabasePerformanceRecord): Result<String> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/performance_metrics"
            val requestBody = json.encodeToString(performanceRecord)

            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                logger.d("Performance metric inserted successfully: ${performanceRecord.id}")
                Result.success(performanceRecord.id)
            } else {
                val errorMessage = "Failed to insert performance metric: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }

        } catch (e: Exception) {
            logger.e("Error inserting performance metric", e)
            Result.failure(e)
        }
    }

    /**
     * 批量插入性能指标
     */
    suspend fun insertPerformanceMetrics(performanceRecords: List<SupabasePerformanceRecord>): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val url = "${config.url}/rest/v1/performance_metrics"
            val requestBody = json.encodeToString(performanceRecords)

            val request = Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=representation")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()

            val response = httpClient.newCall(request).execute()

            if (response.isSuccessful) {
                val ids = performanceRecords.map { it.id }
                logger.d("Batch performance metrics inserted successfully: ${ids.size} records")
                Result.success(ids)
            } else {
                val errorMessage = "Failed to insert batch performance metrics: ${response.code} ${response.message}"
                logger.e(errorMessage)
                Result.failure(Exception(errorMessage))
            }

        } catch (e: Exception) {
            logger.e("Error inserting batch performance metrics", e)
            Result.failure(e)
        }
    }

    /**
     * 设置配置
     */
    fun setConfig(newConfig: SupabaseConfig) {
        this.config = newConfig
        logger.i("Supabase config updated")
    }
    
    /**
     * 获取配置值
     */
    private fun getConfigValue(key: String, defaultValue: String): String {
        // 这里应该从 BuildConfig、SharedPreferences 或其他安全存储中读取
        return defaultValue
    }
    
    /**
     * 认证拦截器
     */
    private inner class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            val authenticatedRequest = originalRequest.newBuilder()
                .addHeader("apikey", config.anonKey)
                .addHeader("Authorization", "Bearer ${config.anonKey}")
                .build()
            
            return chain.proceed(authenticatedRequest)
        }
    }
    
    /**
     * 日志拦截器
     */
    private inner class LoggingInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val request = chain.request()
            val startTime = System.currentTimeMillis()
            
            logger.d("Supabase API request: ${request.method} ${request.url}")
            
            val response = chain.proceed(request)
            val endTime = System.currentTimeMillis()
            
            logger.d("Supabase API response: ${response.code} in ${endTime - startTime}ms")
            
            return response
        }
    }
}

/**
 * Supabase 配置
 */
data class SupabaseConfig(
    val url: String = "",
    val anonKey: String = "",
    val serviceRoleKey: String = ""
)

/**
 * Supabase 崩溃报告数据结构
 */
@Serializable
data class SupabaseCrashReport(
    val id: String,
    val timestamp: Long,
    val app_version: String,
    val user_id: String?,
    val exception_type: String,
    val exception_message: String,
    val stack_trace: String,
    val error_code: String,
    val severity: String,
    val context: String, // JSON 字符串
    val thread: String,
    val is_fatal: Boolean,
    val device_info: String, // JSON 字符串
    val created_at: String // ISO 8601 格式
)

/**
 * 用户操作日志
 */
@Serializable
data class UserActionLog(
    val id: String,
    val crash_report_id: String?,
    val user_id: String?,
    val action: String,
    val parameters: String, // JSON 字符串
    val timestamp: Long
)

/**
 * API 响应数据类
 */
@Serializable
data class CrashStatisticsResponse(
    val total_crashes: Long = 0,
    val fatal_crashes: Long = 0,
    val unique_users: Long = 0,
    val top_exception_type: String? = null,
    val crash_free_rate: Double = 100.0
)
