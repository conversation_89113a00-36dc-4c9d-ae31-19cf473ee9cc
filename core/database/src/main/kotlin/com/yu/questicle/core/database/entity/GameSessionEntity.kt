package com.yu.questicle.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameAction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.Direction
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.Serializable

@Entity(
    tableName = "game_sessions",
    indices = [
        Index(value = ["game_id"]),
        Index(value = ["player_id"]),
        Index(value = ["start_time"])
    ],
    foreignKeys = [
        ForeignKey(
            entity = GameEntity::class,
            parentColumns = ["id"],
            childColumns = ["game_id"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class GameSessionEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "game_id")
    val gameId: String,
    
    @ColumnInfo(name = "player_id")
    val playerId: String,
    
    @ColumnInfo(name = "start_time")
    val startTime: Long,
    
    @ColumnInfo(name = "end_time")
    val endTime: Long? = null,
    
    @ColumnInfo(name = "actions")
    val actions: String = "[]", // JSON array
    
    @ColumnInfo(name = "achievements")
    val achievements: String = "[]" // JSON array
)

fun GameSessionEntity.toDomain(): GameSession {
    return GameSession(
        id = id,
        gameId = gameId,
        playerId = playerId,
        startTime = startTime,
        endTime = endTime,
        actions = parseActions(actions),
        achievements = parseAchievements(achievements)
    )
}

fun GameSession.toEntity(): GameSessionEntity {
    return GameSessionEntity(
        id = id,
        gameId = gameId,
        playerId = playerId,
        startTime = startTime,
        endTime = endTime,
        actions = serializeActions(actions),
        achievements = serializeAchievements(achievements)
    )
}

private fun parseActions(json: String): List<GameAction> {
    return try {
        if (json.isBlank() || json == "[]") {
            emptyList()
        } else {
            val serializableActions = Json.decodeFromString<List<SerializableGameAction>>(json)
            serializableActions.mapNotNull { it.toGameAction() }
        }
    } catch (e: Exception) {
        emptyList()
    }
}

private fun serializeActions(actions: List<GameAction>): String {
    return try {
        if (actions.isEmpty()) {
            "[]"
        } else {
            val serializableActions = actions.map { it.toSerializable() }
            Json.encodeToString(serializableActions)
        }
    } catch (e: Exception) {
        "[]"
    }
}

private fun parseAchievements(json: String): List<String> {
    return try {
        if (json.isBlank() || json == "[]") {
            emptyList()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<List<String>>(json)
        }
    } catch (e: Exception) {
        emptyList()
    }
}



private fun serializeAchievements(achievements: List<String>): String {
    return try {
        if (achievements.isEmpty()) {
            "[]"
        } else {
            // 简化实现：手动构建JSON数组
            val jsonItems = achievements.joinToString(",") { "\"$it\"" }
            "[$jsonItems]"
        }
    } catch (e: Exception) {
        "[]"
    }
}

// GameAction序列化支持
@Serializable
data class SerializableGameAction(
    val type: String,
    val playerId: String,
    val timestamp: Long,
    val data: Map<String, String> = emptyMap()
)

private fun GameAction.toSerializable(): SerializableGameAction {
    return when (this) {
        is TetrisAction.Move -> SerializableGameAction(
            type = "tetris_move",
            playerId = playerId,
            timestamp = timestamp,
            data = mapOf("direction" to direction.name)
        )
        is TetrisAction.Rotate -> SerializableGameAction(
            type = "tetris_rotate",
            playerId = playerId,
            timestamp = timestamp,
            data = mapOf("clockwise" to clockwise.toString())
        )
        is TetrisAction.Drop -> SerializableGameAction(
            type = "tetris_drop",
            playerId = playerId,
            timestamp = timestamp,
            data = mapOf("hard" to hard.toString())
        )
        is TetrisAction.Hold -> SerializableGameAction(
            type = "tetris_hold",
            playerId = playerId,
            timestamp = timestamp
        )
        else -> SerializableGameAction(
            type = "unknown",
            playerId = playerId,
            timestamp = timestamp
        )
    }
}

private fun SerializableGameAction.toGameAction(): GameAction? {
    return try {
        when (type) {
            "tetris_move" -> TetrisAction.Move(
                direction = Direction.valueOf(data["direction"] ?: "DOWN"),
                playerId = playerId
            )
            "tetris_rotate" -> TetrisAction.Rotate(
                clockwise = data["clockwise"]?.toBoolean() ?: true,
                playerId = playerId
            )
            "tetris_drop" -> TetrisAction.Drop(
                hard = data["hard"]?.toBoolean() ?: false,
                playerId = playerId
            )
            "tetris_hold" -> TetrisAction.Hold(playerId = playerId)
            else -> null
        }
    } catch (e: Exception) {
        null
    }
}
