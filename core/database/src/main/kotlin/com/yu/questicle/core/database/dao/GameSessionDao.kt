package com.yu.questicle.core.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.yu.questicle.core.database.entity.GameSessionEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface GameSessionDao {
    
    @Query("SELECT * FROM game_sessions WHERE id = :sessionId")
    suspend fun getSessionById(sessionId: String): GameSessionEntity?
    
    @Query("SELECT * FROM game_sessions WHERE game_id = :gameId ORDER BY start_time DESC")
    fun getSessionsForGame(gameId: String): Flow<List<GameSessionEntity>>
    
    @Query("SELECT * FROM game_sessions WHERE player_id = :playerId ORDER BY start_time DESC LIMIT :limit")
    fun getSessionsForPlayer(playerId: String, limit: Int): Flow<List<GameSessionEntity>>
    
    @Query("DELETE FROM game_sessions WHERE id = :sessionId")
    suspend fun deleteSession(sessionId: String)
    
    @Query("DELETE FROM game_sessions WHERE game_id = :gameId")
    suspend fun deleteSessionsForGame(gameId: String)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: GameSessionEntity)
}
