package com.yu.questicle.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType

@Entity(
    tableName = "game_stats",
    indices = [
        Index(value = ["player_id", "game_type"], unique = true),
        Index(value = ["best_score"]),
        Index(value = ["total_score"]),
        Index(value = ["last_played"])
    ]
)
data class GameStatsEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "game_type")
    val gameType: String,
    
    @ColumnInfo(name = "player_id")
    val playerId: String,
    
    @ColumnInfo(name = "total_games")
    val totalGames: Int = 0,
    
    @ColumnInfo(name = "total_score")
    val totalScore: Long = 0,
    
    @ColumnInfo(name = "best_score")
    val bestScore: Int = 0,
    
    @ColumnInfo(name = "average_score")
    val averageScore: Double = 0.0,
    
    @ColumnInfo(name = "total_play_time")
    val totalPlayTime: Long = 0,
    
    @ColumnInfo(name = "achievements")
    val achievements: String = "[]", // JSON array
    
    @ColumnInfo(name = "last_played")
    val lastPlayed: Long? = null
)

fun GameStatsEntity.toDomain(): GameStats {
    return GameStats(
        gameType = GameType.fromString(gameType) ?: GameType.TETRIS,
        playerId = playerId,
        totalGames = totalGames,
        totalScore = totalScore,
        bestScore = bestScore,
        averageScore = averageScore,
        totalPlayTime = totalPlayTime,
        achievements = parseAchievements(achievements),
        lastPlayed = lastPlayed
    )
}

fun GameStats.toEntity(): GameStatsEntity {
    return GameStatsEntity(
        id = "${playerId}_${gameType.name}",
        gameType = gameType.name,
        playerId = playerId,
        totalGames = totalGames,
        totalScore = totalScore,
        bestScore = bestScore,
        averageScore = averageScore,
        totalPlayTime = totalPlayTime,
        achievements = serializeAchievements(achievements),
        lastPlayed = lastPlayed
    )
}

private fun parseAchievements(json: String): Set<String> {
    return try {
        if (json.isBlank() || json == "[]") {
            emptySet()
        } else {
            // 简化解析：移除方括号和引号，按逗号分割
            val cleanJson = json.trim().removeSurrounding("[", "]")
            if (cleanJson.isBlank()) {
                emptySet()
            } else {
                cleanJson.split(",")
                    .map { it.trim().removeSurrounding("\"") }
                    .filter { it.isNotBlank() }
                    .toSet()
            }
        }
    } catch (e: Exception) {
        emptySet()
    }
}

private fun serializeAchievements(achievements: Set<String>): String {
    return try {
        // 手动构建JSON数组，避免序列化器问题
        if (achievements.isEmpty()) {
            "[]"
        } else {
            val jsonItems = achievements.joinToString(",") { "\"$it\"" }
            "[$jsonItems]"
        }
    } catch (e: Exception) {
        "[]"
    }
}
