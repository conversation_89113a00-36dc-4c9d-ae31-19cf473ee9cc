package com.yu.questicle.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.yu.questicle.core.domain.model.User
import com.yu.questicle.core.domain.model.UserPreferences
import com.yu.questicle.core.domain.model.UserStats

@Entity(
    tableName = "users",
    indices = [
        Index(value = ["username"], unique = true),
        Index(value = ["email"], unique = true),
        Index(value = ["level"]),
        Index(value = ["experience"]),
        Index(value = ["last_login_at"])
    ]
)
data class UserEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "username")
    val username: String,
    
    @ColumnInfo(name = "email")
    val email: String? = null,
    
    @ColumnInfo(name = "display_name")
    val displayName: String,
    
    @ColumnInfo(name = "avatar_url")
    val avatarUrl: String? = null,
    
    @ColumnInfo(name = "level")
    val level: Int = 1,
    
    @ColumnInfo(name = "experience")
    val experience: Long = 0,
    
    @ColumnInfo(name = "coins")
    val coins: Long = 0,
    
    @ColumnInfo(name = "gems")
    val gems: Long = 0,
    
    @ColumnInfo(name = "preferences")
    val preferences: String = "{}", // JSON string
    
    @ColumnInfo(name = "stats")
    val stats: String = "{}", // JSON string
    
    @ColumnInfo(name = "achievements")
    val achievements: String = "[]", // JSON array
    
    @ColumnInfo(name = "friends")
    val friends: String = "[]", // JSON array
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "last_login_at")
    val lastLoginAt: Long,
    
    @ColumnInfo(name = "is_active")
    val isActive: Boolean = true
)

fun UserEntity.toDomain(): User {
    return User(
        id = id,
        username = username,
        email = email,
        displayName = displayName,
        avatarUrl = avatarUrl,
        level = level,
        experience = experience,
        coins = coins,
        gems = gems,
        preferences = parsePreferences(preferences),
        stats = parseStats(stats),
        achievements = parseAchievements(achievements),
        friends = parseFriends(friends),
        createdAt = createdAt,
        lastLoginAt = lastLoginAt,
        isActive = isActive
    )
}

fun User.toEntity(): UserEntity {
    return UserEntity(
        id = id,
        username = username,
        email = email,
        displayName = displayName.ifBlank { username },
        avatarUrl = avatarUrl,
        level = level,
        experience = experience,
        coins = coins,
        gems = gems,
        preferences = serializePreferences(preferences),
        stats = serializeStats(stats),
        achievements = serializeAchievements(achievements),
        friends = serializeFriends(friends),
        createdAt = createdAt,
        lastLoginAt = lastLoginAt,
        isActive = isActive
    )
}

private fun parsePreferences(json: String): UserPreferences {
    return try {
        if (json.isBlank() || json == "{}") {
            UserPreferences()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<UserPreferences>(json)
        }
    } catch (e: Exception) {
        UserPreferences()
    }
}

private fun parseStats(json: String): UserStats {
    return try {
        if (json.isBlank() || json == "{}") {
            UserStats()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<UserStats>(json)
        }
    } catch (e: Exception) {
        UserStats()
    }
}

private fun parseAchievements(json: String): Set<String> {
    return try {
        if (json.isBlank() || json == "[]") {
            emptySet()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<Set<String>>(json)
        }
    } catch (e: Exception) {
        emptySet()
    }
}

private fun parseFriends(json: String): Set<String> {
    return try {
        if (json.isBlank() || json == "[]") {
            emptySet()
        } else {
            kotlinx.serialization.json.Json.decodeFromString<Set<String>>(json)
        }
    } catch (e: Exception) {
        emptySet()
    }
}

private fun serializePreferences(preferences: UserPreferences): String {
    return try {
        kotlinx.serialization.json.Json.encodeToString(UserPreferences.serializer(), preferences)
    } catch (e: Exception) {
        // 如果序列化失败，返回默认值的JSON
        kotlinx.serialization.json.Json.encodeToString(UserPreferences.serializer(), UserPreferences())
    }
}

private fun serializeStats(stats: UserStats): String {
    return try {
        kotlinx.serialization.json.Json.encodeToString(UserStats.serializer(), stats)
    } catch (e: Exception) {
        // 如果序列化失败，返回默认值的JSON
        kotlinx.serialization.json.Json.encodeToString(UserStats.serializer(), UserStats())
    }
}

private fun serializeAchievements(achievements: Set<String>): String {
    return try {
        if (achievements.isEmpty()) {
            "[]"
        } else {
            // 简化实现：手动构建JSON数组
            val jsonItems = achievements.joinToString(",") { "\"$it\"" }
            "[$jsonItems]"
        }
    } catch (e: Exception) {
        "[]"
    }
}

private fun serializeFriends(friends: Set<String>): String {
    return try {
        if (friends.isEmpty()) {
            "[]"
        } else {
            // 简化实现：手动构建JSON数组
            val jsonItems = friends.joinToString(",") { "\"$it\"" }
            "[$jsonItems]"
        }
    } catch (e: Exception) {
        "[]"
    }
}
