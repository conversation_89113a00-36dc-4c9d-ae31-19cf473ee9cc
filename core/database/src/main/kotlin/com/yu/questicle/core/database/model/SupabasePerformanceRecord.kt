package com.yu.questicle.core.database.model

import kotlinx.serialization.Serializable

/**
 * Supabase 性能记录数据结构
 * 
 * 用于向 Supabase 后端发送性能监控数据
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Serializable
data class SupabasePerformanceRecord(
    val id: String,
    val timestamp: Long,
    val user_id: String?,
    val session_id: String?,
    val metric_type: String,
    val metric_name: String,
    val metric_value: Double,
    val metric_unit: String,
    val context: String?, // JSON 字符串
    val device_info: String?, // JSON 字符串
    val app_version: String,
    val build_number: String,
    val platform: String = "android",
    val created_at: String // ISO 8601 格式
)

/**
 * 性能指标类型枚举
 */
enum class PerformanceMetricType(val value: String) {
    FRAME_RATE("frame_rate"),
    MEMORY("memory"),
    CPU("cpu"),
    NETWORK("network"),
    GAME_OPERATION("game_operation"),
    UI_RESPONSE("ui_response"),
    DATABASE("database"),
    CACHE("cache"),
    STARTUP("startup"),
    BATTERY("battery")
}

/**
 * 性能指标单位枚举
 */
enum class PerformanceMetricUnit(val value: String) {
    MILLISECONDS("ms"),
    SECONDS("s"),
    FPS("fps"),
    BYTES("bytes"),
    KILOBYTES("kb"),
    MEGABYTES("mb"),
    PERCENTAGE("%"),
    COUNT("count"),
    OPERATIONS_PER_SECOND("ops/s"),
    REQUESTS_PER_SECOND("req/s")
}

/**
 * 设备信息数据结构
 */
@Serializable
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val android_version: String,
    val api_level: Int,
    val total_memory: Long,
    val available_memory: Long,
    val cpu_cores: Int,
    val screen_width: Int,
    val screen_height: Int,
    val screen_density: Float
)

/**
 * 性能上下文数据结构
 */
@Serializable
data class PerformanceContext(
    val feature: String,
    val operation: String,
    val game_state: String?,
    val user_action: String?,
    val additional_data: Map<String, String> = emptyMap()
)
