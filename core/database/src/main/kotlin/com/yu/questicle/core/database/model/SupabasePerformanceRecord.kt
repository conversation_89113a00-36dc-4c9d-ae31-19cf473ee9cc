package com.yu.questicle.core.database.model

import kotlinx.serialization.Serializable

/**
 * Supabase 性能记录数据模型
 * 用于存储性能监控数据到 Supabase 数据库
 */
@Serializable
data class SupabasePerformanceRecord(
    val id: String,
    val timestamp: Long,
    val app_version: String,
    val user_id: String?,
    val session_id: String?,
    val metric_type: String,
    val metric_name: String,
    val value: Double,
    val unit: String,
    val metadata: String, // JSON 字符串
    val device_info: String, // JSON 字符串
    val created_at: String // ISO 8601 格式
) {
    companion object {
        /**
         * 从性能指标创建 Supabase 记录
         */
        fun fromPerformanceMetric(
            id: String,
            timestamp: Long,
            appVersion: String,
            userId: String?,
            sessionId: String?,
            metricType: String,
            metricName: String,
            value: Double,
            unit: String,
            metadata: Map<String, Any?> = emptyMap(),
            deviceInfo: Map<String, Any?> = emptyMap()
        ): SupabasePerformanceRecord {
            return SupabasePerformanceRecord(
                id = id,
                timestamp = timestamp,
                app_version = appVersion,
                user_id = userId,
                session_id = sessionId,
                metric_type = metricType,
                metric_name = metricName,
                value = value,
                unit = unit,
                metadata = kotlinx.serialization.json.Json.encodeToString(
                    kotlinx.serialization.json.JsonObject.serializer(),
                    kotlinx.serialization.json.JsonObject(
                        metadata.mapValues { (_, v) ->
                            when (v) {
                                is String -> kotlinx.serialization.json.JsonPrimitive(v)
                                is Number -> kotlinx.serialization.json.JsonPrimitive(v)
                                is Boolean -> kotlinx.serialization.json.JsonPrimitive(v)
                                null -> kotlinx.serialization.json.JsonNull
                                else -> kotlinx.serialization.json.JsonPrimitive(v.toString())
                            }
                        }
                    )
                ),
                device_info = kotlinx.serialization.json.Json.encodeToString(
                    kotlinx.serialization.json.JsonObject.serializer(),
                    kotlinx.serialization.json.JsonObject(
                        deviceInfo.mapValues { (_, v) ->
                            when (v) {
                                is String -> kotlinx.serialization.json.JsonPrimitive(v)
                                is Number -> kotlinx.serialization.json.JsonPrimitive(v)
                                is Boolean -> kotlinx.serialization.json.JsonPrimitive(v)
                                null -> kotlinx.serialization.json.JsonNull
                                else -> kotlinx.serialization.json.JsonPrimitive(v.toString())
                            }
                        }
                    )
                ),
                created_at = java.time.Instant.ofEpochMilli(timestamp).toString()
            )
        }
    }
}

/**
 * 性能指标类型枚举
 */
enum class PerformanceMetricType(val value: String) {
    FRAME_RATE("frame_rate"),
    MEMORY_USAGE("memory_usage"),
    CPU_USAGE("cpu_usage"),
    NETWORK_LATENCY("network_latency"),
    BATTERY_USAGE("battery_usage"),
    DISK_IO("disk_io"),
    GAME_ENGINE("game_engine"),
    RENDERING("rendering"),
    AUDIO("audio"),
    USER_INTERACTION("user_interaction")
}

/**
 * 设备信息数据类
 */
@Serializable
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val osVersion: String,
    val apiLevel: Int,
    val screenDensity: Float,
    val screenWidth: Int,
    val screenHeight: Int,
    val totalMemory: Long,
    val availableMemory: Long,
    val cpuCores: Int,
    val cpuFrequency: Long,
    val batteryLevel: Float,
    val isCharging: Boolean,
    val networkType: String,
    val locale: String,
    val timezone: String
) {
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "manufacturer" to manufacturer,
            "model" to model,
            "osVersion" to osVersion,
            "apiLevel" to apiLevel,
            "screenDensity" to screenDensity,
            "screenWidth" to screenWidth,
            "screenHeight" to screenHeight,
            "totalMemory" to totalMemory,
            "availableMemory" to availableMemory,
            "cpuCores" to cpuCores,
            "cpuFrequency" to cpuFrequency,
            "batteryLevel" to batteryLevel,
            "isCharging" to isCharging,
            "networkType" to networkType,
            "locale" to locale,
            "timezone" to timezone
        )
    }
}

/**
 * 性能指标元数据
 */
@Serializable
data class PerformanceMetadata(
    val testName: String? = null,
    val testIteration: Int? = null,
    val gameMode: String? = null,
    val gameLevel: Int? = null,
    val userLevel: Int? = null,
    val connectionType: String? = null,
    val backgroundApps: Int? = null,
    val memoryPressure: String? = null,
    val thermalState: String? = null,
    val powerMode: String? = null,
    val customTags: List<String> = emptyList(),
    val additionalData: Map<String, String> = emptyMap()
) {
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "testName" to testName,
            "testIteration" to testIteration,
            "gameMode" to gameMode,
            "gameLevel" to gameLevel,
            "userLevel" to userLevel,
            "connectionType" to connectionType,
            "backgroundApps" to backgroundApps,
            "memoryPressure" to memoryPressure,
            "thermalState" to thermalState,
            "powerMode" to powerMode,
            "customTags" to customTags,
            "additionalData" to additionalData
        )
    }
}

/**
 * 批量性能记录请求
 */
@Serializable
data class BatchPerformanceRequest(
    val records: List<SupabasePerformanceRecord>,
    val batchId: String = java.util.UUID.randomUUID().toString(),
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 性能查询参数
 */
data class PerformanceQueryParams(
    val userId: String? = null,
    val sessionId: String? = null,
    val metricType: String? = null,
    val metricName: String? = null,
    val startTime: Long? = null,
    val endTime: Long? = null,
    val limit: Int = 100,
    val offset: Int = 0,
    val orderBy: String = "timestamp",
    val orderDirection: String = "desc"
) {
    fun toQueryMap(): Map<String, String> {
        val params = mutableMapOf<String, String>()
        
        userId?.let { params["user_id"] = "eq.$it" }
        sessionId?.let { params["session_id"] = "eq.$it" }
        metricType?.let { params["metric_type"] = "eq.$it" }
        metricName?.let { params["metric_name"] = "eq.$it" }
        startTime?.let { params["timestamp"] = "gte.$it" }
        endTime?.let { 
            val existingTimestamp = params["timestamp"]
            params["timestamp"] = if (existingTimestamp != null) {
                "$existingTimestamp,lte.$it"
            } else {
                "lte.$it"
            }
        }
        
        params["limit"] = limit.toString()
        params["offset"] = offset.toString()
        params["order"] = "$orderBy.$orderDirection"
        
        return params
    }
}

/**
 * 性能统计响应
 */
@Serializable
data class PerformanceStatsResponse(
    val total_records: Long = 0,
    val unique_users: Long = 0,
    val unique_sessions: Long = 0,
    val avg_value: Double = 0.0,
    val min_value: Double = 0.0,
    val max_value: Double = 0.0,
    val std_deviation: Double = 0.0,
    val percentile_50: Double = 0.0,
    val percentile_95: Double = 0.0,
    val percentile_99: Double = 0.0
)
