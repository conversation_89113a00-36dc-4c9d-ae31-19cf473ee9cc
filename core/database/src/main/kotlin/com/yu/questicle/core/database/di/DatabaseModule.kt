package com.yu.questicle.core.database.di

import android.content.Context
import androidx.room.Room
import com.yu.questicle.core.database.QuesticleDatabase
import com.yu.questicle.core.database.dao.GameDao
import com.yu.questicle.core.database.dao.GameSessionDao
import com.yu.questicle.core.database.dao.GameStatsDao
import com.yu.questicle.core.database.dao.UserDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideQuesticleDatabase(
        @ApplicationContext context: Context
    ): QuesticleDatabase {
        return Room.databaseBuilder(
            context,
            QuesticleDatabase::class.java,
            QuesticleDatabase.DATABASE_NAME
        )
            .fallbackToDestructiveMigration(false)
        .build()
    }

    @Provides
    fun provideGameDao(database: QuesticleDatabase): GameDao = database.gameDao()

    @Provides
    fun provideUserDao(database: QuesticleDatabase): UserDao = database.userDao()

    @Provides
    fun provideGameSessionDao(database: QuesticleDatabase): GameSessionDao = database.gameSessionDao()

    @Provides
    fun provideGameStatsDao(database: QuesticleDatabase): GameStatsDao = database.gameStatsDao()
}
