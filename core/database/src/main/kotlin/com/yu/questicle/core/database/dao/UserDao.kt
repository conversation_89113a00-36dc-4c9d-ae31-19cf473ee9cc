package com.yu.questicle.core.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.yu.questicle.core.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    
    @Query("SELECT * FROM users WHERE id = :userId")
    suspend fun getUserById(userId: String): UserEntity?
    
    @Query("SELECT * FROM users WHERE username = :username")
    suspend fun getUserByUsername(username: String): UserEntity?
    
    @Query("SELECT * FROM users WHERE email = :email")
    suspend fun getUserByEmail(email: String): UserEntity?
    
    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY level DESC, experience DESC LIMIT :limit")
    suspend fun getTopUsers(limit: Int): List<UserEntity>
    
    @Query("SELECT * FROM users WHERE username LIKE '%' || :query || '%' OR display_name LIKE '%' || :query || '%' LIMIT :limit")
    suspend fun searchUsers(query: String, limit: Int): List<UserEntity>
    
    @Query("SELECT * FROM users WHERE id IN (:userIds)")
    suspend fun getUsersByIds(userIds: List<String>): List<UserEntity>
    
    @Query("UPDATE users SET experience = experience + :amount WHERE id = :userId")
    suspend fun addExperience(userId: String, amount: Long)
    
    @Query("UPDATE users SET coins = coins + :amount WHERE id = :userId")
    suspend fun addCoins(userId: String, amount: Long)
    
    @Query("UPDATE users SET gems = gems + :amount WHERE id = :userId")
    suspend fun addGems(userId: String, amount: Long)
    
    @Query("UPDATE users SET level = :level WHERE id = :userId")
    suspend fun updateLevel(userId: String, level: Int)
    
    @Query("UPDATE users SET last_login_at = :timestamp WHERE id = :userId")
    suspend fun updateLastLogin(userId: String, timestamp: Long)
    
    @Query("DELETE FROM users WHERE id = :userId")
    suspend fun deleteUser(userId: String)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserEntity)
    
    @Update
    suspend fun updateUser(user: UserEntity)
    
    @Query("SELECT COUNT(*) FROM users WHERE is_active = 1")
    suspend fun getActiveUserCount(): Int
}
