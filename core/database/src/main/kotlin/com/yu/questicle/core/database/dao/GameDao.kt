package com.yu.questicle.core.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.yu.questicle.core.database.entity.GameEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface GameDao {
    
    @Query("SELECT * FROM games WHERE id = :gameId")
    suspend fun getGameById(gameId: String): GameEntity?
    
    @Query("SELECT * FROM games WHERE player_id = :playerId ORDER BY start_time DESC")
    fun getGamesForPlayer(playerId: String): Flow<List<GameEntity>>
    
    @Query("SELECT * FROM games WHERE player_id = :playerId AND type = :gameType ORDER BY start_time DESC")
    fun getGamesByType(playerId: String, gameType: String): Flow<List<GameEntity>>
    
    @Query("SELECT * FROM games WHERE player_id = :playerId AND status = :status ORDER BY start_time DESC")
    fun getGamesByStatus(playerId: String, status: String): Flow<List<GameEntity>>
    
    @Query("""
        SELECT * FROM games 
        WHERE (:playerId IS NULL OR player_id = :playerId)
        AND (:gameType IS NULL OR type = :gameType)
        AND (:minScore IS NULL OR score >= :minScore)
        AND (:maxScore IS NULL OR score <= :maxScore)
        ORDER BY start_time DESC
        LIMIT :limit
    """)
    suspend fun searchGames(
        playerId: String?,
        gameType: String?,
        minScore: Int?,
        maxScore: Int?,
        limit: Int
    ): List<GameEntity>
    
    @Query("SELECT * FROM games WHERE type = :gameType ORDER BY score DESC LIMIT :limit")
    suspend fun getTopScoresByType(gameType: String, limit: Int): List<GameEntity>
    
    @Query("SELECT COUNT(*) FROM games WHERE player_id = :playerId")
    suspend fun getGameCountForPlayer(playerId: String): Int
    
    @Query("SELECT SUM(score) FROM games WHERE player_id = :playerId")
    suspend fun getTotalScoreForPlayer(playerId: String): Long?
    
    @Query("SELECT MAX(score) FROM games WHERE player_id = :playerId AND type = :gameType")
    suspend fun getBestScoreForPlayerAndType(playerId: String, gameType: String): Int?
    
    @Query("SELECT AVG(score) FROM games WHERE player_id = :playerId AND type = :gameType")
    suspend fun getAverageScoreForPlayerAndType(playerId: String, gameType: String): Double?
    
    @Query("SELECT SUM(duration) FROM games WHERE player_id = :playerId")
    suspend fun getTotalPlayTimeForPlayer(playerId: String): Long?
    
    @Query("DELETE FROM games WHERE id = :gameId")
    suspend fun deleteGameById(gameId: String)
    
    @Query("DELETE FROM games WHERE player_id = :playerId")
    suspend fun deleteAllGamesForPlayer(playerId: String)
    
    @Query("DELETE FROM games WHERE start_time < :timestamp")
    suspend fun deleteOldGames(timestamp: Long)
    
    @Query("DELETE FROM games")
    suspend fun deleteAllGames()
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGame(game: GameEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGames(games: List<GameEntity>)
    
    @Update
    suspend fun updateGame(game: GameEntity)
    
    @Delete
    suspend fun deleteGame(game: GameEntity)
    
    // Statistics queries
    @Query("""
        SELECT type, COUNT(*) as count, SUM(score) as total_score, 
               MAX(score) as best_score, AVG(score) as avg_score,
               SUM(duration) as total_time
        FROM games 
        WHERE player_id = :playerId 
        GROUP BY type
    """)
    suspend fun getGameStatsByType(playerId: String): List<GameStatsResult>
    
    @Query("""
        SELECT COUNT(*) as total_games,
               SUM(score) as total_score,
               MAX(score) as best_score,
               AVG(score) as avg_score,
               SUM(duration) as total_time,
               SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as games_won,
               SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as games_lost
        FROM games 
        WHERE player_id = :playerId
    """)
    suspend fun getOverallStats(playerId: String): OverallStatsResult?
}

data class GameStatsResult(
    val type: String,
    val count: Int,
    val total_score: Long,
    val best_score: Int,
    val avg_score: Double,
    val total_time: Long
)

data class OverallStatsResult(
    val total_games: Int,
    val total_score: Long,
    val best_score: Int,
    val avg_score: Double,
    val total_time: Long,
    val games_won: Int,
    val games_lost: Int
)
