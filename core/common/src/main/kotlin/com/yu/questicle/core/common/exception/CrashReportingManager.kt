package com.yu.questicle.core.common.exception

import android.content.Context
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 崩溃报告管理器
 * 
 * 负责：
 * - 集成Firebase Crashlytics
 * - 自定义崩溃报告
 * - 异常数据收集和分析
 * - 崩溃恢复策略
 * - 用户反馈收集
 */
@Singleton
class CrashReportingManager @Inject constructor(
    private val context: Context
) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(CrashReportingManager::class)
    
    // 崩溃统计
    private val crashCount = AtomicLong(0)
    private val crashesByType = ConcurrentHashMap<String, AtomicLong>()
    private val crashesBySeverity = ConcurrentHashMap<ErrorSeverity, AtomicLong>()
    
    // 崩溃事件流
    private val _crashEvents = MutableSharedFlow<CrashEvent>()
    val crashEvents: SharedFlow<CrashEvent> = _crashEvents.asSharedFlow()
    
    // 配置
    private var config = CrashReportingConfig()
    
    // 崩溃报告器列表
    private val reporters = mutableListOf<CrashReporter>()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    init {
        initializeReporters()
        setupUncaughtExceptionHandler()
    }
    
    /**
     * 初始化崩溃报告器
     */
    private fun initializeReporters() {
        // 添加Firebase Crashlytics报告器
        if (config.enableFirebaseCrashlytics) {
            reporters.add(FirebaseCrashlyticsReporter())
        }
        
        // 添加自定义崩溃报告器
        if (config.enableCustomReporting) {
            reporters.add(CustomCrashReporter(context))
        }
        
        // 添加本地文件报告器
        if (config.enableLocalReporting) {
            reporters.add(LocalFileCrashReporter(context))
        }
        
        logger.i("Initialized ${reporters.size} crash reporters")
    }
    
    /**
     * 设置未捕获异常处理器
     */
    private fun setupUncaughtExceptionHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            handleUncaughtException(thread, exception)
            
            // 调用默认处理器
            defaultHandler?.uncaughtException(thread, exception)
        }
        
        logger.i("Uncaught exception handler configured")
    }
    
    /**
     * 处理未捕获异常
     */
    private fun handleUncaughtException(thread: Thread, exception: Throwable) {
        try {
            val questicleException = exception.toQuesticleException()
            val crashEvent = CrashEvent(
                exception = questicleException,
                thread = thread.name,
                timestamp = System.currentTimeMillis(),
                context = collectCrashContext(),
                stackTrace = exception.stackTraceToString(),
                isFatal = true
            )
            
            // 记录崩溃统计
            recordCrash(questicleException)
            
            // 发送崩溃事件
            scope.launch {
                try {
                    _crashEvents.emit(crashEvent)
                } catch (e: Exception) {
                    logger.e("Failed to emit crash event", e)
                }
            }
            
            // 报告崩溃
            reportCrash(crashEvent)
            
            logger.f("Uncaught exception in thread ${thread.name}", exception)
            
        } catch (e: Exception) {
            // 崩溃报告系统本身不应该崩溃
            logger.e("Error in crash reporting system", e)
        }
    }
    
    /**
     * 报告异常（非致命）
     */
    fun reportException(
        exception: QuesticleException,
        context: ExceptionContext = ExceptionContext.EMPTY,
        isFatal: Boolean = false
    ) {
        scope.launch {
            try {
                val crashEvent = CrashEvent(
                    exception = exception,
                    thread = Thread.currentThread().name,
                    timestamp = System.currentTimeMillis(),
                    context = collectCrashContext() + context.toMap(),
                    stackTrace = exception.stackTraceToString(),
                    isFatal = isFatal
                )
                
                // 记录统计
                if (isFatal) {
                    recordCrash(exception)
                }
                
                // 发送事件
                _crashEvents.emit(crashEvent)
                
                // 报告异常
                reportCrash(crashEvent)
                
                logger.w("Exception reported: ${exception.message}", exception)
                
            } catch (e: Exception) {
                logger.e("Failed to report exception", e)
            }
        }
    }
    
    /**
     * 收集崩溃上下文信息
     */
    private fun collectCrashContext(): Map<String, Any?> {
        return mapOf(
            "app_version" to getAppVersion(),
            "build_type" to getBuildType(),
            "device_info" to getDeviceInfo(),
            "memory_info" to getMemoryInfo(),
            "storage_info" to getStorageInfo(),
            "network_info" to getNetworkInfo(),
            "user_id" to getCurrentUserId(),
            "session_id" to getCurrentSessionId(),
            "crash_count" to crashCount.get(),
            "uptime" to getAppUptime()
        )
    }
    
    /**
     * 报告崩溃到所有报告器
     */
    private fun reportCrash(crashEvent: CrashEvent) {
        reporters.forEach { reporter ->
            scope.launch {
                try {
                    reporter.reportCrash(crashEvent)
                } catch (e: Exception) {
                    logger.e("Crash reporter ${reporter::class.simpleName} failed", e)
                }
            }
        }
    }
    
    /**
     * 记录崩溃统计
     */
    private fun recordCrash(exception: QuesticleException) {
        crashCount.incrementAndGet()
        
        // 按类型统计
        val typeName = exception::class.java.simpleName
        crashesByType.computeIfAbsent(typeName) { AtomicLong(0) }.incrementAndGet()
        
        // 按严重程度统计
        crashesBySeverity.computeIfAbsent(exception.severity) { AtomicLong(0) }.incrementAndGet()
    }
    
    /**
     * 设置用户标识符
     */
    fun setUserId(userId: String) {
        reporters.forEach { reporter ->
            try {
                reporter.setUserId(userId)
            } catch (e: Exception) {
                logger.e("Failed to set user ID in reporter", e)
            }
        }
    }
    
    /**
     * 设置自定义键值对
     */
    fun setCustomKey(key: String, value: Any?) {
        reporters.forEach { reporter ->
            try {
                reporter.setCustomKey(key, value)
            } catch (e: Exception) {
                logger.e("Failed to set custom key in reporter", e)
            }
        }
    }
    
    /**
     * 记录用户操作
     */
    fun logUserAction(action: String, parameters: Map<String, Any?> = emptyMap()) {
        reporters.forEach { reporter ->
            try {
                reporter.logUserAction(action, parameters)
            } catch (e: Exception) {
                logger.e("Failed to log user action in reporter", e)
            }
        }
    }
    
    /**
     * 获取崩溃统计
     */
    fun getCrashStatistics(): CrashStatistics {
        return CrashStatistics(
            totalCrashes = crashCount.get(),
            crashesByType = crashesByType.mapValues { it.value.get() },
            crashesBySeverity = crashesBySeverity.mapValues { it.value.get() }
        )
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: CrashReportingConfig) {
        this.config = newConfig
        // 重新初始化报告器
        reporters.clear()
        initializeReporters()
    }
    
    // 辅助方法
    private fun getAppVersion(): String = try {
        context.packageManager.getPackageInfo(context.packageName, 0).versionName ?: "unknown"
    } catch (e: Exception) { "unknown" }
    
    private fun getBuildType(): String = try {
        // 这里可以通过BuildConfig获取
        "debug" // 或 "release"
    } catch (e: Exception) { "unknown" }
    
    private fun getDeviceInfo(): Map<String, String> = mapOf(
        "manufacturer" to android.os.Build.MANUFACTURER,
        "model" to android.os.Build.MODEL,
        "android_version" to android.os.Build.VERSION.RELEASE,
        "api_level" to android.os.Build.VERSION.SDK_INT.toString()
    )
    
    private fun getMemoryInfo(): Map<String, Long> {
        val runtime = Runtime.getRuntime()
        return mapOf(
            "max_memory" to runtime.maxMemory(),
            "total_memory" to runtime.totalMemory(),
            "free_memory" to runtime.freeMemory(),
            "used_memory" to (runtime.totalMemory() - runtime.freeMemory())
        )
    }
    
    private fun getStorageInfo(): Map<String, Long> = try {
        val internalDir = context.filesDir
        mapOf(
            "total_space" to internalDir.totalSpace,
            "free_space" to internalDir.freeSpace,
            "usable_space" to internalDir.usableSpace
        )
    } catch (e: Exception) { emptyMap() }
    
    private fun getNetworkInfo(): Map<String, String> = try {
        // 这里可以添加网络状态检测
        mapOf("type" to "unknown", "connected" to "unknown")
    } catch (e: Exception) { emptyMap() }
    
    private fun getCurrentUserId(): String? = null // 从用户管理器获取
    
    private fun getCurrentSessionId(): String? = null // 从会话管理器获取
    
    private fun getAppUptime(): Long = System.currentTimeMillis() // 需要记录应用启动时间
}

/**
 * 崩溃事件
 */
data class CrashEvent(
    val exception: QuesticleException,
    val thread: String,
    val timestamp: Long,
    val context: Map<String, Any?>,
    val stackTrace: String,
    val isFatal: Boolean
)

/**
 * 崩溃统计
 */
data class CrashStatistics(
    val totalCrashes: Long,
    val crashesByType: Map<String, Long>,
    val crashesBySeverity: Map<ErrorSeverity, Long>
)

/**
 * 崩溃报告配置
 */
data class CrashReportingConfig(
    val enableFirebaseCrashlytics: Boolean = true,
    val enableCustomReporting: Boolean = true,
    val enableLocalReporting: Boolean = true,
    val enableUserFeedback: Boolean = true,
    val enableAutoUpload: Boolean = true,
    val maxLocalReports: Int = 100,
    val reportRetentionDays: Int = 30
)
