package com.yu.questicle.core.common.exception

import kotlinx.serialization.Serializable

/**
 * 异常类型枚举
 * 
 * 用于分类不同类型的异常
 */
@Serializable
enum class ExceptionType {
    /**
     * 业务逻辑异常
     */
    BUSINESS_LOGIC,
    
    /**
     * 网络异常
     */
    NETWORK,
    
    /**
     * 数据库异常
     */
    DATABASE,
    
    /**
     * 系统异常
     */
    SYSTEM,
    
    /**
     * 权限异常
     */
    PERMISSION,
    
    /**
     * 验证异常
     */
    VALIDATION,
    
    /**
     * UI异常
     */
    UI,
    
    /**
     * 配置异常
     */
    CONFIGURATION,
    
    /**
     * 第三方服务异常
     */
    THIRD_PARTY,

    /**
     * 游戏逻辑异常
     */
    GAME_ERROR,

    /**
     * 未知异常
     */
    UNKNOWN
}

/**
 * 错误严重程度枚举
 * 
 * 用于表示异常的严重程度
 */
@Serializable
enum class ErrorSeverity {
    /**
     * 低 - 不影响核心功能，用户可以继续使用
     */
    LOW,
    
    /**
     * 中等 - 影响部分功能，但不阻塞主要流程
     */
    MEDIUM,
    
    /**
     * 高 - 影响核心功能，需要立即处理
     */
    HIGH,
    
    /**
     * 严重 - 导致应用崩溃或无法使用
     */
    CRITICAL
}

/**
 * 异常处理动作枚举
 * 
 * 定义对异常的处理方式
 */
@Serializable
enum class ExceptionAction {
    /**
     * 仅记录日志
     */
    LOG_ONLY,
    
    /**
     * 记录日志并继续执行
     */
    LOG_AND_CONTINUE,
    
    /**
     * 记录日志并尝试恢复
     */
    LOG_AND_RECOVER,
    
    /**
     * 记录日志并终止操作
     */
    LOG_AND_TERMINATE,
    
    /**
     * 记录日志并重试
     */
    LOG_AND_RETRY,
    
    /**
     * 记录日志并降级处理
     */
    LOG_AND_DEGRADE
}

/**
 * 恢复策略枚举
 * 
 * 定义异常恢复的策略
 */
@Serializable
enum class RecoveryStrategy {
    /**
     * 无恢复策略
     */
    NONE,
    
    /**
     * 重试
     */
    RETRY,
    
    /**
     * 带退避的重试
     */
    RETRY_WITH_BACKOFF,
    
    /**
     * 重新连接
     */
    RECONNECT,
    
    /**
     * 降级处理
     */
    GRACEFUL_DEGRADATION,
    
    /**
     * 回滚操作
     */
    ROLLBACK,
    
    /**
     * 切换到备用方案
     */
    FALLBACK
}
