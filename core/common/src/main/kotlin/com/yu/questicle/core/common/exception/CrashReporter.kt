package com.yu.questicle.core.common.exception

import android.content.Context
import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩溃报告器接口
 */
interface CrashReporter {
    
    /**
     * 报告崩溃
     */
    suspend fun reportCrash(crashEvent: CrashEvent)
    
    /**
     * 设置用户ID
     */
    fun setUserId(userId: String)
    
    /**
     * 设置自定义键值对
     */
    fun setCustomKey(key: String, value: Any?)
    
    /**
     * 记录用户操作
     */
    fun logUserAction(action: String, parameters: Map<String, Any?>)
    
    /**
     * 获取报告器名称
     */
    fun getName(): String
}

/**
 * Supabase 崩溃报告器
 */
class SupabaseCrashReporter(
    private val context: Context
) : CrashReporter {

    private val logger: QLogger = QLoggerFactory.getLogger(SupabaseCrashReporter::class)

    // Supabase 客户端配置
    private var supabaseUrl: String = ""
    private var supabaseAnonKey: String = ""
    private var userId: String? = null
    private val customKeys = mutableMapOf<String, Any?>()

    // JSON 序列化器
    private val json = Json {
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }

    init {
        // 从配置中获取 Supabase 凭据
        loadSupabaseConfig()
    }

    private fun loadSupabaseConfig() {
        // 这里应该从 BuildConfig 或配置文件中读取
        // supabaseUrl = BuildConfig.SUPABASE_URL
        // supabaseAnonKey = BuildConfig.SUPABASE_ANON_KEY

        // 临时硬编码，实际应该从安全配置中读取
        supabaseUrl = "https://whwbfadkzbmkgxlxxjiv.supabase.co"
        supabaseAnonKey = "your_anon_key_here" // 需要配置实际的密钥
    }

    override suspend fun reportCrash(crashEvent: CrashEvent) = withContext(Dispatchers.IO) {
        try {
            val crashReport = SupabaseCrashReport(
                id = java.util.UUID.randomUUID().toString(),
                timestamp = crashEvent.timestamp,
                app_version = getAppVersion(),
                user_id = userId,
                exception_type = crashEvent.exception::class.java.simpleName,
                exception_message = crashEvent.exception.message ?: "No message",
                stack_trace = crashEvent.stackTrace,
                error_code = crashEvent.exception.errorCode,
                severity = crashEvent.exception.severity.name,
                context = json.encodeToString(crashEvent.context + customKeys),
                thread = crashEvent.thread,
                is_fatal = crashEvent.isFatal,
                device_info = json.encodeToString(getDeviceInfo()),
                created_at = java.time.Instant.ofEpochMilli(crashEvent.timestamp).toString()
            )

            // 发送到 Supabase
            sendToSupabase(crashReport)

            logger.i("Supabase crash report sent for: ${crashEvent.exception.message}")

        } catch (e: Exception) {
            logger.e("Failed to report crash to Supabase", e)
        }
    }

    override fun setUserId(userId: String) {
        this.userId = userId
        logger.d("Supabase crash reporter user ID set: $userId")
    }

    override fun setCustomKey(key: String, value: Any?) {
        customKeys[key] = value
        logger.d("Supabase crash reporter custom key set: $key = $value")
    }

    override fun logUserAction(action: String, parameters: Map<String, Any?>) {
        logger.i("User action logged: $action, params: $parameters")
        customKeys["last_user_action"] = mapOf(
            "action" to action,
            "parameters" to parameters,
            "timestamp" to System.currentTimeMillis()
        )
    }

    override fun getName(): String = "Supabase"

    private suspend fun sendToSupabase(crashReport: SupabaseCrashReport) {
        try {
            // 使用 Supabase REST API 发送崩溃报告
            val url = "$supabaseUrl/rest/v1/crash_reports"
            val requestBody = json.encodeToString(crashReport)

            // 这里应该使用 HTTP 客户端（如 OkHttp 或 Ktor）发送请求
            // 示例实现：
            /*
            val client = OkHttpClient()
            val request = Request.Builder()
                .url(url)
                .addHeader("apikey", supabaseAnonKey)
                .addHeader("Authorization", "Bearer $supabaseAnonKey")
                .addHeader("Content-Type", "application/json")
                .addHeader("Prefer", "return=minimal")
                .post(requestBody.toRequestBody("application/json".toMediaType()))
                .build()

            val response = client.newCall(request).execute()
            if (!response.isSuccessful) {
                throw Exception("Supabase API error: ${response.code} ${response.message}")
            }
            */

            // 临时实现：记录到日志
            logger.d("Would send crash report to Supabase: $requestBody")

        } catch (e: Exception) {
            logger.e("Failed to send crash report to Supabase", e)
            throw e
        }
    }

    private fun getAppVersion(): String = try {
        context.packageManager.getPackageInfo(context.packageName, 0).versionName ?: "unknown"
    } catch (e: Exception) { "unknown" }

    private fun getDeviceInfo(): Map<String, String> = mapOf(
        "manufacturer" to android.os.Build.MANUFACTURER,
        "model" to android.os.Build.MODEL,
        "android_version" to android.os.Build.VERSION.RELEASE,
        "api_level" to android.os.Build.VERSION.SDK_INT.toString(),
        "architecture" to (System.getProperty("os.arch") ?: "unknown")
    )
}

/**
 * Supabase 崩溃报告数据结构
 * 对应 Supabase 数据库表结构
 */
@Serializable
data class SupabaseCrashReport(
    val id: String,
    val timestamp: Long,
    val app_version: String,
    val user_id: String?,
    val exception_type: String,
    val exception_message: String,
    val stack_trace: String,
    val error_code: String,
    val severity: String,
    val context: String, // JSON 字符串
    val thread: String,
    val is_fatal: Boolean,
    val device_info: String, // JSON 字符串
    val created_at: String // ISO 8601 格式
)

/**
 * 自定义崩溃报告器
 * 发送到自定义服务器
 */
class CustomCrashReporter(
    private val context: Context
) : CrashReporter {
    
    private val logger: QLogger = QLoggerFactory.getLogger(CustomCrashReporter::class)
    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    private var userId: String? = null
    private val customKeys = mutableMapOf<String, Any?>()
    
    override suspend fun reportCrash(crashEvent: CrashEvent) = withContext(Dispatchers.IO) {
        try {
            val report = CrashReport(
                id = UUID.randomUUID().toString(),
                timestamp = crashEvent.timestamp,
                appVersion = getAppVersion(),
                userId = userId,
                exception = ExceptionInfo(
                    type = crashEvent.exception::class.java.simpleName,
                    message = crashEvent.exception.message ?: "No message",
                    stackTrace = crashEvent.stackTrace,
                    errorCode = crashEvent.exception.errorCode,
                    severity = crashEvent.exception.severity.name
                ),
                context = crashEvent.context + customKeys,
                thread = crashEvent.thread,
                isFatal = crashEvent.isFatal,
                deviceInfo = getDeviceInfo()
            )
            
            // 发送到服务器
            sendToServer(report)
            
            logger.i("Custom crash report sent for: ${crashEvent.exception.message}")
            
        } catch (e: Exception) {
            logger.e("Failed to send custom crash report", e)
        }
    }
    
    override fun setUserId(userId: String) {
        this.userId = userId
        logger.d("Custom crash reporter user ID set: $userId")
    }
    
    override fun setCustomKey(key: String, value: Any?) {
        customKeys[key] = value
        logger.d("Custom crash reporter key set: $key = $value")
    }
    
    override fun logUserAction(action: String, parameters: Map<String, Any?>) {
        logger.i("User action logged: $action, params: $parameters")
        // 可以将用户操作添加到崩溃报告的上下文中
        customKeys["last_user_action"] = mapOf(
            "action" to action,
            "parameters" to parameters,
            "timestamp" to System.currentTimeMillis()
        )
    }
    
    override fun getName(): String = "Custom Server"
    
    private suspend fun sendToServer(report: CrashReport) {
        // 这里实现发送到自定义服务器的逻辑
        // 可以使用Retrofit、OkHttp等网络库
        
        // 临时实现：记录到日志
        val reportJson = json.encodeToString(report)
        logger.d("Would send crash report to server: $reportJson")
        
        // 实际实现示例：
        // val response = crashReportingApi.submitCrashReport(report)
        // if (!response.isSuccessful) {
        //     throw Exception("Server returned error: ${response.code()}")
        // }
    }
    
    private fun getAppVersion(): String = try {
        context.packageManager.getPackageInfo(context.packageName, 0).versionName ?: "unknown"
    } catch (e: Exception) { "unknown" }
    
    private fun getDeviceInfo(): DeviceInfo = DeviceInfo(
        manufacturer = android.os.Build.MANUFACTURER,
        model = android.os.Build.MODEL,
        androidVersion = android.os.Build.VERSION.RELEASE,
        apiLevel = android.os.Build.VERSION.SDK_INT,
        architecture = System.getProperty("os.arch") ?: "unknown"
    )
}

/**
 * 本地文件崩溃报告器
 * 将崩溃报告保存到本地文件
 */
class LocalFileCrashReporter(
    private val context: Context
) : CrashReporter {
    
    private val logger: QLogger = QLoggerFactory.getLogger(LocalFileCrashReporter::class)
    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    private val crashReportsDir = File(context.filesDir, "crash_reports")
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    private var userId: String? = null
    private val customKeys = mutableMapOf<String, Any?>()
    
    init {
        // 确保目录存在
        if (!crashReportsDir.exists()) {
            crashReportsDir.mkdirs()
        }
    }
    
    override suspend fun reportCrash(crashEvent: CrashEvent) = withContext(Dispatchers.IO) {
        try {
            val report = CrashReport(
                id = UUID.randomUUID().toString(),
                timestamp = crashEvent.timestamp,
                appVersion = getAppVersion(),
                userId = userId,
                exception = ExceptionInfo(
                    type = crashEvent.exception::class.java.simpleName,
                    message = crashEvent.exception.message ?: "No message",
                    stackTrace = crashEvent.stackTrace,
                    errorCode = crashEvent.exception.errorCode,
                    severity = crashEvent.exception.severity.name
                ),
                context = crashEvent.context + customKeys,
                thread = crashEvent.thread,
                isFatal = crashEvent.isFatal,
                deviceInfo = getDeviceInfo()
            )
            
            // 保存到本地文件
            saveToFile(report)
            
            // 清理旧文件
            cleanupOldReports()
            
            logger.i("Crash report saved to local file: ${crashEvent.exception.message}")
            
        } catch (e: Exception) {
            logger.e("Failed to save crash report to local file", e)
        }
    }
    
    override fun setUserId(userId: String) {
        this.userId = userId
        logger.d("Local crash reporter user ID set: $userId")
    }
    
    override fun setCustomKey(key: String, value: Any?) {
        customKeys[key] = value
        logger.d("Local crash reporter key set: $key = $value")
    }
    
    override fun logUserAction(action: String, parameters: Map<String, Any?>) {
        logger.i("User action logged: $action, params: $parameters")
        customKeys["last_user_action"] = mapOf(
            "action" to action,
            "parameters" to parameters,
            "timestamp" to System.currentTimeMillis()
        )
    }
    
    override fun getName(): String = "Local File"
    
    private fun saveToFile(report: CrashReport) {
        val timestamp = dateFormat.format(Date(report.timestamp))
        val fileName = "crash_${timestamp}_${report.id.take(8)}.json"
        val file = File(crashReportsDir, fileName)
        
        val reportJson = json.encodeToString(report)
        file.writeText(reportJson)
    }
    
    private fun cleanupOldReports() {
        try {
            val files = crashReportsDir.listFiles() ?: return
            val maxFiles = 50 // 最多保留50个崩溃报告
            
            if (files.size > maxFiles) {
                files.sortedBy { it.lastModified() }
                    .take(files.size - maxFiles)
                    .forEach { it.delete() }
            }
        } catch (e: Exception) {
            logger.e("Failed to cleanup old crash reports", e)
        }
    }
    
    private fun getAppVersion(): String = try {
        context.packageManager.getPackageInfo(context.packageName, 0).versionName ?: "unknown"
    } catch (e: Exception) { "unknown" }
    
    private fun getDeviceInfo(): DeviceInfo = DeviceInfo(
        manufacturer = android.os.Build.MANUFACTURER,
        model = android.os.Build.MODEL,
        androidVersion = android.os.Build.VERSION.RELEASE,
        apiLevel = android.os.Build.VERSION.SDK_INT,
        architecture = System.getProperty("os.arch") ?: "unknown"
    )
}

/**
 * 崩溃报告数据结构
 */
@Serializable
data class CrashReport(
    val id: String,
    val timestamp: Long,
    val appVersion: String,
    val userId: String?,
    val exception: ExceptionInfo,
    val context: Map<String, @Serializable(with = AnyValueSerializer::class) Any?>,
    val thread: String,
    val isFatal: Boolean,
    val deviceInfo: DeviceInfo
)

@Serializable
data class ExceptionInfo(
    val type: String,
    val message: String,
    val stackTrace: String,
    val errorCode: String,
    val severity: String
)

@Serializable
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val androidVersion: String,
    val apiLevel: Int,
    val architecture: String
)

/**
 * 任意值序列化器
 */
object AnyValueSerializer : kotlinx.serialization.KSerializer<Any?> {
    override val descriptor = kotlinx.serialization.descriptors.buildClassSerialDescriptor("AnyValue")
    
    override fun serialize(encoder: kotlinx.serialization.encoding.Encoder, value: Any?) {
        when (value) {
            null -> encoder.encodeNull()
            is String -> encoder.encodeString(value)
            is Int -> encoder.encodeInt(value)
            is Long -> encoder.encodeLong(value)
            is Double -> encoder.encodeDouble(value)
            is Boolean -> encoder.encodeBoolean(value)
            else -> encoder.encodeString(value.toString())
        }
    }
    
    override fun deserialize(decoder: kotlinx.serialization.encoding.Decoder): Any? {
        return decoder.decodeString()
    }
}
