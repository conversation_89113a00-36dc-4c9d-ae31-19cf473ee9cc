package com.yu.questicle.core.common.performance

/**
 * 高级性能监控配置
 */
data class AdvancedPerformanceConfig(
    // 监控开关
    val enableFrameRateMonitoring: Boolean = true,
    val enableMemoryMonitoring: Boolean = true,
    val enableCpuMonitoring: Boolean = true,
    val enableNetworkMonitoring: Boolean = false,
    
    // 采样间隔
    val snapshotIntervalMs: Long = 5000L, // 5秒
    val frameRateSamplingInterval: Long = 1000L, // 1秒
    val memorySamplingInterval: Long = 5000L,    // 5秒
    val cpuSamplingInterval: Long = 10000L,      // 10秒
    val networkSamplingInterval: Long = 30000L,  // 30秒
    
    // 性能阈值
    val frameRateThreshold: Int = 55,            // 55fps
    val memoryThreshold: Double = 0.8,           // 80%堆内存
    val cpuThreshold: Double = 80.0,             // 80%CPU
    val networkLatencyThreshold: Double = 1000.0, // 1秒
    
    // 存储配置
    val enableLocalStorage: Boolean = true,
    val enableCloudSync: Boolean = true,
    val maxLocalSnapshots: Int = 1000,
    val uploadBatchSize: Int = 50,
    
    // 告警配置
    val enableAlerts: Boolean = true,
    val alertCooldownMs: Long = 60000L,          // 1分钟冷却
    
    // 分析配置
    val enableTrendAnalysis: Boolean = true,
    val trendAnalysisWindowMs: Long = 3600000L,  // 1小时窗口
    val enablePerformanceScoring: Boolean = true
)

/**
 * 网络监控器
 */
class NetworkMonitor : BaseMonitor<NetworkMetric>() {
    
    // 网络请求统计
    private val requestCount = java.util.concurrent.atomic.AtomicLong(0)
    private val totalResponseTime = java.util.concurrent.atomic.AtomicLong(0)
    private val errorCount = java.util.concurrent.atomic.AtomicLong(0)
    private val totalBytes = java.util.concurrent.atomic.AtomicLong(0)
    
    // 响应时间历史
    private val responseTimeHistory = java.util.concurrent.ConcurrentLinkedQueue<Long>()
    private val maxHistorySize = 100
    
    override suspend fun collectMetric(): NetworkMetric {
        val timestamp = System.currentTimeMillis()
        val requests = requestCount.get()
        
        val avgResponseTime = if (requests > 0) {
            totalResponseTime.get().toDouble() / requests
        } else 0.0
        
        val errorRate = if (requests > 0) {
            errorCount.get().toDouble() / requests
        } else 0.0
        
        val throughput = calculateThroughput()
        
        return NetworkMetric(
            timestamp = timestamp,
            requestCount = requests,
            avgResponseTime = avgResponseTime,
            errorRate = errorRate,
            throughput = throughput
        )
    }
    
    /**
     * 记录网络请求
     */
    fun recordRequest(responseTimeMs: Long, isError: Boolean = false, bytesTransferred: Long = 0) {
        requestCount.incrementAndGet()
        totalResponseTime.addAndGet(responseTimeMs)
        totalBytes.addAndGet(bytesTransferred)
        
        if (isError) {
            errorCount.incrementAndGet()
        }
        
        // 添加到响应时间历史
        responseTimeHistory.offer(responseTimeMs)
        while (responseTimeHistory.size > maxHistorySize) {
            responseTimeHistory.poll()
        }
    }
    
    /**
     * 计算吞吐量 (简化实现)
     */
    private fun calculateThroughput(): Double {
        // 返回每秒字节数
        val durationSeconds = (System.currentTimeMillis() - startTime) / 1000.0
        return if (durationSeconds > 0) {
            totalBytes.get() / durationSeconds
        } else 0.0
    }
    
    /**
     * 重置统计
     */
    fun resetStats() {
        requestCount.set(0)
        totalResponseTime.set(0)
        errorCount.set(0)
        totalBytes.set(0)
        responseTimeHistory.clear()
    }
    
    companion object {
        private val startTime = System.currentTimeMillis()
    }
}

/**
 * 扩展的性能事件
 */
sealed class PerformanceEvent(val timestamp: Long) {
    
    // 原有事件保持不变...
    
    // 新增：高级监控事件
    data class MonitoringStarted(
        val enabledMonitors: List<String>,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class MonitoringStopped(
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class MonitoringError(
        val error: String,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class AdvancedFrameRateAlert(
        val fps: Int,
        val threshold: Int,
        val droppedFrameRate: Double,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class AdvancedMemoryAlert(
        val heapUtilization: Double,
        val threshold: Double,
        val usedHeapMB: Long,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class AdvancedCpuAlert(
        val cpuUsage: Double,
        val threshold: Double,
        val threadCount: Int,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class PerformanceSnapshotTaken(
        val snapshot: PerformanceSnapshot,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class PerformanceScoreChanged(
        val oldScore: Double,
        val newScore: Double,
        val grade: PerformanceGrade,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
    
    data class TrendAnalysisCompleted(
        val trend: PerformanceTrend,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceEvent(eventTimestamp)
}

/**
 * 性能趋势
 */
data class PerformanceTrend(
    val timeWindow: TimeRange,
    val frameRateTrend: TrendDirection,
    val memoryTrend: TrendDirection,
    val cpuTrend: TrendDirection,
    val overallTrend: TrendDirection,
    val recommendations: List<String>
)

/**
 * 趋势方向
 */
enum class TrendDirection {
    IMPROVING,    // 改善
    STABLE,       // 稳定
    DEGRADING,    // 恶化
    UNKNOWN       // 未知
}

/**
 * 性能分析器
 */
class PerformanceAnalyzer {
    
    private val _performanceStatistics = kotlinx.coroutines.flow.MutableStateFlow(PerformanceStatistics.EMPTY)
    val performanceStatistics: kotlinx.coroutines.flow.StateFlow<PerformanceStatistics> = _performanceStatistics.asStateFlow()
    
    /**
     * 开始分析
     */
    suspend fun startAnalysis(performanceData: kotlinx.coroutines.flow.SharedFlow<PerformanceSnapshot>) {
        performanceData.collect { snapshot ->
            // 更新统计信息
            updateStatistics(snapshot)
            
            // 分析趋势
            analyzeTrends(snapshot)
        }
    }
    
    private fun updateStatistics(snapshot: PerformanceSnapshot) {
        // 简化实现，实际应该维护更复杂的统计
        val currentStats = _performanceStatistics.value
        _performanceStatistics.value = currentStats.copy(
            totalSnapshots = currentStats.totalSnapshots + 1,
            performanceScore = snapshot.overall.score
        )
    }
    
    private fun analyzeTrends(snapshot: PerformanceSnapshot) {
        // 简化实现，实际应该分析历史数据趋势
    }
}

/**
 * 性能告警器
 */
class PerformanceAlerter {
    
    private val lastAlertTime = mutableMapOf<String, Long>()
    
    /**
     * 开始告警
     */
    suspend fun startAlerting(
        performanceData: kotlinx.coroutines.flow.SharedFlow<PerformanceSnapshot>,
        config: AdvancedPerformanceConfig
    ) {
        performanceData.collect { snapshot ->
            checkAlerts(snapshot, config)
        }
    }
    
    private suspend fun checkAlerts(snapshot: PerformanceSnapshot, config: AdvancedPerformanceConfig) {
        if (!config.enableAlerts) return
        
        val currentTime = System.currentTimeMillis()
        
        // 检查帧率告警
        snapshot.frameRate?.let { frameRate ->
            if (frameRate.fps < config.frameRateThreshold) {
                triggerAlert("frame_rate", currentTime, config.alertCooldownMs) {
                    // 发送帧率告警
                }
            }
        }
        
        // 检查内存告警
        snapshot.memory?.let { memory ->
            if (memory.heapUtilization > config.memoryThreshold) {
                triggerAlert("memory", currentTime, config.alertCooldownMs) {
                    // 发送内存告警
                }
            }
        }
        
        // 检查CPU告警
        snapshot.cpu?.let { cpu ->
            if (cpu.cpuUsage > config.cpuThreshold) {
                triggerAlert("cpu", currentTime, config.alertCooldownMs) {
                    // 发送CPU告警
                }
            }
        }
    }
    
    private fun triggerAlert(alertType: String, currentTime: Long, cooldownMs: Long, action: () -> Unit) {
        val lastTime = lastAlertTime[alertType] ?: 0
        if (currentTime - lastTime >= cooldownMs) {
            action()
            lastAlertTime[alertType] = currentTime
        }
    }
}
