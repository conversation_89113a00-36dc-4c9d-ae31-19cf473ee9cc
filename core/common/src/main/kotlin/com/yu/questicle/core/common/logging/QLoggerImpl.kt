package com.yu.questicle.core.common.logging

import kotlinx.coroutines.runBlocking
import kotlin.system.measureTimeMillis

/**
 * QLogger的默认实现
 * 
 * 特性:
 * - 高性能异步处理
 * - 自动上下文管理
 * - 敏感信息脱敏
 * - 性能监控集成
 */
internal class QLoggerImpl(
    override val name: String,
    private var _config: LoggerConfig,
    private val processor: LogProcessor
) : QLogger {
    
    override val config: LoggerConfig get() = _config
    
    private var currentContext: LogContext = LogContext.EMPTY
    private var currentTags: Set<String> = emptySet()
    
    // 性能优化：缓存级别检查结果
    @Volatile
    private var verboseEnabled = _config.isLevelEnabled(LogLevel.VERBOSE)
    @Volatile
    private var debugEnabled = _config.isLevelEnabled(LogLevel.DEBUG)
    @Volatile
    private var infoEnabled = _config.isLevelEnabled(LogLevel.INFO)
    @Volatile
    private var warnEnabled = _config.isLevelEnabled(LogLevel.WARN)
    @Volatile
    private var errorEnabled = _config.isLevelEnabled(LogLevel.ERROR)
    @Volatile
    private var fatalEnabled = _config.isLevelEnabled(LogLevel.FATAL)
    
    override fun v(message: String, vararg args: Any?) {
        if (verboseEnabled) {
            log(LogLevel.VERBOSE, null, message, *args)
        }
    }
    
    override fun v(throwable: Throwable, message: String, vararg args: Any?) {
        if (verboseEnabled) {
            log(LogLevel.VERBOSE, throwable, message, *args)
        }
    }
    
    override fun d(message: String, vararg args: Any?) {
        if (debugEnabled) {
            log(LogLevel.DEBUG, null, message, *args)
        }
    }
    
    override fun d(throwable: Throwable, message: String, vararg args: Any?) {
        if (debugEnabled) {
            log(LogLevel.DEBUG, throwable, message, *args)
        }
    }
    
    override fun i(message: String, vararg args: Any?) {
        if (infoEnabled) {
            log(LogLevel.INFO, null, message, *args)
        }
    }
    
    override fun i(throwable: Throwable, message: String, vararg args: Any?) {
        if (infoEnabled) {
            log(LogLevel.INFO, throwable, message, *args)
        }
    }
    
    override fun w(message: String, vararg args: Any?) {
        if (warnEnabled) {
            log(LogLevel.WARN, null, message, *args)
        }
    }
    
    override fun w(throwable: Throwable, message: String, vararg args: Any?) {
        if (warnEnabled) {
            log(LogLevel.WARN, throwable, message, *args)
        }
    }
    
    override fun e(message: String, vararg args: Any?) {
        if (errorEnabled) {
            log(LogLevel.ERROR, null, message, *args)
        }
    }
    
    override fun e(throwable: Throwable, message: String, vararg args: Any?) {
        if (errorEnabled) {
            log(LogLevel.ERROR, throwable, message, *args)
        }
    }
    
    override fun f(message: String, vararg args: Any?) {
        if (fatalEnabled) {
            log(LogLevel.FATAL, null, message, *args)
        }
    }
    
    override fun f(throwable: Throwable, message: String, vararg args: Any?) {
        if (fatalEnabled) {
            log(LogLevel.FATAL, throwable, message, *args)
        }
    }
    
    override fun withContext(key: String, value: Any?): QLogger {
        val newContext = currentContext.copy(
            custom = currentContext.custom + (key to value)
        )
        return createChild(newContext, currentTags)
    }
    
    override fun withContext(context: Map<String, Any?>): QLogger {
        val newContext = currentContext.copy(
            custom = currentContext.custom + context
        )
        return createChild(newContext, currentTags)
    }
    
    override fun withContext(context: LogContext): QLogger {
        val newContext = currentContext.merge(context)
        return createChild(newContext, currentTags)
    }
    
    override fun withTag(tag: String): QLogger {
        val newTags = currentTags + tag
        return createChild(currentContext, newTags)
    }
    
    override fun withTags(vararg tags: String): QLogger {
        val newTags = currentTags + tags.toSet()
        return createChild(currentContext, newTags)
    }
    
    override fun withTags(tags: Collection<String>): QLogger {
        val newTags = currentTags + tags
        return createChild(currentContext, newTags)
    }
    
    override fun withUserId(userId: String): QLogger {
        val newContext = currentContext.copy(userId = userId)
        return createChild(newContext, currentTags)
    }
    
    override fun withSessionId(sessionId: String): QLogger {
        val newContext = currentContext.copy(sessionId = sessionId)
        return createChild(newContext, currentTags)
    }
    
    override fun <T> measureTime(operation: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        return try {
            val result = block()
            val duration = System.currentTimeMillis() - startTime
            if (infoEnabled) {
                logWithDuration(LogLevel.INFO, null, "Operation '$operation' completed", duration)
            }
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            if (errorEnabled) {
                logWithDuration(LogLevel.ERROR, e, "Operation '$operation' failed", duration)
            }
            throw e
        }
    }
    
    override suspend fun <T> measureTimeAsync(operation: String, block: suspend () -> T): T {
        val startTime = System.currentTimeMillis()
        return try {
            val result = block()
            val duration = System.currentTimeMillis() - startTime
            if (infoEnabled) {
                logWithDuration(LogLevel.INFO, null, "Async operation '$operation' completed", duration)
            }
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            if (errorEnabled) {
                logWithDuration(LogLevel.ERROR, e, "Async operation '$operation' failed", duration)
            }
            throw e
        }
    }
    
    override fun isVerboseEnabled(): Boolean = verboseEnabled
    override fun isDebugEnabled(): Boolean = debugEnabled
    override fun isInfoEnabled(): Boolean = infoEnabled
    override fun isWarnEnabled(): Boolean = warnEnabled
    override fun isErrorEnabled(): Boolean = errorEnabled
    override fun isFatalEnabled(): Boolean = fatalEnabled
    
    /**
     * 更新配置
     */
    internal fun updateConfig(newConfig: LoggerConfig) {
        this._config = newConfig
        // 更新缓存的级别检查结果
        verboseEnabled = newConfig.isLevelEnabled(LogLevel.VERBOSE)
        debugEnabled = newConfig.isLevelEnabled(LogLevel.DEBUG)
        infoEnabled = newConfig.isLevelEnabled(LogLevel.INFO)
        warnEnabled = newConfig.isLevelEnabled(LogLevel.WARN)
        errorEnabled = newConfig.isLevelEnabled(LogLevel.ERROR)
        fatalEnabled = newConfig.isLevelEnabled(LogLevel.FATAL)
    }
    
    /**
     * 创建子Logger实例
     */
    private fun createChild(context: LogContext, tags: Set<String>): QLogger {
        return QLoggerImpl(name, _config, processor).apply {
            this.currentContext = context
            this.currentTags = tags
        }
    }
    
    /**
     * 核心日志记录方法
     */
    private fun log(level: LogLevel, throwable: Throwable?, message: String, vararg args: Any?) {
        if (!_config.isLevelEnabled(level) || !_config.shouldSample()) {
            return
        }
        
        val formattedMessage = if (args.isNotEmpty()) {
            try {
                String.format(message, *args)
            } catch (e: Exception) {
                "$message [格式化错误: ${e.message}]"
            }
        } else {
            message
        }
        
        val event = LogEvent(
            level = level,
            loggerName = name,
            message = formattedMessage,
            throwable = throwable,
            context = currentContext,
            tags = currentTags + _config.tags
        )
        
        processor.process(event)
    }
    
    /**
     * 带持续时间的日志记录
     */
    private fun logWithDuration(level: LogLevel, throwable: Throwable?, message: String, duration: Long) {
        if (!_config.isLevelEnabled(level) || !_config.shouldSample()) {
            return
        }
        
        val event = LogEvent(
            level = level,
            loggerName = name,
            message = message,
            throwable = throwable,
            context = currentContext,
            tags = currentTags + _config.tags,
            duration = duration
        )
        
        processor.process(event)
    }
}
