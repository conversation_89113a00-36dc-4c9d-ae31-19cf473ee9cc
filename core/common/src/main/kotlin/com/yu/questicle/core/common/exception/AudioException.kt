package com.yu.questicle.core.common.exception

/**
 * 音频异常基类
 * 
 * 处理所有音频相关的异常，包括音效播放、音乐播放、音频资源加载等
 */
abstract class AudioException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AUDIO_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = true,
    val audioType: AudioType? = null,
    val resourceId: String? = null
) : TechnicalException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.SYSTEM,
    severity = severity,
    userMessage = userMessage,
    context = context + listOfNotNull(
        audioType?.let { "audioType" to it.name },
        resourceId?.let { "resourceId" to it }
    ).toMap(),
    retryable = retryable
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return when (this) {
            is SoundNotFound -> SoundNotFound(message, cause, errorCode, userMessage, context, resourceId)
            is MusicNotFound -> MusicNotFound(message, cause, errorCode, userMessage, context, resourceId)
            is PlaybackFailed -> PlaybackFailed(message, cause, errorCode, severity, userMessage, context, retryable, audioType)
            is AudioInitializationFailed -> AudioInitializationFailed(message, cause, errorCode, userMessage, context)
            is AudioResourceLoadFailed -> AudioResourceLoadFailed(message, cause, errorCode, userMessage, context, resourceId)
            else -> PlaybackFailed(message, cause, errorCode, severity, userMessage, context, retryable, audioType)
        }
    }

    override fun getDefaultUserMessage(): String {
        return when (this) {
            is SoundNotFound -> "音效资源未找到"
            is MusicNotFound -> "音乐资源未找到"
            is PlaybackFailed -> "音频播放失败，请检查设备音频设置"
            is AudioInitializationFailed -> "音频系统初始化失败"
            is AudioResourceLoadFailed -> "音频资源加载失败"
            else -> "音频操作失败，请稍后重试"
        }
    }

    /**
     * 音频类型枚举
     */
    enum class AudioType {
        SOUND_EFFECT,
        MUSIC,
        SYSTEM_SOUND
    }
}

/**
 * 音效资源未找到异常
 */
class SoundNotFound(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "SOUND_NOT_FOUND",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    resourceId: String? = null
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = ErrorSeverity.MEDIUM,
    userMessage = userMessage,
    context = context,
    retryable = false,
    audioType = AudioType.SOUND_EFFECT,
    resourceId = resourceId
)

/**
 * 音乐资源未找到异常
 */
class MusicNotFound(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "MUSIC_NOT_FOUND",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    resourceId: String? = null
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = ErrorSeverity.MEDIUM,
    userMessage = userMessage,
    context = context,
    retryable = false,
    audioType = AudioType.MUSIC,
    resourceId = resourceId
)

/**
 * 音频播放失败异常
 */
class PlaybackFailed(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PLAYBACK_FAILED",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = true,
    audioType: AudioType? = null
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = severity,
    userMessage = userMessage,
    context = context,
    retryable = retryable,
    audioType = audioType
)

/**
 * 音频系统初始化失败异常
 */
class AudioInitializationFailed(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AUDIO_INIT_FAILED",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap()
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context,
    retryable = false,
    audioType = null
)

/**
 * 音频资源加载失败异常
 */
class AudioResourceLoadFailed(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AUDIO_RESOURCE_LOAD_FAILED",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    resourceId: String? = null
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = ErrorSeverity.MEDIUM,
    userMessage = userMessage,
    context = context,
    retryable = true,
    audioType = null,
    resourceId = resourceId
)

/**
 * 音频权限异常
 */
class AudioPermissionException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AUDIO_PERMISSION_DENIED",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val audioPermission: String? = null
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.PERMISSION,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context + listOfNotNull(audioPermission?.let { "permission" to it }).toMap(),
    retryable = false
) {
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return AudioPermissionException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            audioPermission = audioPermission
        )
    }

    override fun getDefaultUserMessage(): String {
        return "需要音频权限才能播放声音"
    }
}

/**
 * 音频设备不可用异常
 */
class AudioDeviceUnavailable(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "AUDIO_DEVICE_UNAVAILABLE",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap()
) : AudioException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context,
    retryable = false,
    audioType = null
) {
    override fun getDefaultUserMessage(): String {
        return "音频设备不可用，请检查设备设置"
    }
}
