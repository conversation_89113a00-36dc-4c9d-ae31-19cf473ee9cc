package com.yu.questicle.core.common.exception

/**
 * 音频相关异常基类
 */
abstract class AudioException(
    message: String,
    errorCode: String,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null,
    val retryable: Boolean = false
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.SYSTEM
}

/**
 * 音效未找到异常
 */
class SoundNotFound(
    soundId: String,
    cause: Throwable? = null
) : AudioException(
    message = "Sound not found: $soundId",
    errorCode = "SOUND_NOT_FOUND",
    severity = ErrorSeverity.MEDIUM,
    cause = cause,
    retryable = false
) {
    init {
        addContext("soundId", soundId)
    }

    override fun getDefaultUserMessage(): String {
        return "音效文件未找到"
    }
}

/**
 * 音乐未找到异常
 */
class MusicNotFound(
    musicId: String,
    cause: Throwable? = null
) : AudioException(
    message = "Music not found: $musicId",
    errorCode = "MUSIC_NOT_FOUND",
    severity = ErrorSeverity.MEDIUM,
    cause = cause,
    retryable = false
) {
    init {
        addContext("musicId", musicId)
    }

    override fun getDefaultUserMessage(): String {
        return "音乐文件未找到"
    }
}

/**
 * 播放失败异常
 */
class PlaybackFailed(
    audioId: String,
    reason: String,
    cause: Throwable? = null
) : AudioException(
    message = "Playback failed for $audioId: $reason",
    errorCode = "PLAYBACK_FAILED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
) {
    init {
        addContext("audioId", audioId)
        addContext("reason", reason)
    }

    override fun getDefaultUserMessage(): String {
        return "音频播放失败"
    }
}

/**
 * 音频初始化失败异常
 */
class AudioInitializationFailed(
    reason: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio initialization failed: $reason",
    errorCode = "AUDIO_INIT_FAILED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
) {
    init {
        addContext("reason", reason)
    }

    override fun getDefaultUserMessage(): String {
        return "音频系统初始化失败"
    }
}

/**
 * 音频资源加载失败异常
 */
class AudioResourceLoadFailed(
    resourcePath: String,
    cause: Throwable? = null
) : AudioException(
    message = "Failed to load audio resource: $resourcePath",
    errorCode = "AUDIO_RESOURCE_LOAD_FAILED",
    severity = ErrorSeverity.MEDIUM,
    cause = cause,
    retryable = true
) {
    init {
        addContext("resourcePath", resourcePath)
    }

    override fun getDefaultUserMessage(): String {
        return "音频资源加载失败"
    }
}

/**
 * 音频权限异常
 */
class AudioPermissionDenied(
    permission: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio permission denied: $permission",
    errorCode = "AUDIO_PERMISSION_DENIED",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = false
) {
    init {
        addContext("permission", permission)
    }

    override fun getDefaultUserMessage(): String {
        return "音频权限被拒绝"
    }
}

/**
 * 音频设备不可用异常
 */
class AudioDeviceUnavailable(
    deviceType: String,
    cause: Throwable? = null
) : AudioException(
    message = "Audio device unavailable: $deviceType",
    errorCode = "AUDIO_DEVICE_UNAVAILABLE",
    severity = ErrorSeverity.HIGH,
    cause = cause,
    retryable = true
) {
    init {
        addContext("deviceType", deviceType)
    }

    override fun getDefaultUserMessage(): String {
        return "音频设备不可用"
    }
}
