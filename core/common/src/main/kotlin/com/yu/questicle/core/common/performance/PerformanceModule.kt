package com.yu.questicle.core.common.performance

import android.content.Context
import com.yu.questicle.core.database.supabase.SupabaseClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 性能监控依赖注入模块
 * 
 * 提供性能监控系统的所有依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object PerformanceModule {
    
    /**
     * 提供帧率监控器
     */
    @Provides
    @Singleton
    fun provideFrameRateMonitor(
        @ApplicationContext context: Context
    ): FrameRateMonitor {
        return FrameRateMonitor(context)
    }
    
    /**
     * 提供内存监控器
     */
    @Provides
    @Singleton
    fun provideMemoryMonitor(
        @ApplicationContext context: Context
    ): MemoryMonitor {
        return MemoryMonitor(context)
    }
    
    /**
     * 提供CPU监控器
     */
    @Provides
    @Singleton
    fun provideCPUMonitor(): CPUMonitor {
        return CPUMonitor()
    }
    
    /**
     * 提供网络监控器
     */
    @Provides
    @Singleton
    fun provideNetworkMonitor(): NetworkMonitor {
        return NetworkMonitor()
    }
    
    /**
     * 提供性能数据仓库
     */
    @Provides
    @Singleton
    fun providePerformanceRepository(
        supabaseClient: SupabaseClient
    ): PerformanceRepository {
        return PerformanceRepository(supabaseClient)
    }
    
    /**
     * 提供性能分析器
     */
    @Provides
    @Singleton
    fun providePerformanceAnalyzer(): PerformanceAnalyzer {
        return PerformanceAnalyzer()
    }
    
    /**
     * 提供性能告警器
     */
    @Provides
    @Singleton
    fun providePerformanceAlerter(): PerformanceAlerter {
        return PerformanceAlerter()
    }
    
    /**
     * 提供性能监控门面
     */
    @Provides
    @Singleton
    fun providePerformanceMonitorFacade(
        performanceMonitor: PerformanceMonitor
    ): PerformanceMonitorFacade {
        return PerformanceMonitorFacade(performanceMonitor)
    }
}

/**
 * 性能监控门面
 * 
 * 提供简化的性能监控接口，隐藏内部复杂性
 */
@Singleton
class PerformanceMonitorFacade @javax.inject.Inject constructor(
    private val performanceMonitor: PerformanceMonitor
) {
    
    /**
     * 启动性能监控
     */
    suspend fun startMonitoring(config: AdvancedPerformanceConfig = AdvancedPerformanceConfig()) {
        performanceMonitor.startAdvancedMonitoring(config)
    }
    
    /**
     * 停止性能监控
     */
    suspend fun stopMonitoring() {
        performanceMonitor.stopAdvancedMonitoring()
    }
    
    /**
     * 获取当前性能快照
     */
    suspend fun getCurrentSnapshot(): PerformanceSnapshot? {
        return performanceMonitor.getCurrentPerformanceSnapshot()
    }
    
    /**
     * 获取性能历史
     */
    suspend fun getPerformanceHistory(timeRange: TimeRange): List<PerformanceSnapshot> {
        return performanceMonitor.getPerformanceHistory(timeRange)
    }
    
    /**
     * 获取性能统计
     */
    suspend fun getPerformanceStatistics(): PerformanceStatistics {
        return performanceMonitor.getPerformanceStatistics()
    }
    
    /**
     * 监听性能快照
     */
    fun observePerformanceSnapshots() = performanceMonitor.performanceSnapshots
    
    /**
     * 监听性能事件
     */
    fun observePerformanceEvents() = performanceMonitor.performanceEvents
    
    /**
     * 检查是否正在监控
     */
    fun isMonitoring() = performanceMonitor.isAdvancedMonitoring.value
    
    /**
     * 强制触发性能快照
     */
    suspend fun triggerSnapshot(): PerformanceSnapshot? {
        return performanceMonitor.triggerSnapshot()
    }
    
    /**
     * 添加性能监听器
     */
    fun addPerformanceListener(listener: PerformanceListener) {
        // 通过 Flow 转换为监听器模式
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Default).launch {
            performanceMonitor.performanceSnapshots.collect { snapshot ->
                listener.onPerformanceSnapshot(snapshot)
            }
        }
    }
}

/**
 * 性能监听器接口
 */
interface PerformanceListener {
    fun onPerformanceSnapshot(snapshot: PerformanceSnapshot)
    fun onPerformanceAlert(alert: PerformanceAlert) {}
    fun onMonitoringStateChanged(isMonitoring: Boolean) {}
}

/**
 * 性能告警
 */
sealed class PerformanceAlert(val timestamp: Long, val severity: AlertSeverity) {
    
    data class LowFrameRate(
        val fps: Int,
        val threshold: Int,
        private val alertTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceAlert(alertTimestamp, AlertSeverity.MEDIUM)
    
    data class HighMemoryUsage(
        val heapUtilization: Double,
        val threshold: Double,
        private val alertTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceAlert(alertTimestamp, AlertSeverity.HIGH)
    
    data class HighCpuUsage(
        val cpuUsage: Double,
        val threshold: Double,
        private val alertTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceAlert(alertTimestamp, AlertSeverity.HIGH)
    
    data class MemoryPressure(
        val availableMemoryMB: Long,
        private val alertTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceAlert(alertTimestamp, AlertSeverity.CRITICAL)
    
    data class PerformanceDegradation(
        val oldScore: Double,
        val newScore: Double,
        private val alertTimestamp: Long = System.currentTimeMillis()
    ) : PerformanceAlert(alertTimestamp, AlertSeverity.MEDIUM)
}

/**
 * 告警严重程度
 */
enum class AlertSeverity {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

/**
 * 扩展函数：简化性能监控使用
 */
suspend inline fun <T> PerformanceMonitorFacade.measurePerformance(
    operationName: String,
    crossinline operation: suspend () -> T
): T {
    val startTime = System.currentTimeMillis()
    val startSnapshot = getCurrentSnapshot()
    
    return try {
        val result = operation()
        val endTime = System.currentTimeMillis()
        val endSnapshot = getCurrentSnapshot()
        
        // 记录操作性能
        logOperationPerformance(operationName, startTime, endTime, startSnapshot, endSnapshot)
        
        result
    } catch (e: Exception) {
        val endTime = System.currentTimeMillis()
        logOperationError(operationName, startTime, endTime, e)
        throw e
    }
}

/**
 * 记录操作性能
 */
private fun logOperationPerformance(
    operationName: String,
    startTime: Long,
    endTime: Long,
    startSnapshot: PerformanceSnapshot?,
    endSnapshot: PerformanceSnapshot?
) {
    val duration = endTime - startTime
    
    // 这里可以记录到日志或发送事件
    println("Operation '$operationName' completed in ${duration}ms")
    
    if (startSnapshot != null && endSnapshot != null) {
        val scoreDiff = endSnapshot.overall.score - startSnapshot.overall.score
        if (scoreDiff < -10) {
            println("Performance degradation detected during '$operationName': ${scoreDiff.toInt()} points")
        }
    }
}

/**
 * 记录操作错误
 */
private fun logOperationError(
    operationName: String,
    startTime: Long,
    endTime: Long,
    error: Exception
) {
    val duration = endTime - startTime
    println("Operation '$operationName' failed after ${duration}ms: ${error.message}")
}
