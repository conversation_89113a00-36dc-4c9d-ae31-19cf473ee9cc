package com.yu.questicle.core.common.performance

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 企业级性能监控系统
 * 
 * 提供全面的性能监控、度量收集和分析功能
 * 支持实时监控、历史数据分析和性能优化建议
 * 
 * 功能特性:
 * - 方法执行时间监控
 * - 内存使用情况跟踪
 * - 帧率性能监控
 * - 网络请求性能分析
 * - 自动性能报告生成
 * - 性能阈值告警
 * 
 * 使用示例:
 * ```kotlin
 * // 监控方法执行
 * val result = performanceMonitor.measureOperation("fetchData") {
 *     repository.fetchData()
 * }
 * 
 * // 监控内存使用
 * performanceMonitor.recordMemoryUsage("GameEngine", 150)
 * 
 * // 监控帧率
 * performanceMonitor.recordFrameRate(60)
 * ```
 */
@Singleton
class PerformanceMonitor @Inject constructor() {

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    
    // 性能指标存储
    private val operationMetrics = ConcurrentHashMap<String, OperationMetrics>()
    private val memoryMetrics = ConcurrentHashMap<String, MemoryMetrics>()
    private val frameRateMetrics = FrameRateMetrics()
    private val networkMetrics = ConcurrentHashMap<String, NetworkMetrics>()
    
    // 事件流
    private val _performanceEvents = MutableSharedFlow<PerformanceEvent>()
    val performanceEvents: SharedFlow<PerformanceEvent> = _performanceEvents.asSharedFlow()
    
    // 配置
    private val config = PerformanceConfig()

    /**
     * 测量操作执行时间
     */
    inline fun <T> measureOperation(
        operationName: String,
        category: String = "default",
        block: () -> T
    ): T {
        val startTime = System.nanoTime()
        val startMemory = getCurrentMemoryUsage()

        return try {
            block()
        } finally {
            val endTime = System.nanoTime()
            val endMemory = getCurrentMemoryUsage()
            val duration = endTime - startTime
            val memoryDelta = endMemory - startMemory

            recordOperationMetric(operationName, category, duration, memoryDelta)
        }
    }

    /**
     * 测量挂起操作执行时间
     */
    suspend inline fun <T> measureSuspendOperation(
        operationName: String,
        category: String = "default",
        block: suspend () -> T
    ): T {
        val startTime = System.nanoTime()
        val startMemory = getCurrentMemoryUsage()

        return try {
            block()
        } finally {
            val endTime = System.nanoTime()
            val endMemory = getCurrentMemoryUsage()
            val duration = endTime - startTime
            val memoryDelta = endMemory - startMemory

            recordOperationMetric(operationName, category, duration, memoryDelta)
        }
    }

    /**
     * 记录操作指标
     */
    fun recordOperationMetric(
        operationName: String,
        category: String,
        durationNanos: Long,
        memoryDeltaBytes: Long
    ) {
        val metrics = operationMetrics.getOrPut(operationName) { 
            OperationMetrics(operationName, category) 
        }
        
        metrics.addSample(durationNanos, memoryDeltaBytes)
        
        // 检查性能阈值
        checkPerformanceThresholds(operationName, metrics)
        
        // 发送性能事件
        scope.launch {
            _performanceEvents.emit(
                PerformanceEvent.OperationCompleted(
                    operationName = operationName,
                    category = category,
                    durationMs = durationNanos / 1_000_000,
                    memoryDeltaMB = memoryDeltaBytes / (1024 * 1024),
                    timestamp = System.currentTimeMillis()
                )
            )
        }
    }

    /**
     * 记录内存使用情况
     */
    fun recordMemoryUsage(component: String, memoryMB: Long) {
        val metrics = memoryMetrics.getOrPut(component) { MemoryMetrics(component) }
        metrics.addSample(memoryMB)
        
        // 检查内存阈值
        if (memoryMB > config.memoryWarningThresholdMB) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.MemoryWarning(
                        component = component,
                        memoryMB = memoryMB,
                        threshold = config.memoryWarningThresholdMB,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 记录帧率
     */
    fun recordFrameRate(fps: Int) {
        frameRateMetrics.addSample(fps)
        
        // 检查帧率阈值
        if (fps < config.frameRateWarningThreshold) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.FrameRateWarning(
                        fps = fps,
                        threshold = config.frameRateWarningThreshold,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 记录网络请求性能
     */
    fun recordNetworkRequest(
        url: String,
        method: String,
        durationMs: Long,
        responseSize: Long,
        statusCode: Int
    ) {
        val key = "$method:$url"
        val metrics = networkMetrics.getOrPut(key) { NetworkMetrics(url, method) }
        metrics.addSample(durationMs, responseSize, statusCode)
        
        // 检查网络性能阈值
        if (durationMs > config.networkSlowThresholdMs) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.SlowNetworkRequest(
                        url = url,
                        method = method,
                        durationMs = durationMs,
                        threshold = config.networkSlowThresholdMs,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取操作性能报告
     */
    fun getOperationReport(operationName: String): OperationReport? {
        return operationMetrics[operationName]?.let { metrics ->
            OperationReport(
                operationName = operationName,
                category = metrics.category,
                totalCalls = metrics.totalCalls.get(),
                averageDurationMs = metrics.getAverageDurationMs(),
                minDurationMs = metrics.getMinDurationMs(),
                maxDurationMs = metrics.getMaxDurationMs(),
                p95DurationMs = metrics.getP95DurationMs(),
                averageMemoryDeltaMB = metrics.getAverageMemoryDeltaMB(),
                lastUpdated = metrics.lastUpdated.get()
            )
        }
    }

    /**
     * 获取全部性能报告
     */
    fun getFullPerformanceReport(): PerformanceReport {
        return PerformanceReport(
            operationReports = operationMetrics.values.mapNotNull { metrics ->
                getOperationReport(metrics.operationName)
            },
            memoryReport = MemoryReport(
                components = memoryMetrics.values.map { metrics ->
                    ComponentMemoryReport(
                        component = metrics.component,
                        currentMemoryMB = metrics.getCurrentMemoryMB(),
                        averageMemoryMB = metrics.getAverageMemoryMB(),
                        peakMemoryMB = metrics.getPeakMemoryMB()
                    )
                }
            ),
            frameRateReport = FrameRateReport(
                averageFps = frameRateMetrics.getAverageFps(),
                minFps = frameRateMetrics.getMinFps(),
                maxFps = frameRateMetrics.getMaxFps(),
                frameDrops = frameRateMetrics.getFrameDrops()
            ),
            networkReport = NetworkReport(
                requests = networkMetrics.values.map { metrics ->
                    NetworkRequestReport(
                        url = metrics.url,
                        method = metrics.method,
                        totalRequests = metrics.totalRequests.get(),
                        averageDurationMs = metrics.getAverageDurationMs(),
                        successRate = metrics.getSuccessRate(),
                        averageResponseSizeKB = metrics.getAverageResponseSizeKB()
                    )
                }
            ),
            timestamp = System.currentTimeMillis()
        )
    }

    /**
     * 清理旧的性能数据
     */
    fun cleanup() {
        val cutoffTime = System.currentTimeMillis() - config.dataRetentionMs
        
        operationMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        memoryMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        networkMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        
        frameRateMetrics.cleanup(cutoffTime)
    }

    private fun checkPerformanceThresholds(operationName: String, metrics: OperationMetrics) {
        val avgDurationMs = metrics.getAverageDurationMs()
        
        if (avgDurationMs > config.operationSlowThresholdMs) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.SlowOperation(
                        operationName = operationName,
                        averageDurationMs = avgDurationMs,
                        threshold = config.operationSlowThresholdMs,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取当前内存使用量（公开方法，可被inline函数调用）
     */
    fun getCurrentMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }
}

/**
 * 性能监控配置
 */
data class PerformanceConfig(
    val operationSlowThresholdMs: Long = 1000L,
    val memoryWarningThresholdMB: Long = 200L,
    val frameRateWarningThreshold: Int = 30,
    val networkSlowThresholdMs: Long = 3000L,
    val dataRetentionMs: Long = 24 * 60 * 60 * 1000L // 24小时
)

/**
 * 性能事件密封类
 */
sealed class PerformanceEvent {
    abstract val timestamp: Long

    data class OperationCompleted(
        val operationName: String,
        val category: String,
        val durationMs: Long,
        val memoryDeltaMB: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class SlowOperation(
        val operationName: String,
        val averageDurationMs: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class MemoryWarning(
        val component: String,
        val memoryMB: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class FrameRateWarning(
        val fps: Int,
        val threshold: Int,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class SlowNetworkRequest(
        val url: String,
        val method: String,
        val durationMs: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()
}
