package com.yu.questicle.core.common.performance

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 企业级性能监控系统 (Enhanced for Day 8-9)
 *
 * 提供全面的性能监控、度量收集和分析功能
 * 支持实时监控、历史数据分析和性能优化建议
 *
 * 功能特性:
 * - 方法执行时间监控
 * - 内存使用情况跟踪
 * - 帧率性能监控 (Enhanced with Choreographer)
 * - CPU使用率监控 (New)
 * - 网络请求性能分析
 * - 自动性能报告生成
 * - 性能阈值告警
 * - Supabase云端数据同步 (New)
 * - 实时性能快照 (New)
 *
 * 使用示例:
 * ```kotlin
 * // 启动全面监控
 * performanceMonitor.startAdvancedMonitoring()
 *
 * // 监控方法执行
 * val result = performanceMonitor.measureOperation("fetchData") {
 *     repository.fetchData()
 * }
 *
 * // 获取实时性能快照
 * val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
 * ```
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    private val frameRateMonitor: FrameRateMonitor,
    private val memoryMonitor: MemoryMonitor,
    private val cpuMonitor: CPUMonitor,
    private val performanceRepository: PerformanceRepository
) {

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    // 原有性能指标存储
    private val operationMetrics = ConcurrentHashMap<String, OperationMetrics>()
    private val memoryMetrics = ConcurrentHashMap<String, MemoryMetrics>()
    private val frameRateMetrics = FrameRateMetrics()
    private val networkMetrics = ConcurrentHashMap<String, NetworkMetrics>()

    // 新增：高级监控状态
    private val _isAdvancedMonitoring = MutableStateFlow(false)
    val isAdvancedMonitoring: StateFlow<Boolean> = _isAdvancedMonitoring.asStateFlow()

    // 新增：实时性能快照流
    private val _performanceSnapshots = MutableSharedFlow<PerformanceSnapshot>(
        replay = 1,
        extraBufferCapacity = 50
    )
    val performanceSnapshots: SharedFlow<PerformanceSnapshot> = _performanceSnapshots.asSharedFlow()

    // 事件流
    private val _performanceEvents = MutableSharedFlow<PerformanceEvent>()
    val performanceEvents: SharedFlow<PerformanceEvent> = _performanceEvents.asSharedFlow()

    // 配置
    private val config = PerformanceConfig()

    // 新增：高级监控配置
    private var advancedConfig = AdvancedPerformanceConfig()

    /**
     * 测量操作执行时间
     */
    inline fun <T> measureOperation(
        operationName: String,
        category: String = "default",
        block: () -> T
    ): T {
        val startTime = System.nanoTime()
        val startMemory = getCurrentMemoryUsage()

        return try {
            block()
        } finally {
            val endTime = System.nanoTime()
            val endMemory = getCurrentMemoryUsage()
            val duration = endTime - startTime
            val memoryDelta = endMemory - startMemory

            recordOperationMetric(operationName, category, duration, memoryDelta)
        }
    }

    /**
     * 测量挂起操作执行时间
     */
    suspend inline fun <T> measureSuspendOperation(
        operationName: String,
        category: String = "default",
        block: suspend () -> T
    ): T {
        val startTime = System.nanoTime()
        val startMemory = getCurrentMemoryUsage()

        return try {
            block()
        } finally {
            val endTime = System.nanoTime()
            val endMemory = getCurrentMemoryUsage()
            val duration = endTime - startTime
            val memoryDelta = endMemory - startMemory

            recordOperationMetric(operationName, category, duration, memoryDelta)
        }
    }

    /**
     * 记录操作指标
     */
    fun recordOperationMetric(
        operationName: String,
        category: String,
        durationNanos: Long,
        memoryDeltaBytes: Long
    ) {
        val metrics = operationMetrics.getOrPut(operationName) { 
            OperationMetrics(operationName, category) 
        }
        
        metrics.addSample(durationNanos, memoryDeltaBytes)
        
        // 检查性能阈值
        checkPerformanceThresholds(operationName, metrics)
        
        // 发送性能事件
        scope.launch {
            _performanceEvents.emit(
                PerformanceEvent.OperationCompleted(
                    operationName = operationName,
                    category = category,
                    durationMs = durationNanos / 1_000_000,
                    memoryDeltaMB = memoryDeltaBytes / (1024 * 1024),
                    timestamp = System.currentTimeMillis()
                )
            )
        }
    }

    /**
     * 记录内存使用情况
     */
    fun recordMemoryUsage(component: String, memoryMB: Long) {
        val metrics = memoryMetrics.getOrPut(component) { MemoryMetrics(component) }
        metrics.addSample(memoryMB)
        
        // 检查内存阈值
        if (memoryMB > config.memoryWarningThresholdMB) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.MemoryWarning(
                        component = component,
                        memoryMB = memoryMB,
                        threshold = config.memoryWarningThresholdMB,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 记录帧率
     */
    fun recordFrameRate(fps: Int) {
        frameRateMetrics.addSample(fps)
        
        // 检查帧率阈值
        if (fps < config.frameRateWarningThreshold) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.FrameRateWarning(
                        fps = fps,
                        threshold = config.frameRateWarningThreshold,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 记录网络请求性能
     */
    fun recordNetworkRequest(
        url: String,
        method: String,
        durationMs: Long,
        responseSize: Long,
        statusCode: Int
    ) {
        val key = "$method:$url"
        val metrics = networkMetrics.getOrPut(key) { NetworkMetrics(url, method) }
        metrics.addSample(durationMs, responseSize, statusCode)
        
        // 检查网络性能阈值
        if (durationMs > config.networkSlowThresholdMs) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.SlowNetworkRequest(
                        url = url,
                        method = method,
                        durationMs = durationMs,
                        threshold = config.networkSlowThresholdMs,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取操作性能报告
     */
    fun getOperationReport(operationName: String): OperationReport? {
        return operationMetrics[operationName]?.let { metrics ->
            OperationReport(
                operationName = operationName,
                category = metrics.category,
                totalCalls = metrics.totalCalls.get(),
                averageDurationMs = metrics.getAverageDurationMs(),
                minDurationMs = metrics.getMinDurationMs(),
                maxDurationMs = metrics.getMaxDurationMs(),
                p95DurationMs = metrics.getP95DurationMs(),
                averageMemoryDeltaMB = metrics.getAverageMemoryDeltaMB(),
                lastUpdated = metrics.lastUpdated.get()
            )
        }
    }

    /**
     * 获取全部性能报告
     */
    fun getFullPerformanceReport(): PerformanceReport {
        return PerformanceReport(
            operationReports = operationMetrics.values.mapNotNull { metrics ->
                getOperationReport(metrics.operationName)
            },
            memoryReport = MemoryReport(
                components = memoryMetrics.values.map { metrics ->
                    ComponentMemoryReport(
                        component = metrics.component,
                        currentMemoryMB = metrics.getCurrentMemoryMB(),
                        averageMemoryMB = metrics.getAverageMemoryMB(),
                        peakMemoryMB = metrics.getPeakMemoryMB()
                    )
                }
            ),
            frameRateReport = FrameRateReport(
                averageFps = frameRateMetrics.getAverageFps(),
                minFps = frameRateMetrics.getMinFps(),
                maxFps = frameRateMetrics.getMaxFps(),
                frameDrops = frameRateMetrics.getFrameDrops()
            ),
            networkReport = NetworkReport(
                requests = networkMetrics.values.map { metrics ->
                    NetworkRequestReport(
                        url = metrics.url,
                        method = metrics.method,
                        totalRequests = metrics.totalRequests.get(),
                        averageDurationMs = metrics.getAverageDurationMs(),
                        successRate = metrics.getSuccessRate(),
                        averageResponseSizeKB = metrics.getAverageResponseSizeKB()
                    )
                }
            ),
            timestamp = System.currentTimeMillis()
        )
    }

    /**
     * 清理旧的性能数据
     */
    fun cleanup() {
        val cutoffTime = System.currentTimeMillis() - config.dataRetentionMs
        
        operationMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        memoryMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        networkMetrics.values.removeAll { it.lastUpdated.get() < cutoffTime }
        
        frameRateMetrics.cleanup(cutoffTime)
    }

    private fun checkPerformanceThresholds(operationName: String, metrics: OperationMetrics) {
        val avgDurationMs = metrics.getAverageDurationMs()
        
        if (avgDurationMs > config.operationSlowThresholdMs) {
            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.SlowOperation(
                        operationName = operationName,
                        averageDurationMs = avgDurationMs,
                        threshold = config.operationSlowThresholdMs,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取当前内存使用量（公开方法，可被inline函数调用）
     */
    fun getCurrentMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        return runtime.totalMemory() - runtime.freeMemory()
    }

    // ==================== 新增：高级监控功能 ====================

    /**
     * 启动高级性能监控
     */
    suspend fun startAdvancedMonitoring(config: AdvancedPerformanceConfig = AdvancedPerformanceConfig()) {
        if (_isAdvancedMonitoring.compareAndSet(false, true)) {
            advancedConfig = config

            // 启动各个监控器
            if (config.enableFrameRateMonitoring) {
                frameRateMonitor.startMonitoring()
            }

            if (config.enableMemoryMonitoring) {
                memoryMonitor.startMonitoring()
            }

            if (config.enableCpuMonitoring) {
                cpuMonitor.startMonitoring()
            }

            // 启动性能快照收集
            startPerformanceSnapshotCollection()

            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.MonitoringStarted(
                        timestamp = System.currentTimeMillis(),
                        enabledMonitors = listOfNotNull(
                            if (config.enableFrameRateMonitoring) "FrameRate" else null,
                            if (config.enableMemoryMonitoring) "Memory" else null,
                            if (config.enableCpuMonitoring) "CPU" else null
                        )
                    )
                )
            }
        }
    }

    /**
     * 停止高级性能监控
     */
    suspend fun stopAdvancedMonitoring() {
        if (_isAdvancedMonitoring.compareAndSet(true, false)) {
            // 停止各个监控器
            frameRateMonitor.stopMonitoring()
            memoryMonitor.stopMonitoring()
            cpuMonitor.stopMonitoring()

            scope.launch {
                _performanceEvents.emit(
                    PerformanceEvent.MonitoringStopped(
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取当前性能快照
     */
    suspend fun getCurrentPerformanceSnapshot(): PerformanceSnapshot {
        val timestamp = System.currentTimeMillis()

        val frameRateMetric = if (advancedConfig.enableFrameRateMonitoring) {
            frameRateMonitor.getCurrentMetric()
        } else null

        val memoryMetric = if (advancedConfig.enableMemoryMonitoring) {
            memoryMonitor.getCurrentMetric()
        } else null

        val cpuMetric = if (advancedConfig.enableCpuMonitoring) {
            cpuMonitor.getCurrentMetric()
        } else null

        return PerformanceSnapshot(
            timestamp = timestamp,
            frameRate = frameRateMetric,
            memory = memoryMetric,
            cpu = cpuMetric,
            network = null, // TODO: 实现网络监控
            overall = calculateOverallPerformance(frameRateMetric, memoryMetric, cpuMetric)
        )
    }

    /**
     * 启动性能快照收集
     */
    private fun startPerformanceSnapshotCollection() {
        scope.launch {
            while (_isAdvancedMonitoring.value) {
                try {
                    val snapshot = getCurrentPerformanceSnapshot()

                    // 发送到快照流
                    _performanceSnapshots.emit(snapshot)

                    // 保存到仓库
                    performanceRepository.savePerformanceSnapshot(snapshot)

                    // 检查性能告警
                    checkAdvancedPerformanceAlerts(snapshot)

                } catch (e: Exception) {
                    scope.launch {
                        _performanceEvents.emit(
                            PerformanceEvent.MonitoringError(
                                error = e.message ?: "Unknown error",
                                timestamp = System.currentTimeMillis()
                            )
                        )
                    }
                }

                delay(advancedConfig.snapshotIntervalMs)
            }
        }
    }

    /**
     * 计算整体性能评分
     */
    private fun calculateOverallPerformance(
        frameRate: FrameRateMetric?,
        memory: MemoryMetric?,
        cpu: CPUMetric?
    ): OverallPerformance {
        var score = 100.0
        val issues = mutableListOf<String>()

        // 帧率评分
        frameRate?.let { fr ->
            if (fr.fps < 30) {
                score -= 30
                issues.add("Low frame rate: ${fr.fps} fps")
            } else if (fr.fps < 50) {
                score -= 15
                issues.add("Moderate frame rate: ${fr.fps} fps")
            }
        }

        // 内存评分
        memory?.let { mem ->
            if (mem.heapUtilization > 0.9) {
                score -= 25
                issues.add("High memory usage: ${(mem.heapUtilization * 100).toInt()}%")
            } else if (mem.heapUtilization > 0.7) {
                score -= 10
                issues.add("Moderate memory usage: ${(mem.heapUtilization * 100).toInt()}%")
            }
        }

        // CPU评分
        cpu?.let { c ->
            if (c.cpuUsage > 80) {
                score -= 20
                issues.add("High CPU usage: ${c.cpuUsage.toInt()}%")
            } else if (c.cpuUsage > 60) {
                score -= 10
                issues.add("Moderate CPU usage: ${c.cpuUsage.toInt()}%")
            }
        }

        return OverallPerformance(
            score = score.coerceAtLeast(0.0),
            grade = when {
                score >= 90 -> PerformanceGrade.EXCELLENT
                score >= 75 -> PerformanceGrade.GOOD
                score >= 60 -> PerformanceGrade.FAIR
                score >= 40 -> PerformanceGrade.POOR
                else -> PerformanceGrade.CRITICAL
            },
            issues = issues
        )
    }

    /**
     * 检查高级性能告警
     */
    private suspend fun checkAdvancedPerformanceAlerts(snapshot: PerformanceSnapshot) {
        // 帧率告警
        snapshot.frameRate?.let { frameRate ->
            if (frameRate.fps < advancedConfig.frameRateThreshold) {
                _performanceEvents.emit(
                    PerformanceEvent.AdvancedFrameRateAlert(
                        fps = frameRate.fps,
                        threshold = advancedConfig.frameRateThreshold,
                        droppedFrameRate = frameRate.droppedFrameRate,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }

        // 内存告警
        snapshot.memory?.let { memory ->
            if (memory.heapUtilization > advancedConfig.memoryThreshold) {
                _performanceEvents.emit(
                    PerformanceEvent.AdvancedMemoryAlert(
                        heapUtilization = memory.heapUtilization,
                        threshold = advancedConfig.memoryThreshold,
                        usedHeapMB = memory.usedHeap / (1024 * 1024),
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }

        // CPU告警
        snapshot.cpu?.let { cpu ->
            if (cpu.cpuUsage > advancedConfig.cpuThreshold) {
                _performanceEvents.emit(
                    PerformanceEvent.AdvancedCpuAlert(
                        cpuUsage = cpu.cpuUsage,
                        threshold = advancedConfig.cpuThreshold,
                        threadCount = cpu.threadCount,
                        timestamp = System.currentTimeMillis()
                    )
                )
            }
        }
    }

    /**
     * 获取性能历史数据
     */
    suspend fun getPerformanceHistory(timeRange: TimeRange): List<PerformanceSnapshot> {
        return performanceRepository.getPerformanceHistory(timeRange)
    }

    /**
     * 获取性能统计
     */
    suspend fun getPerformanceStatistics(): PerformanceStatistics {
        return performanceRepository.getPerformanceStatistics()
    }
}

/**
 * 性能监控配置
 */
data class PerformanceConfig(
    val operationSlowThresholdMs: Long = 1000L,
    val memoryWarningThresholdMB: Long = 200L,
    val frameRateWarningThreshold: Int = 30,
    val networkSlowThresholdMs: Long = 3000L,
    val dataRetentionMs: Long = 24 * 60 * 60 * 1000L // 24小时
)

/**
 * 性能事件密封类
 */
sealed class PerformanceEvent {
    abstract val timestamp: Long

    data class OperationCompleted(
        val operationName: String,
        val category: String,
        val durationMs: Long,
        val memoryDeltaMB: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class SlowOperation(
        val operationName: String,
        val averageDurationMs: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class MemoryWarning(
        val component: String,
        val memoryMB: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class FrameRateWarning(
        val fps: Int,
        val threshold: Int,
        override val timestamp: Long
    ) : PerformanceEvent()

    data class SlowNetworkRequest(
        val url: String,
        val method: String,
        val durationMs: Long,
        val threshold: Long,
        override val timestamp: Long
    ) : PerformanceEvent()
}
