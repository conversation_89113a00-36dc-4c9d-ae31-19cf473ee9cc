package com.yu.questicle.core.common.exception

/**
 * Questicle应用的根异常类
 * 
 * 所有业务异常都应该继承自此类，提供统一的异常处理机制
 */
abstract class QuesticleException(
    message: String,
    cause: Throwable? = null,
    val errorCode: String = "UNKNOWN_ERROR",
    val errorType: ErrorType = ErrorType.UNKNOWN,
    val severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    val userMessage: String? = null,
    val context: Map<String, Any?> = emptyMap(),
    val timestamp: Long = System.currentTimeMillis(),
    val retryable: Boolean = false
) : Exception(message, cause) {
    
    /**
     * 获取用户友好的错误消息
     */
    fun getUserFriendlyMessage(): String {
        return userMessage ?: getDefaultUserMessage()
    }
    
    /**
     * 获取完整的错误信息
     */
    fun getFullErrorInfo(): ErrorInfo {
        return ErrorInfo(
            errorCode = errorCode,
            errorType = errorType,
            severity = severity,
            message = message ?: "Unknown error",
            userMessage = getUserFriendlyMessage(),
            cause = cause?.message,
            context = context,
            timestamp = timestamp,
            retryable = retryable,
            stackTrace = stackTraceToString()
        )
    }
    
    /**
     * 添加上下文信息
     */
    fun withContext(key: String, value: Any?): QuesticleException {
        return createCopy(context = context + (key to value))
    }
    
    /**
     * 添加多个上下文信息
     */
    fun withContext(additionalContext: Map<String, Any?>): QuesticleException {
        return createCopy(context = context + additionalContext)
    }
    
    /**
     * 设置用户消息
     */
    fun withUserMessage(message: String): QuesticleException {
        return createCopy(userMessage = message)
    }
    
    /**
     * 创建副本（子类需要实现）
     */
    protected abstract fun createCopy(
        message: String = this.message ?: "Unknown error",
        cause: Throwable? = this.cause,
        errorCode: String = this.errorCode,
        errorType: ErrorType = this.errorType,
        severity: ErrorSeverity = this.severity,
        userMessage: String? = this.userMessage,
        context: Map<String, Any?> = this.context,
        retryable: Boolean = this.retryable
    ): QuesticleException
    
    /**
     * 获取默认用户消息
     */
    protected abstract fun getDefaultUserMessage(): String
}

/**
 * 错误类型枚举
 */
enum class ErrorType(val displayName: String) {
    VALIDATION("输入验证错误"),
    BUSINESS("业务逻辑错误"),
    NETWORK("网络连接错误"),
    DATABASE("数据存储错误"),
    PERMISSION("权限错误"),
    CONFIGURATION("配置错误"),
    SYSTEM("系统错误"),
    UNKNOWN("未知错误")
}

/**
 * 错误严重级别
 */
enum class ErrorSeverity(val level: Int, val displayName: String) {
    LOW(1, "轻微"),        // 不影响核心功能，用户可以继续使用
    MEDIUM(2, "中等"),     // 影响部分功能，但不影响主要流程
    HIGH(3, "严重"),       // 影响核心功能，需要立即处理
    CRITICAL(4, "致命")    // 导致应用不可用，需要紧急处理
}

/**
 * 错误信息数据类
 */
data class ErrorInfo(
    val errorCode: String,
    val errorType: ErrorType,
    val severity: ErrorSeverity,
    val message: String,
    val userMessage: String,
    val cause: String? = null,
    val context: Map<String, Any?> = emptyMap(),
    val timestamp: Long,
    val retryable: Boolean = false,
    val stackTrace: String? = null
) {
    
    /**
     * 转换为Map格式，便于序列化
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "errorCode" to errorCode,
            "errorType" to errorType.name,
            "severity" to severity.name,
            "message" to message,
            "userMessage" to userMessage,
            "cause" to cause,
            "context" to context,
            "timestamp" to timestamp.toString(),
            "retryable" to retryable,
            "stackTrace" to stackTrace
        )
    }
}

// ================================
// 业务异常类型
// ================================

/**
 * 业务逻辑异常基类
 */
open class BusinessException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "BUSINESS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = false
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.BUSINESS,
    severity = severity,
    userMessage = userMessage,
    context = context,
    retryable = retryable
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return BusinessException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            severity = severity,
            userMessage = userMessage,
            context = context,
            retryable = retryable
        )
    }

    override fun getDefaultUserMessage(): String {
        return "操作失败，请稍后重试"
    }
}

/**
 * 验证异常
 */
class ValidationException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "VALIDATION_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val field: String? = null,
    val value: Any? = null
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.VALIDATION,
    severity = ErrorSeverity.LOW,
    userMessage = userMessage,
    context = context + mapOfNotNull(
        "field" to field,
        "value" to value
    ),
    retryable = false
) {
    
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return ValidationException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            field = field,
            value = value
        )
    }
    
    override fun getDefaultUserMessage(): String {
        return field?.let { "输入的 $it 不正确，请检查后重试" } ?: "输入信息不正确，请检查后重试"
    }
}

/**
 * 游戏异常基类
 */
abstract class GameException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "GAME_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = false,
    val gameType: String? = null,
    val gameId: String? = null
) : BusinessException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = severity,
    userMessage = userMessage,
    context = context + mapOfNotNull(
        "gameType" to gameType,
        "gameId" to gameId
    ),
    retryable = retryable
)

/**
 * 俄罗斯方块游戏异常
 */
class TetrisException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "TETRIS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = false,
    gameId: String? = null
) : GameException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = severity,
    userMessage = userMessage,
    context = context,
    retryable = retryable,
    gameType = "TETRIS",
    gameId = gameId
) {
    
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return TetrisException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            severity = severity,
            userMessage = userMessage,
            context = context,
            retryable = retryable,
            gameId = gameId
        )
    }
    
    override fun getDefaultUserMessage(): String {
        return "游戏操作失败，请重试"
    }
}

/**
 * 用户异常基类
 */
abstract class UserException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "USER_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = false,
    val userId: String? = null
) : BusinessException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    severity = severity,
    userMessage = userMessage,
    context = context + mapOfNotNull("userId" to userId),
    retryable = retryable
)

// ================================
// 技术异常类型
// ================================

/**
 * 技术异常基类
 */
open class TechnicalException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "TECHNICAL_ERROR",
    errorType: ErrorType = ErrorType.SYSTEM,
    severity: ErrorSeverity = ErrorSeverity.HIGH,
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    retryable: Boolean = true
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = errorType,
    severity = severity,
    userMessage = userMessage,
    context = context,
    retryable = retryable
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return TechnicalException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            errorType = errorType,
            severity = severity,
            userMessage = userMessage,
            context = context,
            retryable = retryable
        )
    }

    override fun getDefaultUserMessage(): String {
        return "系统错误，请稍后重试"
    }
}

/**
 * 网络异常
 */
class NetworkException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "NETWORK_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val url: String? = null,
    val statusCode: Int? = null
) : TechnicalException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.NETWORK,
    severity = ErrorSeverity.MEDIUM,
    userMessage = userMessage,
    context = context + mapOfNotNull(
        "url" to url,
        "statusCode" to statusCode
    ),
    retryable = true
) {
    
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return NetworkException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            url = url,
            statusCode = statusCode
        )
    }
    
    override fun getDefaultUserMessage(): String {
        return "网络连接失败，请检查网络后重试"
    }
}

/**
 * 数据库异常
 */
class DatabaseException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "DATABASE_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val operation: String? = null,
    val table: String? = null
) : TechnicalException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.DATABASE,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context + mapOfNotNull(
        "operation" to operation,
        "table" to table
    ),
    retryable = true
) {
    
    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return DatabaseException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            operation = operation,
            table = table
        )
    }
    
    override fun getDefaultUserMessage(): String {
        return "数据保存失败，请稍后重试"
    }
}

/**
 * 权限异常
 */
class PermissionException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PERMISSION_DENIED",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val permission: String? = null
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.PERMISSION,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context + mapOfNotNull("permission" to permission),
    retryable = false
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return PermissionException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            permission = permission
        )
    }

    override fun getDefaultUserMessage(): String {
        return "没有执行此操作的权限"
    }
}

/**
 * 未知异常
 */
class UnknownException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "UNKNOWN_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap()
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.UNKNOWN,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context,
    retryable = false
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return UnknownException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context
        )
    }

    override fun getDefaultUserMessage(): String {
        return "发生了未知错误，请稍后重试"
    }
}

// ================================
// 工具函数
// ================================

/**
 * 创建只包含非空值的Map
 */
private fun <K, V> mapOfNotNull(vararg pairs: Pair<K, V?>): Map<K, V> {
    return pairs.mapNotNull { (key, value) ->
        value?.let { key to it }
    }.toMap()
}
