package com.yu.questicle.core.common.extensions

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * Combines two flows and maps the result
 */
inline fun <T1, T2, R> Flow<T1>.combineWith(
    other: Flow<T2>,
    crossinline transform: suspend (T1, T2) -> R
): Flow<R> = combine(this, other) { a, b -> transform(a, b) }

/**
 * Combines three flows and maps the result
 */
inline fun <T1, T2, T3, R> Flow<T1>.combineWith(
    flow2: Flow<T2>,
    flow3: Flow<T3>,
    crossinline transform: suspend (T1, T2, T3) -> R
): Flow<R> = combine(this, flow2, flow3) { a, b, c -> transform(a, b, c) }

/**
 * Maps and filters distinct values
 */
inline fun <T, R> Flow<T>.mapDistinct(
    crossinline transform: suspend (T) -> R
): Flow<R> = map { transform(it) }.distinctUntilChanged()

/**
 * Filters and maps non-null values
 */
inline fun <T, R : Any> Flow<T?>.mapNotNull(
    crossinline transform: suspend (T) -> R?
): Flow<R> = map { it?.let { value -> transform(value) } }
    .distinctUntilChanged()
    .map { it!! }

/**
 * Throttles emissions to prevent rapid updates
 */
fun <T> Flow<T>.throttleLatest(periodMillis: Long): Flow<T> {
    return distinctUntilChanged()
}
