package com.yu.questicle.core.common.performance

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import com.yu.questicle.core.database.supabase.SupabaseClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 性能数据仓库
 * 
 * 负责性能数据的存储、查询和同步
 * 支持本地缓存和云端同步
 */
@Singleton
class PerformanceRepository @Inject constructor(
    private val supabaseClient: SupabaseClient
) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(PerformanceRepository::class)
    
    // 本地缓存
    private val localSnapshots = ConcurrentLinkedQueue<PerformanceSnapshot>()
    private val maxLocalSnapshots = 1000
    
    // 统计计数器
    private val totalSnapshots = AtomicLong(0)
    private val uploadedSnapshots = AtomicLong(0)
    private val failedUploads = AtomicLong(0)
    
    // JSON序列化器
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // 配置
    private var config = PerformanceRepositoryConfig()
    
    /**
     * 保存性能快照
     */
    suspend fun savePerformanceSnapshot(snapshot: PerformanceSnapshot) {
        try {
            // 保存到本地缓存
            saveToLocalCache(snapshot)
            
            // 尝试上传到云端
            if (config.enableCloudSync) {
                uploadToCloud(snapshot)
            }
            
            totalSnapshots.incrementAndGet()
            
        } catch (e: Exception) {
            logger.e("Error saving performance snapshot", e)
        }
    }
    
    /**
     * 批量保存性能快照
     */
    suspend fun savePerformanceSnapshots(snapshots: List<PerformanceSnapshot>) {
        snapshots.forEach { snapshot ->
            savePerformanceSnapshot(snapshot)
        }
    }
    
    /**
     * 获取性能历史数据
     */
    suspend fun getPerformanceHistory(timeRange: TimeRange): List<PerformanceSnapshot> {
        return withContext(Dispatchers.IO) {
            try {
                // 从本地缓存获取
                val localData = getFromLocalCache(timeRange)
                
                // 如果启用云端同步，尝试从云端获取更多数据
                if (config.enableCloudSync && localData.size < config.minHistorySize) {
                    val cloudData = getFromCloud(timeRange)
                    (localData + cloudData).distinctBy { it.timestamp }.sortedBy { it.timestamp }
                } else {
                    localData
                }
                
            } catch (e: Exception) {
                logger.e("Error getting performance history", e)
                emptyList()
            }
        }
    }
    
    /**
     * 获取性能统计
     */
    suspend fun getPerformanceStatistics(): PerformanceStatistics {
        return withContext(Dispatchers.IO) {
            try {
                val recentSnapshots = getRecentSnapshots(3600000L) // 最近1小时
                
                if (recentSnapshots.isEmpty()) {
                    return@withContext PerformanceStatistics.EMPTY
                }
                
                val avgFrameRate = recentSnapshots.mapNotNull { it.frameRate?.fps }
                    .takeIf { it.isNotEmpty() }?.average() ?: 0.0
                
                val avgMemoryUsage = recentSnapshots.mapNotNull { it.memory?.heapUtilization }
                    .takeIf { it.isNotEmpty() }?.average() ?: 0.0
                
                val avgCpuUsage = recentSnapshots.mapNotNull { it.cpu?.cpuUsage }
                    .takeIf { it.isNotEmpty() }?.average() ?: 0.0
                
                val avgPerformanceScore = recentSnapshots.map { it.overall.score }
                    .takeIf { it.isNotEmpty() }?.average() ?: 0.0
                
                val totalIssues = recentSnapshots.sumOf { it.overall.issues.size }
                
                PerformanceStatistics(
                    totalSnapshots = totalSnapshots.get(),
                    avgFrameRate = avgFrameRate,
                    avgMemoryUsage = avgMemoryUsage * 100, // 转换为百分比
                    avgCpuUsage = avgCpuUsage,
                    performanceScore = avgPerformanceScore,
                    issueCount = totalIssues
                )
                
            } catch (e: Exception) {
                logger.e("Error calculating performance statistics", e)
                PerformanceStatistics.EMPTY
            }
        }
    }
    
    /**
     * 清理旧数据
     */
    suspend fun cleanupOldData() {
        withContext(Dispatchers.IO) {
            try {
                val cutoffTime = System.currentTimeMillis() - config.dataRetentionMs
                
                // 清理本地缓存中的旧数据
                val iterator = localSnapshots.iterator()
                var removedCount = 0
                
                while (iterator.hasNext()) {
                    val snapshot = iterator.next()
                    if (snapshot.timestamp < cutoffTime) {
                        iterator.remove()
                        removedCount++
                    }
                }
                
                if (removedCount > 0) {
                    logger.i("Cleaned up $removedCount old performance snapshots")
                }
                
            } catch (e: Exception) {
                logger.e("Error cleaning up old data", e)
            }
        }
    }
    
    /**
     * 保存到本地缓存
     */
    private fun saveToLocalCache(snapshot: PerformanceSnapshot) {
        localSnapshots.offer(snapshot)
        
        // 限制缓存大小
        while (localSnapshots.size > maxLocalSnapshots) {
            localSnapshots.poll()
        }
    }
    
    /**
     * 从本地缓存获取数据
     */
    private fun getFromLocalCache(timeRange: TimeRange): List<PerformanceSnapshot> {
        return localSnapshots.filter { snapshot ->
            snapshot.timestamp >= timeRange.startTime && snapshot.timestamp <= timeRange.endTime
        }.sortedBy { it.timestamp }
    }
    
    /**
     * 获取最近的快照
     */
    private fun getRecentSnapshots(timeWindowMs: Long): List<PerformanceSnapshot> {
        val cutoffTime = System.currentTimeMillis() - timeWindowMs
        return localSnapshots.filter { it.timestamp >= cutoffTime }.sortedBy { it.timestamp }
    }
    
    /**
     * 上传到云端
     */
    private suspend fun uploadToCloud(snapshot: PerformanceSnapshot) {
        try {
            val performanceRecord = snapshot.toSupabasePerformanceRecord()
            val result = supabaseClient.insertPerformanceMetric(performanceRecord)
            
            if (result.isSuccess) {
                uploadedSnapshots.incrementAndGet()
                logger.v("Performance snapshot uploaded to cloud: ${snapshot.timestamp}")
            } else {
                failedUploads.incrementAndGet()
                logger.w("Failed to upload performance snapshot: ${result.exceptionOrNull()?.message}")
            }
            
        } catch (e: Exception) {
            failedUploads.incrementAndGet()
            logger.e("Error uploading performance snapshot to cloud", e)
        }
    }
    
    /**
     * 从云端获取数据
     */
    private suspend fun getFromCloud(timeRange: TimeRange): List<PerformanceSnapshot> {
        return try {
            // 这里应该调用 Supabase API 获取历史数据
            // 暂时返回空列表，等待 Supabase API 实现
            emptyList()
        } catch (e: Exception) {
            logger.e("Error getting performance data from cloud", e)
            emptyList()
        }
    }
    
    /**
     * 获取仓库统计信息
     */
    fun getRepositoryStats(): RepositoryStats {
        return RepositoryStats(
            totalSnapshots = totalSnapshots.get(),
            localCacheSize = localSnapshots.size,
            uploadedSnapshots = uploadedSnapshots.get(),
            failedUploads = failedUploads.get(),
            uploadSuccessRate = if (totalSnapshots.get() > 0) {
                uploadedSnapshots.get().toDouble() / totalSnapshots.get()
            } else 0.0
        )
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: PerformanceRepositoryConfig) {
        this.config = newConfig
        logger.i("Performance repository config updated")
    }
    
    /**
     * 强制同步到云端
     */
    suspend fun forceSyncToCloud(): SyncResult {
        return withContext(Dispatchers.IO) {
            try {
                val pendingSnapshots = localSnapshots.toList()
                var successCount = 0
                var failureCount = 0
                
                for (snapshot in pendingSnapshots) {
                    try {
                        uploadToCloud(snapshot)
                        successCount++
                    } catch (e: Exception) {
                        failureCount++
                        logger.e("Error syncing snapshot to cloud", e)
                    }
                }
                
                SyncResult(
                    totalAttempted = pendingSnapshots.size,
                    successCount = successCount,
                    failureCount = failureCount,
                    isSuccess = failureCount == 0
                )
                
            } catch (e: Exception) {
                logger.e("Error during force sync to cloud", e)
                SyncResult(0, 0, 0, false)
            }
        }
    }
}

/**
 * 性能快照扩展函数
 */
fun PerformanceSnapshot.toSupabasePerformanceRecord(): SupabasePerformanceRecord {
    return SupabasePerformanceRecord(
        id = java.util.UUID.randomUUID().toString(),
        timestamp = timestamp,
        user_id = null, // 可以从用户会话中获取
        session_id = null, // 可以从会话管理器中获取
        app_version = getAppVersion(),
        metric_type = "PERFORMANCE_SNAPSHOT",
        metric_value = overall.score,
        metadata = Json.encodeToString(mapOf(
            "frameRate" to frameRate?.toMap(),
            "memory" to memory?.toMap(),
            "cpu" to cpu?.toMap(),
            "network" to network?.toMap(),
            "overall" to mapOf(
                "score" to overall.score,
                "grade" to overall.grade.name,
                "issues" to overall.issues
            )
        ))
    )
}

/**
 * 获取应用版本 (简化实现)
 */
private fun getAppVersion(): String = "1.0.0" // 应该从 BuildConfig 获取

/**
 * Supabase 性能记录
 */
data class SupabasePerformanceRecord(
    val id: String,
    val timestamp: Long,
    val user_id: String?,
    val session_id: String?,
    val app_version: String,
    val metric_type: String,
    val metric_value: Double,
    val metadata: String
)

/**
 * 仓库统计信息
 */
data class RepositoryStats(
    val totalSnapshots: Long,
    val localCacheSize: Int,
    val uploadedSnapshots: Long,
    val failedUploads: Long,
    val uploadSuccessRate: Double
)

/**
 * 同步结果
 */
data class SyncResult(
    val totalAttempted: Int,
    val successCount: Int,
    val failureCount: Int,
    val isSuccess: Boolean
)

/**
 * 性能仓库配置
 */
data class PerformanceRepositoryConfig(
    val enableCloudSync: Boolean = true,
    val dataRetentionMs: Long = 7 * 24 * 60 * 60 * 1000L, // 7天
    val minHistorySize: Int = 100,
    val maxLocalCacheSize: Int = 1000,
    val syncBatchSize: Int = 50
)
