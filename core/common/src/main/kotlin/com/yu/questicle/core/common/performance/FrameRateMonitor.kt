package com.yu.questicle.core.common.performance

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.Choreographer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 帧率监控器
 * 
 * 使用 Choreographer 监控 UI 渲染性能
 * 提供实时帧率、帧时间和丢帧率统计
 */
@Singleton
class FrameRateMonitor @Inject constructor(
    private val context: Context
) : BaseMonitor<FrameRateMetric>() {
    
    // Choreographer 实例 (必须在主线程获取)
    private var choreographer: Choreographer? = null
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 帧率计算相关
    private val frameTimeHistory = ConcurrentLinkedQueue<Long>()
    private val frameCount = AtomicInteger(0)
    private val droppedFrameCount = AtomicInteger(0)
    private val lastFrameTime = AtomicLong(0L)
    private val windowStartTime = AtomicLong(0L)
    
    // 配置
    private val maxHistorySize = 120 // 保存2秒的帧时间历史 (60fps * 2)
    private val targetFrameTime = 16_666_667L // 60fps 对应的帧时间 (纳秒)
    private val measurementWindowMs = 1000L // 1秒测量窗口
    
    // 帧回调
    private val frameCallback = object : Choreographer.FrameCallback {
        override fun doFrame(frameTimeNanos: Long) {
            recordFrame(frameTimeNanos)
            
            // 继续监听下一帧
            if (isMonitoring) {
                choreographer?.postFrameCallback(this)
            }
        }
    }
    
    override suspend fun onStartMonitoring() {
        // 在主线程初始化 Choreographer
        withContext(Dispatchers.Main) {
            choreographer = Choreographer.getInstance()
            
            // 重置计数器
            frameCount.set(0)
            droppedFrameCount.set(0)
            frameTimeHistory.clear()
            windowStartTime.set(System.currentTimeMillis())
            
            // 开始监听帧回调
            choreographer?.postFrameCallback(frameCallback)
        }
    }
    
    override suspend fun onStopMonitoring() {
        withContext(Dispatchers.Main) {
            choreographer?.removeFrameCallback(frameCallback)
            choreographer = null
        }
    }
    
    override suspend fun collectMetric(): FrameRateMetric {
        val currentTime = System.currentTimeMillis()
        val windowStart = windowStartTime.get()
        val windowDuration = currentTime - windowStart
        
        // 如果窗口时间不足，返回默认值
        if (windowDuration < measurementWindowMs) {
            return FrameRateMetric(
                timestamp = currentTime,
                fps = 0,
                avgFrameTime = 0.0,
                droppedFrameRate = 0.0,
                isSmooth = false
            )
        }
        
        // 计算帧率
        val totalFrames = frameCount.get()
        val fps = if (windowDuration > 0) {
            (totalFrames * 1000L / windowDuration).toInt()
        } else 0
        
        // 计算平均帧时间
        val avgFrameTime = calculateAverageFrameTime()
        
        // 计算丢帧率
        val expectedFrames = (windowDuration * 60 / 1000).toInt() // 假设目标60fps
        val droppedFrames = droppedFrameCount.get()
        val droppedFrameRate = if (expectedFrames > 0) {
            droppedFrames.toDouble() / expectedFrames
        } else 0.0
        
        // 判断是否流畅 (55fps以上且丢帧率低于10%)
        val isSmooth = fps >= 55 && droppedFrameRate < 0.1
        
        return FrameRateMetric(
            timestamp = currentTime,
            fps = fps,
            avgFrameTime = avgFrameTime,
            droppedFrameRate = droppedFrameRate,
            isSmooth = isSmooth
        )
    }
    
    /**
     * 记录帧时间
     */
    private fun recordFrame(frameTimeNanos: Long) {
        val lastTime = lastFrameTime.get()
        
        if (lastTime > 0) {
            val frameDuration = frameTimeNanos - lastTime
            
            // 添加到历史记录
            frameTimeHistory.offer(frameDuration)
            
            // 限制历史记录大小
            while (frameTimeHistory.size > maxHistorySize) {
                frameTimeHistory.poll()
            }
            
            // 检查是否丢帧 (帧时间超过目标时间的1.5倍)
            if (frameDuration > targetFrameTime * 1.5) {
                droppedFrameCount.incrementAndGet()
            }
        }
        
        lastFrameTime.set(frameTimeNanos)
        frameCount.incrementAndGet()
        
        // 定期重置计数器 (每秒)
        val currentTime = System.currentTimeMillis()
        val windowStart = windowStartTime.get()
        if (currentTime - windowStart >= measurementWindowMs) {
            resetCounters(currentTime)
        }
    }
    
    /**
     * 计算平均帧时间
     */
    private fun calculateAverageFrameTime(): Double {
        if (frameTimeHistory.isEmpty()) return 0.0
        
        val sum = frameTimeHistory.sumOf { it }
        return sum.toDouble() / frameTimeHistory.size / 1_000_000.0 // 转换为毫秒
    }
    
    /**
     * 重置计数器
     */
    private fun resetCounters(currentTime: Long) {
        frameCount.set(0)
        droppedFrameCount.set(0)
        windowStartTime.set(currentTime)
    }
    
    override suspend fun checkThresholds(metric: FrameRateMetric) {
        if (!config.enableThresholdChecking) return
        
        // 检查帧率阈值
        if (metric.fps < 30) {
            logger.w("Low frame rate detected: ${metric.fps} fps")
        }
        
        // 检查丢帧率阈值
        if (metric.droppedFrameRate > 0.2) {
            logger.w("High dropped frame rate detected: ${(metric.droppedFrameRate * 100).toInt()}%")
        }
        
        // 检查帧时间阈值
        if (metric.avgFrameTime > 20.0) {
            logger.w("High average frame time detected: ${metric.avgFrameTime}ms")
        }
    }
    
    /**
     * 获取详细的帧率统计
     */
    fun getDetailedFrameRateStats(): FrameRateStats {
        val currentMetric = getCurrentMetric()
        
        return FrameRateStats(
            currentFps = currentMetric?.fps ?: 0,
            averageFrameTime = currentMetric?.avgFrameTime ?: 0.0,
            droppedFrameRate = currentMetric?.droppedFrameRate ?: 0.0,
            isSmooth = currentMetric?.isSmooth ?: false,
            totalFrames = frameCount.get(),
            totalDroppedFrames = droppedFrameCount.get(),
            frameTimeHistory = frameTimeHistory.map { it / 1_000_000.0 }, // 转换为毫秒
            monitoringDuration = System.currentTimeMillis() - windowStartTime.get()
        )
    }
    
    /**
     * 强制触发帧率测量
     */
    suspend fun triggerMeasurement(): FrameRateMetric {
        return collectMetric()
    }
}

/**
 * 详细帧率统计
 */
data class FrameRateStats(
    val currentFps: Int,
    val averageFrameTime: Double,
    val droppedFrameRate: Double,
    val isSmooth: Boolean,
    val totalFrames: Int,
    val totalDroppedFrames: Int,
    val frameTimeHistory: List<Double>,
    val monitoringDuration: Long
)

/**
 * 帧率监控配置
 */
data class FrameRateMonitorConfig(
    val targetFps: Int = 60,
    val smoothnessThreshold: Int = 55,
    val droppedFrameThreshold: Double = 0.1,
    val measurementWindowMs: Long = 1000L
) : MonitorConfig(
    samplingIntervalMs = 1000L,
    enableThresholdChecking = true,
    retainHistoryCount = 120
)
