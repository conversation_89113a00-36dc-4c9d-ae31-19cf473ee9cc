package com.yu.questicle.core.common.logging

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 高性能异步日志处理器
 * 
 * 特性:
 * - 无锁队列设计
 * - 批量处理优化
 * - 背压控制
 * - 自动故障恢复
 * - 性能监控
 */
class LogProcessor private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: LogProcessor? = null
        
        fun getInstance(): LogProcessor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LogProcessor().also { INSTANCE = it }
            }
        }
    }
    
    // 配置参数
    private var bufferSize = 1024
    private var batchSize = 50
    private var flushInterval = 5000L // 毫秒
    private var maxRetries = 3
    
    // 异步处理组件
    private val scope = CoroutineScope(
        SupervisorJob() + 
        Dispatchers.IO + 
        CoroutineName("LogProcessor")
    )
    
    private val logChannel = Channel<LogEvent>(capacity = Channel.UNLIMITED)
    private val isRunning = AtomicBoolean(false)
    private val isShutdown = AtomicBoolean(false)
    
    // 性能监控
    private val processedCount = AtomicLong(0)
    private val droppedCount = AtomicLong(0)
    private val errorCount = AtomicLong(0)
    
    // 输出处理器
    private val appenders = mutableListOf<LogAppender>()
    private val sensitiveDataMasker = SensitiveDataMasker()
    
    init {
        // 添加默认的输出处理器
        appenders.add(AndroidLogAppender())
        
        // 启动处理协程
        start()
    }
    
    /**
     * 处理日志事件
     */
    fun process(event: LogEvent) {
        if (isShutdown.get()) {
            return
        }
        
        try {
            // 应用敏感数据脱敏
            val maskedEvent = sensitiveDataMasker.mask(event)
            
            // 发送到处理队列
            val result = logChannel.trySend(maskedEvent)
            if (result.isSuccess) {
                processedCount.incrementAndGet()
            } else {
                droppedCount.incrementAndGet()
                // 背压处理：在高负载时丢弃部分日志
                handleBackpressure(maskedEvent)
            }
        } catch (e: Exception) {
            errorCount.incrementAndGet()
            // 日志系统本身的错误不应该影响主业务
            handleProcessingError(e, event)
        }
    }
    
    /**
     * 添加输出处理器
     */
    fun addAppender(appender: LogAppender) {
        synchronized(appenders) {
            appenders.add(appender)
        }
    }
    
    /**
     * 移除输出处理器
     */
    fun removeAppender(appender: LogAppender) {
        synchronized(appenders) {
            appenders.remove(appender)
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getStats(): LogProcessorStats {
        return LogProcessorStats(
            processedCount = processedCount.get(),
            droppedCount = droppedCount.get(),
            errorCount = errorCount.get(),
            queueSize = logChannel.tryReceive().getOrNull()?.let { 0 } ?: 0,
            isRunning = isRunning.get()
        )
    }
    
    /**
     * 强制刷新所有缓冲区
     */
    fun flush() {
        synchronized(appenders) {
            appenders.forEach { appender ->
                try {
                    appender.flush()
                } catch (e: Exception) {
                    handleAppenderError(e, appender)
                }
            }
        }
    }
    
    /**
     * 关闭处理器
     */
    fun shutdown() {
        if (isShutdown.compareAndSet(false, true)) {
            // 停止接收新的日志事件
            logChannel.close()
            
            // 等待处理完成
            runBlocking {
                scope.coroutineContext[Job]?.children?.forEach { it.join() }
            }
            
            // 关闭所有输出处理器
            synchronized(appenders) {
                appenders.forEach { appender ->
                    try {
                        appender.close()
                    } catch (e: Exception) {
                        // 忽略关闭时的错误
                    }
                }
                appenders.clear()
            }
            
            // 取消协程作用域
            scope.cancel()
        }
    }
    
    /**
     * 启动处理协程
     */
    private fun start() {
        if (isRunning.compareAndSet(false, true)) {
            scope.launch {
                val events = mutableListOf<LogEvent>()
                var lastFlushTime = System.currentTimeMillis()
                
                logChannel.receiveAsFlow().collect { event ->
                    events.add(event)
                    
                    val currentTime = System.currentTimeMillis()
                    val shouldFlush = events.size >= batchSize || 
                                    (currentTime - lastFlushTime) >= flushInterval
                    
                    if (shouldFlush) {
                        processEvents(events.toList())
                        events.clear()
                        lastFlushTime = currentTime
                    }
                }
                
                // 处理剩余的事件
                if (events.isNotEmpty()) {
                    processEvents(events)
                }
            }
        }
    }
    
    /**
     * 批量处理日志事件
     */
    private suspend fun processEvents(events: List<LogEvent>) {
        if (events.isEmpty()) return
        
        synchronized(appenders) {
            appenders.forEach { appender ->
                try {
                    appender.append(events)
                } catch (e: Exception) {
                    handleAppenderError(e, appender)
                }
            }
        }
    }
    
    /**
     * 处理背压情况
     */
    private fun handleBackpressure(event: LogEvent) {
        // 在高负载时，优先保留ERROR和FATAL级别的日志
        if (event.level.priority >= LogLevel.ERROR.priority) {
            // 尝试强制发送重要日志
            scope.launch {
                try {
                    logChannel.send(event)
                } catch (e: Exception) {
                    // 如果仍然失败，直接输出到Android Log
                    Log.e("LogProcessor", "Failed to process critical log: ${event.message}", e)
                }
            }
        }
    }
    
    /**
     * 处理处理器错误
     */
    private fun handleProcessingError(error: Exception, event: LogEvent) {
        // 使用Android原生日志作为后备
        val level = when (event.level) {
            LogLevel.VERBOSE -> Log.VERBOSE
            LogLevel.DEBUG -> Log.DEBUG
            LogLevel.INFO -> Log.INFO
            LogLevel.WARN -> Log.WARN
            LogLevel.ERROR -> Log.ERROR
            LogLevel.FATAL -> Log.ERROR
        }
        
        Log.println(level, "LogProcessor", "Processing error: ${error.message}")
        Log.println(level, event.loggerName, event.message)
    }
    
    /**
     * 处理输出处理器错误
     */
    private fun handleAppenderError(error: Exception, appender: LogAppender) {
        Log.e("LogProcessor", "Appender error: ${appender::class.simpleName}", error)
        
        // 可以考虑暂时禁用有问题的appender
        // 或者实现重试机制
    }
}

/**
 * 日志输出处理器接口
 */
interface LogAppender {
    
    /**
     * 输出单个日志事件
     */
    fun append(event: LogEvent)
    
    /**
     * 批量输出日志事件
     */
    fun append(events: List<LogEvent>) {
        events.forEach { append(it) }
    }
    
    /**
     * 刷新缓冲区
     */
    fun flush() {}
    
    /**
     * 关闭输出处理器
     */
    fun close() {}
}

/**
 * Android Log输出处理器
 */
class AndroidLogAppender : LogAppender {
    
    override fun append(event: LogEvent) {
        val androidLevel = when (event.level) {
            LogLevel.VERBOSE -> Log.VERBOSE
            LogLevel.DEBUG -> Log.DEBUG
            LogLevel.INFO -> Log.INFO
            LogLevel.WARN -> Log.WARN
            LogLevel.ERROR -> Log.ERROR
            LogLevel.FATAL -> Log.ERROR
        }
        
        val tag = event.loggerName.substringAfterLast('.')
        val message = formatMessage(event)
        
        if (event.throwable != null) {
            Log.println(androidLevel, tag, "$message\n${Log.getStackTraceString(event.throwable)}")
        } else {
            Log.println(androidLevel, tag, message)
        }
    }
    
    private fun formatMessage(event: LogEvent): String {
        val builder = StringBuilder()
        builder.append(event.message)
        
        // 添加上下文信息
        if (event.context != LogContext.EMPTY) {
            val contextMap = event.context.toMap()
            if (contextMap.isNotEmpty()) {
                builder.append(" | Context: ")
                builder.append(contextMap.entries.joinToString(", ") { "${it.key}=${it.value}" })
            }
        }
        
        // 添加标签
        if (event.tags.isNotEmpty()) {
            builder.append(" | Tags: ")
            builder.append(event.tags.joinToString(", "))
        }
        
        // 添加持续时间
        event.duration?.let { duration ->
            builder.append(" | Duration: ${duration}ms")
        }
        
        return builder.toString()
    }
}

/**
 * 敏感数据脱敏器
 */
class SensitiveDataMasker {
    
    private val patterns = listOf(
        // 手机号
        Regex("""1[3-9]\d{9}""") to "***-****-****",
        // 邮箱
        Regex("""[\w._%+-]+@[\w.-]+\.[A-Za-z]{2,}""") to "***@***.***",
        // 身份证号
        Regex("""\d{17}[\dXx]""") to "***************",
        // 银行卡号
        Regex("""\d{16,19}""") to "****-****-****-****",
        // 密码字段
        Regex("""("password"\s*:\s*")[^"]*"""") to "$1****",
        Regex("""("pwd"\s*:\s*")[^"]*"""") to "$1****",
        Regex("""("token"\s*:\s*")[^"]*"""") to "$1****"
    )
    
    fun mask(event: LogEvent): LogEvent {
        var maskedMessage = event.message
        
        patterns.forEach { (pattern, replacement) ->
            maskedMessage = pattern.replace(maskedMessage, replacement)
        }
        
        return event.copy(message = maskedMessage)
    }
}

/**
 * 日志处理器统计信息
 */
data class LogProcessorStats(
    val processedCount: Long,
    val droppedCount: Long,
    val errorCount: Long,
    val queueSize: Int,
    val isRunning: Boolean
)
