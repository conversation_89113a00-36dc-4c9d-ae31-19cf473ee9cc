package com.yu.questicle.core.common.performance

import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.ConcurrentLinkedQueue
import kotlin.math.max
import kotlin.math.min

/**
 * 操作性能指标
 */
class OperationMetrics(
    val operationName: String,
    val category: String
) {
    val totalCalls = AtomicLong(0)
    val totalDurationNanos = AtomicLong(0)
    val totalMemoryDelta = AtomicLong(0)
    val lastUpdated = AtomicLong(System.currentTimeMillis())
    
    private val recentSamples = ConcurrentLinkedQueue<OperationSample>()
    private var minDuration = Long.MAX_VALUE
    private var maxDuration = Long.MIN_VALUE
    
    fun addSample(durationNanos: Long, memoryDeltaBytes: Long) {
        totalCalls.incrementAndGet()
        totalDurationNanos.addAndGet(durationNanos)
        totalMemoryDelta.addAndGet(memoryDeltaBytes)
        lastUpdated.set(System.currentTimeMillis())
        
        // 更新最小最大值
        synchronized(this) {
            minDuration = min(minDuration, durationNanos)
            maxDuration = max(maxDuration, durationNanos)
        }
        
        // 保存最近的样本用于百分位数计算
        recentSamples.offer(OperationSample(durationNanos, memoryDeltaBytes, System.currentTimeMillis()))
        
        // 限制样本数量
        while (recentSamples.size > MAX_SAMPLES) {
            recentSamples.poll()
        }
    }
    
    fun getAverageDurationMs(): Long {
        val calls = totalCalls.get()
        return if (calls > 0) {
            (totalDurationNanos.get() / calls) / 1_000_000
        } else 0L
    }
    
    fun getMinDurationMs(): Long = if (minDuration == Long.MAX_VALUE) 0L else minDuration / 1_000_000
    fun getMaxDurationMs(): Long = if (maxDuration == Long.MIN_VALUE) 0L else maxDuration / 1_000_000
    
    fun getP95DurationMs(): Long {
        val samples = recentSamples.map { it.durationNanos }.sorted()
        return if (samples.isNotEmpty()) {
            val index = (samples.size * 0.95).toInt()
            samples.getOrElse(index) { samples.last() } / 1_000_000
        } else 0L
    }
    
    fun getAverageMemoryDeltaMB(): Long {
        val calls = totalCalls.get()
        return if (calls > 0) {
            (totalMemoryDelta.get() / calls) / (1024 * 1024)
        } else 0L
    }
    
    companion object {
        private const val MAX_SAMPLES = 1000
    }
}

/**
 * 操作样本数据
 */
data class OperationSample(
    val durationNanos: Long,
    val memoryDeltaBytes: Long,
    val timestamp: Long
)

/**
 * 内存性能指标
 */
class MemoryMetrics(val component: String) {
    val lastUpdated = AtomicLong(System.currentTimeMillis())
    private val recentSamples = ConcurrentLinkedQueue<MemorySample>()
    private var peakMemory = 0L
    
    fun addSample(memoryMB: Long) {
        lastUpdated.set(System.currentTimeMillis())
        
        synchronized(this) {
            peakMemory = max(peakMemory, memoryMB)
        }
        
        recentSamples.offer(MemorySample(memoryMB, System.currentTimeMillis()))
        
        // 限制样本数量
        while (recentSamples.size > MAX_SAMPLES) {
            recentSamples.poll()
        }
    }
    
    fun getCurrentMemoryMB(): Long = recentSamples.lastOrNull()?.memoryMB ?: 0L
    
    fun getAverageMemoryMB(): Long {
        val samples = recentSamples.toList()
        return if (samples.isNotEmpty()) {
            samples.sumOf { it.memoryMB } / samples.size
        } else 0L
    }
    
    fun getPeakMemoryMB(): Long = peakMemory
    
    companion object {
        private const val MAX_SAMPLES = 500
    }
}

/**
 * 内存样本数据
 */
data class MemorySample(
    val memoryMB: Long,
    val timestamp: Long
)

/**
 * 帧率性能指标
 */
class FrameRateMetrics {
    private val recentSamples = ConcurrentLinkedQueue<FrameRateSample>()
    private var minFps = Int.MAX_VALUE
    private var maxFps = Int.MIN_VALUE
    private var frameDrops = 0
    
    fun addSample(fps: Int) {
        synchronized(this) {
            minFps = min(minFps, fps)
            maxFps = max(maxFps, fps)
            
            if (fps < TARGET_FPS) {
                frameDrops++
            }
        }
        
        recentSamples.offer(FrameRateSample(fps, System.currentTimeMillis()))
        
        // 限制样本数量
        while (recentSamples.size > MAX_SAMPLES) {
            recentSamples.poll()
        }
    }
    
    fun getAverageFps(): Int {
        val samples = recentSamples.toList()
        return if (samples.isNotEmpty()) {
            samples.sumOf { it.fps } / samples.size
        } else 0
    }
    
    fun getMinFps(): Int = if (minFps == Int.MAX_VALUE) 0 else minFps
    fun getMaxFps(): Int = if (maxFps == Int.MIN_VALUE) 0 else maxFps
    fun getFrameDrops(): Int = frameDrops
    
    fun cleanup(cutoffTime: Long) {
        while (recentSamples.isNotEmpty() && recentSamples.peek()?.timestamp ?: Long.MAX_VALUE < cutoffTime) {
            recentSamples.poll()
        }
    }
    
    companion object {
        private const val MAX_SAMPLES = 300
        private const val TARGET_FPS = 60
    }
}

/**
 * 帧率样本数据
 */
data class FrameRateSample(
    val fps: Int,
    val timestamp: Long
)

/**
 * 网络性能指标
 */
class NetworkMetrics(
    val url: String,
    val method: String
) {
    val totalRequests = AtomicLong(0)
    val totalDurationMs = AtomicLong(0)
    val totalResponseSize = AtomicLong(0)
    val successfulRequests = AtomicLong(0)
    val lastUpdated = AtomicLong(System.currentTimeMillis())
    
    fun addSample(durationMs: Long, responseSizeBytes: Long, statusCode: Int) {
        totalRequests.incrementAndGet()
        totalDurationMs.addAndGet(durationMs)
        totalResponseSize.addAndGet(responseSizeBytes)
        lastUpdated.set(System.currentTimeMillis())
        
        if (statusCode in 200..299) {
            successfulRequests.incrementAndGet()
        }
    }
    
    fun getAverageDurationMs(): Long {
        val requests = totalRequests.get()
        return if (requests > 0) {
            totalDurationMs.get() / requests
        } else 0L
    }
    
    fun getSuccessRate(): Double {
        val total = totalRequests.get()
        return if (total > 0) {
            successfulRequests.get().toDouble() / total
        } else 0.0
    }
    
    fun getAverageResponseSizeKB(): Long {
        val requests = totalRequests.get()
        return if (requests > 0) {
            (totalResponseSize.get() / requests) / 1024
        } else 0L
    }
}

/**
 * 操作性能报告
 */
data class OperationReport(
    val operationName: String,
    val category: String,
    val totalCalls: Long,
    val averageDurationMs: Long,
    val minDurationMs: Long,
    val maxDurationMs: Long,
    val p95DurationMs: Long,
    val averageMemoryDeltaMB: Long,
    val lastUpdated: Long
)

/**
 * 组件内存报告
 */
data class ComponentMemoryReport(
    val component: String,
    val currentMemoryMB: Long,
    val averageMemoryMB: Long,
    val peakMemoryMB: Long
)

/**
 * 内存报告
 */
data class MemoryReport(
    val components: List<ComponentMemoryReport>
)

/**
 * 帧率报告
 */
data class FrameRateReport(
    val averageFps: Int,
    val minFps: Int,
    val maxFps: Int,
    val frameDrops: Int
)

/**
 * 网络请求报告
 */
data class NetworkRequestReport(
    val url: String,
    val method: String,
    val totalRequests: Long,
    val averageDurationMs: Long,
    val successRate: Double,
    val averageResponseSizeKB: Long
)

/**
 * 网络报告
 */
data class NetworkReport(
    val requests: List<NetworkRequestReport>
)

/**
 * 完整性能报告
 */
data class PerformanceReport(
    val operationReports: List<OperationReport>,
    val memoryReport: MemoryReport,
    val frameRateReport: FrameRateReport,
    val networkReport: NetworkReport,
    val timestamp: Long
)
