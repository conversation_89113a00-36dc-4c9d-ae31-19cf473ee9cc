package com.yu.questicle.core.common.performance

import android.app.ActivityManager
import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.management.GarbageCollectorMXBean
import java.lang.management.ManagementFactory
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内存监控器
 * 
 * 监控堆内存、系统内存、GC活动等
 * 提供内存使用情况和内存压力检测
 */
@Singleton
class MemoryMonitor @Inject constructor(
    private val context: Context
) : BaseMonitor<MemoryMetric>() {
    
    // 系统服务
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val runtime = Runtime.getRuntime()
    
    // GC监控
    private val gcBeans = ManagementFactory.getGarbageCollectorMXBeans()
    private var lastGcCount = AtomicLong(0)
    private var lastGcTime = AtomicLong(0)
    private val startTime = System.currentTimeMillis()
    
    init {
        // 初始化GC计数
        updateGcStats()
    }
    
    override suspend fun collectMetric(): MemoryMetric = withContext(Dispatchers.IO) {
        val timestamp = System.currentTimeMillis()
        
        // 堆内存信息
        val totalHeap = runtime.totalMemory()
        val freeHeap = runtime.freeMemory()
        val usedHeap = totalHeap - freeHeap
        val maxHeap = runtime.maxMemory()
        val heapUtilization = usedHeap.toDouble() / maxHeap
        
        // 系统内存信息
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val availableMemory = memoryInfo.availMem
        val totalMemory = memoryInfo.totalMem
        val usedMemory = totalMemory - availableMemory
        val memoryPressure = memoryInfo.lowMemory
        
        // GC信息
        val (gcCount, gcRate) = calculateGcStats()
        
        MemoryMetric(
            timestamp = timestamp,
            usedHeap = usedHeap,
            totalHeap = totalHeap,
            maxHeap = maxHeap,
            heapUtilization = heapUtilization,
            usedMemory = usedMemory,
            totalMemory = totalMemory,
            availableMemory = availableMemory,
            memoryPressure = memoryPressure,
            gcCount = gcCount,
            gcRate = gcRate
        )
    }
    
    /**
     * 计算GC统计信息
     */
    private fun calculateGcStats(): Pair<Long, Double> {
        val currentGcCount = getTotalGcCount()
        val currentTime = System.currentTimeMillis()
        
        val lastCount = lastGcCount.get()
        val lastTime = lastGcTime.get()
        
        val gcRate = if (lastTime > 0 && currentTime > lastTime) {
            val timeDelta = currentTime - lastTime
            val countDelta = currentGcCount - lastCount
            (countDelta * 60000.0) / timeDelta // GC次数/分钟
        } else 0.0
        
        lastGcCount.set(currentGcCount)
        lastGcTime.set(currentTime)
        
        return Pair(currentGcCount, gcRate)
    }
    
    /**
     * 获取总GC次数
     */
    private fun getTotalGcCount(): Long {
        return gcBeans.sumOf { it.collectionCount.coerceAtLeast(0) }
    }
    
    /**
     * 更新GC统计
     */
    private fun updateGcStats() {
        lastGcCount.set(getTotalGcCount())
        lastGcTime.set(System.currentTimeMillis())
    }
    
    override suspend fun checkThresholds(metric: MemoryMetric) {
        if (!config.enableThresholdChecking) return
        
        // 检查堆内存使用率
        if (metric.heapUtilization > 0.9) {
            logger.w("High heap memory usage: ${(metric.heapUtilization * 100).toInt()}%")
        }
        
        // 检查系统内存压力
        if (metric.memoryPressure) {
            logger.w("System memory pressure detected")
        }
        
        // 检查GC频率
        if (metric.gcRate > 10.0) { // 每分钟超过10次GC
            logger.w("High GC rate detected: ${metric.gcRate} GC/min")
        }
        
        // 检查可用内存
        val availableMemoryMB = metric.availableMemory / (1024 * 1024)
        if (availableMemoryMB < 100) { // 可用内存少于100MB
            logger.w("Low available memory: ${availableMemoryMB}MB")
        }
    }
    
    /**
     * 获取详细内存统计
     */
    fun getDetailedMemoryStats(): DetailedMemoryStats {
        val currentMetric = getCurrentMetric()
        
        // 获取详细的GC信息
        val gcDetails = gcBeans.map { bean ->
            GcDetails(
                name = bean.name,
                collectionCount = bean.collectionCount,
                collectionTime = bean.collectionTime
            )
        }
        
        // 计算内存分配速率
        val allocationRate = calculateAllocationRate()
        
        return DetailedMemoryStats(
            heapUsedMB = (currentMetric?.usedHeap ?: 0) / (1024 * 1024),
            heapTotalMB = (currentMetric?.totalHeap ?: 0) / (1024 * 1024),
            heapMaxMB = (currentMetric?.maxHeap ?: 0) / (1024 * 1024),
            heapUtilization = currentMetric?.heapUtilization ?: 0.0,
            systemUsedMB = (currentMetric?.usedMemory ?: 0) / (1024 * 1024),
            systemTotalMB = (currentMetric?.totalMemory ?: 0) / (1024 * 1024),
            systemAvailableMB = (currentMetric?.availableMemory ?: 0) / (1024 * 1024),
            memoryPressure = currentMetric?.memoryPressure ?: false,
            gcCount = currentMetric?.gcCount ?: 0,
            gcRate = currentMetric?.gcRate ?: 0.0,
            gcDetails = gcDetails,
            allocationRate = allocationRate,
            monitoringDuration = System.currentTimeMillis() - startTime
        )
    }
    
    /**
     * 计算内存分配速率 (简化实现)
     */
    private fun calculateAllocationRate(): Double {
        val currentMetric = getCurrentMetric() ?: return 0.0
        val duration = System.currentTimeMillis() - startTime
        
        return if (duration > 0) {
            (currentMetric.usedHeap.toDouble() / (1024 * 1024)) / (duration / 1000.0) // MB/s
        } else 0.0
    }
    
    /**
     * 强制执行GC (仅用于测试)
     */
    fun forceGarbageCollection() {
        logger.d("Forcing garbage collection")
        System.gc()
        System.runFinalization()
    }
    
    /**
     * 获取内存快照
     */
    suspend fun takeMemorySnapshot(label: String = "snapshot"): MemorySnapshot {
        val metric = collectMetric()
        
        return MemorySnapshot(
            timestamp = metric.timestamp,
            label = label,
            heapUsedMB = metric.usedHeap / (1024 * 1024),
            heapTotalMB = metric.totalHeap / (1024 * 1024),
            heapMaxMB = metric.maxHeap / (1024 * 1024),
            systemUsedMB = metric.usedMemory / (1024 * 1024),
            systemAvailableMB = metric.availableMemory / (1024 * 1024),
            gcCount = metric.gcCount
        )
    }
}

/**
 * 详细内存统计
 */
data class DetailedMemoryStats(
    val heapUsedMB: Long,
    val heapTotalMB: Long,
    val heapMaxMB: Long,
    val heapUtilization: Double,
    val systemUsedMB: Long,
    val systemTotalMB: Long,
    val systemAvailableMB: Long,
    val memoryPressure: Boolean,
    val gcCount: Long,
    val gcRate: Double,
    val gcDetails: List<GcDetails>,
    val allocationRate: Double,
    val monitoringDuration: Long
)

/**
 * GC详细信息
 */
data class GcDetails(
    val name: String,
    val collectionCount: Long,
    val collectionTime: Long
)

/**
 * 内存快照
 */
data class MemorySnapshot(
    val timestamp: Long,
    val label: String,
    val heapUsedMB: Long,
    val heapTotalMB: Long,
    val heapMaxMB: Long,
    val systemUsedMB: Long,
    val systemAvailableMB: Long,
    val gcCount: Long
)

/**
 * 内存监控配置
 */
data class MemoryMonitorConfig(
    val heapThreshold: Double = 0.8,
    val gcRateThreshold: Double = 10.0,
    val lowMemoryThresholdMB: Long = 100L
) : MonitorConfig(
    samplingIntervalMs = 5000L,
    enableThresholdChecking = true,
    retainHistoryCount = 60
)
