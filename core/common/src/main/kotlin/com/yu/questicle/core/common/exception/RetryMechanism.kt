package com.yu.questicle.core.common.exception

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.resultSuccess
import com.yu.questicle.core.common.result.resultError
import kotlinx.coroutines.delay
import kotlin.math.min
import kotlin.math.pow
import kotlin.random.Random

/**
 * 重试机制实现
 * 
 * 提供多种重试策略：
 * - 固定延迟重试
 * - 指数退避重试
 * - 线性退避重试
 * - 随机抖动重试
 */
object RetryMechanism {
    
    private val logger: QLogger = QLoggerFactory.getLogger(RetryMechanism::class)
    
    /**
     * 带指数退避的重试
     */
    suspend fun <T> retryWithExponentialBackoff(
        maxRetries: Int = 3,
        initialDelayMs: Long = 1000,
        maxDelayMs: Long = 30000,
        backoffFactor: Double = 2.0,
        jitter: Boolean = true,
        retryIf: (QuesticleException) -> Bo<PERSON>an = { it.retryable },
        operation: suspend () -> T
    ): Result<T> {
        var lastException: QuesticleException? = null
        
        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    logger.i("Operation succeeded after $attempt retries")
                }
                return resultSuccess(result)
            } catch (e: Exception) {
                val questicleException = e.toQuesticleException()
                lastException = questicleException
                
                // 检查是否应该重试
                if (attempt >= maxRetries || !retryIf(questicleException)) {
                    logger.e("Operation failed after $attempt attempts", questicleException)
                    return resultError(questicleException)
                }
                
                // 计算延迟时间
                val baseDelay = (initialDelayMs * backoffFactor.pow(attempt)).toLong()
                val delayMs = min(baseDelay, maxDelayMs)
                val finalDelay = if (jitter) {
                    delayMs + Random.nextLong(-delayMs / 4, delayMs / 4)
                } else {
                    delayMs
                }
                
                logger.w("Operation failed (attempt ${attempt + 1}/$maxRetries), retrying in ${finalDelay}ms", questicleException)
                delay(finalDelay)
            }
        }
        
        return resultError(lastException ?: UnknownException("Retry failed"))
    }
    
    /**
     * 带线性退避的重试
     */
    suspend fun <T> retryWithLinearBackoff(
        maxRetries: Int = 3,
        initialDelayMs: Long = 1000,
        delayIncrementMs: Long = 1000,
        maxDelayMs: Long = 10000,
        jitter: Boolean = true,
        retryIf: (QuesticleException) -> Boolean = { it.retryable },
        operation: suspend () -> T
    ): Result<T> {
        var lastException: QuesticleException? = null
        
        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    logger.i("Operation succeeded after $attempt retries")
                }
                return resultSuccess(result)
            } catch (e: Exception) {
                val questicleException = e.toQuesticleException()
                lastException = questicleException

                if (attempt >= maxRetries || !retryIf(questicleException)) {
                    logger.e("Operation failed after $attempt attempts", questicleException)
                    return resultError(questicleException)
                }
                
                val baseDelay = min(initialDelayMs + (attempt * delayIncrementMs), maxDelayMs)
                val finalDelay = if (jitter) {
                    baseDelay + Random.nextLong(-baseDelay / 4, baseDelay / 4)
                } else {
                    baseDelay
                }
                
                logger.w("Operation failed (attempt ${attempt + 1}/$maxRetries), retrying in ${finalDelay}ms", questicleException)
                delay(finalDelay)
            }
        }
        
        return resultError(lastException ?: UnknownException("Retry failed"))
    }
    
    /**
     * 固定延迟重试
     */
    suspend fun <T> retryWithFixedDelay(
        maxRetries: Int = 3,
        delayMs: Long = 1000,
        retryIf: (QuesticleException) -> Boolean = { it.retryable },
        operation: suspend () -> T
    ): Result<T> {
        var lastException: QuesticleException? = null
        
        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    logger.i("Operation succeeded after $attempt retries")
                }
                return resultSuccess(result)
            } catch (e: Exception) {
                val questicleException = e.toQuesticleException()
                lastException = questicleException

                if (attempt >= maxRetries || !retryIf(questicleException)) {
                    logger.e("Operation failed after $attempt attempts", questicleException)
                    return resultError(questicleException)
                }
                
                logger.w("Operation failed (attempt ${attempt + 1}/$maxRetries), retrying in ${delayMs}ms", questicleException)
                delay(delayMs)
            }
        }
        
        return resultError(lastException ?: UnknownException("Retry failed"))
    }
    
    /**
     * 立即重试（无延迟）
     */
    suspend fun <T> retryImmediately(
        maxRetries: Int = 3,
        retryIf: (QuesticleException) -> Boolean = { it.retryable },
        operation: suspend () -> T
    ): Result<T> {
        var lastException: QuesticleException? = null
        
        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    logger.i("Operation succeeded after $attempt retries")
                }
                return resultSuccess(result)
            } catch (e: Exception) {
                val questicleException = e.toQuesticleException()
                lastException = questicleException

                if (attempt >= maxRetries || !retryIf(questicleException)) {
                    logger.e("Operation failed after $attempt attempts", questicleException)
                    return resultError(questicleException)
                }
                
                logger.w("Operation failed (attempt ${attempt + 1}/$maxRetries), retrying immediately", questicleException)
            }
        }
        
        return resultError(lastException ?: UnknownException("Retry failed"))
    }
}

/**
 * 重试策略配置
 */
data class RetryConfig(
    val maxRetries: Int = 3,
    val initialDelayMs: Long = 1000,
    val maxDelayMs: Long = 30000,
    val backoffFactor: Double = 2.0,
    val jitter: Boolean = true,
    val retryableExceptions: Set<Class<out QuesticleException>> = setOf(
        NetworkException::class.java,
        DatabaseException::class.java
    )
) {
    
    fun shouldRetry(exception: QuesticleException): Boolean {
        return exception.retryable || retryableExceptions.any { it.isInstance(exception) }
    }
}

/**
 * 重试策略枚举
 */
enum class RetryStrategy {
    EXPONENTIAL_BACKOFF,
    LINEAR_BACKOFF,
    FIXED_DELAY,
    IMMEDIATE
}

/**
 * 重试执行器
 */
class RetryExecutor(private val config: RetryConfig = RetryConfig()) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(RetryExecutor::class)
    
    /**
     * 执行带重试的操作
     */
    suspend fun <T> execute(
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
        operation: suspend () -> T
    ): Result<T> {
        return when (strategy) {
            RetryStrategy.EXPONENTIAL_BACKOFF -> RetryMechanism.retryWithExponentialBackoff(
                maxRetries = config.maxRetries,
                initialDelayMs = config.initialDelayMs,
                maxDelayMs = config.maxDelayMs,
                backoffFactor = config.backoffFactor,
                jitter = config.jitter,
                retryIf = config::shouldRetry,
                operation = operation
            )
            
            RetryStrategy.LINEAR_BACKOFF -> RetryMechanism.retryWithLinearBackoff(
                maxRetries = config.maxRetries,
                initialDelayMs = config.initialDelayMs,
                delayIncrementMs = config.initialDelayMs,
                maxDelayMs = config.maxDelayMs,
                jitter = config.jitter,
                retryIf = config::shouldRetry,
                operation = operation
            )
            
            RetryStrategy.FIXED_DELAY -> RetryMechanism.retryWithFixedDelay(
                maxRetries = config.maxRetries,
                delayMs = config.initialDelayMs,
                retryIf = config::shouldRetry,
                operation = operation
            )
            
            RetryStrategy.IMMEDIATE -> RetryMechanism.retryImmediately(
                maxRetries = config.maxRetries,
                retryIf = config::shouldRetry,
                operation = operation
            )
        }
    }
}

/**
 * 重试扩展函数
 */
suspend fun <T> retryOnFailure(
    maxRetries: Int = 3,
    delayMs: Long = 1000,
    operation: suspend () -> T
): Result<T> {
    return RetryMechanism.retryWithFixedDelay(
        maxRetries = maxRetries,
        delayMs = delayMs,
        operation = operation
    )
}

/**
 * 带指数退避的重试扩展函数
 */
suspend fun <T> retryWithBackoff(
    maxRetries: Int = 3,
    initialDelayMs: Long = 1000,
    maxDelayMs: Long = 30000,
    operation: suspend () -> T
): Result<T> {
    return RetryMechanism.retryWithExponentialBackoff(
        maxRetries = maxRetries,
        initialDelayMs = initialDelayMs,
        maxDelayMs = maxDelayMs,
        operation = operation
    )
}

/**
 * 断路器模式实现
 *
 * 当服务频繁失败时，暂时停止调用以避免雪崩效应
 */
class CircuitBreaker(
    private val failureThreshold: Int = 5,
    private val recoveryTimeoutMs: Long = 60000,
    private val monitoringPeriodMs: Long = 10000
) {

    private val logger: QLogger = QLoggerFactory.getLogger(CircuitBreaker::class)

    @Volatile
    private var state: CircuitBreakerState = CircuitBreakerState.CLOSED

    @Volatile
    private var failureCount: Int = 0

    @Volatile
    private var lastFailureTime: Long = 0

    @Volatile
    private var nextAttemptTime: Long = 0

    /**
     * 执行操作
     */
    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        when (state) {
            CircuitBreakerState.OPEN -> {
                if (System.currentTimeMillis() >= nextAttemptTime) {
                    state = CircuitBreakerState.HALF_OPEN
                    logger.i("Circuit breaker transitioning to HALF_OPEN state")
                } else {
                    val exception = CircuitBreakerOpenException(
                        "Circuit breaker is OPEN, next attempt at ${nextAttemptTime}"
                    )
                    return resultError(exception)
                }
            }

            CircuitBreakerState.HALF_OPEN -> {
                // 在半开状态下，只允许一个请求通过
            }

            CircuitBreakerState.CLOSED -> {
                // 正常状态，允许所有请求
            }
        }

        return try {
            val result = operation()
            onSuccess()
            resultSuccess(result)
        } catch (e: Exception) {
            val questicleException = e.toQuesticleException()
            onFailure(questicleException)
            resultError(questicleException)
        }
    }

    /**
     * 成功时的处理
     */
    private fun onSuccess() {
        failureCount = 0
        if (state == CircuitBreakerState.HALF_OPEN) {
            state = CircuitBreakerState.CLOSED
            logger.i("Circuit breaker transitioning to CLOSED state")
        }
    }

    /**
     * 失败时的处理
     */
    private fun onFailure(exception: QuesticleException) {
        failureCount++
        lastFailureTime = System.currentTimeMillis()

        if (failureCount >= failureThreshold) {
            state = CircuitBreakerState.OPEN
            nextAttemptTime = System.currentTimeMillis() + recoveryTimeoutMs
            logger.w("Circuit breaker transitioning to OPEN state after $failureCount failures")
        }
    }

    /**
     * 获取断路器状态
     */
    fun getState(): CircuitBreakerState = state

    /**
     * 获取失败次数
     */
    fun getFailureCount(): Int = failureCount

    /**
     * 重置断路器
     */
    fun reset() {
        state = CircuitBreakerState.CLOSED
        failureCount = 0
        lastFailureTime = 0
        nextAttemptTime = 0
        logger.i("Circuit breaker reset to CLOSED state")
    }
}

/**
 * 断路器状态
 */
enum class CircuitBreakerState {
    CLOSED,     // 关闭状态，正常工作
    OPEN,       // 开启状态，拒绝请求
    HALF_OPEN   // 半开状态，允许少量请求测试服务是否恢复
}

/**
 * 断路器开启异常
 */
class CircuitBreakerOpenException(
    message: String,
    cause: Throwable? = null
) : TechnicalException(
    message = message,
    cause = cause,
    errorCode = "CIRCUIT_BREAKER_OPEN",
    errorType = ErrorType.SYSTEM,
    severity = ErrorSeverity.MEDIUM,
    userMessage = "服务暂时不可用，请稍后重试",
    retryable = true
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return CircuitBreakerOpenException(message, cause)
    }

    override fun getDefaultUserMessage(): String {
        return "服务暂时不可用，请稍后重试"
    }
}
