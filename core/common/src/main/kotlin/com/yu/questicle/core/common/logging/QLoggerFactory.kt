package com.yu.questicle.core.common.logging

import java.util.concurrent.ConcurrentHashMap
import kotlin.reflect.KClass

/**
 * QLogger工厂类
 * 负责创建和管理Logger实例
 */
object QLoggerFactory {
    
    private val loggers = ConcurrentHashMap<String, QLogger>()
    private var loggerManager: LoggerManager = DefaultLoggerManager()
    
    /**
     * 获取指定名称的Logger
     */
    fun getLogger(name: String): QLogger {
        return loggers.computeIfAbsent(name) { loggerManager.createLogger(it) }
    }
    
    /**
     * 获取指定类的Logger
     */
    fun getLogger(clazz: Class<*>): QLogger {
        return getLogger(clazz.name)
    }
    
    /**
     * 获取指定Kotlin类的Logger
     */
    fun getLogger(clazz: KClass<*>): QLogger {
        return getLogger(clazz.qualifiedName ?: clazz.java.name)
    }
    
    /**
     * 获取调用者类的Logger
     */
    inline fun <reified T> getLogger(): QLogger {
        return getLogger(T::class)
    }
    
    /**
     * 设置Logger管理器
     */
    fun setLoggerManager(manager: LoggerManager) {
        this.loggerManager = manager
    }
    
    /**
     * 获取所有Logger
     */
    fun getAllLoggers(): Map<String, QLogger> {
        return loggers.toMap()
    }
    
    /**
     * 清除所有Logger缓存
     */
    fun clearCache() {
        loggers.clear()
    }
    
    /**
     * 重新配置所有Logger
     */
    fun reconfigure() {
        loggers.clear()
        loggerManager.reconfigure()
    }
}

/**
 * Logger管理器接口
 */
interface LoggerManager {
    
    /**
     * 创建Logger实例
     */
    fun createLogger(name: String): QLogger
    
    /**
     * 获取全局配置
     */
    fun getGlobalConfig(): LogConfig
    
    /**
     * 设置全局配置
     */
    fun setGlobalConfig(config: LogConfig)
    
    /**
     * 获取Logger配置
     */
    fun getLoggerConfig(name: String): LoggerConfig
    
    /**
     * 设置Logger配置
     */
    fun setLoggerConfig(name: String, config: LoggerConfig)
    
    /**
     * 重新配置
     */
    fun reconfigure()
    
    /**
     * 关闭管理器
     */
    fun shutdown()
}

/**
 * 默认Logger管理器实现
 */
class DefaultLoggerManager(
    private val configLoader: LogConfigLoader? = null
) : LoggerManager {

    private var globalConfig = LogConfig()
    private val loggerConfigs = ConcurrentHashMap<String, LoggerConfig>()
    private val logProcessor = LogProcessor.getInstance()

    init {
        // 初始化时加载配置
        loadConfiguration()
    }
    
    override fun createLogger(name: String): QLogger {
        val config = getLoggerConfig(name)
        return QLoggerImpl(name, config, logProcessor)
    }
    
    override fun getGlobalConfig(): LogConfig {
        return globalConfig
    }
    
    override fun setGlobalConfig(config: LogConfig) {
        this.globalConfig = config
        // 通知所有Logger更新配置
        QLoggerFactory.getAllLoggers().values.forEach { logger ->
            if (logger is QLoggerImpl) {
                logger.updateConfig(getLoggerConfig(logger.name))
            }
        }
    }
    
    override fun getLoggerConfig(name: String): LoggerConfig {
        // 首先查找精确匹配
        loggerConfigs[name]?.let { return it }
        
        // 查找包级别配置
        var packageName = name
        while (packageName.contains('.')) {
            packageName = packageName.substringBeforeLast('.')
            loggerConfigs[packageName]?.let { 
                return it.copy(name = name)
            }
        }
        
        // 使用全局配置
        return LoggerConfig(
            name = name,
            level = globalConfig.globalLevel,
            enabled = globalConfig.enabled,
            samplingRate = globalConfig.samplingRate,
            enableAsync = globalConfig.enableAsync,
            enableSensitiveDataMasking = globalConfig.enableSensitiveDataMasking
        )
    }
    
    override fun setLoggerConfig(name: String, config: LoggerConfig) {
        loggerConfigs[name] = config
        
        // 更新对应的Logger
        QLoggerFactory.getAllLoggers()[name]?.let { logger ->
            if (logger is QLoggerImpl) {
                logger.updateConfig(config)
            }
        }
    }
    
    override fun reconfigure() {
        // 重新加载配置
        loadConfiguration()
        
        // 更新所有Logger
        QLoggerFactory.getAllLoggers().forEach { (name, logger) ->
            if (logger is QLoggerImpl) {
                logger.updateConfig(getLoggerConfig(name))
            }
        }
    }
    
    override fun shutdown() {
        logProcessor.shutdown()
    }
    
    private fun loadConfiguration() {
        configLoader?.let { loader ->
            try {
                // 使用协程在后台加载配置
                kotlinx.coroutines.runBlocking {
                    // 加载全局配置
                    globalConfig = loader.loadGlobalConfig()

                    // 加载Logger配置
                    val loadedConfigs = loader.loadLoggerConfigs()
                    loggerConfigs.clear()
                    loggerConfigs.putAll(loadedConfigs)
                }
            } catch (e: Exception) {
                // 配置加载失败，使用默认配置
                globalConfig = LogConfig()
                loggerConfigs.clear()
            }
        }
    }

    /**
     * 保存当前配置
     */
    suspend fun saveConfiguration() {
        configLoader?.let { loader ->
            try {
                loader.saveGlobalConfig(globalConfig)
                loader.saveLoggerConfigs(loggerConfigs.toMap())
            } catch (e: Exception) {
                // 保存失败，记录错误但不抛出异常
                System.err.println("Failed to save log configuration: ${e.message}")
            }
        }
    }

    /**
     * 重置为默认配置
     */
    suspend fun resetToDefaults() {
        configLoader?.let { loader ->
            try {
                loader.resetToDefaults()
                loadConfiguration()
            } catch (e: Exception) {
                // 重置失败，使用硬编码默认值
                globalConfig = LogConfig()
                loggerConfigs.clear()
            }
        }
    }
}

/**
 * 全局日志配置
 */
data class LogConfig(
    val globalLevel: LogLevel = LogLevel.INFO,
    val enabled: Boolean = true,
    val enableAsync: Boolean = true,
    val bufferSize: Int = 1024,
    val flushInterval: Long = 5000, // 毫秒
    val maxFileSize: Long = 10 * 1024 * 1024, // 10MB
    val maxFiles: Int = 5,
    val enableCompression: Boolean = true,
    val enableEncryption: Boolean = false,
    val enableSensitiveDataMasking: Boolean = true,
    val samplingRate: Double = 1.0,
    val outputDirectory: String = "logs",
    val fileNamePattern: String = "questicle-%d{yyyy-MM-dd}.log",
    val enableConsoleOutput: Boolean = true,
    val enableFileOutput: Boolean = true,
    val enableRemoteLogging: Boolean = false,
    val remoteEndpoint: String? = null,
    val enableMetrics: Boolean = true,
    val enableTracing: Boolean = true
)

/**
 * Logger扩展函数
 * 提供便捷的使用方式
 */

/**
 * 为类获取Logger
 */
inline fun <reified T> T.logger(): QLogger = QLoggerFactory.getLogger(T::class)

/**
 * 为对象获取Logger（使用不同的方法名避免签名冲突）
 */
fun Any.getLogger(): QLogger = QLoggerFactory.getLogger(this::class.java)

/**
 * 获取指定名称的Logger
 */
fun logger(name: String): QLogger = QLoggerFactory.getLogger(name)

/**
 * 获取调用者的Logger
 */
fun logger(): QLogger {
    val stackTrace = Thread.currentThread().stackTrace
    val callerClass = stackTrace[2].className
    return QLoggerFactory.getLogger(callerClass)
}

/**
 * 日志DSL支持
 */
inline fun QLogger.verbose(block: () -> String) {
    if (isVerboseEnabled()) {
        v(block())
    }
}

inline fun QLogger.debug(block: () -> String) {
    if (isDebugEnabled()) {
        d(block())
    }
}

inline fun QLogger.info(block: () -> String) {
    if (isInfoEnabled()) {
        i(block())
    }
}

inline fun QLogger.warn(block: () -> String) {
    if (isWarnEnabled()) {
        w(block())
    }
}

inline fun QLogger.error(block: () -> String) {
    if (isErrorEnabled()) {
        e(block())
    }
}

inline fun QLogger.fatal(block: () -> String) {
    if (isFatalEnabled()) {
        f(block())
    }
}

/**
 * 结构化日志DSL
 */
class LogBuilder {
    private val context = mutableMapOf<String, Any?>()
    private val tags = mutableSetOf<String>()
    private var message: String = ""
    private var throwable: Throwable? = null
    
    fun message(msg: String) {
        this.message = msg
    }
    
    fun context(key: String, value: Any?) {
        this.context[key] = value
    }
    
    fun context(map: Map<String, Any?>) {
        this.context.putAll(map)
    }
    
    fun tag(tag: String) {
        this.tags.add(tag)
    }
    
    fun tags(vararg tags: String) {
        this.tags.addAll(tags)
    }
    
    fun exception(throwable: Throwable) {
        this.throwable = throwable
    }
    
    internal fun build(): Triple<String, Map<String, Any?>, Set<String>> {
        return Triple(message, context.toMap(), tags.toSet())
    }
    
    internal fun getThrowable(): Throwable? = throwable
}

/**
 * 结构化日志扩展函数
 */
fun QLogger.infoStructured(block: LogBuilder.() -> Unit) {
    if (isInfoEnabled()) {
        val builder = LogBuilder()
        builder.block()
        val (message, context, tags) = builder.build()
        val logger = this.withContext(context).withTags(tags)
        val throwable = builder.getThrowable()
        if (throwable != null) {
            logger.i(throwable, message)
        } else {
            logger.i(message)
        }
    }
}

fun QLogger.errorStructured(block: LogBuilder.() -> Unit) {
    if (isErrorEnabled()) {
        val builder = LogBuilder()
        builder.block()
        val (message, context, tags) = builder.build()
        val logger = this.withContext(context).withTags(tags)
        val throwable = builder.getThrowable()
        if (throwable != null) {
            logger.e(throwable, message)
        } else {
            logger.e(message)
        }
    }
}

// 类似地为其他级别添加结构化日志支持
fun QLogger.debugStructured(block: LogBuilder.() -> Unit) {
    if (isDebugEnabled()) {
        val builder = LogBuilder()
        builder.block()
        val (message, context, tags) = builder.build()
        val logger = this.withContext(context).withTags(tags)
        val throwable = builder.getThrowable()
        if (throwable != null) {
            logger.d(throwable, message)
        } else {
            logger.d(message)
        }
    }
}

fun QLogger.warnStructured(block: LogBuilder.() -> Unit) {
    if (isWarnEnabled()) {
        val builder = LogBuilder()
        builder.block()
        val (message, context, tags) = builder.build()
        val logger = this.withContext(context).withTags(tags)
        val throwable = builder.getThrowable()
        if (throwable != null) {
            logger.w(throwable, message)
        } else {
            logger.w(message)
        }
    }
}
