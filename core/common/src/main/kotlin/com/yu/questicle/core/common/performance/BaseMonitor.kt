package com.yu.questicle.core.common.performance

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 性能监控器基类
 * 
 * 提供通用的监控功能和生命周期管理
 */
abstract class BaseMonitor<T : PerformanceMetric> {
    
    protected val logger: QLogger = QLoggerFactory.getLogger(this::class)
    
    // 监控状态
    private val _isMonitoring = AtomicBoolean(false)
    val isMonitoring: Boolean get() = _isMonitoring.get()
    
    // 指标数据流
    private val _metrics = MutableSharedFlow<T>(
        replay = 1,
        extraBufferCapacity = 100
    )
    val metrics: SharedFlow<T> = _metrics.asSharedFlow()
    
    // 当前指标
    private val _currentMetric = MutableStateFlow<T?>(null)
    val currentMetric: StateFlow<T?> = _currentMetric.asStateFlow()
    
    // 协程作用域
    protected val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 监控任务
    private var monitoringJob: Job? = null
    
    // 配置
    protected var config = MonitorConfig()
    
    /**
     * 开始监控
     */
    suspend fun startMonitoring() {
        if (_isMonitoring.compareAndSet(false, true)) {
            logger.i("Starting ${this::class.simpleName} monitoring")
            
            try {
                onStartMonitoring()
                startMetricCollection()
                logger.i("${this::class.simpleName} monitoring started successfully")
            } catch (e: Exception) {
                _isMonitoring.set(false)
                logger.e("Failed to start ${this::class.simpleName} monitoring", e)
                throw e
            }
        }
    }
    
    /**
     * 停止监控
     */
    suspend fun stopMonitoring() {
        if (_isMonitoring.compareAndSet(true, false)) {
            logger.i("Stopping ${this::class.simpleName} monitoring")
            
            try {
                monitoringJob?.cancel()
                onStopMonitoring()
                logger.i("${this::class.simpleName} monitoring stopped successfully")
            } catch (e: Exception) {
                logger.e("Error stopping ${this::class.simpleName} monitoring", e)
            }
        }
    }
    
    /**
     * 获取当前指标
     */
    fun getCurrentMetric(): T? = _currentMetric.value
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: MonitorConfig) {
        this.config = newConfig
        onConfigChanged(newConfig)
    }
    
    /**
     * 启动指标收集
     */
    private fun startMetricCollection() {
        monitoringJob = scope.launch {
            while (isActive && _isMonitoring.get()) {
                try {
                    val metric = collectMetric()
                    
                    // 更新当前指标
                    _currentMetric.value = metric
                    
                    // 发送到数据流
                    _metrics.emit(metric)
                    
                    // 检查阈值
                    checkThresholds(metric)
                    
                } catch (e: Exception) {
                    logger.e("Error collecting metric in ${this::class.simpleName}", e)
                }
                
                delay(config.samplingIntervalMs)
            }
        }
    }
    
    /**
     * 收集指标数据 - 子类实现
     */
    protected abstract suspend fun collectMetric(): T
    
    /**
     * 开始监控时的回调 - 子类可重写
     */
    protected open suspend fun onStartMonitoring() {}
    
    /**
     * 停止监控时的回调 - 子类可重写
     */
    protected open suspend fun onStopMonitoring() {}
    
    /**
     * 配置变更时的回调 - 子类可重写
     */
    protected open fun onConfigChanged(newConfig: MonitorConfig) {}
    
    /**
     * 检查阈值 - 子类可重写
     */
    protected open suspend fun checkThresholds(metric: T) {}
}

/**
 * 监控器配置
 */
data class MonitorConfig(
    val samplingIntervalMs: Long = 1000L,
    val enableThresholdChecking: Boolean = true,
    val retainHistoryCount: Int = 100
)

/**
 * 性能指标基类
 */
abstract class PerformanceMetric {
    abstract val timestamp: Long
    abstract val metricType: MetricType
    abstract fun toMap(): Map<String, Any?>
}

/**
 * 指标类型枚举
 */
enum class MetricType {
    FRAME_RATE,
    MEMORY,
    CPU,
    NETWORK,
    OPERATION,
    CUSTOM
}

/**
 * 帧率指标
 */
data class FrameRateMetric(
    override val timestamp: Long,
    val fps: Int,
    val avgFrameTime: Double,
    val droppedFrameRate: Double,
    val isSmooth: Boolean
) : PerformanceMetric() {
    override val metricType = MetricType.FRAME_RATE
    
    override fun toMap(): Map<String, Any?> = mapOf(
        "timestamp" to timestamp,
        "fps" to fps,
        "avgFrameTime" to avgFrameTime,
        "droppedFrameRate" to droppedFrameRate,
        "isSmooth" to isSmooth
    )
}

/**
 * 内存指标
 */
data class MemoryMetric(
    override val timestamp: Long,
    val usedHeap: Long,
    val totalHeap: Long,
    val maxHeap: Long,
    val heapUtilization: Double,
    val usedMemory: Long,
    val totalMemory: Long,
    val availableMemory: Long,
    val memoryPressure: Boolean,
    val gcCount: Long,
    val gcRate: Double
) : PerformanceMetric() {
    override val metricType = MetricType.MEMORY
    
    override fun toMap(): Map<String, Any?> = mapOf(
        "timestamp" to timestamp,
        "usedHeap" to usedHeap,
        "totalHeap" to totalHeap,
        "maxHeap" to maxHeap,
        "heapUtilization" to heapUtilization,
        "usedMemory" to usedMemory,
        "totalMemory" to totalMemory,
        "availableMemory" to availableMemory,
        "memoryPressure" to memoryPressure,
        "gcCount" to gcCount,
        "gcRate" to gcRate
    )
}

/**
 * CPU指标
 */
data class CPUMetric(
    override val timestamp: Long,
    val cpuUsage: Double,
    val threadCount: Int,
    val loadAverage: Double,
    val isHighUsage: Boolean
) : PerformanceMetric() {
    override val metricType = MetricType.CPU
    
    override fun toMap(): Map<String, Any?> = mapOf(
        "timestamp" to timestamp,
        "cpuUsage" to cpuUsage,
        "threadCount" to threadCount,
        "loadAverage" to loadAverage,
        "isHighUsage" to isHighUsage
    )
}

/**
 * 性能快照
 */
data class PerformanceSnapshot(
    val timestamp: Long,
    val frameRate: FrameRateMetric?,
    val memory: MemoryMetric?,
    val cpu: CPUMetric?,
    val network: NetworkMetric?,
    val overall: OverallPerformance
)

/**
 * 网络指标
 */
data class NetworkMetric(
    override val timestamp: Long,
    val requestCount: Long,
    val avgResponseTime: Double,
    val errorRate: Double,
    val throughput: Double
) : PerformanceMetric() {
    override val metricType = MetricType.NETWORK
    
    override fun toMap(): Map<String, Any?> = mapOf(
        "timestamp" to timestamp,
        "requestCount" to requestCount,
        "avgResponseTime" to avgResponseTime,
        "errorRate" to errorRate,
        "throughput" to throughput
    )
}

/**
 * 整体性能评估
 */
data class OverallPerformance(
    val score: Double,
    val grade: PerformanceGrade,
    val issues: List<String>
)

/**
 * 性能等级
 */
enum class PerformanceGrade {
    EXCELLENT,  // 优秀
    GOOD,       // 良好
    FAIR,       // 一般
    POOR,       // 较差
    CRITICAL    // 严重
}

/**
 * 时间范围
 */
data class TimeRange(
    val startTime: Long,
    val endTime: Long
)

/**
 * 性能统计
 */
data class PerformanceStatistics(
    val totalSnapshots: Long,
    val avgFrameRate: Double,
    val avgMemoryUsage: Double,
    val avgCpuUsage: Double,
    val performanceScore: Double,
    val issueCount: Int
) {
    companion object {
        val EMPTY = PerformanceStatistics(0, 0.0, 0.0, 0.0, 0.0, 0)
    }
}
