package com.yu.questicle.core.common.exception

import android.content.Context
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 异常处理模块
 * 
 * 提供异常处理系统的所有依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class ExceptionModule {
    
    companion object {
        
        /**
         * 提供崩溃报告管理器
         */
        @Provides
        @Singleton
        fun provideCrashReportingManager(
            @ApplicationContext context: Context
        ): CrashReportingManager {
            return CrashReportingManager(context)
        }
        
        /**
         * 提供异常恢复管理器
         */
        @Provides
        @Singleton
        fun provideExceptionRecoveryManager(): ExceptionRecoveryManager {
            return ExceptionRecoveryManager()
        }
        
        /**
         * 提供异常监控服务
         */
        @Provides
        @Singleton
        fun provideExceptionMonitoringService(
            exceptionHandler: QuesticleExceptionHandler,
            recoveryManager: ExceptionRecoveryManager,
            crashReportingManager: CrashReportingManager
        ): ExceptionMonitoringService {
            return ExceptionMonitoringService(
                exceptionHandler = exceptionHandler,
                recoveryManager = recoveryManager,
                crashReportingManager = crashReportingManager
            )
        }
        
        /**
         * 提供异常处理门面
         */
        @Provides
        @Singleton
        fun provideExceptionHandlingFacade(
            exceptionHandler: QuesticleExceptionHandler,
            recoveryManager: ExceptionRecoveryManager,
            crashReportingManager: CrashReportingManager,
            monitoringService: ExceptionMonitoringService
        ): ExceptionHandlingFacade {
            return ExceptionHandlingFacade(
                exceptionHandler = exceptionHandler,
                recoveryManager = recoveryManager,
                crashReportingManager = crashReportingManager,
                monitoringService = monitoringService
            )
        }
    }
}

/**
 * 异常处理门面
 * 
 * 提供统一的异常处理接口，简化客户端使用
 */
@Singleton
class ExceptionHandlingFacade @javax.inject.Inject constructor(
    private val exceptionHandler: QuesticleExceptionHandler,
    private val recoveryManager: ExceptionRecoveryManager,
    private val crashReportingManager: CrashReportingManager,
    private val monitoringService: ExceptionMonitoringService
) {
    
    /**
     * 处理异常（带自动恢复）
     */
    suspend fun handleException(
        exception: QuesticleException,
        context: ExceptionContext = ExceptionContext.EMPTY,
        enableAutoRecovery: Boolean = true
    ): ExceptionHandlingResult {
        
        // 1. 基础异常处理
        val handlingResult = exceptionHandler.handleException(exception, context)
        
        // 2. 尝试自动恢复
        val recoveryResult = if (enableAutoRecovery && exception.retryable) {
            recoveryManager.attemptRecovery(exception, context)
        } else {
            null
        }
        
        // 3. 报告异常（如果需要）
        if (handlingResult.shouldReport) {
            crashReportingManager.reportException(
                exception = exception,
                context = context,
                isFatal = false
            )
        }
        
        return ExceptionHandlingResult(
            originalException = exception,
            handlingResult = handlingResult,
            recoveryResult = recoveryResult,
            wasRecovered = recoveryResult?.isSuccess == true
        )
    }
    
    /**
     * 处理致命异常
     */
    suspend fun handleFatalException(
        exception: QuesticleException,
        context: ExceptionContext = ExceptionContext.EMPTY
    ): ExceptionHandlingResult {
        
        // 致命异常直接处理，不尝试恢复
        val handlingResult = exceptionHandler.handleException(exception, context)
        
        // 立即报告崩溃
        crashReportingManager.reportException(
            exception = exception,
            context = context,
            isFatal = true
        )
        
        return ExceptionHandlingResult(
            originalException = exception,
            handlingResult = handlingResult,
            recoveryResult = null,
            wasRecovered = false
        )
    }
    
    /**
     * 处理Throwable（转换为QuesticleException）
     */
    suspend fun handleThrowable(
        throwable: Throwable,
        context: ExceptionContext = ExceptionContext.EMPTY,
        enableAutoRecovery: Boolean = true
    ): ExceptionHandlingResult {
        val questicleException = throwable.toQuesticleException()
        return handleException(questicleException, context, enableAutoRecovery)
    }
    
    /**
     * 安全执行代码块
     */
    suspend fun <T> safeExecute(
        context: ExceptionContext = ExceptionContext.EMPTY,
        enableAutoRecovery: Boolean = true,
        fallbackValue: T? = null,
        block: suspend () -> T
    ): SafeExecutionResult<T> {
        return try {
            val result = block()
            SafeExecutionResult.Success(result)
        } catch (throwable: Throwable) {
            val handlingResult = handleThrowable(throwable, context, enableAutoRecovery)
            
            if (handlingResult.wasRecovered && fallbackValue != null) {
                SafeExecutionResult.Recovered(fallbackValue, handlingResult)
            } else {
                SafeExecutionResult.Failed(handlingResult)
            }
        }
    }
    
    /**
     * 设置用户标识符
     */
    fun setUserId(userId: String) {
        crashReportingManager.setUserId(userId)
    }
    
    /**
     * 设置自定义键值对
     */
    fun setCustomKey(key: String, value: Any?) {
        crashReportingManager.setCustomKey(key, value)
    }
    
    /**
     * 记录用户操作
     */
    fun logUserAction(action: String, parameters: Map<String, Any?> = emptyMap()) {
        crashReportingManager.logUserAction(action, parameters)
    }
    
    /**
     * 获取异常统计
     */
    fun getExceptionStatistics(): ExceptionStatistics {
        val crashStats = crashReportingManager.getCrashStatistics()
        val recoveryStats = recoveryManager.getRecoveryStatistics()
        val monitoringStatus = monitoringService.getMonitoringStatus()
        
        return ExceptionStatistics(
            totalExceptions = monitoringStatus.totalExceptions,
            totalCrashes = crashStats.totalCrashes,
            totalRecoveries = recoveryStats.totalSuccesses,
            crashesByType = crashStats.crashesByType,
            crashesBySeverity = crashStats.crashesBySeverity,
            recoverySuccessRate = recoveryStats.successRate,
            monitoringUptime = monitoringStatus.uptime
        )
    }
    
    /**
     * 获取异常趋势
     */
    fun getExceptionTrends(timeWindowMs: Long = 3600000): ExceptionTrends {
        return monitoringService.getExceptionTrends(timeWindowMs)
    }
    
    /**
     * 配置异常处理系统
     */
    fun configure(config: ExceptionHandlingConfig) {
        // 配置崩溃报告
        crashReportingManager.setConfig(config.crashReportingConfig)
        
        // 配置恢复管理器
        recoveryManager.setConfig(config.recoveryConfig)
        
        // 配置监控服务
        monitoringService.setConfig(config.monitoringConfig)
    }
}

/**
 * 异常处理结果
 */
data class ExceptionHandlingResult(
    val originalException: QuesticleException,
    val handlingResult: ExceptionHandlingResult,
    val recoveryResult: RecoveryResult?,
    val wasRecovered: Boolean
) {
    val isSuccess: Boolean
        get() = wasRecovered || handlingResult.action != ExceptionAction.CRASH
}

/**
 * 安全执行结果
 */
sealed class SafeExecutionResult<T> {
    data class Success<T>(val value: T) : SafeExecutionResult<T>()
    data class Recovered<T>(val fallbackValue: T, val handlingResult: ExceptionHandlingResult) : SafeExecutionResult<T>()
    data class Failed<T>(val handlingResult: ExceptionHandlingResult) : SafeExecutionResult<T>()
    
    val isSuccess: Boolean get() = this is Success || this is Recovered
    val value: T? get() = when (this) {
        is Success -> value
        is Recovered -> fallbackValue
        is Failed -> null
    }
}

/**
 * 异常统计
 */
data class ExceptionStatistics(
    val totalExceptions: Long,
    val totalCrashes: Long,
    val totalRecoveries: Long,
    val crashesByType: Map<String, Long>,
    val crashesBySeverity: Map<ErrorSeverity, Long>,
    val recoverySuccessRate: Double,
    val monitoringUptime: Long
)

/**
 * 异常处理配置
 */
data class ExceptionHandlingConfig(
    val crashReportingConfig: CrashReportingConfig = CrashReportingConfig(),
    val recoveryConfig: RecoveryConfig = RecoveryConfig(),
    val monitoringConfig: MonitoringConfig = MonitoringConfig()
)

/**
 * 扩展函数：将Throwable转换为QuesticleException
 */
fun Throwable.toQuesticleException(): QuesticleException {
    return when (this) {
        is QuesticleException -> this
        is IllegalArgumentException -> ValidationException(
            message = this.message ?: "Validation failed",
            cause = this,
            errorCode = "VALIDATION_ERROR"
        )
        is IllegalStateException -> BusinessLogicException(
            message = this.message ?: "Invalid state",
            cause = this,
            errorCode = "INVALID_STATE"
        )
        is SecurityException -> PermissionException(
            message = this.message ?: "Permission denied",
            cause = this,
            errorCode = "PERMISSION_DENIED"
        )
        is java.net.SocketTimeoutException -> NetworkException(
            message = "Network timeout",
            cause = this,
            errorCode = "NETWORK_TIMEOUT"
        )
        is java.net.UnknownHostException -> NetworkException(
            message = "Network unreachable",
            cause = this,
            errorCode = "NETWORK_UNREACHABLE"
        )
        is java.io.IOException -> SystemException(
            message = this.message ?: "IO error",
            cause = this,
            errorCode = "IO_ERROR"
        )
        is OutOfMemoryError -> SystemException(
            message = "Out of memory",
            cause = this,
            errorCode = "OUT_OF_MEMORY",
            severity = ErrorSeverity.CRITICAL
        )
        else -> UnknownException(
            message = this.message ?: "Unknown error",
            cause = this,
            errorCode = "UNKNOWN_ERROR"
        )
    }
}

/**
 * 扩展函数：创建异常上下文
 */
fun createExceptionContext(vararg pairs: Pair<String, Any?>): ExceptionContext {
    return ExceptionContext(pairs.toMap())
}

/**
 * 扩展函数：安全调用
 */
suspend inline fun <T> safeCall(
    context: ExceptionContext = ExceptionContext.EMPTY,
    enableAutoRecovery: Boolean = true,
    fallbackValue: T? = null,
    crossinline block: suspend () -> T
): SafeExecutionResult<T> {
    // 这里需要获取ExceptionHandlingFacade实例
    // 在实际使用中，应该通过依赖注入获取
    return try {
        val result = block()
        SafeExecutionResult.Success(result)
    } catch (throwable: Throwable) {
        // 简化实现，实际应该使用ExceptionHandlingFacade
        SafeExecutionResult.Failed(
            ExceptionHandlingResult(
                originalException = throwable.toQuesticleException(),
                handlingResult = ExceptionHandlingResult(
                    originalException = throwable.toQuesticleException(),
                    handlingResult = null!!,
                    recoveryResult = null,
                    wasRecovered = false
                ),
                recoveryResult = null,
                wasRecovered = false
            )
        )
    }
}
