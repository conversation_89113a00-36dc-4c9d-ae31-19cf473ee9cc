package com.yu.questicle.core.common.exception

/**
 * Questicle 应用基础异常类
 * 
 * 所有应用特定异常的基类
 * 
 * @property errorCode 错误代码
 * @property severity 错误严重程度
 * @property context 异常上下文
 */
abstract class QuesticleException(
    message: String,
    val errorCode: String,
    val severity: ErrorSeverity,
    cause: Throwable? = null
) : Exception(message, cause) {
    
    val context: MutableMap<String, Any?> = mutableMapOf()
    
    /**
     * 获取异常类型
     */
    abstract val type: ExceptionType
    
    /**
     * 添加上下文信息
     */
    fun addContext(key: String, value: Any?) {
        context[key] = value
    }
    
    /**
     * 获取格式化的错误信息
     */
    fun getFormattedMessage(): String {
        return "[$errorCode] $message (severity: $severity, type: $type)"
    }
    
    /**
     * 是否为严重错误
     */
    fun isCritical(): Boolean = severity == ErrorSeverity.CRITICAL
    
    /**
     * 是否需要立即处理
     */
    fun requiresImmediateAction(): Boolean = severity >= ErrorSeverity.HIGH

    /**
     * 获取用户友好的错误信息
     */
    open fun getDefaultUserMessage(): String {
        return "操作失败，请稍后重试"
    }
}

/**
 * 业务逻辑异常
 */
class BusinessLogicException(
    message: String,
    errorCode: String = "BUSINESS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.BUSINESS_LOGIC
}

/**
 * 网络异常
 */
class NetworkException(
    message: String,
    errorCode: String = "NETWORK_ERROR",
    severity: ErrorSeverity = ErrorSeverity.HIGH,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.NETWORK
}

/**
 * 数据库异常
 */
class DatabaseException(
    message: String,
    errorCode: String = "DATABASE_ERROR",
    severity: ErrorSeverity = ErrorSeverity.HIGH,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.DATABASE
}

/**
 * 系统异常
 */
class SystemException(
    message: String,
    errorCode: String = "SYSTEM_ERROR",
    severity: ErrorSeverity = ErrorSeverity.CRITICAL,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.SYSTEM
}

/**
 * 权限异常
 */
class PermissionException(
    message: String,
    errorCode: String = "PERMISSION_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.PERMISSION
}

/**
 * 验证异常
 */
class ValidationException(
    message: String,
    errorCode: String = "VALIDATION_ERROR",
    severity: ErrorSeverity = ErrorSeverity.LOW,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.VALIDATION
}

/**
 * UI异常
 */
class UIException(
    message: String,
    errorCode: String = "UI_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.UI
}

/**
 * 配置异常
 */
class ConfigurationException(
    message: String,
    errorCode: String = "CONFIG_ERROR",
    severity: ErrorSeverity = ErrorSeverity.HIGH,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.CONFIGURATION
}

/**
 * 第三方服务异常
 */
class ThirdPartyException(
    message: String,
    errorCode: String = "THIRD_PARTY_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.THIRD_PARTY
}

/**
 * 业务异常 - 业务逻辑错误
 */
open class BusinessException(
    message: String,
    errorCode: String = "BUSINESS_ERROR",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    cause: Throwable? = null
) : QuesticleException(message, errorCode, severity, cause) {
    override val type: ExceptionType = ExceptionType.BUSINESS_LOGIC

    override fun getDefaultUserMessage(): String {
        return "业务操作失败，请稍后重试"
    }
}
