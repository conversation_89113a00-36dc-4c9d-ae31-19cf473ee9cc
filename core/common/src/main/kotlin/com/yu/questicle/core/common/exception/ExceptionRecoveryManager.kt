package com.yu.questicle.core.common.exception

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 异常恢复管理器
 * 
 * 负责：
 * - 智能异常恢复策略
 * - 自动重试机制
 * - 降级处理
 * - 恢复状态监控
 * - 恢复成功率统计
 */
@Singleton
class ExceptionRecoveryManager @Inject constructor() {
    
    private val logger: QLogger = QLoggerFactory.getLogger(ExceptionRecoveryManager::class)
    
    // 恢复策略注册表
    private val recoveryStrategies = ConcurrentHashMap<Class<out QuesticleException>, RecoveryStrategy<*>>()
    private val globalStrategies = mutableListOf<GlobalRecoveryStrategy>()
    
    // 恢复统计
    private val recoveryAttempts = AtomicLong(0)
    private val recoverySuccesses = AtomicLong(0)
    private val recoveryFailures = AtomicLong(0)
    private val recoveryByType = ConcurrentHashMap<String, RecoveryStats>()
    
    // 重试计数器
    private val retryCounters = ConcurrentHashMap<String, AtomicInteger>()
    
    // 恢复事件流
    private val _recoveryEvents = MutableSharedFlow<RecoveryEvent>()
    val recoveryEvents: SharedFlow<RecoveryEvent> = _recoveryEvents.asSharedFlow()
    
    // 配置
    private var config = RecoveryConfig()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    init {
        registerDefaultStrategies()
    }
    
    /**
     * 注册默认恢复策略
     */
    private fun registerDefaultStrategies() {
        // 网络异常恢复策略
        registerStrategy(NetworkException::class.java, NetworkRecoveryStrategy())
        
        // 数据库异常恢复策略
        registerStrategy(DatabaseException::class.java, DatabaseRecoveryStrategy())
        
        // 验证异常恢复策略
        registerStrategy(ValidationException::class.java, ValidationRecoveryStrategy())
        
        // 权限异常恢复策略
        registerStrategy(PermissionException::class.java, PermissionRecoveryStrategy())
        
        // 游戏异常恢复策略
        registerStrategy(GameException::class.java, GameRecoveryStrategy())
        
        logger.i("Default recovery strategies registered")
    }
    
    /**
     * 尝试恢复异常
     */
    suspend fun attemptRecovery(
        exception: QuesticleException,
        context: ExceptionContext = ExceptionContext.EMPTY
    ): RecoveryResult {
        recoveryAttempts.incrementAndGet()
        
        val recoveryKey = generateRecoveryKey(exception, context)
        val retryCount = retryCounters.computeIfAbsent(recoveryKey) { AtomicInteger(0) }
        
        // 检查重试次数限制
        if (retryCount.get() >= config.maxRetryAttempts) {
            logger.w("Max retry attempts reached for: ${exception.message}")
            return RecoveryResult.failure("Max retry attempts exceeded")
        }
        
        retryCount.incrementAndGet()
        
        try {
            val result = performRecovery(exception, context)
            
            // 记录恢复事件
            val event = RecoveryEvent(
                exception = exception,
                context = context,
                result = result,
                retryCount = retryCount.get(),
                timestamp = System.currentTimeMillis()
            )
            
            scope.launch {
                _recoveryEvents.emit(event)
            }
            
            // 更新统计
            updateRecoveryStats(exception, result)
            
            // 如果恢复成功，重置重试计数器
            if (result.isSuccess) {
                retryCounters.remove(recoveryKey)
                recoverySuccesses.incrementAndGet()
                logger.i("Recovery successful for: ${exception.message}")
            } else {
                recoveryFailures.incrementAndGet()
                logger.w("Recovery failed for: ${exception.message}, reason: ${result.errorMessage}")
            }
            
            return result
            
        } catch (e: Exception) {
            recoveryFailures.incrementAndGet()
            logger.e("Recovery attempt failed with exception", e)
            return RecoveryResult.failure("Recovery attempt threw exception: ${e.message}")
        }
    }
    
    /**
     * 执行恢复操作
     */
    private suspend fun performRecovery(
        exception: QuesticleException,
        context: ExceptionContext
    ): RecoveryResult {
        // 查找特定恢复策略
        val specificStrategy = findSpecificStrategy(exception)
        if (specificStrategy != null) {
            return try {
                @Suppress("UNCHECKED_CAST")
                (specificStrategy as RecoveryStrategy<QuesticleException>).recover(exception, context)
            } catch (e: Exception) {
                logger.e("Specific recovery strategy failed", e)
                RecoveryResult.failure("Strategy execution failed: ${e.message}")
            }
        }
        
        // 尝试全局恢复策略
        for (globalStrategy in globalStrategies) {
            try {
                val result = globalStrategy.recover(exception, context)
                if (result.isSuccess) {
                    return result
                }
            } catch (e: Exception) {
                logger.e("Global recovery strategy failed", e)
            }
        }
        
        // 默认恢复策略
        return performDefaultRecovery(exception, context)
    }
    
    /**
     * 默认恢复策略
     */
    private suspend fun performDefaultRecovery(
        exception: QuesticleException,
        context: ExceptionContext
    ): RecoveryResult {
        return when {
            exception.retryable -> {
                // 可重试异常：延迟后重试
                delay(config.retryDelayMs)
                RecoveryResult.retry("Default retry strategy")
            }
            
            exception.severity <= ErrorSeverity.MEDIUM -> {
                // 中低严重程度：尝试降级处理
                RecoveryResult.fallback("Using fallback for medium severity error")
            }
            
            else -> {
                // 高严重程度：无法恢复
                RecoveryResult.failure("High severity error cannot be recovered")
            }
        }
    }
    
    /**
     * 注册恢复策略
     */
    fun <T : QuesticleException> registerStrategy(
        exceptionClass: Class<T>,
        strategy: RecoveryStrategy<T>
    ) {
        recoveryStrategies[exceptionClass] = strategy
        logger.d("Recovery strategy registered for: ${exceptionClass.simpleName}")
    }
    
    /**
     * 注册全局恢复策略
     */
    fun registerGlobalStrategy(strategy: GlobalRecoveryStrategy) {
        globalStrategies.add(strategy)
        logger.d("Global recovery strategy registered")
    }
    
    /**
     * 查找特定恢复策略
     */
    private fun findSpecificStrategy(exception: QuesticleException): RecoveryStrategy<*>? {
        var clazz: Class<*> = exception::class.java
        
        while (clazz != Any::class.java) {
            recoveryStrategies[clazz]?.let { return it }
            clazz = clazz.superclass ?: break
        }
        
        return null
    }
    
    /**
     * 生成恢复键
     */
    private fun generateRecoveryKey(exception: QuesticleException, context: ExceptionContext): String {
        return "${exception::class.java.simpleName}_${exception.errorCode}_${context.hashCode()}"
    }
    
    /**
     * 更新恢复统计
     */
    private fun updateRecoveryStats(exception: QuesticleException, result: RecoveryResult) {
        val typeName = exception::class.java.simpleName
        val stats = recoveryByType.computeIfAbsent(typeName) { RecoveryStats() }
        
        stats.totalAttempts.incrementAndGet()
        if (result.isSuccess) {
            stats.successCount.incrementAndGet()
        } else {
            stats.failureCount.incrementAndGet()
        }
    }
    
    /**
     * 获取恢复统计
     */
    fun getRecoveryStatistics(): RecoveryStatistics {
        return RecoveryStatistics(
            totalAttempts = recoveryAttempts.get(),
            totalSuccesses = recoverySuccesses.get(),
            totalFailures = recoveryFailures.get(),
            successRate = if (recoveryAttempts.get() > 0) {
                recoverySuccesses.get().toDouble() / recoveryAttempts.get()
            } else 0.0,
            recoveryByType = recoveryByType.mapValues { (_, stats) ->
                TypeRecoveryStats(
                    attempts = stats.totalAttempts.get(),
                    successes = stats.successCount.get(),
                    failures = stats.failureCount.get(),
                    successRate = if (stats.totalAttempts.get() > 0) {
                        stats.successCount.get().toDouble() / stats.totalAttempts.get()
                    } else 0.0
                )
            }
        )
    }
    
    /**
     * 清除重试计数器
     */
    fun clearRetryCounters() {
        retryCounters.clear()
        logger.i("Retry counters cleared")
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: RecoveryConfig) {
        this.config = newConfig
        logger.i("Recovery config updated")
    }
}

/**
 * 恢复策略接口
 */
interface RecoveryStrategy<T : QuesticleException> {
    suspend fun recover(exception: T, context: ExceptionContext): RecoveryResult
}

/**
 * 全局恢复策略接口
 */
interface GlobalRecoveryStrategy {
    suspend fun recover(exception: QuesticleException, context: ExceptionContext): RecoveryResult
}

/**
 * 恢复结果
 */
sealed class RecoveryResult(
    val isSuccess: Boolean,
    val action: RecoveryAction,
    val message: String,
    val errorMessage: String? = null,
    val metadata: Map<String, Any?> = emptyMap()
) {
    
    class Success(
        action: RecoveryAction,
        message: String,
        metadata: Map<String, Any?> = emptyMap()
    ) : RecoveryResult(true, action, message, null, metadata)
    
    class Failure(
        errorMessage: String,
        metadata: Map<String, Any?> = emptyMap()
    ) : RecoveryResult(false, RecoveryAction.NONE, "Recovery failed", errorMessage, metadata)
    
    companion object {
        fun success(message: String, metadata: Map<String, Any?> = emptyMap()) = 
            Success(RecoveryAction.NONE, message, metadata)
        
        fun retry(message: String, metadata: Map<String, Any?> = emptyMap()) = 
            Success(RecoveryAction.RETRY, message, metadata)
        
        fun fallback(message: String, metadata: Map<String, Any?> = emptyMap()) = 
            Success(RecoveryAction.FALLBACK, message, metadata)
        
        fun refresh(message: String, metadata: Map<String, Any?> = emptyMap()) = 
            Success(RecoveryAction.REFRESH, message, metadata)
        
        fun failure(errorMessage: String, metadata: Map<String, Any?> = emptyMap()) = 
            Failure(errorMessage, metadata)
    }
}

/**
 * 恢复事件
 */
data class RecoveryEvent(
    val exception: QuesticleException,
    val context: ExceptionContext,
    val result: RecoveryResult,
    val retryCount: Int,
    val timestamp: Long
)

/**
 * 恢复统计
 */
data class RecoveryStatistics(
    val totalAttempts: Long,
    val totalSuccesses: Long,
    val totalFailures: Long,
    val successRate: Double,
    val recoveryByType: Map<String, TypeRecoveryStats>
)

data class TypeRecoveryStats(
    val attempts: Long,
    val successes: Long,
    val failures: Long,
    val successRate: Double
)

/**
 * 内部恢复统计
 */
private data class RecoveryStats(
    val totalAttempts: AtomicLong = AtomicLong(0),
    val successCount: AtomicLong = AtomicLong(0),
    val failureCount: AtomicLong = AtomicLong(0)
)

/**
 * 恢复配置
 */
data class RecoveryConfig(
    val maxRetryAttempts: Int = 3,
    val retryDelayMs: Long = 1000,
    val enableAutoRecovery: Boolean = true,
    val enableFallback: Boolean = true,
    val enableStatistics: Boolean = true
)

// 具体恢复策略实现
private class NetworkRecoveryStrategy : RecoveryStrategy<NetworkException> {
    override suspend fun recover(exception: NetworkException, context: ExceptionContext): RecoveryResult {
        return when (exception.errorCode) {
            "NETWORK_TIMEOUT" -> RecoveryResult.retry("Retrying network request")
            "NETWORK_UNREACHABLE" -> RecoveryResult.fallback("Using cached data")
            else -> RecoveryResult.failure("Unknown network error")
        }
    }
}

private class DatabaseRecoveryStrategy : RecoveryStrategy<DatabaseException> {
    override suspend fun recover(exception: DatabaseException, context: ExceptionContext): RecoveryResult {
        return RecoveryResult.retry("Retrying database operation")
    }
}

private class ValidationRecoveryStrategy : RecoveryStrategy<ValidationException> {
    override suspend fun recover(exception: ValidationException, context: ExceptionContext): RecoveryResult {
        return RecoveryResult.failure("Validation errors require user input correction")
    }
}

private class PermissionRecoveryStrategy : RecoveryStrategy<PermissionException> {
    override suspend fun recover(exception: PermissionException, context: ExceptionContext): RecoveryResult {
        return RecoveryResult.failure("Permission errors require user action")
    }
}

private class GameRecoveryStrategy : RecoveryStrategy<GameException> {
    override suspend fun recover(exception: GameException, context: ExceptionContext): RecoveryResult {
        return when (exception.severity) {
            ErrorSeverity.LOW -> RecoveryResult.success("Game state recovered")
            ErrorSeverity.MEDIUM -> RecoveryResult.fallback("Using safe game state")
            else -> RecoveryResult.failure("Critical game error cannot be recovered")
        }
    }
}
