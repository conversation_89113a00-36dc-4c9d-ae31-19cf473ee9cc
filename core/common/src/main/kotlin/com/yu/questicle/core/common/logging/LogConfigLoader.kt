package com.yu.questicle.core.common.logging

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.FileNotFoundException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志配置加载器
 * 负责从不同来源加载和保存日志配置
 */
@Singleton
class LogConfigLoader @Inject constructor(
    private val context: Context
) {
    
    private val json = Json {
        prettyPrint = true
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    private val configFileName = "log_config.json"
    private val loggerConfigFileName = "logger_configs.json"
    
    /**
     * 加载全局日志配置
     */
    suspend fun loadGlobalConfig(): LogConfig = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, configFileName)
            if (configFile.exists()) {
                val configJson = configFile.readText()
                val serializedConfig = json.decodeFromString<SerializableLogConfig>(configJson)
                serializedConfig.toLogConfig()
            } else {
                // 返回默认配置并保存
                val defaultConfig = getDefaultConfig()
                saveGlobalConfig(defaultConfig)
                defaultConfig
            }
        } catch (e: Exception) {
            // 配置文件损坏或读取失败，返回默认配置
            getDefaultConfig()
        }
    }
    
    /**
     * 保存全局日志配置
     */
    suspend fun saveGlobalConfig(config: LogConfig): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, configFileName)
            val serializedConfig = SerializableLogConfig.fromLogConfig(config)
            val configJson = json.encodeToString(serializedConfig)
            configFile.writeText(configJson)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 加载Logger配置
     */
    suspend fun loadLoggerConfigs(): Map<String, LoggerConfig> = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, loggerConfigFileName)
            if (configFile.exists()) {
                val configJson = configFile.readText()
                val serializedConfigs = json.decodeFromString<Map<String, SerializableLoggerConfig>>(configJson)
                serializedConfigs.mapValues { it.value.toLoggerConfig() }
            } else {
                // 返回默认Logger配置
                getDefaultLoggerConfigs()
            }
        } catch (e: Exception) {
            // 配置文件损坏或读取失败，返回默认配置
            getDefaultLoggerConfigs()
        }
    }
    
    /**
     * 保存Logger配置
     */
    suspend fun saveLoggerConfigs(configs: Map<String, LoggerConfig>): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, loggerConfigFileName)
            val serializedConfigs = configs.mapValues { SerializableLoggerConfig.fromLoggerConfig(it.value) }
            val configJson = json.encodeToString(serializedConfigs)
            configFile.writeText(configJson)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 从Assets加载配置
     */
    suspend fun loadConfigFromAssets(fileName: String): LogConfig? = withContext(Dispatchers.IO) {
        try {
            val configJson = context.assets.open(fileName).bufferedReader().use { it.readText() }
            val serializedConfig = json.decodeFromString<SerializableLogConfig>(configJson)
            serializedConfig.toLogConfig()
        } catch (e: FileNotFoundException) {
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 重置为默认配置
     */
    suspend fun resetToDefaults(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val defaultConfig = getDefaultConfig()
            val defaultLoggerConfigs = getDefaultLoggerConfigs()
            
            saveGlobalConfig(defaultConfig)
            saveLoggerConfigs(defaultLoggerConfigs)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 导出配置
     */
    suspend fun exportConfig(): Result<String> = withContext(Dispatchers.IO) {
        try {
            val globalConfig = loadGlobalConfig()
            val loggerConfigs = loadLoggerConfigs()
            
            val exportData = ConfigExportData(
                globalConfig = SerializableLogConfig.fromLogConfig(globalConfig),
                loggerConfigs = loggerConfigs.mapValues { SerializableLoggerConfig.fromLoggerConfig(it.value) },
                exportedAt = System.currentTimeMillis(),
                version = "1.0"
            )
            
            val exportJson = json.encodeToString(exportData)
            Result.success(exportJson)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 导入配置
     */
    suspend fun importConfig(configJson: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val exportData = json.decodeFromString<ConfigExportData>(configJson)
            
            val globalConfig = exportData.globalConfig.toLogConfig()
            val loggerConfigs = exportData.loggerConfigs.mapValues { it.value.toLoggerConfig() }
            
            saveGlobalConfig(globalConfig)
            saveLoggerConfigs(loggerConfigs)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取默认全局配置
     */
    private fun getDefaultConfig(): LogConfig {
        return LogConfig(
            globalLevel = LogLevel.INFO,
            enabled = true,
            enableAsync = true,
            bufferSize = 1024,
            flushInterval = 5000,
            maxFileSize = 10 * 1024 * 1024,
            maxFiles = 5,
            enableCompression = true,
            enableEncryption = false,
            enableSensitiveDataMasking = true,
            samplingRate = 1.0,
            outputDirectory = "logs",
            fileNamePattern = "questicle-%d{yyyy-MM-dd}.log",
            enableConsoleOutput = true,
            enableFileOutput = true,
            enableRemoteLogging = false,
            remoteEndpoint = null,
            enableMetrics = true,
            enableTracing = true
        )
    }
    
    /**
     * 获取默认Logger配置
     */
    private fun getDefaultLoggerConfigs(): Map<String, LoggerConfig> {
        return mapOf(
            "com.yu.questicle.core" to LoggerConfig(
                name = "com.yu.questicle.core",
                level = LogLevel.DEBUG,
                enabled = true,
                samplingRate = 1.0,
                enableAsync = true,
                enableSensitiveDataMasking = true
            ),
            "com.yu.questicle.feature.tetris" to LoggerConfig(
                name = "com.yu.questicle.feature.tetris",
                level = LogLevel.INFO,
                enabled = true,
                samplingRate = 1.0,
                enableAsync = true,
                enableSensitiveDataMasking = false
            ),
            "com.yu.questicle.feature.user" to LoggerConfig(
                name = "com.yu.questicle.feature.user",
                level = LogLevel.INFO,
                enabled = true,
                samplingRate = 1.0,
                enableAsync = true,
                enableSensitiveDataMasking = true
            ),
            "com.yu.questicle.feature.settings" to LoggerConfig(
                name = "com.yu.questicle.feature.settings",
                level = LogLevel.INFO,
                enabled = true,
                samplingRate = 1.0,
                enableAsync = true,
                enableSensitiveDataMasking = false
            )
        )
    }
}

/**
 * 可序列化的日志配置
 */
@Serializable
data class SerializableLogConfig(
    val globalLevel: String = "INFO",
    val enabled: Boolean = true,
    val enableAsync: Boolean = true,
    val bufferSize: Int = 1024,
    val flushInterval: Long = 5000,
    val maxFileSize: Long = 10485760,
    val maxFiles: Int = 5,
    val enableCompression: Boolean = true,
    val enableEncryption: Boolean = false,
    val enableSensitiveDataMasking: Boolean = true,
    val samplingRate: Double = 1.0,
    val outputDirectory: String = "logs",
    val fileNamePattern: String = "questicle-%d{yyyy-MM-dd}.log",
    val enableConsoleOutput: Boolean = true,
    val enableFileOutput: Boolean = true,
    val enableRemoteLogging: Boolean = false,
    val remoteEndpoint: String? = null,
    val enableMetrics: Boolean = true,
    val enableTracing: Boolean = true
) {
    fun toLogConfig(): LogConfig {
        return LogConfig(
            globalLevel = LogLevel.valueOf(globalLevel),
            enabled = enabled,
            enableAsync = enableAsync,
            bufferSize = bufferSize,
            flushInterval = flushInterval,
            maxFileSize = maxFileSize,
            maxFiles = maxFiles,
            enableCompression = enableCompression,
            enableEncryption = enableEncryption,
            enableSensitiveDataMasking = enableSensitiveDataMasking,
            samplingRate = samplingRate,
            outputDirectory = outputDirectory,
            fileNamePattern = fileNamePattern,
            enableConsoleOutput = enableConsoleOutput,
            enableFileOutput = enableFileOutput,
            enableRemoteLogging = enableRemoteLogging,
            remoteEndpoint = remoteEndpoint,
            enableMetrics = enableMetrics,
            enableTracing = enableTracing
        )
    }
    
    companion object {
        fun fromLogConfig(config: LogConfig): SerializableLogConfig {
            return SerializableLogConfig(
                globalLevel = config.globalLevel.name,
                enabled = config.enabled,
                enableAsync = config.enableAsync,
                bufferSize = config.bufferSize,
                flushInterval = config.flushInterval,
                maxFileSize = config.maxFileSize,
                maxFiles = config.maxFiles,
                enableCompression = config.enableCompression,
                enableEncryption = config.enableEncryption,
                enableSensitiveDataMasking = config.enableSensitiveDataMasking,
                samplingRate = config.samplingRate,
                outputDirectory = config.outputDirectory,
                fileNamePattern = config.fileNamePattern,
                enableConsoleOutput = config.enableConsoleOutput,
                enableFileOutput = config.enableFileOutput,
                enableRemoteLogging = config.enableRemoteLogging,
                remoteEndpoint = config.remoteEndpoint,
                enableMetrics = config.enableMetrics,
                enableTracing = config.enableTracing
            )
        }
    }
}

/**
 * 可序列化的Logger配置
 */
@Serializable
data class SerializableLoggerConfig(
    val name: String,
    val level: String = "INFO",
    val enabled: Boolean = true,
    val samplingRate: Double = 1.0,
    val enableAsync: Boolean = true,
    val enableSensitiveDataMasking: Boolean = true
) {
    fun toLoggerConfig(): LoggerConfig {
        return LoggerConfig(
            name = name,
            level = LogLevel.valueOf(level),
            enabled = enabled,
            samplingRate = samplingRate,
            enableAsync = enableAsync,
            enableSensitiveDataMasking = enableSensitiveDataMasking
        )
    }
    
    companion object {
        fun fromLoggerConfig(config: LoggerConfig): SerializableLoggerConfig {
            return SerializableLoggerConfig(
                name = config.name,
                level = config.level.name,
                enabled = config.enabled,
                samplingRate = config.samplingRate,
                enableAsync = config.enableAsync,
                enableSensitiveDataMasking = config.enableSensitiveDataMasking
            )
        }
    }
}

/**
 * 配置导出数据
 */
@Serializable
data class ConfigExportData(
    val globalConfig: SerializableLogConfig,
    val loggerConfigs: Map<String, SerializableLoggerConfig>,
    val exportedAt: Long,
    val version: String
)
