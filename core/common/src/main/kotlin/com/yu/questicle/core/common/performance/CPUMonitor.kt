package com.yu.questicle.core.common.performance

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.lang.management.ManagementFactory
import java.lang.management.ThreadMXBean
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * CPU监控器
 * 
 * 监控CPU使用率、线程状态、负载等
 * 提供应用和系统级别的CPU性能指标
 */
@Singleton
class CPUMonitor @Inject constructor() : BaseMonitor<CPUMetric>() {
    
    // 系统信息
    private val threadBean: ThreadMXBean = ManagementFactory.getThreadMXBean()
    private val availableProcessors = Runtime.getRuntime().availableProcessors()
    
    // CPU使用率计算
    private var lastCpuTime = AtomicLong(0L)
    private var lastIdleTime = AtomicLong(0L)
    private var lastMeasureTime = AtomicLong(0L)
    
    // 进程CPU时间
    private var lastProcessCpuTime = AtomicLong(0L)
    private var lastProcessMeasureTime = AtomicLong(0L)
    
    init {
        // 初始化基准值
        initializeCpuMeasurement()
    }
    
    override suspend fun collectMetric(): CPUMetric = withContext(Dispatchers.IO) {
        val timestamp = System.currentTimeMillis()
        
        // 系统CPU使用率
        val systemCpuUsage = calculateSystemCpuUsage()
        
        // 进程CPU使用率
        val processCpuUsage = calculateProcessCpuUsage()
        
        // 使用较高的值作为主要指标
        val cpuUsage = maxOf(systemCpuUsage, processCpuUsage)
        
        // 线程信息
        val threadCount = threadBean.threadCount
        val daemonThreadCount = threadBean.daemonThreadCount
        val peakThreadCount = threadBean.peakThreadCount
        
        // 负载平均值
        val loadAverage = getLoadAverage()
        
        // 判断是否高CPU使用
        val isHighUsage = cpuUsage > 80.0
        
        CPUMetric(
            timestamp = timestamp,
            cpuUsage = cpuUsage,
            threadCount = threadCount,
            loadAverage = loadAverage,
            isHighUsage = isHighUsage
        )
    }
    
    /**
     * 计算系统CPU使用率
     */
    private fun calculateSystemCpuUsage(): Double {
        return try {
            val statFile = File("/proc/stat")
            if (!statFile.exists()) return 0.0
            
            val firstLine = statFile.readLines().firstOrNull() ?: return 0.0
            val cpuTimes = firstLine.split("\\s+".toRegex())
                .drop(1) // 跳过 "cpu" 标签
                .take(7) // 取前7个值
                .mapNotNull { it.toLongOrNull() }
            
            if (cpuTimes.size < 4) return 0.0
            
            val idleTime = cpuTimes[3] // idle time
            val totalTime = cpuTimes.sum()
            val currentTime = System.currentTimeMillis()
            
            val lastTotal = lastCpuTime.get()
            val lastIdle = lastIdleTime.get()
            val lastTime = lastMeasureTime.get()
            
            if (lastTotal > 0 && currentTime > lastTime) {
                val totalDelta = totalTime - lastTotal
                val idleDelta = idleTime - lastIdle
                
                val usage = if (totalDelta > 0) {
                    (1.0 - idleDelta.toDouble() / totalDelta) * 100.0
                } else 0.0
                
                // 更新上次的值
                lastCpuTime.set(totalTime)
                lastIdleTime.set(idleTime)
                lastMeasureTime.set(currentTime)
                
                usage.coerceIn(0.0, 100.0)
            } else {
                // 首次测量，更新基准值
                lastCpuTime.set(totalTime)
                lastIdleTime.set(idleTime)
                lastMeasureTime.set(currentTime)
                0.0
            }
        } catch (e: Exception) {
            logger.e("Error calculating system CPU usage", e)
            0.0
        }
    }
    
    /**
     * 计算进程CPU使用率
     */
    private fun calculateProcessCpuUsage(): Double {
        return try {
            // 获取进程CPU时间 (纳秒)
            val processCpuTime = getProcessCpuTime()
            val currentTime = System.currentTimeMillis()
            
            val lastCpuTime = lastProcessCpuTime.get()
            val lastTime = lastProcessMeasureTime.get()
            
            if (lastCpuTime > 0 && currentTime > lastTime) {
                val cpuTimeDelta = processCpuTime - lastCpuTime
                val realTimeDelta = (currentTime - lastTime) * 1_000_000L // 转换为纳秒
                
                val usage = if (realTimeDelta > 0) {
                    (cpuTimeDelta.toDouble() / realTimeDelta) * 100.0 * availableProcessors
                } else 0.0
                
                // 更新上次的值
                lastProcessCpuTime.set(processCpuTime)
                lastProcessMeasureTime.set(currentTime)
                
                usage.coerceIn(0.0, 100.0)
            } else {
                // 首次测量，更新基准值
                lastProcessCpuTime.set(processCpuTime)
                lastProcessMeasureTime.set(currentTime)
                0.0
            }
        } catch (e: Exception) {
            logger.e("Error calculating process CPU usage", e)
            0.0
        }
    }
    
    /**
     * 获取进程CPU时间
     */
    private fun getProcessCpuTime(): Long {
        return try {
            val pid = android.os.Process.myPid()
            val statFile = File("/proc/$pid/stat")
            
            if (statFile.exists()) {
                val statContent = statFile.readText()
                val fields = statContent.split(" ")
                
                if (fields.size >= 15) {
                    val utime = fields[13].toLongOrNull() ?: 0L // 用户态时间
                    val stime = fields[14].toLongOrNull() ?: 0L // 内核态时间
                    
                    // 转换为纳秒 (假设时钟频率为100Hz)
                    (utime + stime) * 10_000_000L
                } else 0L
            } else 0L
        } catch (e: Exception) {
            logger.e("Error getting process CPU time", e)
            0L
        }
    }
    
    /**
     * 获取负载平均值
     */
    private fun getLoadAverage(): Double {
        return try {
            val loadAvgFile = File("/proc/loadavg")
            if (loadAvgFile.exists()) {
                val content = loadAvgFile.readText().trim()
                val loadValues = content.split(" ")
                if (loadValues.isNotEmpty()) {
                    loadValues[0].toDoubleOrNull() ?: 0.0 // 1分钟负载平均值
                } else 0.0
            } else 0.0
        } catch (e: Exception) {
            logger.e("Error getting load average", e)
            0.0
        }
    }
    
    /**
     * 初始化CPU测量
     */
    private fun initializeCpuMeasurement() {
        try {
            // 初始化系统CPU基准值
            calculateSystemCpuUsage()
            
            // 初始化进程CPU基准值
            calculateProcessCpuUsage()
        } catch (e: Exception) {
            logger.e("Error initializing CPU measurement", e)
        }
    }
    
    override suspend fun checkThresholds(metric: CPUMetric) {
        if (!config.enableThresholdChecking) return
        
        // 检查CPU使用率
        if (metric.cpuUsage > 80.0) {
            logger.w("High CPU usage detected: ${metric.cpuUsage.toInt()}%")
        }
        
        // 检查线程数
        if (metric.threadCount > 100) {
            logger.w("High thread count detected: ${metric.threadCount}")
        }
        
        // 检查负载平均值
        if (metric.loadAverage > availableProcessors * 2) {
            logger.w("High load average detected: ${metric.loadAverage}")
        }
    }
    
    /**
     * 获取详细CPU统计
     */
    fun getDetailedCpuStats(): DetailedCpuStats {
        val currentMetric = getCurrentMetric()
        
        // 获取线程详细信息
        val threadInfo = threadBean.allThreadIds.map { threadId ->
            val info = threadBean.getThreadInfo(threadId)
            ThreadInfo(
                id = threadId,
                name = info?.threadName ?: "Unknown",
                state = info?.threadState?.name ?: "Unknown",
                cpuTime = if (threadBean.isThreadCpuTimeSupported) {
                    threadBean.getThreadCpuTime(threadId)
                } else 0L
            )
        }
        
        return DetailedCpuStats(
            cpuUsage = currentMetric?.cpuUsage ?: 0.0,
            threadCount = currentMetric?.threadCount ?: 0,
            daemonThreadCount = threadBean.daemonThreadCount,
            peakThreadCount = threadBean.peakThreadCount,
            loadAverage = currentMetric?.loadAverage ?: 0.0,
            availableProcessors = availableProcessors,
            threadInfo = threadInfo,
            isHighUsage = currentMetric?.isHighUsage ?: false
        )
    }
    
    /**
     * 强制触发CPU测量
     */
    suspend fun triggerMeasurement(): CPUMetric {
        return collectMetric()
    }
}

/**
 * 详细CPU统计
 */
data class DetailedCpuStats(
    val cpuUsage: Double,
    val threadCount: Int,
    val daemonThreadCount: Int,
    val peakThreadCount: Int,
    val loadAverage: Double,
    val availableProcessors: Int,
    val threadInfo: List<ThreadInfo>,
    val isHighUsage: Boolean
)

/**
 * 线程信息
 */
data class ThreadInfo(
    val id: Long,
    val name: String,
    val state: String,
    val cpuTime: Long
)

/**
 * CPU监控配置
 */
data class CpuMonitorConfig(
    val cpuThreshold: Double = 80.0,
    val threadCountThreshold: Int = 100,
    val loadAverageThreshold: Double = 4.0
) : MonitorConfig(
    samplingIntervalMs = 10000L,
    enableThresholdChecking = true,
    retainHistoryCount = 30
)
