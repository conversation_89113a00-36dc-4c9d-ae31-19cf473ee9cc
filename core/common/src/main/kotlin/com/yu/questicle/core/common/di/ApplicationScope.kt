package com.yu.questicle.core.common.di

import javax.inject.Qualifier

/**
 * Qualifier for application-level [kotlinx.coroutines.CoroutineScope].
 * The scope lives as long as the process and **must be cancelled** when the
 * application is terminated.  Use it for long-lived background work that should
 * survive configuration changes but not outlive the process.
 */
@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class ApplicationScope 