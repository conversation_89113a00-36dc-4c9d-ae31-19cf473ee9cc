package com.yu.questicle.core.common.exception

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 全局异常处理器
 *
 * 负责统一处理应用中的所有异常，提供：
 * - 异常收集和聚合
 * - 异常分析和分类
 * - 异常报告和监控
 * - 异常恢复策略
 * - 用户友好的错误反馈
 */
object QuesticleExceptionHandler {
    
    private val logger: QLogger = QLoggerFactory.getLogger(QuesticleExceptionHandler::class)
    
    // 异常统计
    private val exceptionCount = AtomicLong(0)
    private val exceptionsByType = ConcurrentHashMap<String, AtomicLong>()
    private val exceptionsBySeverity = ConcurrentHashMap<ErrorSeverity, AtomicLong>()
    
    // 异常事件流
    private val _exceptionEvents = MutableSharedFlow<ExceptionEvent>()
    val exceptionEvents: SharedFlow<ExceptionEvent> = _exceptionEvents.asSharedFlow()
    
    // 异常处理器注册表
    private val handlers = ConcurrentHashMap<Class<out QuesticleException>, ExceptionHandler<*>>()
    private val globalHandlers = mutableListOf<QuesticleGlobalExceptionHandler>()
    
    // 配置
    private var config = ExceptionHandlerConfig()
    
    /**
     * 处理异常
     */
    fun handleException(
        exception: Throwable,
        context: ExceptionContext = ExceptionContext.EMPTY
    ): ExceptionResult {
        val questicleException = exception.toQuesticleException()
        val enhancedException = enhanceException(questicleException, context)
        
        // 记录异常
        recordException(enhancedException)
        
        // 发送异常事件
        val event = ExceptionEvent(
            exception = enhancedException,
            context = context,
            timestamp = System.currentTimeMillis()
        )
        
        try {
            _exceptionEvents.tryEmit(event)
        } catch (e: Exception) {
            logger.e("Failed to emit exception event", e)
        }
        
        // 执行异常处理
        return processException(enhancedException, context)
    }
    
    /**
     * 注册异常处理器
     */
    fun <T : QuesticleException> registerHandler(
        exceptionClass: Class<T>,
        handler: ExceptionHandler<T>
    ) {
        handlers[exceptionClass] = handler
    }
    
    /**
     * 注册全局异常处理器
     */
    fun registerGlobalHandler(handler: QuesticleGlobalExceptionHandler) {
        globalHandlers.add(handler)
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: ExceptionHandlerConfig) {
        this.config = newConfig
    }
    
    /**
     * 获取异常统计
     */
    fun getStatistics(): ExceptionStatistics {
        return ExceptionStatistics(
            totalExceptions = exceptionCount.get(),
            exceptionsByType = exceptionsByType.mapValues { it.value.get() },
            exceptionsBySeverity = exceptionsBySeverity.mapValues { it.value.get() }
        )
    }
    
    /**
     * 清除统计数据
     */
    fun clearStatistics() {
        exceptionCount.set(0)
        exceptionsByType.clear()
        exceptionsBySeverity.clear()
    }
    
    /**
     * 增强异常信息
     */
    private fun enhanceException(
        exception: QuesticleException,
        context: ExceptionContext
    ): QuesticleException {
        val enhancedContext = exception.context + context.toMap()
        return exception.withContext(enhancedContext)
    }
    
    /**
     * 记录异常统计
     */
    private fun recordException(exception: QuesticleException) {
        exceptionCount.incrementAndGet()
        
        // 按类型统计
        val typeName = exception::class.java.simpleName
        exceptionsByType.computeIfAbsent(typeName) { AtomicLong(0) }.incrementAndGet()
        
        // 按严重程度统计
        exceptionsBySeverity.computeIfAbsent(exception.severity) { AtomicLong(0) }.incrementAndGet()
        
        // 记录日志
        when (exception.severity) {
            ErrorSeverity.LOW -> logger.w("Low severity exception: ${exception.message}", exception)
            ErrorSeverity.MEDIUM -> logger.e("Medium severity exception: ${exception.message}", exception)
            ErrorSeverity.HIGH -> logger.e("High severity exception: ${exception.message}", exception)
            ErrorSeverity.CRITICAL -> logger.f("Critical exception: ${exception.message}", exception)
        }
    }
    
    /**
     * 处理异常
     */
    private fun processException(
        exception: QuesticleException,
        context: ExceptionContext
    ): ExceptionResult {
        // 查找特定处理器
        val specificHandler = findSpecificHandler(exception)
        if (specificHandler != null) {
            return try {
                @Suppress("UNCHECKED_CAST")
                (specificHandler as ExceptionHandler<QuesticleException>).handle(exception, context)
            } catch (e: Exception) {
                logger.e("Exception handler failed", e)
                createDefaultResult(exception)
            }
        }
        
        // 执行全局处理器
        for (globalHandler in globalHandlers) {
            try {
                val result = globalHandler.handleException(exception, context)
                if (result.handled) {
                    return result
                }
            } catch (e: Exception) {
                logger.e("Global exception handler failed", e)
            }
        }
        
        // 默认处理
        return createDefaultResult(exception)
    }
    
    /**
     * 查找特定异常处理器
     */
    private fun findSpecificHandler(exception: QuesticleException): ExceptionHandler<*>? {
        var clazz: Class<*> = exception::class.java
        
        while (clazz != Any::class.java) {
            handlers[clazz]?.let { return it }
            clazz = clazz.superclass ?: break
        }
        
        return null
    }
    
    /**
     * 创建默认处理结果
     */
    private fun createDefaultResult(exception: QuesticleException): ExceptionResult {
        return ExceptionResult(
            handled = true,
            userMessage = exception.getUserFriendlyMessage(),
            shouldRetry = exception.retryable,
            shouldReport = exception.severity >= ErrorSeverity.HIGH,
            recoveryAction = if (exception.retryable) RecoveryAction.RETRY else RecoveryAction.NONE
        )
    }
}

/**
 * 异常处理器接口
 */
interface ExceptionHandler<T : QuesticleException> {
    
    /**
     * 处理异常
     */
    fun handle(exception: T, context: ExceptionContext): ExceptionResult
}

/**
 * 全局异常处理器接口
 */
interface QuesticleGlobalExceptionHandler {

    /**
     * 处理异常
     */
    fun handleException(exception: QuesticleException, context: ExceptionContext): ExceptionResult
}

/**
 * 异常上下文
 */
data class ExceptionContext(
    val userId: String? = null,
    val sessionId: String? = null,
    val feature: String? = null,
    val action: String? = null,
    val screen: String? = null,
    val deviceInfo: Map<String, Any?> = emptyMap(),
    val custom: Map<String, Any?> = emptyMap()
) {
    
    fun toMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        userId?.let { map["userId"] = it }
        sessionId?.let { map["sessionId"] = it }
        feature?.let { map["feature"] = it }
        action?.let { map["action"] = it }
        screen?.let { map["screen"] = it }
        map.putAll(deviceInfo)
        map.putAll(custom)
        return map
    }
    
    companion object {
        val EMPTY = ExceptionContext()
    }
}

/**
 * 异常处理结果
 */
data class ExceptionResult(
    val handled: Boolean = true,
    val userMessage: String? = null,
    val shouldRetry: Boolean = false,
    val shouldReport: Boolean = true,
    val recoveryAction: RecoveryAction = RecoveryAction.NONE,
    val metadata: Map<String, Any?> = emptyMap()
)

/**
 * 恢复动作
 */
enum class RecoveryAction {
    NONE,           // 无恢复动作
    RETRY,          // 重试操作
    FALLBACK,       // 使用备用方案
    REFRESH,        // 刷新数据
    NAVIGATE_BACK,  // 返回上一页
    RESTART_APP     // 重启应用
}

/**
 * 异常事件
 */
data class ExceptionEvent(
    val exception: QuesticleException,
    val context: ExceptionContext,
    val timestamp: Long
)

/**
 * 异常统计
 */
data class ExceptionStatistics(
    val totalExceptions: Long,
    val exceptionsByType: Map<String, Long>,
    val exceptionsBySeverity: Map<ErrorSeverity, Long>
)

/**
 * 异常处理器配置
 */
data class ExceptionHandlerConfig(
    val enableReporting: Boolean = true,
    val enableUserFeedback: Boolean = true,
    val enableAutoRetry: Boolean = true,
    val maxRetryAttempts: Int = 3,
    val retryDelayMs: Long = 1000,
    val enableCrashReporting: Boolean = true,
    val enableAnalytics: Boolean = true
)

/**
 * 扩展函数：将普通异常转换为QuesticleException
 */
fun Throwable.toQuesticleException(): QuesticleException {
    return when (this) {
        is QuesticleException -> this
        is IllegalArgumentException -> ValidationException(
            message = message ?: "Invalid argument",
            cause = this
        )
        is IllegalStateException -> BusinessException(
            message = message ?: "Invalid state",
            cause = this
        )
        is SecurityException -> PermissionException(
            message = message ?: "Permission denied",
            cause = this
        )
        is java.net.UnknownHostException -> NetworkException(
            message = "Network connection failed",
            cause = this,
            errorCode = "NETWORK_UNREACHABLE"
        )
        is java.net.SocketTimeoutException -> NetworkException(
            message = "Network request timeout",
            cause = this,
            errorCode = "NETWORK_TIMEOUT"
        )
        else -> UnknownException(
            message = message ?: "Unknown error",
            cause = this
        )
    }
}
