package com.yu.questicle.core.common.logging

/**
 * 高性能结构化日志接口
 * 
 * 特性:
 * - 异步处理，零阻塞
 * - 结构化日志格式
 * - 上下文自动注入
 * - 性能监控集成
 * - 敏感信息自动脱敏
 */
interface QLogger {
    
    /**
     * 记录详细信息 (VERBOSE)
     * 用于开发调试，生产环境通常关闭
     */
    fun v(message: String, vararg args: Any?)
    fun v(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 记录调试信息 (DEBUG)
     * 用于开发环境调试
     */
    fun d(message: String, vararg args: Any?)
    fun d(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 记录一般信息 (INFO)
     * 重要业务流程和系统状态
     */
    fun i(message: String, vararg args: Any?)
    fun i(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 记录警告信息 (WARN)
     * 潜在问题和性能警告
     */
    fun w(message: String, vararg args: Any?)
    fun w(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 记录错误信息 (ERROR)
     * 需要关注的错误和异常
     */
    fun e(message: String, vararg args: Any?)
    fun e(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 记录致命错误 (FATAL)
     * 系统崩溃和严重错误
     */
    fun f(message: String, vararg args: Any?)
    fun f(throwable: Throwable, message: String, vararg args: Any?)
    
    /**
     * 添加上下文信息
     * 返回新的Logger实例，不影响原实例
     */
    fun withContext(key: String, value: Any?): QLogger
    fun withContext(context: Map<String, Any?>): QLogger
    fun withContext(context: LogContext): QLogger
    
    /**
     * 添加标签
     * 用于日志分类和过滤
     */
    fun withTag(tag: String): QLogger
    fun withTags(vararg tags: String): QLogger
    fun withTags(tags: Collection<String>): QLogger
    
    /**
     * 设置用户ID
     * 自动添加到上下文中
     */
    fun withUserId(userId: String): QLogger
    
    /**
     * 设置会话ID
     * 自动添加到上下文中
     */
    fun withSessionId(sessionId: String): QLogger
    
    /**
     * 测量执行时间
     * 自动记录操作耗时
     */
    fun <T> measureTime(operation: String, block: () -> T): T
    
    /**
     * 测量异步操作时间
     * 自动记录异步操作耗时
     */
    suspend fun <T> measureTimeAsync(operation: String, block: suspend () -> T): T
    
    /**
     * 检查日志级别是否启用
     * 用于避免不必要的字符串构建
     */
    fun isVerboseEnabled(): Boolean
    fun isDebugEnabled(): Boolean
    fun isInfoEnabled(): Boolean
    fun isWarnEnabled(): Boolean
    fun isErrorEnabled(): Boolean
    fun isFatalEnabled(): Boolean
    
    /**
     * 获取Logger名称
     */
    val name: String
    
    /**
     * 获取当前配置
     */
    val config: LoggerConfig
}

/**
 * 日志级别枚举
 */
enum class LogLevel(val priority: Int, val tag: String, val displayName: String) {
    VERBOSE(2, "V", "Verbose"),
    DEBUG(3, "D", "Debug"),
    INFO(4, "I", "Info"),
    WARN(5, "W", "Warn"),
    ERROR(6, "E", "Error"),
    FATAL(7, "F", "Fatal");
    
    companion object {
        fun fromPriority(priority: Int): LogLevel? {
            return values().find { it.priority == priority }
        }
        
        fun fromTag(tag: String): LogLevel? {
            return values().find { it.tag.equals(tag, ignoreCase = true) }
        }
    }
}

/**
 * 日志上下文
 * 包含业务相关的上下文信息
 */
data class LogContext(
    val userId: String? = null,
    val sessionId: String? = null,
    val requestId: String? = null,
    val traceId: String? = null,
    val spanId: String? = null,
    val gameType: String? = null,
    val gameId: String? = null,
    val feature: String? = null,
    val action: String? = null,
    val custom: Map<String, Any?> = emptyMap()
) {
    
    fun toMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        userId?.let { map["userId"] = it }
        sessionId?.let { map["sessionId"] = it }
        requestId?.let { map["requestId"] = it }
        traceId?.let { map["traceId"] = it }
        spanId?.let { map["spanId"] = it }
        gameType?.let { map["gameType"] = it }
        gameId?.let { map["gameId"] = it }
        feature?.let { map["feature"] = it }
        action?.let { map["action"] = it }
        map.putAll(custom)
        return map
    }
    
    fun merge(other: LogContext): LogContext {
        return copy(
            userId = other.userId ?: userId,
            sessionId = other.sessionId ?: sessionId,
            requestId = other.requestId ?: requestId,
            traceId = other.traceId ?: traceId,
            spanId = other.spanId ?: spanId,
            gameType = other.gameType ?: gameType,
            gameId = other.gameId ?: gameId,
            feature = other.feature ?: feature,
            action = other.action ?: action,
            custom = custom + other.custom
        )
    }
    
    companion object {
        val EMPTY = LogContext()
        
        fun builder(): LogContextBuilder = LogContextBuilder()
    }
}

/**
 * 日志上下文构建器
 */
class LogContextBuilder {
    private var userId: String? = null
    private var sessionId: String? = null
    private var requestId: String? = null
    private var traceId: String? = null
    private var spanId: String? = null
    private var gameType: String? = null
    private var gameId: String? = null
    private var feature: String? = null
    private var action: String? = null
    private val custom = mutableMapOf<String, Any?>()
    
    fun userId(userId: String?) = apply { this.userId = userId }
    fun sessionId(sessionId: String?) = apply { this.sessionId = sessionId }
    fun requestId(requestId: String?) = apply { this.requestId = requestId }
    fun traceId(traceId: String?) = apply { this.traceId = traceId }
    fun spanId(spanId: String?) = apply { this.spanId = spanId }
    fun gameType(gameType: String?) = apply { this.gameType = gameType }
    fun gameId(gameId: String?) = apply { this.gameId = gameId }
    fun feature(feature: String?) = apply { this.feature = feature }
    fun action(action: String?) = apply { this.action = action }
    fun custom(key: String, value: Any?) = apply { this.custom[key] = value }
    fun custom(map: Map<String, Any?>) = apply { this.custom.putAll(map) }
    
    fun build(): LogContext {
        return LogContext(
            userId = userId,
            sessionId = sessionId,
            requestId = requestId,
            traceId = traceId,
            spanId = spanId,
            gameType = gameType,
            gameId = gameId,
            feature = feature,
            action = action,
            custom = custom.toMap()
        )
    }
}

/**
 * Logger配置
 */
data class LoggerConfig(
    val name: String,
    val level: LogLevel = LogLevel.INFO,
    val enabled: Boolean = true,
    val tags: Set<String> = emptySet(),
    val samplingRate: Double = 1.0,
    val enableAsync: Boolean = true,
    val enableSensitiveDataMasking: Boolean = true,
    val enableStackTrace: Boolean = true,
    val enableMetrics: Boolean = true
) {
    
    fun isLevelEnabled(level: LogLevel): Boolean {
        return enabled && level.priority >= this.level.priority
    }
    
    fun shouldSample(): Boolean {
        return samplingRate >= 1.0 || Math.random() < samplingRate
    }
}

/**
 * 日志事件
 * 内部使用的日志数据结构
 */
data class LogEvent(
    val timestamp: Long = System.currentTimeMillis(),
    val level: LogLevel,
    val loggerName: String,
    val thread: String = Thread.currentThread().name,
    val message: String,
    val throwable: Throwable? = null,
    val context: LogContext = LogContext.EMPTY,
    val tags: Set<String> = emptySet(),
    val duration: Long? = null,
    val metadata: Map<String, Any?> = emptyMap()
) {
    
    fun toMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        map["timestamp"] = timestamp
        map["level"] = level.displayName
        map["logger"] = loggerName
        map["thread"] = thread
        map["message"] = message
        
        if (context != LogContext.EMPTY) {
            map["context"] = context.toMap()
        }
        
        if (tags.isNotEmpty()) {
            map["tags"] = tags.toList()
        }
        
        duration?.let { map["duration"] = it }
        
        throwable?.let { t ->
            map["exception"] = mapOf(
                "type" to t::class.java.simpleName,
                "message" to t.message,
                "stackTrace" to t.stackTraceToString()
            )
        }
        
        if (metadata.isNotEmpty()) {
            map["metadata"] = metadata
        }
        
        return map
    }
}
