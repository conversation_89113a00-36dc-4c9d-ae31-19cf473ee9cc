package com.yu.questicle.core.common.exception

import com.yu.questicle.core.common.logging.QLogger
import com.yu.questicle.core.common.logging.QLoggerFactory
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 异常监控服务
 * 
 * 负责：
 * - 实时异常监控
 * - 异常趋势分析
 * - 异常模式识别
 * - 异常告警
 * - 监控报告生成
 */
@Singleton
class ExceptionMonitoringService @Inject constructor(
    private val exceptionHandler: QuesticleExceptionHandler,
    private val recoveryManager: ExceptionRecoveryManager,
    private val crashReportingManager: CrashReportingManager
) {
    
    private val logger: QLogger = QLoggerFactory.getLogger(ExceptionMonitoringService::class)
    
    // 监控统计
    private val monitoringStats = MonitoringStats()
    
    // 异常模式检测
    private val patternDetector = ExceptionPatternDetector()
    
    // 告警管理器
    private val alertManager = AlertManager()
    
    // 监控事件流
    private val _monitoringEvents = MutableSharedFlow<MonitoringEvent>()
    val monitoringEvents: SharedFlow<MonitoringEvent> = _monitoringEvents.asSharedFlow()
    
    // 配置
    private var config = MonitoringConfig()
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    init {
        startMonitoring()
    }
    
    /**
     * 开始监控
     */
    private fun startMonitoring() {
        // 监听异常事件
        scope.launch {
            exceptionHandler.exceptionEvents.collect { event ->
                processExceptionEvent(event)
            }
        }
        
        // 监听恢复事件
        scope.launch {
            recoveryManager.recoveryEvents.collect { event ->
                processRecoveryEvent(event)
            }
        }
        
        // 监听崩溃事件
        scope.launch {
            crashReportingManager.crashEvents.collect { event ->
                processCrashEvent(event)
            }
        }
        
        // 定期生成监控报告
        scope.launch {
            while (isActive) {
                delay(config.reportIntervalMs)
                generateMonitoringReport()
            }
        }
        
        // 定期检测异常模式
        scope.launch {
            while (isActive) {
                delay(config.patternDetectionIntervalMs)
                detectExceptionPatterns()
            }
        }
        
        logger.i("Exception monitoring service started")
    }
    
    /**
     * 处理异常事件
     */
    private suspend fun processExceptionEvent(event: ExceptionEvent) {
        try {
            // 更新统计
            monitoringStats.recordException(event.exception)
            
            // 检测异常模式
            patternDetector.addException(event.exception, event.timestamp)
            
            // 检查是否需要告警
            checkForAlerts(event.exception)
            
            // 发送监控事件
            val monitoringEvent = MonitoringEvent.ExceptionDetected(
                exception = event.exception,
                context = event.context,
                timestamp = event.timestamp,
                severity = event.exception.severity
            )
            
            _monitoringEvents.emit(monitoringEvent)
            
        } catch (e: Exception) {
            logger.e("Error processing exception event", e)
        }
    }
    
    /**
     * 处理恢复事件
     */
    private suspend fun processRecoveryEvent(event: RecoveryEvent) {
        try {
            // 更新恢复统计
            monitoringStats.recordRecovery(event.exception, event.result.isSuccess)
            
            // 发送监控事件
            val monitoringEvent = if (event.result.isSuccess) {
                MonitoringEvent.RecoverySuccessful(
                    exception = event.exception,
                    recoveryAction = event.result.action,
                    retryCount = event.retryCount,
                    timestamp = event.timestamp
                )
            } else {
                MonitoringEvent.RecoveryFailed(
                    exception = event.exception,
                    errorMessage = event.result.errorMessage ?: "Unknown error",
                    retryCount = event.retryCount,
                    timestamp = event.timestamp
                )
            }
            
            _monitoringEvents.emit(monitoringEvent)
            
        } catch (e: Exception) {
            logger.e("Error processing recovery event", e)
        }
    }
    
    /**
     * 处理崩溃事件
     */
    private suspend fun processCrashEvent(event: CrashEvent) {
        try {
            // 更新崩溃统计
            monitoringStats.recordCrash(event.exception, event.isFatal)
            
            // 立即触发高优先级告警
            if (event.isFatal) {
                alertManager.triggerCriticalAlert(
                    "Fatal crash detected: ${event.exception.message}",
                    event.exception
                )
            }
            
            // 发送监控事件
            val monitoringEvent = MonitoringEvent.CrashDetected(
                exception = event.exception,
                isFatal = event.isFatal,
                thread = event.thread,
                timestamp = event.timestamp
            )
            
            _monitoringEvents.emit(monitoringEvent)
            
        } catch (e: Exception) {
            logger.e("Error processing crash event", e)
        }
    }
    
    /**
     * 检查告警条件
     */
    private suspend fun checkForAlerts(exception: QuesticleException) {
        // 检查异常频率
        val recentCount = monitoringStats.getRecentExceptionCount(
            exception::class.java.simpleName,
            config.alertTimeWindowMs
        )
        
        if (recentCount >= config.alertThreshold) {
            alertManager.triggerAlert(
                "High frequency exception detected: ${exception::class.java.simpleName}",
                exception,
                AlertLevel.HIGH
            )
        }
        
        // 检查严重程度
        if (exception.severity >= ErrorSeverity.HIGH) {
            alertManager.triggerAlert(
                "High severity exception: ${exception.message}",
                exception,
                AlertLevel.CRITICAL
            )
        }
    }
    
    /**
     * 检测异常模式
     */
    private suspend fun detectExceptionPatterns() {
        try {
            val patterns = patternDetector.detectPatterns()
            
            for (pattern in patterns) {
                logger.w("Exception pattern detected: $pattern")
                
                val monitoringEvent = MonitoringEvent.PatternDetected(
                    pattern = pattern,
                    timestamp = System.currentTimeMillis()
                )
                
                _monitoringEvents.emit(monitoringEvent)
                
                // 如果模式严重，触发告警
                if (pattern.severity >= PatternSeverity.HIGH) {
                    alertManager.triggerAlert(
                        "Critical exception pattern detected: ${pattern.description}",
                        null,
                        AlertLevel.CRITICAL
                    )
                }
            }
            
        } catch (e: Exception) {
            logger.e("Error detecting exception patterns", e)
        }
    }
    
    /**
     * 生成监控报告
     */
    private suspend fun generateMonitoringReport() {
        try {
            val report = MonitoringReport(
                timestamp = System.currentTimeMillis(),
                timeWindowMs = config.reportIntervalMs,
                exceptionStats = monitoringStats.getExceptionStatistics(),
                recoveryStats = monitoringStats.getRecoveryStatistics(),
                crashStats = monitoringStats.getCrashStatistics(),
                patterns = patternDetector.getRecentPatterns(),
                alerts = alertManager.getRecentAlerts()
            )
            
            // 发送监控事件
            val monitoringEvent = MonitoringEvent.ReportGenerated(
                report = report,
                timestamp = report.timestamp
            )
            
            _monitoringEvents.emit(monitoringEvent)
            
            // 记录报告摘要
            logger.i("Monitoring report generated: ${report.getSummary()}")
            
        } catch (e: Exception) {
            logger.e("Error generating monitoring report", e)
        }
    }
    
    /**
     * 获取实时监控状态
     */
    fun getMonitoringStatus(): MonitoringStatus {
        return MonitoringStatus(
            isActive = scope.isActive,
            totalExceptions = monitoringStats.getTotalExceptions(),
            totalRecoveries = monitoringStats.getTotalRecoveries(),
            totalCrashes = monitoringStats.getTotalCrashes(),
            activeAlerts = alertManager.getActiveAlerts().size,
            lastReportTime = monitoringStats.getLastReportTime(),
            uptime = System.currentTimeMillis() - monitoringStats.getStartTime()
        )
    }
    
    /**
     * 获取异常趋势
     */
    fun getExceptionTrends(timeWindowMs: Long = 3600000): ExceptionTrends {
        return monitoringStats.getExceptionTrends(timeWindowMs)
    }
    
    /**
     * 设置配置
     */
    fun setConfig(newConfig: MonitoringConfig) {
        this.config = newConfig
        alertManager.setConfig(newConfig.alertConfig)
        patternDetector.setConfig(newConfig.patternConfig)
        logger.i("Monitoring config updated")
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring() {
        scope.cancel()
        logger.i("Exception monitoring service stopped")
    }
}

/**
 * 监控事件
 */
sealed class MonitoringEvent(val timestamp: Long) {
    
    data class ExceptionDetected(
        val exception: QuesticleException,
        val context: ExceptionContext,
        val severity: ErrorSeverity,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
    
    data class RecoverySuccessful(
        val exception: QuesticleException,
        val recoveryAction: RecoveryAction,
        val retryCount: Int,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
    
    data class RecoveryFailed(
        val exception: QuesticleException,
        val errorMessage: String,
        val retryCount: Int,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
    
    data class CrashDetected(
        val exception: QuesticleException,
        val isFatal: Boolean,
        val thread: String,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
    
    data class PatternDetected(
        val pattern: ExceptionPattern,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
    
    data class ReportGenerated(
        val report: MonitoringReport,
        private val eventTimestamp: Long = System.currentTimeMillis()
    ) : MonitoringEvent(eventTimestamp)
}

/**
 * 监控状态
 */
data class MonitoringStatus(
    val isActive: Boolean,
    val totalExceptions: Long,
    val totalRecoveries: Long,
    val totalCrashes: Long,
    val activeAlerts: Int,
    val lastReportTime: Long,
    val uptime: Long
)

/**
 * 异常趋势
 */
data class ExceptionTrends(
    val timeWindowMs: Long,
    val totalExceptions: Long,
    val exceptionRate: Double, // 每分钟异常数
    val topExceptionTypes: List<Pair<String, Long>>,
    val severityDistribution: Map<ErrorSeverity, Long>,
    val recoveryRate: Double
)

/**
 * 监控配置
 */
data class MonitoringConfig(
    val reportIntervalMs: Long = 300000, // 5分钟
    val patternDetectionIntervalMs: Long = 60000, // 1分钟
    val alertTimeWindowMs: Long = 300000, // 5分钟
    val alertThreshold: Int = 10, // 5分钟内同类异常超过10次告警
    val alertConfig: AlertConfig = AlertConfig(),
    val patternConfig: PatternConfig = PatternConfig()
)

/**
 * 告警配置
 */
data class AlertConfig(
    val enableAlerts: Boolean = true,
    val maxActiveAlerts: Int = 100,
    val alertRetentionMs: Long = 3600000 // 1小时
)

/**
 * 模式检测配置
 */
data class PatternConfig(
    val enablePatternDetection: Boolean = true,
    val minPatternOccurrences: Int = 3,
    val patternTimeWindowMs: Long = 600000 // 10分钟
)

// 内部类定义
private class MonitoringStats {
    private val startTime = System.currentTimeMillis()
    private var lastReportTime = startTime
    
    private val totalExceptions = AtomicLong(0)
    private val totalRecoveries = AtomicLong(0)
    private val totalCrashes = AtomicLong(0)
    
    private val exceptionsByType = ConcurrentHashMap<String, AtomicLong>()
    private val exceptionTimestamps = ConcurrentHashMap<String, MutableList<Long>>()
    
    fun recordException(exception: QuesticleException) {
        totalExceptions.incrementAndGet()
        val typeName = exception::class.java.simpleName
        exceptionsByType.computeIfAbsent(typeName) { AtomicLong(0) }.incrementAndGet()
        
        // 记录时间戳用于趋势分析
        exceptionTimestamps.computeIfAbsent(typeName) { mutableListOf() }.add(System.currentTimeMillis())
    }
    
    fun recordRecovery(exception: QuesticleException, success: Boolean) {
        if (success) {
            totalRecoveries.incrementAndGet()
        }
    }
    
    fun recordCrash(exception: QuesticleException, isFatal: Boolean) {
        if (isFatal) {
            totalCrashes.incrementAndGet()
        }
    }
    
    fun getRecentExceptionCount(typeName: String, timeWindowMs: Long): Int {
        val timestamps = exceptionTimestamps[typeName] ?: return 0
        val cutoffTime = System.currentTimeMillis() - timeWindowMs
        return timestamps.count { it >= cutoffTime }
    }
    
    fun getTotalExceptions() = totalExceptions.get()
    fun getTotalRecoveries() = totalRecoveries.get()
    fun getTotalCrashes() = totalCrashes.get()
    fun getStartTime() = startTime
    fun getLastReportTime() = lastReportTime
    
    fun getExceptionStatistics(): Map<String, Long> = exceptionsByType.mapValues { it.value.get() }
    fun getRecoveryStatistics(): Map<String, Long> = emptyMap() // 简化实现
    fun getCrashStatistics(): Map<String, Long> = emptyMap() // 简化实现
    
    fun getExceptionTrends(timeWindowMs: Long): ExceptionTrends {
        val cutoffTime = System.currentTimeMillis() - timeWindowMs
        val recentExceptions = exceptionTimestamps.values.flatten().count { it >= cutoffTime }
        val rate = recentExceptions.toDouble() / (timeWindowMs / 60000.0) // 每分钟
        
        return ExceptionTrends(
            timeWindowMs = timeWindowMs,
            totalExceptions = recentExceptions.toLong(),
            exceptionRate = rate,
            topExceptionTypes = exceptionsByType.entries
                .sortedByDescending { it.value.get() }
                .take(5)
                .map { it.key to it.value.get() },
            severityDistribution = emptyMap(), // 简化实现
            recoveryRate = 0.0 // 简化实现
        )
    }
}

// 简化的模式检测器和告警管理器
private class ExceptionPatternDetector {
    fun addException(exception: QuesticleException, timestamp: Long) {}
    fun detectPatterns(): List<ExceptionPattern> = emptyList()
    fun getRecentPatterns(): List<ExceptionPattern> = emptyList()
    fun setConfig(config: PatternConfig) {}
}

private class AlertManager {
    fun triggerAlert(message: String, exception: QuesticleException?, level: AlertLevel) {}
    fun triggerCriticalAlert(message: String, exception: QuesticleException) {}
    fun getActiveAlerts(): List<Alert> = emptyList()
    fun getRecentAlerts(): List<Alert> = emptyList()
    fun setConfig(config: AlertConfig) {}
}

// 数据类定义
data class ExceptionPattern(
    val description: String,
    val severity: PatternSeverity,
    val occurrences: Int,
    val timeWindow: Long
)

enum class PatternSeverity { LOW, MEDIUM, HIGH, CRITICAL }
enum class AlertLevel { LOW, MEDIUM, HIGH, CRITICAL }

data class Alert(val message: String, val level: AlertLevel, val timestamp: Long)

@Serializable
data class MonitoringReport(
    val timestamp: Long,
    val timeWindowMs: Long,
    val exceptionStats: Map<String, Long>,
    val recoveryStats: Map<String, Long>,
    val crashStats: Map<String, Long>,
    val patterns: List<ExceptionPattern>,
    val alerts: List<Alert>
) {
    fun getSummary(): String {
        val totalExceptions = exceptionStats.values.sum()
        val totalRecoveries = recoveryStats.values.sum()
        return "Exceptions: $totalExceptions, Recoveries: $totalRecoveries, Patterns: ${patterns.size}, Alerts: ${alerts.size}"
    }
}
