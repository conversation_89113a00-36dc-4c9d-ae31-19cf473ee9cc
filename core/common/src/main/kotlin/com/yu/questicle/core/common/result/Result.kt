package com.yu.questicle.core.common.result

import com.yu.questicle.core.common.exception.QuesticleException
import com.yu.questicle.core.common.exception.SystemException
import com.yu.questicle.core.common.exception.ErrorSeverity
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * 表示操作结果的密封类
 * 
 * @param T 成功时返回的数据类型
 */
@Serializable
sealed class Result<out T> {
    
    /**
     * 成功结果
     * 
     * @property data 成功时的数据
     */
    @Serializable
    data class Success<T>(val data: T) : Result<T>()
    
    /**
     * 错误结果
     *
     * @property exception 错误异常
     */
    @Serializable
    data class Error(@Contextual val exception: QuesticleException) : Result<Nothing>()
    
    /**
     * 加载中状态
     */
    @Serializable
    object Loading : Result<Nothing>()
    
    /**
     * 检查是否为成功结果
     */
    val isSuccess: Boolean
        get() = this is Success
    
    /**
     * 检查是否为错误结果
     */
    val isError: Boolean
        get() = this is Error
    
    /**
     * 检查是否为加载中状态
     */
    val isLoading: Boolean
        get() = this is Loading
    
    /**
     * 获取成功时的数据，如果不是成功状态则返回 null
     */
    fun getOrNull(): T? = when (this) {
        is Success -> data
        else -> null
    }
    
    /**
     * 获取成功时的数据，如果不是成功状态则返回默认值
     */
    fun getOrDefault(defaultValue: @UnsafeVariance T): @UnsafeVariance T = when (this) {
        is Success -> data
        else -> defaultValue
    }
    
    /**
     * 获取错误异常，如果不是错误状态则返回 null
     */
    fun getErrorOrNull(): QuesticleException? = when (this) {
        is Error -> exception
        else -> null
    }
    
    /**
     * 映射成功结果
     */
    inline fun <R> map(transform: (T) -> R): Result<R> = when (this) {
        is Success -> Success(transform(data))
        is Error -> this
        is Loading -> this
    }
    
    /**
     * 映射错误结果
     */
    fun mapError(transform: (QuesticleException) -> QuesticleException): Result<T> = when (this) {
        is Success -> this
        is Error -> Error(transform(exception))
        is Loading -> this
    }
    
    /**
     * 扁平映射
     */
    inline fun <R> flatMap(transform: (T) -> Result<R>): Result<R> = when (this) {
        is Success -> transform(data)
        is Error -> this
        is Loading -> this
    }
    
    /**
     * 折叠结果
     */
    inline fun <R> fold(
        onSuccess: (T) -> R,
        onError: (QuesticleException) -> R,
        onLoading: () -> R
    ): R = when (this) {
        is Success -> onSuccess(data)
        is Error -> onError(exception)
        is Loading -> onLoading()
    }
    
    /**
     * 在成功时执行操作
     */
    inline fun onSuccess(action: (T) -> Unit): Result<T> {
        if (this is Success) action(data)
        return this
    }
    
    /**
     * 在错误时执行操作
     */
    inline fun onError(action: (QuesticleException) -> Unit): Result<T> {
        if (this is Error) action(exception)
        return this
    }
    
    /**
     * 在加载时执行操作
     */
    inline fun onLoading(action: () -> Unit): Result<T> {
        if (this is Loading) action()
        return this
    }
    
    companion object {
        /**
         * 创建成功结果
         */
        fun <T> success(data: T): Result<T> = Success(data)
        
        /**
         * 创建错误结果
         */
        fun <T> error(exception: QuesticleException): Result<T> = Error(exception)
        
        /**
         * 创建错误结果（从异常）
         */
        fun <T> error(throwable: Throwable): Result<T> = Error(throwable.toQuesticleException())
        
        /**
         * 创建加载中结果
         */
        fun <T> loading(): Result<T> = Loading
        
        /**
         * 从可能抛出异常的操作创建结果
         */
        inline fun <T> runCatching(action: () -> T): Result<T> = try {
            Success(action())
        } catch (e: QuesticleException) {
            Error(e)
        } catch (e: Exception) {
            Error(e.toQuesticleException())
        }
        
        /**
         * 从可能抛出异常的挂起操作创建结果
         */
        suspend inline fun <T> runCatchingSuspend(crossinline action: suspend () -> T): Result<T> = try {
            Success(action())
        } catch (e: QuesticleException) {
            Error(e)
        } catch (e: Exception) {
            Error(e.toQuesticleException())
        }
    }
}

/**
 * 将异常转换为 QuesticleException
 */
public fun Throwable.toQuesticleException(): QuesticleException {
    return when (this) {
        is QuesticleException -> this
        else -> SystemException(
            message = this.message ?: "Unknown error",
            errorCode = "SYSTEM_ERROR",
            severity = ErrorSeverity.HIGH,
            cause = this
        )
    }
}

/**
 * 便捷函数：创建成功结果
 */
fun <T> resultSuccess(data: T): Result<T> = Result.Success(data)

/**
 * 便捷函数：创建错误结果
 */
fun resultError(exception: QuesticleException): Result<Nothing> = Result.Error(exception)

/**
 * 便捷函数：创建加载中结果
 */
fun resultLoading(): Result<Nothing> = Result.Loading

/**
 * 安全执行操作并返回 Result
 */
inline fun <T> resultCatching(block: () -> T): Result<T> {
    return try {
        Result.Success(block())
    } catch (e: QuesticleException) {
        Result.Error(e)
    } catch (e: Exception) {
        Result.Error(e.toQuesticleException())
    }
}

/**
 * 安全执行挂起操作并返回 Result
 */
suspend inline fun <T> suspendResultCatching(crossinline block: suspend () -> T): Result<T> {
    return try {
        Result.Success(block())
    } catch (e: QuesticleException) {
        Result.Error(e)
    } catch (e: Exception) {
        Result.Error(e.toQuesticleException())
    }
}

/**
 * Result 扩展函数：获取数据或抛出异常
 */
fun <T> Result<T>.getOrThrow(): T {
    return when (this) {
        is Result.Success -> data
        is Result.Error -> throw exception
        is Result.Loading -> throw IllegalStateException("Cannot get data from loading state")
    }
}



/**
 * 类型别名，用于简化使用
 */
typealias ApiResult<T> = Result<T>
typealias RepositoryResult<T> = Result<T>
typealias UseCaseResult<T> = Result<T>
