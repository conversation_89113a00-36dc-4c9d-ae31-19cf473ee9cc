package com.yu.questicle.core.common.result

import com.yu.questicle.core.common.exception.*
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart

/**
 * 函数式错误处理的Result类型
 *
 * 用于替代异常抛出，提供类型安全的错误处理机制
 */
sealed class Result<out T> {

    /**
     * 成功结果
     */
    data class Success<T>(val data: T) : Result<T>()

    /**
     * 错误结果
     */
    data class Error(val exception: QuesticleException) : Result<Nothing>()

    /**
     * 加载状态
     */
    data object Loading : Result<Nothing>()
}

// ================================
// 基础属性和方法
// ================================

/**
 * 是否为成功结果
 */
val <T> Result<T>.isSuccess: Boolean get() = this is Result.Success

/**
 * 是否为错误结果
 */
val <T> Result<T>.isError: Boolean get() = this is Result.Error

/**
 * 是否为加载状态
 */
val <T> Result<T>.isLoading: Boolean get() = this is Result.Loading

/**
 * 获取数据，如果是错误则返回null
 */
fun <T> Result<T>.getOrNull(): T? = when (this) {
    is Result.Success -> data
    else -> null
}

/**
 * 获取数据，如果是错误则抛出异常
 */
fun <T> Result<T>.getOrThrow(): T = when (this) {
    is Result.Success -> data
    is Result.Error -> throw exception
    is Result.Loading -> throw IllegalStateException("Result is still loading")
}

/**
 * 获取数据，如果是错误则返回默认值
 */
fun <T> Result<T>.getOrElse(defaultValue: T): T = when (this) {
    is Result.Success -> data
    else -> defaultValue
}

/**
 * 获取数据，如果是错误则执行lambda返回默认值
 */
inline fun <T> Result<T>.getOrElse(defaultValue: (QuesticleException?) -> T): T = when (this) {
    is Result.Success -> data
    is Result.Error -> defaultValue(exception)
    is Result.Loading -> defaultValue(null)
}

/**
 * 获取异常，如果是成功则返回null
 */
fun <T> Result<T>.exceptionOrNull(): QuesticleException? = when (this) {
    is Result.Error -> exception
    else -> null
}

// ================================
// 工厂方法
// ================================

/**
 * 创建成功结果
 */
fun <T> resultSuccess(data: T): Result<T> = Result.Success(data)

/**
 * 创建错误结果
 */
fun <T> resultError(exception: QuesticleException): Result<T> = Result.Error(exception)

/**
 * 创建错误结果（从普通异常）
 */
fun <T> resultError(exception: Throwable): Result<T> = Result.Error(exception.toQuesticleException())

/**
 * 创建加载状态
 */
fun <T> resultLoading(): Result<T> = Result.Loading

/**
 * 从可能抛出异常的代码块创建Result
 */
inline fun <T> resultCatching(block: () -> T): Result<T> = try {
    Result.Success(block())
} catch (e: CancellationException) {
    throw e
} catch (e: Exception) {
    Result.Error(e.toQuesticleException())
}

/**
 * 从可空值创建Result
 */
fun <T> resultFromNullable(value: T?, error: () -> QuesticleException): Result<T> {
    return if (value != null) Result.Success(value) else Result.Error(error())
}

/**
 * Extension function to map success data
 */
inline fun <T, R> Result<T>.map(transform: (value: T) -> R): Result<R> {
    return when (this) {
        is Result.Success -> try {
            Result.Success(transform(data))
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            Result.Error(e.toQuesticleException())
        }
        is Result.Error -> this
        is Result.Loading -> Result.Loading
    }
}

/**
 * 平铺映射
 */
inline fun <T, R> Result<T>.flatMap(transform: (T) -> Result<R>): Result<R> = when (this) {
    is Result.Success -> try {
        transform(data)
    } catch (e: CancellationException) {
        throw e
    } catch (e: Exception) {
        Result.Error(e.toQuesticleException())
    }
    is Result.Error -> this
    is Result.Loading -> Result.Loading
}

/**
 * 映射错误
 */
inline fun <T> Result<T>.mapError(transform: (QuesticleException) -> QuesticleException): Result<T> = when (this) {
    is Result.Success -> this
    is Result.Error -> Result.Error(transform(exception))
    is Result.Loading -> this
}

/**
 * Extension function to handle success case
 */
inline fun <T> Result<T>.onSuccess(action: (value: T) -> Unit): Result<T> {
    if (this is Result.Success) {
        action(data)
    }
    return this
}

/**
 * Extension function to handle error case
 */
inline fun <T> Result<T>.onError(action: (exception: QuesticleException) -> Unit): Result<T> {
    if (this is Result.Error) {
        action(exception)
    }
    return this
}

/**
 * Extension function to handle loading case
 */
inline fun <T> Result<T>.onLoading(action: () -> Unit): Result<T> {
    if (this is Result.Loading) {
        action()
    }
    return this
}

/**
 * 错误恢复
 */
inline fun <T> Result<T>.recover(recovery: (QuesticleException) -> T): Result<T> = when (this) {
    is Result.Success -> this
    is Result.Error -> try {
        Result.Success(recovery(exception))
    } catch (e: CancellationException) {
        throw e
    } catch (e: Exception) {
        Result.Error(e.toQuesticleException())
    }
    is Result.Loading -> this
}

/**
 * 错误恢复（返回Result）
 */
inline fun <T> Result<T>.recoverWith(recovery: (QuesticleException) -> Result<T>): Result<T> = when (this) {
    is Result.Success -> this
    is Result.Error -> try {
        recovery(exception)
    } catch (e: CancellationException) {
        throw e
    } catch (e: Exception) {
        Result.Error(e.toQuesticleException())
    }
    is Result.Loading -> this
}

/**
 * 将普通异常转换为QuesticleException
 */
fun Throwable.toQuesticleException(): QuesticleException {
    return when (this) {
        is QuesticleException -> this
        is IllegalArgumentException -> ValidationException(
            message = message ?: "Invalid argument",
            cause = this
        )
        is IllegalStateException -> BusinessException(
            message = message ?: "Invalid state",
            cause = this
        )
        is SecurityException -> PermissionException(
            message = message ?: "Permission denied",
            cause = this
        )
        else -> UnknownException(
            message = message ?: "Unknown error",
            cause = this
        )
    }
}

/**
 * 组合多个Result
 */
fun <T1, T2, R> Result<T1>.zip(
    other: Result<T2>,
    transform: (T1, T2) -> R
): Result<R> = flatMap { t1 ->
    other.map { t2 -> transform(t1, t2) }
}

/**
 * 将Result列表转换为列表的Result
 */
fun <T> List<Result<T>>.sequence(): Result<List<T>> {
    val results = mutableListOf<T>()
    for (result in this) {
        when (result) {
            is Result.Success -> results.add(result.data)
            is Result.Error -> return result
            is Result.Loading -> return Result.Loading
        }
    }
    return resultSuccess(results)
}

/**
 * 将Result列表转换为成功结果的列表
 */
fun <T> List<Result<T>>.filterSuccess(): List<T> {
    return mapNotNull { it.getOrNull() }
}

/**
 * 将Result列表转换为错误结果的列表
 */
fun <T> List<Result<T>>.filterErrors(): List<QuesticleException> {
    return mapNotNull { it.exceptionOrNull() }
}

/**
 * Extension function to convert Flow to Result Flow
 */
fun <T> Flow<T>.asResult(): Flow<Result<T>> {
    return this
        .map<T, Result<T>> { Result.Success(it) }
        .onStart { emit(Result.Loading) }
        .catch { emit(Result.Error(it.toQuesticleException())) }
}

/**
 * Utility function to create a success result
 */
fun <T> T.asSuccess(): Result<T> = Result.Success(this)

/**
 * Utility function to create an error result
 */
fun Throwable.asError(): Result<Nothing> = Result.Error(this.toQuesticleException())

/**
 * Utility function to create a loading result
 */
fun loading(): Result<Nothing> = Result.Loading

// ================================
// 特定异常类型（补充）
// ================================

/**
 * 权限异常
 */
class PermissionException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "PERMISSION_DENIED",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap(),
    val permission: String? = null
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.PERMISSION,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context + mapOfNotNull("permission" to permission),
    retryable = false
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return PermissionException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context,
            permission = permission
        )
    }

    override fun getDefaultUserMessage(): String {
        return "没有执行此操作的权限"
    }
}

/**
 * 未知异常
 */
class UnknownException(
    message: String,
    cause: Throwable? = null,
    errorCode: String = "UNKNOWN_ERROR",
    userMessage: String? = null,
    context: Map<String, Any?> = emptyMap()
) : QuesticleException(
    message = message,
    cause = cause,
    errorCode = errorCode,
    errorType = ErrorType.UNKNOWN,
    severity = ErrorSeverity.HIGH,
    userMessage = userMessage,
    context = context,
    retryable = false
) {

    override fun createCopy(
        message: String,
        cause: Throwable?,
        errorCode: String,
        errorType: ErrorType,
        severity: ErrorSeverity,
        userMessage: String?,
        context: Map<String, Any?>,
        retryable: Boolean
    ): QuesticleException {
        return UnknownException(
            message = message,
            cause = cause,
            errorCode = errorCode,
            userMessage = userMessage,
            context = context
        )
    }

    override fun getDefaultUserMessage(): String {
        return "发生了未知错误，请稍后重试"
    }
}

// 工具函数
private fun <K, V> mapOfNotNull(vararg pairs: Pair<K, V?>): Map<K, V> {
    return pairs.mapNotNull { (key, value) ->
        value?.let { key to it }
    }.toMap()
}
