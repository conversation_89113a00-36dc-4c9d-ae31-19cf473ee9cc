package com.yu.questicle.core.common.logging

import android.content.Context
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import java.io.File
import java.io.FileWriter
import java.io.IOException
import kotlin.io.path.createTempDirectory

/**
 * LogConfigLoader单元测试
 * 测试日志配置加载器的核心功能
 */
@DisplayName("LogConfigLoader Tests")
class LogConfigLoaderTest {
    
    private lateinit var context: Context
    private lateinit var logConfigLoader: LogConfigLoader
    private lateinit var tempFilesDir: File

    @BeforeEach
    fun setUp() {
        // 创建临时目录用于测试 - 使用现代化的API
        tempFilesDir = createTempDirectory("log_config_test").toFile()

        context = mockk<Context>(relaxed = true)
        every { context.filesDir } returns tempFilesDir

        logConfigLoader = LogConfigLoader(context)
    }

    @AfterEach
    fun tearDown() {
        // 清理临时文件
        tempFilesDir.deleteRecursively()
    }
    
    @Nested
    @DisplayName("加载全局配置")
    inner class LoadGlobalConfigTests {
        
        @Test
        @DisplayName("配置文件不存在时应该返回默认配置")
        fun `should return default config when file does not exist`() = runTest {
            // Given - 配置文件不存在（tempFilesDir是空的）

            // When
            val config = logConfigLoader.loadGlobalConfig()

            // Then
            assertNotNull(config)
            assertEquals(LogLevel.INFO, config.globalLevel)
            assertTrue(config.enabled)
            assertTrue(config.enableAsync)
            assertEquals(1024, config.bufferSize)
        }
        
        @Test
        @DisplayName("配置文件存在时应该加载配置")
        fun `should load config when file exists`() = runTest {
            // Given
            val configFile = File(tempFilesDir, "log_config.json")
            val validConfigJson = """
                {
                    "globalLevel": "DEBUG",
                    "enabled": true,
                    "enableAsync": false,
                    "bufferSize": 2048
                }
            """.trimIndent()

            // 创建配置文件
            configFile.writeText(validConfigJson)

            // When
            val config = logConfigLoader.loadGlobalConfig()

            // Then
            assertNotNull(config)
            assertEquals(LogLevel.DEBUG, config.globalLevel)
            assertTrue(config.enabled)
            assertEquals(false, config.enableAsync)
            assertEquals(2048, config.bufferSize)
        }
        
        @Test
        @DisplayName("配置文件损坏时应该返回默认配置")
        fun `should return default config when file is corrupted`() = runTest {
            // Given
            val configFile = File(tempFilesDir, "log_config.json")
            // 写入无效的JSON
            configFile.writeText("invalid json content")

            // When
            val config = logConfigLoader.loadGlobalConfig()

            // Then
            assertNotNull(config)
            assertEquals(LogLevel.INFO, config.globalLevel) // 默认值
        }
    }
    
    @Nested
    @DisplayName("保存全局配置")
    inner class SaveGlobalConfigTests {
        
        @Test
        @DisplayName("应该成功保存配置到文件")
        fun `should successfully save config to file`() = runTest {
            // Given
            val config = LogConfig(
                globalLevel = LogLevel.DEBUG,
                enabled = true,
                enableAsync = false,
                bufferSize = 2048
            )

            // When
            val result = logConfigLoader.saveGlobalConfig(config)

            // Then
            assertTrue(result.isSuccess)

            // 验证文件是否被创建并包含正确内容
            val configFile = File(tempFilesDir, "log_config.json")
            assertTrue(configFile.exists())
            val savedContent = configFile.readText()
            assertTrue(savedContent.contains("DEBUG"))
            assertTrue(savedContent.contains("2048"))
        }
        
        @Test
        @DisplayName("写入失败时应该返回错误")
        fun `should return error when write fails`() = runTest {
            // Given
            // 创建一个只读目录来模拟写入失败
            val readOnlyDir = File(tempFilesDir, "readonly")
            readOnlyDir.mkdirs()
            readOnlyDir.setReadOnly()

            // 创建一个指向只读目录的LogConfigLoader
            val readOnlyContext = mockk<Context>(relaxed = true)
            every { readOnlyContext.filesDir } returns readOnlyDir
            val readOnlyLoader = LogConfigLoader(readOnlyContext)

            val config = LogConfig()

            // When
            val result = readOnlyLoader.saveGlobalConfig(config)

            // Then
            assertTrue(result.isFailure)
        }
    }
    
    @Nested
    @DisplayName("加载Logger配置")
    inner class LoadLoggerConfigsTests {
        
        @Test
        @DisplayName("配置文件不存在时应该返回默认Logger配置")
        fun `should return default logger configs when file does not exist`() = runTest {
            // Given - 配置文件不存在（tempFilesDir是空的）

            // When
            val configs = logConfigLoader.loadLoggerConfigs()

            // Then
            assertNotNull(configs)
            assertTrue(configs.isNotEmpty())
            assertTrue(configs.containsKey("com.yu.questicle.core"))
            assertTrue(configs.containsKey("com.yu.questicle.feature.tetris"))
        }
        
        @Test
        @DisplayName("配置文件存在时应该加载Logger配置")
        fun `should load logger configs when file exists`() = runTest {
            // Given
            val configFile = File(tempFilesDir, "logger_configs.json")
            val validConfigJson = """
                {
                    "com.yu.questicle.test": {
                        "name": "com.yu.questicle.test",
                        "level": "ERROR",
                        "enabled": false
                    }
                }
            """.trimIndent()

            // 创建配置文件
            configFile.writeText(validConfigJson)

            // When
            val configs = logConfigLoader.loadLoggerConfigs()

            // Then
            assertNotNull(configs)
            assertTrue(configs.containsKey("com.yu.questicle.test"))
            val testConfig = configs["com.yu.questicle.test"]!!
            assertEquals(LogLevel.ERROR, testConfig.level)
            assertEquals(false, testConfig.enabled)
        }
    }
    
    @Nested
    @DisplayName("保存Logger配置")
    inner class SaveLoggerConfigsTests {
        
        @Test
        @DisplayName("应该成功保存Logger配置到文件")
        fun `should successfully save logger configs to file`() = runTest {
            // Given
            val configs = mapOf(
                "test.logger" to LoggerConfig(
                    name = "test.logger",
                    level = LogLevel.WARN,
                    enabled = true
                )
            )

            // When
            val result = logConfigLoader.saveLoggerConfigs(configs)

            // Then
            assertTrue(result.isSuccess)

            // 验证文件是否被创建并包含正确内容
            val configFile = File(tempFilesDir, "logger_configs.json")
            assertTrue(configFile.exists())
            val savedContent = configFile.readText()
            assertTrue(savedContent.contains("test.logger"))
            assertTrue(savedContent.contains("WARN"))
        }
    }
    
    @Nested
    @DisplayName("重置为默认配置")
    inner class ResetToDefaultsTests {
        
        @Test
        @DisplayName("应该成功重置为默认配置")
        fun `should successfully reset to defaults`() = runTest {
            // Given - 可能已经存在一些配置文件

            // When
            val result = logConfigLoader.resetToDefaults()

            // Then
            assertTrue(result.isSuccess)

            // 验证文件是否被创建
            val globalConfigFile = File(tempFilesDir, "log_config.json")
            val loggerConfigFile = File(tempFilesDir, "logger_configs.json")
            assertTrue(globalConfigFile.exists())
            assertTrue(loggerConfigFile.exists())
        }
    }
    
    @Nested
    @DisplayName("导出配置")
    inner class ExportConfigTests {
        
        @Test
        @DisplayName("应该成功导出配置为JSON")
        fun `should successfully export config as JSON`() = runTest {
            // Given - 配置文件不存在（使用默认配置）

            // When
            val result = logConfigLoader.exportConfig()

            // Then
            assertTrue(result.isSuccess)
            val exportJson = result.getOrNull()
            assertNotNull(exportJson)
            assertTrue(exportJson!!.contains("globalConfig"))
            assertTrue(exportJson.contains("loggerConfigs"))
            assertTrue(exportJson.contains("exportedAt"))
            assertTrue(exportJson.contains("version"))
        }
    }
    
    @Nested
    @DisplayName("导入配置")
    inner class ImportConfigTests {
        
        @Test
        @DisplayName("应该成功导入有效的配置JSON")
        fun `should successfully import valid config JSON`() = runTest {
            // Given
            val validImportJson = """
                {
                    "globalConfig": {
                        "globalLevel": "DEBUG",
                        "enabled": true
                    },
                    "loggerConfigs": {
                        "test.logger": {
                            "name": "test.logger",
                            "level": "ERROR",
                            "enabled": false
                        }
                    },
                    "exportedAt": 1234567890,
                    "version": "1.0"
                }
            """.trimIndent()

            // When
            val result = logConfigLoader.importConfig(validImportJson)

            // Then
            assertTrue(result.isSuccess)

            // 验证文件是否被创建
            val globalConfigFile = File(tempFilesDir, "log_config.json")
            val loggerConfigFile = File(tempFilesDir, "logger_configs.json")
            assertTrue(globalConfigFile.exists())
            assertTrue(loggerConfigFile.exists())
        }
        
        @Test
        @DisplayName("导入无效JSON应该返回错误")
        fun `should return error when importing invalid JSON`() = runTest {
            // Given
            val invalidJson = "{ invalid json }"
            
            // When
            val result = logConfigLoader.importConfig(invalidJson)
            
            // Then
            assertTrue(result.isFailure)
        }
    }
}
