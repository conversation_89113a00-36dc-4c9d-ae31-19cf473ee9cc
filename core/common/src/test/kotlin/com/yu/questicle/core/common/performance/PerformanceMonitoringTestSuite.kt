package com.yu.questicle.core.common.performance

import com.yu.questicle.core.testing.MockDataFactory
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.comparables.shouldBeGreaterThan
import io.kotest.matchers.comparables.shouldBeLessThan
import io.kotest.matchers.doubles.shouldBeGreaterThan
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.longs.shouldBeGreaterThan
import io.mockk.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.*
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.junit.jupiter.params.provider.EnumSource
import javax.inject.Inject

/**
 * 性能监控系统测试套件
 * 
 * 测试范围：
 * - 帧率监控功能
 * - 内存监控功能  
 * - CPU监控功能
 * - 性能数据收集和存储
 * - 性能告警系统
 * - Supabase数据同步
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Execution(ExecutionMode.CONCURRENT)
class PerformanceMonitoringTestSuite {
    
    @Inject
    lateinit var mockDataFactory: MockDataFactory
    
    private lateinit var frameRateMonitor: FrameRateMonitor
    private lateinit var memoryMonitor: MemoryMonitor
    private lateinit var cpuMonitor: CPUMonitor
    private lateinit var performanceRepository: PerformanceRepository
    private lateinit var performanceMonitor: PerformanceMonitor
    
    @BeforeAll
    fun setup() {
        mockDataFactory = MockDataFactory()
        
        // 创建Mock对象
        frameRateMonitor = mockk(relaxed = true)
        memoryMonitor = mockk(relaxed = true)
        cpuMonitor = mockk(relaxed = true)
        performanceRepository = mockk(relaxed = true)
        
        // 创建性能监控器
        performanceMonitor = PerformanceMonitor(
            frameRateMonitor = frameRateMonitor,
            memoryMonitor = memoryMonitor,
            cpuMonitor = cpuMonitor,
            performanceRepository = performanceRepository
        )
    }
    
    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }
    
    @Nested
    @DisplayName("帧率监控测试")
    inner class FrameRateMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控帧率")
        fun `should monitor frame rate correctly`() = runTest {
            // Given
            val expectedFrameRate = mockDataFactory.createFrameRateMetric(fps = 60)
            every { frameRateMonitor.getCurrentMetric() } returns expectedFrameRate
            
            val config = mockDataFactory.createAdvancedPerformanceConfig {
                withFrameRateMonitoring(true)
                withSnapshotInterval(100L)
            }
            
            // When
            performanceMonitor.startAdvancedMonitoring(config)
            delay(200) // 等待采样
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot shouldNotBe null
            snapshot!!.frameRate shouldNotBe null
            snapshot.frameRate!!.fps shouldBe 60
            snapshot.frameRate!!.isSmooth shouldBe true
            
            verify { frameRateMonitor.startMonitoring() }
        }
        
        @ParameterizedTest
        @ValueSource(ints = [30, 45, 60, 90, 120])
        @DisplayName("应该正确检测不同帧率")
        fun `should detect different frame rates`(targetFps: Int) = runTest {
            // Given
            val frameRateMetric = mockDataFactory.createFrameRateMetric(fps = targetFps)
            every { frameRateMonitor.getCurrentMetric() } returns frameRateMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.frameRate!!.fps shouldBe targetFps
            snapshot.frameRate!!.isSmooth shouldBe (targetFps >= 55)
        }
        
        @Test
        @DisplayName("应该检测低帧率性能问题")
        fun `should detect low frame rate performance issues`() = runTest {
            // Given
            val lowFrameRate = mockDataFactory.createFrameRateMetric(fps = 25) {
                withDroppedFrameRate(0.3)
            }
            every { frameRateMonitor.getCurrentMetric() } returns lowFrameRate
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.frameRate!!.fps shouldBeLessThan 30
            snapshot.frameRate!!.isSmooth shouldBe false
            snapshot.overall.issues.shouldNotBeEmpty()
            snapshot.overall.score shouldBeLessThan 70.0
        }
        
        @Test
        @DisplayName("应该正确计算帧时间")
        fun `should calculate frame time correctly`() = runTest {
            // Given
            val frameRateMetric = mockDataFactory.createFrameRateMetric(fps = 60) {
                withAvgFrameTime(16.67)
            }
            every { frameRateMonitor.getCurrentMetric() } returns frameRateMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.frameRate!!.avgFrameTime shouldBeGreaterThan 16.0
            snapshot.frameRate!!.avgFrameTime shouldBeLessThan 17.0
        }
    }
    
    @Nested
    @DisplayName("内存监控测试")
    inner class MemoryMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控内存使用")
        fun `should monitor memory usage correctly`() = runTest {
            // Given
            val memoryMetric = mockDataFactory.createMemoryMetric(heapUtilization = 0.6)
            every { memoryMonitor.getCurrentMetric() } returns memoryMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.memory shouldNotBe null
            snapshot.memory!!.heapUtilization shouldBeGreaterThan 0.5
            snapshot.memory!!.heapUtilization shouldBeLessThan 0.7
            snapshot.memory!!.memoryPressure shouldBe false
        }
        
        @Test
        @DisplayName("应该检测内存压力")
        fun `should detect memory pressure`() = runTest {
            // Given
            val highMemoryUsage = mockDataFactory.createMemoryMetric(heapUtilization = 0.95) {
                withMemoryPressure(true)
            }
            every { memoryMonitor.getCurrentMetric() } returns highMemoryUsage
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.memory!!.heapUtilization shouldBeGreaterThan 0.9
            snapshot.memory!!.memoryPressure shouldBe true
            snapshot.overall.issues.shouldNotBeEmpty()
            snapshot.overall.score shouldBeLessThan 75.0
        }
        
        @Test
        @DisplayName("应该监控GC活动")
        fun `should monitor gc activity`() = runTest {
            // Given
            val memoryMetric = mockDataFactory.createMemoryMetric(heapUtilization = 0.85)
            every { memoryMonitor.getCurrentMetric() } returns memoryMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.memory!!.gcCount shouldBeGreaterThan 0L
            snapshot.memory!!.gcRate shouldBeGreaterThan 0.0
        }
        
        @ParameterizedTest
        @ValueSource(doubles = [0.3, 0.5, 0.7, 0.85, 0.95])
        @DisplayName("应该正确评估不同内存使用率")
        fun `should evaluate different memory utilization levels`(utilization: Double) = runTest {
            // Given
            val memoryMetric = mockDataFactory.createMemoryMetric(heapUtilization = utilization)
            every { memoryMonitor.getCurrentMetric() } returns memoryMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.memory!!.heapUtilization shouldBe utilization
            if (utilization > 0.9) {
                snapshot.overall.grade shouldBe PerformanceGrade.POOR
            } else if (utilization < 0.5) {
                snapshot.overall.grade shouldBe PerformanceGrade.EXCELLENT
            }
        }
    }
    
    @Nested
    @DisplayName("CPU监控测试")
    inner class CpuMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控CPU使用率")
        fun `should monitor cpu usage correctly`() = runTest {
            // Given
            val cpuMetric = mockDataFactory.createCpuMetric(cpuUsage = 45.0)
            every { cpuMonitor.getCurrentMetric() } returns cpuMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.cpu shouldNotBe null
            snapshot.cpu!!.cpuUsage shouldBeGreaterThan 40.0
            snapshot.cpu!!.cpuUsage shouldBeLessThan 50.0
            snapshot.cpu!!.isHighUsage shouldBe false
        }
        
        @Test
        @DisplayName("应该检测高CPU使用率")
        fun `should detect high cpu usage`() = runTest {
            // Given
            val highCpuUsage = mockDataFactory.createCpuMetric(cpuUsage = 85.0) {
                withThreadCount(50)
                withLoadAverage(3.5)
            }
            every { cpuMonitor.getCurrentMetric() } returns highCpuUsage
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.cpu!!.cpuUsage shouldBeGreaterThan 80.0
            snapshot.cpu!!.isHighUsage shouldBe true
            snapshot.overall.issues.shouldNotBeEmpty()
            snapshot.overall.score shouldBeLessThan 80.0
        }
        
        @Test
        @DisplayName("应该监控线程数量")
        fun `should monitor thread count`() = runTest {
            // Given
            val cpuMetric = mockDataFactory.createCpuMetric(cpuUsage = 50.0) {
                withThreadCount(25)
            }
            every { cpuMonitor.getCurrentMetric() } returns cpuMetric
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.cpu!!.threadCount shouldBe 25
        }
    }
    
    @Nested
    @DisplayName("性能数据收集测试")
    inner class PerformanceDataCollectionTests {
        
        @Test
        @DisplayName("应该正确收集性能快照")
        fun `should collect performance snapshots correctly`() = runTest {
            // Given
            val frameRate = mockDataFactory.createFrameRateMetric(fps = 60)
            val memory = mockDataFactory.createMemoryMetric(heapUtilization = 0.7)
            val cpu = mockDataFactory.createCpuMetric(cpuUsage = 50.0)
            
            every { frameRateMonitor.getCurrentMetric() } returns frameRate
            every { memoryMonitor.getCurrentMetric() } returns memory
            every { cpuMonitor.getCurrentMetric() } returns cpu
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot shouldNotBe null
            snapshot.frameRate shouldBe frameRate
            snapshot.memory shouldBe memory
            snapshot.cpu shouldBe cpu
            snapshot.overall.score shouldBeGreaterThan 0.0
        }
        
        @Test
        @DisplayName("应该正确保存性能数据到仓库")
        fun `should save performance data to repository correctly`() = runTest {
            // Given
            val snapshot = mockDataFactory.createPerformanceSnapshot()
            coEvery { performanceRepository.savePerformanceSnapshot(any()) } returns Unit
            
            // When
            performanceMonitor.triggerSnapshot()
            
            // Then
            coVerify { performanceRepository.savePerformanceSnapshot(any()) }
        }
        
        @Test
        @DisplayName("应该正确计算整体性能评分")
        fun `should calculate overall performance score correctly`() = runTest {
            // Given - 优秀性能指标
            val excellentFrameRate = mockDataFactory.createFrameRateMetric(fps = 60)
            val excellentMemory = mockDataFactory.createMemoryMetric(heapUtilization = 0.5)
            val excellentCpu = mockDataFactory.createCpuMetric(cpuUsage = 30.0)
            
            every { frameRateMonitor.getCurrentMetric() } returns excellentFrameRate
            every { memoryMonitor.getCurrentMetric() } returns excellentMemory
            every { cpuMonitor.getCurrentMetric() } returns excellentCpu
            
            // When
            val snapshot = performanceMonitor.getCurrentPerformanceSnapshot()
            
            // Then
            snapshot.overall.score shouldBeGreaterThan 90.0
            snapshot.overall.grade shouldBe PerformanceGrade.EXCELLENT
            snapshot.overall.issues.size shouldBe 0
        }
    }
    
    @Nested
    @DisplayName("性能监控生命周期测试")
    inner class PerformanceMonitoringLifecycleTests {
        
        @Test
        @DisplayName("应该正确启动和停止监控")
        fun `should start and stop monitoring correctly`() = runTest {
            // Given
            val config = mockDataFactory.createAdvancedPerformanceConfig()
            
            // When
            performanceMonitor.startAdvancedMonitoring(config)
            val isMonitoringAfterStart = performanceMonitor.isAdvancedMonitoring.value
            
            performanceMonitor.stopAdvancedMonitoring()
            val isMonitoringAfterStop = performanceMonitor.isAdvancedMonitoring.value
            
            // Then
            isMonitoringAfterStart shouldBe true
            isMonitoringAfterStop shouldBe false
            
            verify { frameRateMonitor.startMonitoring() }
            verify { frameRateMonitor.stopMonitoring() }
        }
        
        @Test
        @DisplayName("应该正确处理监控配置")
        fun `should handle monitoring configuration correctly`() = runTest {
            // Given
            val config = mockDataFactory.createAdvancedPerformanceConfig {
                withFrameRateMonitoring(true)
                withMemoryMonitoring(false)
                withCpuMonitoring(true)
            }
            
            // When
            performanceMonitor.startAdvancedMonitoring(config)
            
            // Then
            verify { frameRateMonitor.startMonitoring() }
            verify(exactly = 0) { memoryMonitor.startMonitoring() }
            verify { cpuMonitor.startMonitoring() }
        }
    }
}
