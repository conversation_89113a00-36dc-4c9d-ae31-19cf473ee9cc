package com.yu.questicle.core.common.exception

import com.yu.questicle.core.testing.MockDataFactory
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.string.shouldContain
import io.mockk.*
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.*
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import javax.inject.Inject

/**
 * 异常处理系统测试套件
 * 
 * 测试范围：
 * - 异常处理机制
 * - 异常恢复策略
 * - 崩溃报告功能
 * - 异常监控和告警
 * - Supabase数据同步
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Execution(ExecutionMode.CONCURRENT)
class ExceptionHandlingTestSuite {
    
    @Inject
    lateinit var mockDataFactory: MockDataFactory
    
    private lateinit var exceptionHandler: QuesticleExceptionHandler
    private lateinit var recoveryManager: ExceptionRecoveryManager
    private lateinit var crashReportingManager: CrashReportingManager
    private lateinit var monitoringService: ExceptionMonitoringService
    
    @BeforeAll
    fun setup() {
        mockDataFactory = MockDataFactory()
        
        // 创建Mock对象
        exceptionHandler = mockk(relaxed = true)
        recoveryManager = mockk(relaxed = true)
        crashReportingManager = mockk(relaxed = true)
        monitoringService = mockk(relaxed = true)
    }
    
    @AfterEach
    fun cleanup() {
        clearAllMocks()
    }
    
    @Nested
    @DisplayName("异常处理测试")
    inner class ExceptionHandlingTests {
        
        @Test
        @DisplayName("应该正确处理业务逻辑异常")
        fun `should handle business logic exceptions correctly`() = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException(
                type = ExceptionType.BUSINESS_LOGIC
            ) {
                withMessage("Test business logic error")
                withErrorCode("BUSINESS_ERROR")
                withSeverity(ErrorSeverity.MEDIUM)
            }
            
            val expectedResult = ExceptionHandlingResult(
                action = ExceptionAction.LOG_AND_CONTINUE,
                shouldReport = true,
                shouldRecover = false,
                message = "Business logic error handled"
            )
            
            every { exceptionHandler.handleException(exception) } returns expectedResult
            
            // When
            val result = exceptionHandler.handleException(exception)
            
            // Then
            result.action shouldBe ExceptionAction.LOG_AND_CONTINUE
            result.shouldReport shouldBe true
            result.shouldRecover shouldBe false
            
            verify { exceptionHandler.handleException(exception) }
        }
        
        @ParameterizedTest
        @EnumSource(ErrorSeverity::class)
        @DisplayName("应该根据严重程度正确处理异常")
        fun `should handle exceptions based on severity`(severity: ErrorSeverity) = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException(
                type = ExceptionType.SYSTEM
            ) {
                withSeverity(severity)
                withMessage("Test exception with severity $severity")
            }
            
            val expectedAction = when (severity) {
                ErrorSeverity.LOW -> ExceptionAction.LOG_ONLY
                ErrorSeverity.MEDIUM -> ExceptionAction.LOG_AND_CONTINUE
                ErrorSeverity.HIGH -> ExceptionAction.LOG_AND_RECOVER
                ErrorSeverity.CRITICAL -> ExceptionAction.LOG_AND_TERMINATE
            }
            
            val expectedResult = ExceptionHandlingResult(
                action = expectedAction,
                shouldReport = severity >= ErrorSeverity.MEDIUM,
                shouldRecover = severity >= ErrorSeverity.HIGH,
                message = "Exception handled based on severity"
            )
            
            every { exceptionHandler.handleException(exception) } returns expectedResult
            
            // When
            val result = exceptionHandler.handleException(exception)
            
            // Then
            result.action shouldBe expectedAction
            result.shouldReport shouldBe (severity >= ErrorSeverity.MEDIUM)
            result.shouldRecover shouldBe (severity >= ErrorSeverity.HIGH)
        }
        
        @Test
        @DisplayName("应该正确处理网络异常")
        fun `should handle network exceptions correctly`() = runTest {
            // Given
            val networkException = mockDataFactory.createQuesticleException(
                type = ExceptionType.NETWORK
            ) {
                withMessage("Network connection failed")
                withErrorCode("NETWORK_TIMEOUT")
                withSeverity(ErrorSeverity.HIGH)
            }
            
            val expectedResult = ExceptionHandlingResult(
                action = ExceptionAction.LOG_AND_RECOVER,
                shouldReport = true,
                shouldRecover = true,
                message = "Network exception handled with recovery"
            )
            
            every { exceptionHandler.handleException(networkException) } returns expectedResult
            
            // When
            val result = exceptionHandler.handleException(networkException)
            
            // Then
            result.action shouldBe ExceptionAction.LOG_AND_RECOVER
            result.shouldRecover shouldBe true
            
            verify { exceptionHandler.handleException(networkException) }
        }
        
        @Test
        @DisplayName("应该正确处理数据库异常")
        fun `should handle database exceptions correctly`() = runTest {
            // Given
            val databaseException = mockDataFactory.createQuesticleException(
                type = ExceptionType.DATABASE
            ) {
                withMessage("Database connection lost")
                withErrorCode("DATABASE_CONNECTION_LOST")
                withSeverity(ErrorSeverity.HIGH)
            }
            
            val expectedResult = ExceptionHandlingResult(
                action = ExceptionAction.LOG_AND_RECOVER,
                shouldReport = true,
                shouldRecover = true,
                message = "Database exception handled with recovery"
            )
            
            every { exceptionHandler.handleException(databaseException) } returns expectedResult
            
            // When
            val result = exceptionHandler.handleException(databaseException)
            
            // Then
            result.action shouldBe ExceptionAction.LOG_AND_RECOVER
            result.shouldRecover shouldBe true
        }
    }
    
    @Nested
    @DisplayName("异常恢复测试")
    inner class ExceptionRecoveryTests {
        
        @Test
        @DisplayName("应该正确恢复网络异常")
        fun `should recover from network exceptions correctly`() = runTest {
            // Given
            val networkException = mockDataFactory.createQuesticleException(
                type = ExceptionType.NETWORK
            ) {
                withErrorCode("NETWORK_TIMEOUT")
            }
            
            val context = mockDataFactory.createExceptionContext {
                withUserId("test-user")
                withSessionId("test-session")
            }
            
            val expectedResult = RecoveryResult(
                isSuccess = true,
                strategy = RecoveryStrategy.RETRY_WITH_BACKOFF,
                attemptsUsed = 2,
                recoveryTime = 1500L,
                message = "Network connection recovered"
            )
            
            coEvery { recoveryManager.attemptRecovery(networkException, context) } returns expectedResult
            
            // When
            val result = recoveryManager.attemptRecovery(networkException, context)
            
            // Then
            result.isSuccess shouldBe true
            result.strategy shouldBe RecoveryStrategy.RETRY_WITH_BACKOFF
            result.attemptsUsed shouldBe 2
            
            coVerify { recoveryManager.attemptRecovery(networkException, context) }
        }
        
        @Test
        @DisplayName("应该正确恢复数据库异常")
        fun `should recover from database exceptions correctly`() = runTest {
            // Given
            val databaseException = mockDataFactory.createQuesticleException(
                type = ExceptionType.DATABASE
            ) {
                withErrorCode("DATABASE_CONNECTION_LOST")
            }
            
            val context = mockDataFactory.createExceptionContext()
            
            val expectedResult = RecoveryResult(
                isSuccess = true,
                strategy = RecoveryStrategy.RECONNECT,
                attemptsUsed = 1,
                recoveryTime = 800L,
                message = "Database connection restored"
            )
            
            coEvery { recoveryManager.attemptRecovery(databaseException, context) } returns expectedResult
            
            // When
            val result = recoveryManager.attemptRecovery(databaseException, context)
            
            // Then
            result.isSuccess shouldBe true
            result.strategy shouldBe RecoveryStrategy.RECONNECT
        }
        
        @Test
        @DisplayName("应该正确处理恢复失败")
        fun `should handle recovery failure correctly`() = runTest {
            // Given
            val criticalException = mockDataFactory.createQuesticleException(
                type = ExceptionType.SYSTEM
            ) {
                withSeverity(ErrorSeverity.CRITICAL)
                withErrorCode("SYSTEM_CRITICAL_ERROR")
            }
            
            val context = mockDataFactory.createExceptionContext()
            
            val expectedResult = RecoveryResult(
                isSuccess = false,
                strategy = RecoveryStrategy.GRACEFUL_DEGRADATION,
                attemptsUsed = 3,
                recoveryTime = 5000L,
                message = "Recovery failed, graceful degradation applied"
            )
            
            coEvery { recoveryManager.attemptRecovery(criticalException, context) } returns expectedResult
            
            // When
            val result = recoveryManager.attemptRecovery(criticalException, context)
            
            // Then
            result.isSuccess shouldBe false
            result.strategy shouldBe RecoveryStrategy.GRACEFUL_DEGRADATION
            result.attemptsUsed shouldBe 3
        }
        
        @ParameterizedTest
        @EnumSource(RecoveryStrategy::class)
        @DisplayName("应该正确应用不同的恢复策略")
        fun `should apply different recovery strategies correctly`(strategy: RecoveryStrategy) = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException()
            val context = mockDataFactory.createExceptionContext()
            
            val expectedResult = RecoveryResult(
                isSuccess = true,
                strategy = strategy,
                attemptsUsed = 1,
                recoveryTime = 1000L,
                message = "Recovery with strategy $strategy"
            )
            
            coEvery { recoveryManager.attemptRecovery(exception, context) } returns expectedResult
            
            // When
            val result = recoveryManager.attemptRecovery(exception, context)
            
            // Then
            result.strategy shouldBe strategy
        }
    }
    
    @Nested
    @DisplayName("崩溃报告测试")
    inner class CrashReportingTests {
        
        @Test
        @DisplayName("应该正确报告崩溃到Supabase")
        fun `should report crashes to supabase correctly`() = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException(
                type = ExceptionType.SYSTEM
            ) {
                withSeverity(ErrorSeverity.CRITICAL)
                withMessage("Critical system error")
            }
            
            val crashEvent = mockDataFactory.createCrashEvent(exception) {
                withFatal(true)
                withThread("main")
            }
            
            val context = mockDataFactory.createExceptionContext {
                withUserId("test-user")
                withSessionId("test-session")
            }
            
            coEvery { crashReportingManager.reportException(exception, context, true) } returns Unit
            
            // When
            crashReportingManager.reportException(exception, context, true)
            
            // Then
            coVerify { crashReportingManager.reportException(exception, context, true) }
        }
        
        @Test
        @DisplayName("应该正确收集崩溃统计")
        fun `should collect crash statistics correctly`() = runTest {
            // Given
            val expectedStats = CrashStatistics(
                totalCrashes = 10,
                fatalCrashes = 3,
                uniqueUsers = 5,
                topExceptionType = "NetworkException",
                crashFreeRate = 95.0,
                timeRange = TimeRange(
                    startTime = System.currentTimeMillis() - 86400000L,
                    endTime = System.currentTimeMillis()
                )
            )
            
            coEvery { crashReportingManager.getCrashStatistics(any()) } returns expectedStats
            
            // When
            val stats = crashReportingManager.getCrashStatistics(expectedStats.timeRange)
            
            // Then
            stats.totalCrashes shouldBe 10
            stats.fatalCrashes shouldBe 3
            stats.uniqueUsers shouldBe 5
            stats.crashFreeRate shouldBe 95.0
        }
        
        @Test
        @DisplayName("应该正确处理用户操作日志")
        fun `should handle user action logs correctly`() = runTest {
            // Given
            val action = "button_click"
            val parameters = mapOf(
                "button_id" to "submit_button",
                "screen" to "login_screen",
                "timestamp" to System.currentTimeMillis()
            )
            
            every { crashReportingManager.logUserAction(action, parameters) } returns Unit
            
            // When
            crashReportingManager.logUserAction(action, parameters)
            
            // Then
            verify { crashReportingManager.logUserAction(action, parameters) }
        }
    }
    
    @Nested
    @DisplayName("异常监控测试")
    inner class ExceptionMonitoringTests {
        
        @Test
        @DisplayName("应该正确监控异常事件")
        fun `should monitor exception events correctly`() = runTest {
            // Given
            val exception = mockDataFactory.createQuesticleException()
            val monitoringEvent = MonitoringEvent.ExceptionOccurred(
                exception = exception,
                timestamp = System.currentTimeMillis(),
                context = mockDataFactory.createExceptionContext()
            )
            
            every { monitoringService.recordEvent(monitoringEvent) } returns Unit
            
            // When
            monitoringService.recordEvent(monitoringEvent)
            
            // Then
            verify { monitoringService.recordEvent(monitoringEvent) }
        }
        
        @Test
        @DisplayName("应该正确检测异常模式")
        fun `should detect exception patterns correctly`() = runTest {
            // Given
            val exceptions = listOf(
                mockDataFactory.createQuesticleException(type = ExceptionType.NETWORK),
                mockDataFactory.createQuesticleException(type = ExceptionType.NETWORK),
                mockDataFactory.createQuesticleException(type = ExceptionType.NETWORK)
            )
            
            val expectedPattern = ExceptionPattern(
                type = ExceptionType.NETWORK,
                frequency = 3,
                timeWindow = 60000L,
                severity = ErrorSeverity.HIGH,
                description = "Repeated network exceptions detected"
            )
            
            every { monitoringService.detectPatterns(exceptions) } returns listOf(expectedPattern)
            
            // When
            val patterns = monitoringService.detectPatterns(exceptions)
            
            // Then
            patterns.shouldNotBeEmpty()
            patterns.first().type shouldBe ExceptionType.NETWORK
            patterns.first().frequency shouldBe 3
        }
        
        @Test
        @DisplayName("应该正确生成异常趋势报告")
        fun `should generate exception trend reports correctly`() = runTest {
            // Given
            val timeRange = TimeRange(
                startTime = System.currentTimeMillis() - 86400000L,
                endTime = System.currentTimeMillis()
            )
            
            val expectedTrends = ExceptionTrends(
                timeRange = timeRange,
                totalExceptions = 50,
                exceptionsByType = mapOf(
                    ExceptionType.NETWORK to 20,
                    ExceptionType.DATABASE to 15,
                    ExceptionType.BUSINESS_LOGIC to 15
                ),
                severityDistribution = mapOf(
                    ErrorSeverity.LOW to 20,
                    ErrorSeverity.MEDIUM to 20,
                    ErrorSeverity.HIGH to 8,
                    ErrorSeverity.CRITICAL to 2
                ),
                trend = TrendDirection.STABLE
            )
            
            coEvery { monitoringService.generateTrends(timeRange) } returns expectedTrends
            
            // When
            val trends = monitoringService.generateTrends(timeRange)
            
            // Then
            trends.totalExceptions shouldBe 50
            trends.exceptionsByType[ExceptionType.NETWORK] shouldBe 20
            trends.trend shouldBe TrendDirection.STABLE
        }
    }
}
