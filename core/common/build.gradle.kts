plugins {
    id("questicle.android.library")
    id("questicle.android.library.compose")
    id("questicle.hilt")
    kotlin("plugin.serialization")
}

android {
    namespace = "com.yu.questicle.core.common"
}

dependencies {
    api(libs.kotlinx.serialization.json)

    // 添加数据库依赖以解决 SupabaseClient 问题
    implementation(project(":core:database"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)

    // 标准化测试依赖
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)

    // 修复缺失的Junit5依赖
    testImplementation(libs.junit5.api)
    testRuntimeOnly(libs.junit5.engine)
}
