plugins {
    id("questicle.android.library.compose")
    alias(libs.plugins.android.junit5)
}

android {
    namespace = "com.yu.questicle.core.designsystem"
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.compose.runtime)
    implementation(libs.androidx.compose.ui.graphics)
    implementation(libs.androidx.compose.ui.tooling.preview)
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.compose.material.icons.extended)

    // 标准化测试依赖 - 由 convention plugin 统一管理
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)

    // Compose测试依赖 - 由 convention plugin 统一管理
    testImplementation(libs.bundles.compose.testing)
    debugImplementation(libs.androidx.compose.ui.test.manifest)
}
