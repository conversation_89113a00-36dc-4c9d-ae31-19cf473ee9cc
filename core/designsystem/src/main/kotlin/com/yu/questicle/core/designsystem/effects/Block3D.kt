package com.yu.questicle.core.designsystem.effects

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme
import com.yu.questicle.core.designsystem.theme.TetrisColors

/**
 * 3D方块渲染效果 - 2025年设计趋势
 * 为俄罗斯方块游戏提供立体感
 */

// 俄罗斯方块类型枚举
enum class TetrisPieceType {
    I, J, L, O, S, T, Z, EMPTY
}

// 3D效果参数
data class Block3DStyle(
    val depth: Dp = 4.dp,
    val shadowIntensity: Float = 0.3f,
    val highlightIntensity: Float = 0.2f,
    val cornerRadius: Dp = 2.dp,
    val animationEnabled: Boolean = true
)

@Composable
fun TetrisBlock3D(
    pieceType: TetrisPieceType,
    modifier: Modifier = Modifier,
    style: Block3DStyle = Block3DStyle(),
    isAnimated: Boolean = false,
    animationDelay: Int = 0
) {
    // 动画状态
    val animatedScale by animateFloatAsState(
        targetValue = if (isAnimated) 1.0f else 0.8f,
        animationSpec = if (animationDelay > 0) {
            tween(
                durationMillis = 300,
                delayMillis = animationDelay,
                easing = FastOutSlowInEasing
            )
        } else {
            spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            )
        },
        label = "block_scale"
    )
    
    // 获取方块颜色
    val blockColor = getBlockColor(pieceType)
    
    if (pieceType == TetrisPieceType.EMPTY) {
        // 空方块 - 只显示网格
        Box(
            modifier = modifier
                .aspectRatio(1f)
                .background(
                    Color.Transparent,
                    RoundedCornerShape(style.cornerRadius)
                )
        )
    } else {
        // 3D方块渲染
        Canvas(
            modifier = modifier
                .aspectRatio(1f)
                .graphicsLayer {
                    scaleX = animatedScale
                    scaleY = animatedScale
                }
        ) {
            draw3DBlock(
                color = blockColor,
                style = style,
                size = size
            )
        }
    }
}

/**
 * 3D方块组合 - 用于显示完整的俄罗斯方块形状
 */
@Composable
fun TetrisPiece3D(
    pieceType: TetrisPieceType,
    rotation: Int = 0,
    modifier: Modifier = Modifier,
    blockSize: Dp = 24.dp,
    style: Block3DStyle = Block3DStyle(),
    isAnimated: Boolean = false
) {
    val pieceShape = getPieceShape(pieceType, rotation)
    
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(1.dp)
    ) {
        pieceShape.forEachIndexed { rowIndex, row ->
            Row(
                horizontalArrangement = Arrangement.spacedBy(1.dp)
            ) {
                row.forEachIndexed { colIndex, isBlock ->
                    TetrisBlock3D(
                        pieceType = if (isBlock) pieceType else TetrisPieceType.EMPTY,
                        modifier = Modifier.size(blockSize),
                        style = style,
                        isAnimated = isAnimated,
                        animationDelay = (rowIndex + colIndex) * 50
                    )
                }
            }
        }
    }
}

/**
 * 游戏板3D渲染
 */
@Composable
fun GameBoard3D(
    board: Array<Array<TetrisPieceType>>,
    modifier: Modifier = Modifier,
    blockSize: Dp = 20.dp,
    style: Block3DStyle = Block3DStyle(depth = 3.dp),
    showGrid: Boolean = true
) {
    Canvas(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(8.dp)
            )
            .padding(4.dp)
    ) {
        val boardWidth = board[0].size
        val boardHeight = board.size
        val blockSizePx = blockSize.toPx()
        
        // 绘制网格
        if (showGrid) {
            drawGrid(
                width = boardWidth,
                height = boardHeight,
                blockSize = blockSizePx
            )
        }
        
        // 绘制方块
        board.forEachIndexed { row, rowData ->
            rowData.forEachIndexed { col, pieceType ->
                if (pieceType != TetrisPieceType.EMPTY) {
                    val x = col * blockSizePx
                    val y = row * blockSizePx
                    
                    draw3DBlockAt(
                        color = getBlockColor(pieceType),
                        style = style,
                        position = Offset(x, y),
                        size = Size(blockSizePx, blockSizePx)
                    )
                }
            }
        }
    }
}

// 绘制3D方块的核心函数
private fun DrawScope.draw3DBlock(
    color: Color,
    style: Block3DStyle,
    size: Size
) {
    val depthPx = style.depth.toPx()
    val cornerRadiusPx = style.cornerRadius.toPx()
    
    // 主面 (正面)
    drawRoundRect(
        color = color,
        topLeft = Offset(0f, depthPx),
        size = Size(size.width - depthPx, size.height - depthPx),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(cornerRadiusPx)
    )
    
    // 顶面 (高光)
    val topPath = Path().apply {
        moveTo(0f, depthPx)
        lineTo(depthPx, 0f)
        lineTo(size.width, 0f)
        lineTo(size.width - depthPx, depthPx)
        close()
    }
    drawPath(
        path = topPath,
        color = color.copy(alpha = color.alpha * (1f + style.highlightIntensity))
    )
    
    // 右面 (阴影)
    val rightPath = Path().apply {
        moveTo(size.width - depthPx, depthPx)
        lineTo(size.width, 0f)
        lineTo(size.width, size.height - depthPx)
        lineTo(size.width - depthPx, size.height)
        close()
    }
    drawPath(
        path = rightPath,
        color = color.copy(alpha = color.alpha * (1f - style.shadowIntensity))
    )
    
    // 边框
    drawRoundRect(
        color = Color.Black.copy(alpha = 0.2f),
        topLeft = Offset(0f, depthPx),
        size = Size(size.width - depthPx, size.height - depthPx),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(cornerRadiusPx),
        style = Stroke(width = 1.dp.toPx())
    )
}

private fun DrawScope.draw3DBlockAt(
    color: Color,
    style: Block3DStyle,
    position: Offset,
    size: Size
) {
    drawIntoCanvas { canvas ->
        canvas.save()
        canvas.translate(position.x, position.y)
        draw3DBlock(color, style, size)
        canvas.restore()
    }
}

private fun DrawScope.drawGrid(
    width: Int,
    height: Int,
    blockSize: Float
) {
    val gridColor = Color.Gray.copy(alpha = 0.3f)
    val strokeWidth = 1.dp.toPx()
    
    // 垂直线
    for (i in 0..width) {
        val x = i * blockSize
        drawLine(
            color = gridColor,
            start = Offset(x, 0f),
            end = Offset(x, height * blockSize),
            strokeWidth = strokeWidth
        )
    }
    
    // 水平线
    for (i in 0..height) {
        val y = i * blockSize
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(width * blockSize, y),
            strokeWidth = strokeWidth
        )
    }
}

// 获取方块颜色
private fun getBlockColor(pieceType: TetrisPieceType): Color {
    return when (pieceType) {
        TetrisPieceType.I -> TetrisColors.IPiece
        TetrisPieceType.J -> TetrisColors.JPiece
        TetrisPieceType.L -> TetrisColors.LPiece
        TetrisPieceType.O -> TetrisColors.OPiece
        TetrisPieceType.S -> TetrisColors.SPiece
        TetrisPieceType.T -> TetrisColors.TPiece
        TetrisPieceType.Z -> TetrisColors.ZPiece
        TetrisPieceType.EMPTY -> Color.Transparent
    }
}

// 获取方块形状 (简化版本)
private fun getPieceShape(pieceType: TetrisPieceType, rotation: Int): Array<Array<Boolean>> {
    return when (pieceType) {
        TetrisPieceType.I -> arrayOf(
            arrayOf(true, true, true, true)
        )
        TetrisPieceType.O -> arrayOf(
            arrayOf(true, true),
            arrayOf(true, true)
        )
        TetrisPieceType.T -> arrayOf(
            arrayOf(false, true, false),
            arrayOf(true, true, true)
        )
        TetrisPieceType.L -> arrayOf(
            arrayOf(true, false),
            arrayOf(true, false),
            arrayOf(true, true)
        )
        TetrisPieceType.J -> arrayOf(
            arrayOf(false, true),
            arrayOf(false, true),
            arrayOf(true, true)
        )
        TetrisPieceType.S -> arrayOf(
            arrayOf(false, true, true),
            arrayOf(true, true, false)
        )
        TetrisPieceType.Z -> arrayOf(
            arrayOf(true, true, false),
            arrayOf(false, true, true)
        )
        TetrisPieceType.EMPTY -> arrayOf(arrayOf(false))
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun Block3DPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 单个3D方块
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TetrisPieceType.values().filter { it != TetrisPieceType.EMPTY }.forEach { type ->
                    TetrisBlock3D(
                        pieceType = type,
                        modifier = Modifier.size(32.dp),
                        isAnimated = true
                    )
                }
            }
            
            // 完整的俄罗斯方块形状
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                TetrisPiece3D(
                    pieceType = TetrisPieceType.T,
                    isAnimated = true
                )
                
                TetrisPiece3D(
                    pieceType = TetrisPieceType.L,
                    isAnimated = true
                )
                
                TetrisPiece3D(
                    pieceType = TetrisPieceType.I,
                    isAnimated = true
                )
            }
        }
    }
}
