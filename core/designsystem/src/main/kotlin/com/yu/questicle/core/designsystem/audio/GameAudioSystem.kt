package com.yu.questicle.core.designsystem.audio

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.SoundPool
import com.yu.questicle.core.designsystem.game.TetrisAction

/**
 * 游戏音效系统 - 2025年设计规范
 * 提供完整的游戏音效管理和播放功能
 */

// 音效类型枚举
enum class GameSoundType {
    // 基础操作音效
    PIECE_MOVE,         // 方块移动
    PIECE_ROTATE,       // 方块旋转
    PIECE_DROP,         // 方块下降
    PIECE_LOCK,         // 方块锁定
    
    // 消除音效
    LINE_CLEAR_SINGLE,  // 单行消除
    LINE_CLEAR_DOUBLE,  // 双行消除
    LINE_CLEAR_TRIPLE,  // 三行消除
    LINE_CLEAR_TETRIS,  // 四行消除(Tetris)
    
    // 特殊音效
    LEVEL_UP,           // 升级
    GAME_OVER,          // 游戏结束
    PAUSE,              // 暂停
    RESUME,             // 继续
    
    // UI交互音效
    BUTTON_CLICK,       // 按钮点击
    MENU_NAVIGATE,      // 菜单导航
    ACHIEVEMENT_UNLOCK, // 成就解锁
    
    // 反馈音效
    SUCCESS,            // 成功
    ERROR,              // 错误
    WARNING             // 警告
}

// 音效设置数据类
data class AudioSettings(
    val soundEnabled: Boolean = true,
    val musicEnabled: Boolean = true,
    val soundVolume: Float = 0.8f,
    val musicVolume: Float = 0.6f,
    val hapticEnabled: Boolean = true
)

/**
 * 游戏音效管理器
 */
class GameAudioManager(private val context: Context) {
    private var soundPool: SoundPool? = null
    private val soundMap = mutableMapOf<GameSoundType, Int>()
    private var audioSettings = AudioSettings()
    
    init {
        initializeSoundPool()
    }
    
    private fun initializeSoundPool() {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_GAME)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build()
        
        soundPool = SoundPool.Builder()
            .setMaxStreams(10)
            .setAudioAttributes(audioAttributes)
            .build()
        
        loadSounds()
    }
    
    private fun loadSounds() {
        try {
            // 尝试加载实际的音效文件
            loadRealSounds()
        } catch (e: Exception) {
            // 如果音效文件不存在，使用模拟方式
            loadMockSounds()
        }
    }

    private fun loadRealSounds() {
        // 加载UI音效 - 使用现有的音效文件
        loadSoundIfExists(GameSoundType.BUTTON_CLICK, "click1")
        loadSoundIfExists(GameSoundType.MENU_NAVIGATE, "rollover1")
        loadSoundIfExists(GameSoundType.SUCCESS, "switch1")
        loadSoundIfExists(GameSoundType.ERROR, "switch9")
        loadSoundIfExists(GameSoundType.WARNING, "switch5")

        // 游戏音效 - 使用UI音效作为替代
        loadSoundIfExists(GameSoundType.PIECE_MOVE, "click2")
        loadSoundIfExists(GameSoundType.PIECE_ROTATE, "click3")
        loadSoundIfExists(GameSoundType.PIECE_DROP, "click4")
        loadSoundIfExists(GameSoundType.PIECE_LOCK, "click5")

        // 特效音效 - 使用开关音效作为替代
        loadSoundIfExists(GameSoundType.LINE_CLEAR_SINGLE, "switch2")
        loadSoundIfExists(GameSoundType.LINE_CLEAR_DOUBLE, "switch3")
        loadSoundIfExists(GameSoundType.LINE_CLEAR_TRIPLE, "switch4")
        loadSoundIfExists(GameSoundType.LINE_CLEAR_TETRIS, "switch6")
        loadSoundIfExists(GameSoundType.LEVEL_UP, "switch7")
        loadSoundIfExists(GameSoundType.GAME_OVER, "switch8")
        loadSoundIfExists(GameSoundType.PAUSE, "rollover2")
        loadSoundIfExists(GameSoundType.RESUME, "rollover3")
        loadSoundIfExists(GameSoundType.ACHIEVEMENT_UNLOCK, "switch10")
    }

    private fun loadSoundIfExists(soundType: GameSoundType, resourceName: String) {
        val resourceId = context.resources.getIdentifier(resourceName, "raw", context.packageName)
        if (resourceId != 0) {
            soundMap[soundType] = soundPool?.load(context, resourceId, 1) ?: 0
        } else {
            // 如果资源不存在，使用模拟ID
            soundMap[soundType] = soundType.ordinal
        }
    }

    private fun loadMockSounds() {
        // 模拟音效加载 - 用于开发阶段
        GameSoundType.values().forEach { soundType ->
            soundMap[soundType] = soundType.ordinal
        }
    }
    
    /**
     * 播放音效
     */
    fun playSound(soundType: GameSoundType, volume: Float = 1.0f) {
        if (!audioSettings.soundEnabled) return
        
        val soundId = soundMap[soundType] ?: return
        val finalVolume = volume * audioSettings.soundVolume
        
        soundPool?.play(soundId, finalVolume, finalVolume, 1, 0, 1.0f)
    }
    
    /**
     * 播放游戏操作音效
     */
    fun playGameAction(action: TetrisAction) {
        when (action) {
            TetrisAction.MOVE_LEFT, TetrisAction.MOVE_RIGHT -> 
                playSound(GameSoundType.PIECE_MOVE, 0.6f)
            TetrisAction.ROTATE -> 
                playSound(GameSoundType.PIECE_ROTATE, 0.7f)
            TetrisAction.SOFT_DROP -> 
                playSound(GameSoundType.PIECE_DROP, 0.5f)
            TetrisAction.HARD_DROP -> 
                playSound(GameSoundType.PIECE_LOCK, 0.8f)
            TetrisAction.PAUSE -> 
                playSound(GameSoundType.PAUSE)
            TetrisAction.RESUME -> 
                playSound(GameSoundType.RESUME)
            else -> {}
        }
    }
    
    /**
     * 播放行消除音效
     */
    fun playLineClear(lines: Int) {
        val soundType = when (lines) {
            1 -> GameSoundType.LINE_CLEAR_SINGLE
            2 -> GameSoundType.LINE_CLEAR_DOUBLE
            3 -> GameSoundType.LINE_CLEAR_TRIPLE
            4 -> GameSoundType.LINE_CLEAR_TETRIS
            else -> return
        }
        
        val volume = when (lines) {
            1 -> 0.7f
            2 -> 0.8f
            3 -> 0.9f
            4 -> 1.0f
            else -> 0.7f
        }
        
        playSound(soundType, volume)
    }
    
    /**
     * 更新音效设置
     */
    fun updateSettings(settings: AudioSettings) {
        audioSettings = settings
    }
    
    /**
     * 释放资源
     */
    fun release() {
        soundPool?.release()
        soundPool = null
        soundMap.clear()
    }
}

/**
 * Compose音效Hook
 */
@Composable
fun rememberGameAudioManager(): GameAudioManager {
    val context = LocalContext.current
    return remember { GameAudioManager(context) }
}

/**
 * 音效设置组件
 */
@Composable
fun AudioSettingsPanel(
    settings: AudioSettings,
    onSettingsChange: (AudioSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "音效设置",
                style = MaterialTheme.typography.titleMedium
            )
            
            // 音效开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("音效")
                Switch(
                    checked = settings.soundEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(soundEnabled = it))
                    }
                )
            }
            
            // 音效音量
            if (settings.soundEnabled) {
                Column {
                    Text(
                        text = "音效音量: ${(settings.soundVolume * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Slider(
                        value = settings.soundVolume,
                        onValueChange = { 
                            onSettingsChange(settings.copy(soundVolume = it))
                        },
                        valueRange = 0f..1f
                    )
                }
            }
            
            // 背景音乐开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("背景音乐")
                Switch(
                    checked = settings.musicEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(musicEnabled = it))
                    }
                )
            }
            
            // 背景音乐音量
            if (settings.musicEnabled) {
                Column {
                    Text(
                        text = "音乐音量: ${(settings.musicVolume * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Slider(
                        value = settings.musicVolume,
                        onValueChange = { 
                            onSettingsChange(settings.copy(musicVolume = it))
                        },
                        valueRange = 0f..1f
                    )
                }
            }
            
            // 触觉反馈开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("触觉反馈")
                Switch(
                    checked = settings.hapticEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(hapticEnabled = it))
                    }
                )
            }
        }
    }
}
