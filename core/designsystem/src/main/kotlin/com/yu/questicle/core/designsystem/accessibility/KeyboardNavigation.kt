package com.yu.questicle.core.designsystem.accessibility

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.game.TetrisAction

/**
 * 键盘导航支持系统
 * 提供完整的键盘和外接控制器支持
 */

// 键盘映射配置
data class KeyboardMapping(
    val moveLeft: Key = Key.A,
    val moveRight: Key = Key.D,
    val rotate: Key = Key.W,
    val softDrop: Key = Key.S,
    val hardDrop: Key = Key.Spacebar,
    val hold: Key = Key.C,
    val pause: Key = Key.P,
    val restart: Key = Key.R
)

// 游戏手柄映射配置
data class GamepadMapping(
    val moveLeft: String = "DPAD_LEFT",
    val moveRight: String = "DPAD_RIGHT",
    val rotate: String = "BUTTON_A",
    val softDrop: String = "DPAD_DOWN",
    val hardDrop: String = "BUTTON_B",
    val hold: String = "BUTTON_X",
    val pause: String = "BUTTON_START",
    val restart: String = "BUTTON_SELECT"
)

/**
 * 键盘导航管理器
 */
class KeyboardNavigationManager {
    private var keyboardMapping = KeyboardMapping()
    private var gamepadMapping = GamepadMapping()
    private var onGameAction: ((TetrisAction) -> Unit)? = null
    
    fun setKeyboardMapping(mapping: KeyboardMapping) {
        keyboardMapping = mapping
    }
    
    fun setGamepadMapping(mapping: GamepadMapping) {
        gamepadMapping = mapping
    }
    
    fun setGameActionHandler(handler: (TetrisAction) -> Unit) {
        onGameAction = handler
    }
    
    /**
     * 处理键盘输入
     */
    fun handleKeyEvent(keyEvent: KeyEvent): Boolean {
        if (keyEvent.type != KeyEventType.KeyDown) return false
        
        val action = when (keyEvent.key) {
            keyboardMapping.moveLeft -> TetrisAction.MOVE_LEFT
            keyboardMapping.moveRight -> TetrisAction.MOVE_RIGHT
            keyboardMapping.rotate -> TetrisAction.ROTATE
            keyboardMapping.softDrop -> TetrisAction.SOFT_DROP
            keyboardMapping.hardDrop -> TetrisAction.HARD_DROP
            keyboardMapping.hold -> TetrisAction.HOLD
            keyboardMapping.pause -> TetrisAction.PAUSE
            keyboardMapping.restart -> TetrisAction.RESTART
            else -> null
        }
        
        action?.let { 
            onGameAction?.invoke(it)
            return true
        }
        
        return false
    }
    
    /**
     * 获取键盘快捷键描述
     */
    fun getKeyboardShortcuts(): List<KeyboardShortcut> {
        return listOf(
            KeyboardShortcut("向左移动", keyboardMapping.moveLeft.toString()),
            KeyboardShortcut("向右移动", keyboardMapping.moveRight.toString()),
            KeyboardShortcut("旋转方块", keyboardMapping.rotate.toString()),
            KeyboardShortcut("软降", keyboardMapping.softDrop.toString()),
            KeyboardShortcut("硬降", keyboardMapping.hardDrop.toString()),
            KeyboardShortcut("暂存方块", keyboardMapping.hold.toString()),
            KeyboardShortcut("暂停游戏", keyboardMapping.pause.toString()),
            KeyboardShortcut("重新开始", keyboardMapping.restart.toString())
        )
    }
}

// 键盘快捷键数据类
data class KeyboardShortcut(
    val description: String,
    val key: String
)

/**
 * 焦点指示器修饰符
 */
fun Modifier.focusIndicator(
    color: Color = Color.Blue,
    width: Float = 2f
): Modifier = this.then(
    Modifier
        .onFocusChanged { focusState ->
            // 焦点状态变化处理
        }
        .drawBehind {
            // 绘制焦点指示器 - 简化实现
            drawRoundRect(
                color = color,
                style = androidx.compose.ui.graphics.drawscope.Stroke(width = width),
                cornerRadius = androidx.compose.ui.geometry.CornerRadius(8.dp.toPx())
            )
        }
)

/**
 * 可键盘导航的游戏控制按钮
 */
@Composable
fun KeyboardNavigableButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    shortcutKey: String? = null
) {
    val focusRequester = remember { FocusRequester() }
    var isFocused by remember { mutableStateOf(false) }
    
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier
            .focusRequester(focusRequester)
            .focusable()
            .onFocusChanged { focusState ->
                isFocused = focusState.isFocused
            }
            .onKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyDown && 
                    (keyEvent.key == Key.Enter || keyEvent.key == Key.Spacebar)) {
                    onClick()
                    true
                } else {
                    false
                }
            }
            .then(
                if (isFocused) {
                    Modifier.focusIndicator()
                } else {
                    Modifier
                }
            ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column {
            Text(text = text)
            shortcutKey?.let {
                Text(
                    text = "($it)",
                    style = MaterialTheme.typography.labelSmall
                )
            }
        }
    }
}

/**
 * 游戏键盘控制面板
 */
@Composable
fun GameKeyboardControlPanel(
    onAction: (TetrisAction) -> Unit,
    keyboardMapping: KeyboardMapping = KeyboardMapping(),
    modifier: Modifier = Modifier
) {
    val navigationManager = remember { KeyboardNavigationManager() }
    
    LaunchedEffect(onAction) {
        navigationManager.setGameActionHandler(onAction)
    }
    
    Card(
        modifier = modifier
            .onKeyEvent { keyEvent ->
                navigationManager.handleKeyEvent(keyEvent)
            }
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "键盘控制",
                style = MaterialTheme.typography.titleMedium
            )
            
            // 移动控制
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "左移",
                    onClick = { onAction(TetrisAction.MOVE_LEFT) },
                    shortcutKey = keyboardMapping.moveLeft.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                KeyboardNavigableButton(
                    text = "右移",
                    onClick = { onAction(TetrisAction.MOVE_RIGHT) },
                    shortcutKey = keyboardMapping.moveRight.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 旋转和下降控制
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "旋转",
                    onClick = { onAction(TetrisAction.ROTATE) },
                    shortcutKey = keyboardMapping.rotate.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                KeyboardNavigableButton(
                    text = "软降",
                    onClick = { onAction(TetrisAction.SOFT_DROP) },
                    shortcutKey = keyboardMapping.softDrop.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 特殊控制
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "硬降",
                    onClick = { onAction(TetrisAction.HARD_DROP) },
                    shortcutKey = keyboardMapping.hardDrop.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                KeyboardNavigableButton(
                    text = "暂存",
                    onClick = { onAction(TetrisAction.HOLD) },
                    shortcutKey = keyboardMapping.hold.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 游戏控制
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "暂停",
                    onClick = { onAction(TetrisAction.PAUSE) },
                    shortcutKey = keyboardMapping.pause.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                KeyboardNavigableButton(
                    text = "重启",
                    onClick = { onAction(TetrisAction.RESTART) },
                    shortcutKey = keyboardMapping.restart.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 键盘快捷键帮助面板
 */
@Composable
fun KeyboardShortcutsHelp(
    shortcuts: List<KeyboardShortcut>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "键盘快捷键",
                style = MaterialTheme.typography.titleMedium
            )
            
            shortcuts.forEach { shortcut ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = shortcut.description,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Surface(
                        color = MaterialTheme.colorScheme.primaryContainer,
                        shape = RoundedCornerShape(4.dp)
                    ) {
                        Text(
                            text = shortcut.key,
                            style = MaterialTheme.typography.labelSmall,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 焦点管理组件
 */
@Composable
fun FocusManagement(
    content: @Composable () -> Unit
) {
    val focusManager = LocalFocusManager.current
    
    Box(
        modifier = Modifier
            .onKeyEvent { keyEvent ->
                when {
                    keyEvent.key == Key.Tab && keyEvent.type == KeyEventType.KeyDown -> {
                        if (keyEvent.isShiftPressed) {
                            focusManager.moveFocus(FocusDirection.Previous)
                        } else {
                            focusManager.moveFocus(FocusDirection.Next)
                        }
                        true
                    }
                    keyEvent.key == Key.Escape && keyEvent.type == KeyEventType.KeyDown -> {
                        focusManager.clearFocus()
                        true
                    }
                    else -> false
                }
            }
    ) {
        content()
    }
}

/**
 * 键盘导航设置面板
 */
@Composable
fun KeyboardNavigationSettings(
    mapping: KeyboardMapping,
    onMappingChange: (KeyboardMapping) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "键盘映射设置",
                style = MaterialTheme.typography.titleMedium
            )
            
            Text(
                text = "点击按钮后按下要映射的键",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 这里可以添加键盘映射自定义功能
            // 由于复杂性，暂时显示当前映射
            
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyMappingItem("向左移动", mapping.moveLeft.toString())
                KeyMappingItem("向右移动", mapping.moveRight.toString())
                KeyMappingItem("旋转方块", mapping.rotate.toString())
                KeyMappingItem("软降", mapping.softDrop.toString())
                KeyMappingItem("硬降", mapping.hardDrop.toString())
                KeyMappingItem("暂存方块", mapping.hold.toString())
                KeyMappingItem("暂停游戏", mapping.pause.toString())
                KeyMappingItem("重新开始", mapping.restart.toString())
            }
        }
    }
}

@Composable
private fun KeyMappingItem(
    action: String,
    key: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = action,
            style = MaterialTheme.typography.bodyMedium
        )
        
        Surface(
            color = MaterialTheme.colorScheme.secondaryContainer,
            shape = RoundedCornerShape(4.dp)
        ) {
            Text(
                text = key,
                style = MaterialTheme.typography.labelMedium,
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
            )
        }
    }
}

/**
 * Compose键盘导航Hook
 */
@Composable
fun rememberKeyboardNavigationManager(): KeyboardNavigationManager {
    return remember { KeyboardNavigationManager() }
}
