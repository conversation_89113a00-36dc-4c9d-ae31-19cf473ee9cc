package com.yu.questicle.core.designsystem.theme

import android.app.Activity
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * Questicle 2025年设计系统主题
 * 融合最新设计趋势，支持多种主题模式
 */

// 主题模式枚举
enum class ThemeMode {
    LIGHT,          // 浅色模式
    DARK,           // 深色模式
    LOW_LIGHT,      // 低光模式 (2025年新趋势)
    SYSTEM,         // 跟随系统
    AUTO            // 智能切换
}

@Composable
fun QuesticleTheme(
    themeMode: ThemeMode = ThemeMode.SYSTEM,
    dynamicColor: Boolean = true,
    lowLightMode: Boolean = false,
    content: @Composable () -> Unit
) {
    val isDarkTheme = when (themeMode) {
        ThemeMode.LIGHT -> false
        ThemeMode.DARK -> true
        ThemeMode.LOW_LIGHT -> true
        ThemeMode.SYSTEM -> isSystemInDarkTheme()
        ThemeMode.AUTO -> isSystemInDarkTheme() // 可以扩展为智能检测
    }

    val colorScheme = when {
        lowLightMode || themeMode == ThemeMode.LOW_LIGHT -> QuesticleLowLightColorScheme
        dynamicColor -> {
            val context = LocalContext.current
            if (isDarkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        isDarkTheme -> QuesticleDarkColorScheme2025
        else -> QuesticleLightColorScheme2025
    }

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !isDarkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = HarmonyOSTypography, // 使用鸿蒙字体系统
        shapes = QuesticleShapes,
        content = content
    )
}

/**
 * 2025年浅色主题配色方案
 */
private val QuesticleLightColorScheme2025 = lightColorScheme(
    primary = PrimaryColors.Primary,
    onPrimary = PrimaryColors.OnPrimary,
    primaryContainer = PrimaryColors.PrimaryContainer,
    onPrimaryContainer = PrimaryColors.OnPrimaryContainer,
    secondary = SecondaryColors.Secondary,
    onSecondary = SecondaryColors.OnSecondary,
    secondaryContainer = SecondaryColors.SecondaryContainer,
    onSecondaryContainer = SecondaryColors.OnSecondaryContainer,
    tertiary = TertiaryColors.Tertiary,
    onTertiary = TertiaryColors.OnTertiary,
    tertiaryContainer = TertiaryColors.TertiaryContainer,
    onTertiaryContainer = TertiaryColors.OnTertiaryContainer,
    error = FunctionalColors.Error,
    onError = FunctionalColors.OnError,
    errorContainer = FunctionalColors.ErrorContainer,
    onErrorContainer = FunctionalColors.OnErrorContainer
)

/**
 * 2025年深色主题配色方案
 */
private val QuesticleDarkColorScheme2025 = darkColorScheme(
    primary = PrimaryColors.Primary,
    onPrimary = PrimaryColors.OnPrimary,
    primaryContainer = PrimaryColors.PrimaryContainer,
    onPrimaryContainer = PrimaryColors.OnPrimaryContainer,
    secondary = SecondaryColors.Secondary,
    onSecondary = SecondaryColors.OnSecondary,
    secondaryContainer = SecondaryColors.SecondaryContainer,
    onSecondaryContainer = SecondaryColors.OnSecondaryContainer,
    tertiary = TertiaryColors.Tertiary,
    onTertiary = TertiaryColors.OnTertiary,
    tertiaryContainer = TertiaryColors.TertiaryContainer,
    onTertiaryContainer = TertiaryColors.OnTertiaryContainer,
    error = FunctionalColors.Error,
    onError = FunctionalColors.OnError,
    errorContainer = FunctionalColors.ErrorContainer,
    onErrorContainer = FunctionalColors.OnErrorContainer
)

/**
 * 2025年低光模式配色方案 - 减少视觉疲劳
 */
private val QuesticleLowLightColorScheme = darkColorScheme(
    primary = LowLightColors.Primary,
    secondary = LowLightColors.Secondary,
    background = LowLightColors.Background,
    surface = LowLightColors.Surface,
    onSurface = LowLightColors.OnSurface,
    outline = LowLightColors.Outline,
    surfaceVariant = LowLightColors.SurfaceVariant,
    onSurfaceVariant = LowLightColors.OnSurfaceVariant
)
