package com.yu.questicle.core.designsystem.tokens

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Questicle 设计令牌系统
 * 统一管理设计系统中的所有设计决策
 */

// ============================================================================
// 间距系统 (Spacing System) - 8dp基准
// ============================================================================
object Spacing {
    val None = 0.dp
    val ExtraSmall = 4.dp      // 1/2 基准
    val Small = 8.dp           // 1x 基准
    val Medium = 16.dp         // 2x 基准
    val Large = 24.dp          // 3x 基准
    val ExtraLarge = 32.dp     // 4x 基准
    val Huge = 48.dp           // 6x 基准
    val Massive = 64.dp        // 8x 基准
    
    // 特殊间距
    val Tiny = 2.dp            // 极小间距
    val Component = 12.dp      // 组件内间距
    val Section = 20.dp        // 区域间距
    val Page = 40.dp           // 页面间距
}

// ============================================================================
// 圆角系统 (Corner Radius System)
// ============================================================================
object CornerRadius {
    val None = 0.dp
    val Small = 4.dp
    val Medium = 8.dp
    val Large = 12.dp
    val ExtraLarge = 16.dp
    val Huge = 24.dp
    val Circle = 50.dp         // 圆形
    
    // 组件专用圆角
    val Button = 8.dp
    val Card = 12.dp
    val Dialog = 16.dp
    val BottomSheet = 16.dp
}

// ============================================================================
// 阴影系统 (Elevation System)
// ============================================================================
object Elevation {
    val Level0 = 0.dp          // 无阴影
    val Level1 = 1.dp          // 轻微阴影
    val Level2 = 3.dp          // 标准阴影
    val Level3 = 6.dp          // 中等阴影
    val Level4 = 8.dp          // 较强阴影
    val Level5 = 12.dp         // 强阴影
    val Level6 = 16.dp         // 极强阴影
    
    // 组件专用阴影
    val Button = Level1
    val Card = Level2
    val FloatingActionButton = Level3
    val NavigationBar = Level2
    val AppBar = Level1
    val Dialog = Level5
    val BottomSheet = Level4
}

// ============================================================================
// 边框系统 (Border System)
// ============================================================================
object BorderWidth {
    val None = 0.dp
    val Thin = 1.dp
    val Medium = 2.dp
    val Thick = 4.dp
    val ExtraThick = 6.dp
    
    // 组件专用边框
    val Button = Thin
    val Card = Thin
    val TextField = Medium
    val Divider = Thin
}

// ============================================================================
// 尺寸系统 (Size System)
// ============================================================================
object Size {
    // 图标尺寸
    object Icon {
        val Small = 16.dp
        val Medium = 24.dp
        val Large = 32.dp
        val ExtraLarge = 48.dp
        val Huge = 64.dp
    }
    
    // 按钮尺寸
    object Button {
        val Small = 32.dp
        val Medium = 40.dp
        val Large = 48.dp
        val ExtraLarge = 56.dp
    }
    
    // 触摸目标尺寸
    object TouchTarget {
        val Minimum = 48.dp        // 最小触摸目标
        val Recommended = 56.dp    // 推荐触摸目标
        val Comfortable = 64.dp    // 舒适触摸目标
    }
    
    // 头像尺寸
    object Avatar {
        val Small = 24.dp
        val Medium = 32.dp
        val Large = 48.dp
        val ExtraLarge = 64.dp
        val Huge = 96.dp
    }
}

// ============================================================================
// 动画系统 (Animation System)
// ============================================================================
object AnimationDuration {
    const val Instant = 0
    const val Quick = 150        // 快速动画
    const val Normal = 300       // 标准动画
    const val Slow = 500         // 慢速动画
    const val Dramatic = 800     // 戏剧性动画
    const val ExtraSlow = 1200   // 极慢动画
    
    // 组件专用动画时长
    const val ButtonPress = Quick
    const val CardHover = Normal
    const val PageTransition = Normal
    const val DialogAppear = Normal
    const val BottomSheetSlide = Normal
}

// ============================================================================
// 透明度系统 (Alpha System)
// ============================================================================
object Alpha {
    const val Invisible = 0.0f
    const val Disabled = 0.38f
    const val Medium = 0.6f
    const val High = 0.87f
    const val Opaque = 1.0f
    
    // 状态透明度
    const val Hover = 0.08f
    const val Focus = 0.12f
    const val Pressed = 0.16f
    const val Selected = 0.12f
    const val Activated = 0.12f
    
    // 毛玻璃效果透明度
    const val GlassLight = 0.1f
    const val GlassMedium = 0.2f
    const val GlassHeavy = 0.3f
}

// ============================================================================
// Z轴层级系统 (Z-Index System)
// ============================================================================
object ZIndex {
    const val Background = 0f
    const val Content = 1f
    const val Overlay = 2f
    const val FloatingActionButton = 3f
    const val NavigationBar = 4f
    const val AppBar = 5f
    const val Snackbar = 6f
    const val Dialog = 7f
    const val Tooltip = 8f
    const val DropdownMenu = 9f
    const val Modal = 10f
}

// ============================================================================
// 断点系统 (Breakpoint System)
// ============================================================================
object Breakpoints {
    val Compact = 0.dp..599.dp        // 手机竖屏
    val Medium = 600.dp..839.dp       // 手机横屏/小平板
    val Expanded = 840.dp..1199.dp    // 平板
    val Large = 1200.dp..1599.dp      // 桌面
    val ExtraLarge = 1600.dp..Int.MAX_VALUE.dp // 大屏桌面
    
    // 断点值
    val CompactMax = 599.dp
    val MediumMin = 600.dp
    val MediumMax = 839.dp
    val ExpandedMin = 840.dp
    val ExpandedMax = 1199.dp
    val LargeMin = 1200.dp
    val LargeMax = 1599.dp
    val ExtraLargeMin = 1600.dp
}

// ============================================================================
// 网格系统 (Grid System)
// ============================================================================
object Grid {
    // 列数
    const val CompactColumns = 4
    const val MediumColumns = 8
    const val ExpandedColumns = 12
    
    // 边距
    val CompactMargin = 16.dp
    val MediumMargin = 24.dp
    val ExpandedMargin = 32.dp
    
    // 间隙
    val Gutter = 16.dp
}

// ============================================================================
// Bento网格系统 (Bento Grid System) - 2025年趋势
// ============================================================================
object BentoGrid {
    // 基础网格列数
    const val BaseColumns = 4
    
    // 网格项目跨度
    enum class Span(val value: Int) {
        Single(1),      // 1列
        Double(2),      // 2列
        Triple(3),      // 3列
        Full(4)         // 4列
    }
    
    // 网格项目高度
    val SmallHeight = 80.dp
    val MediumHeight = 120.dp
    val LargeHeight = 160.dp
    val ExtraLargeHeight = 200.dp
}

// ============================================================================
// 游戏专用设计令牌 (Game-Specific Tokens)
// ============================================================================
object GameTokens {
    // 俄罗斯方块网格
    val BlockSize = 24.dp
    val GridLineWidth = 1.dp
    val BoardPadding = 8.dp
    
    // 游戏界面间距
    val GamePadding = 16.dp
    val InfoPanelSpacing = 12.dp
    val ControlPanelSpacing = 8.dp
    
    // 3D效果参数
    val Block3DDepth = 4.dp
    val ShadowIntensity = 0.3f
    val HighlightIntensity = 0.2f
    
    // 动画参数
    const val PieceDropDuration = 500
    const val LineClearDuration = 300
    const val LevelUpDuration = 800
}

// ============================================================================
// 无障碍设计令牌 (Accessibility Tokens)
// ============================================================================
object AccessibilityTokens {
    // 对比度要求
    const val MinContrastNormal = 4.5f    // 正常文字最小对比度
    const val MinContrastLarge = 3.0f     // 大文字最小对比度
    const val MinContrastIcon = 3.0f      // 图标最小对比度
    
    // 触摸目标
    val MinTouchTarget = 48.dp            // 最小触摸目标
    val RecommendedTouchTarget = 56.dp    // 推荐触摸目标
    val GameControlTarget = 64.dp         // 游戏控制按钮
    
    // 焦点指示器
    val FocusIndicatorWidth = 2.dp
    val FocusIndicatorOffset = 2.dp
}

// ============================================================================
// 性能相关令牌 (Performance Tokens)
// ============================================================================
object PerformanceTokens {
    // 渲染性能目标
    const val TargetFPS = 60
    const val MaxRenderTime = 16 // 毫秒
    
    // 内存使用目标
    const val MaxMemoryUsage = 100 // MB
    const val MaxImageCacheSize = 50 // MB
    
    // 动画性能
    const val MaxConcurrentAnimations = 5
    const val AnimationFrameSkipThreshold = 2
}
