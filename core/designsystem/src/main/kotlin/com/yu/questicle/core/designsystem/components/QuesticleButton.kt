package com.yu.questicle.core.designsystem.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.*


/**
 * Questicle 按钮组件
 * 支持多种样式和尺寸，融合2025年设计趋势
 */

// 按钮样式枚举
enum class ButtonStyle {
    Primary,        // 主要按钮 - 品牌色
    Secondary,      // 次要按钮 - 辅助色
    Tertiary,       // 第三按钮 - 边框样式
    Ghost,          // 幽灵按钮 - 透明背景
    Danger,         // 危险按钮 - 错误色
    Success         // 成功按钮 - 成功色
}

// 按钮尺寸枚举
enum class ButtonSize {
    Small,          // 32dp 高度
    Medium,         // 40dp 高度
    Large,          // 48dp 高度
    ExtraLarge      // 56dp 高度
}

@Composable
fun QuesticleButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    style: ButtonStyle = ButtonStyle.Primary,
    size: ButtonSize = ButtonSize.Medium,
    icon: ImageVector? = null,
    enabled: Boolean = true,
    loading: Boolean = false
) {
    // 微交互动画状态
    var isPressed by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    
    // 按钮缩放动画
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "button_scale"
    )
    
    // 按钮高度
    val buttonHeight = when (size) {
        ButtonSize.Small -> 32.dp
        ButtonSize.Medium -> 40.dp
        ButtonSize.Large -> 48.dp
        ButtonSize.ExtraLarge -> 56.dp
    }
    
    // 按钮颜色
    val buttonColors = getButtonColors(style)
    
    // 按钮内容
    val buttonContent: @Composable RowScope.() -> Unit = {
        if (loading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = buttonColors.contentColor
            )
        } else {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = text,
                style = when (size) {
                    ButtonSize.Small -> MaterialTheme.typography.labelMedium
                    ButtonSize.Medium -> MaterialTheme.typography.labelLarge
                    ButtonSize.Large -> MaterialTheme.typography.titleSmall
                    ButtonSize.ExtraLarge -> MaterialTheme.typography.titleMedium
                }
            )
        }
    }
    
    when (style) {
        ButtonStyle.Primary -> {
            Button(
                onClick = {
                    isPressed = true
                    onClick()
                    isPressed = false
                },
                modifier = modifier
                    .height(buttonHeight)
                    .scale(scale),
                enabled = enabled && !loading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = buttonColors.containerColor,
                    contentColor = buttonColors.contentColor,
                    disabledContainerColor = buttonColors.containerColor.copy(alpha = 0.38f),
                    disabledContentColor = buttonColors.contentColor.copy(alpha = 0.38f)
                ),
                shape = RoundedCornerShape(8.dp),
                interactionSource = interactionSource,
                content = buttonContent
            )
        }
        
        ButtonStyle.Secondary -> {
            OutlinedButton(
                onClick = {
                    isPressed = true
                    onClick()
                    isPressed = false
                },
                modifier = modifier
                    .height(buttonHeight)
                    .scale(scale),
                enabled = enabled && !loading,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = buttonColors.contentColor,
                    disabledContentColor = buttonColors.contentColor.copy(alpha = 0.38f)
                ),
                border = BorderStroke(
                    width = 1.dp,
                    color = buttonColors.contentColor
                ),
                shape = RoundedCornerShape(8.dp),
                interactionSource = interactionSource,
                content = buttonContent
            )
        }
        
        ButtonStyle.Tertiary, ButtonStyle.Ghost -> {
            TextButton(
                onClick = {
                    isPressed = true
                    onClick()
                    isPressed = false
                },
                modifier = modifier
                    .height(buttonHeight)
                    .scale(scale),
                enabled = enabled && !loading,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = buttonColors.contentColor,
                    disabledContentColor = buttonColors.contentColor.copy(alpha = 0.38f)
                ),
                shape = RoundedCornerShape(8.dp),
                interactionSource = interactionSource,
                content = buttonContent
            )
        }
        
        ButtonStyle.Danger, ButtonStyle.Success -> {
            Button(
                onClick = {
                    isPressed = true
                    onClick()
                    isPressed = false
                },
                modifier = modifier
                    .height(buttonHeight)
                    .scale(scale),
                enabled = enabled && !loading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = buttonColors.containerColor,
                    contentColor = buttonColors.contentColor,
                    disabledContainerColor = buttonColors.containerColor.copy(alpha = 0.38f),
                    disabledContentColor = buttonColors.contentColor.copy(alpha = 0.38f)
                ),
                shape = RoundedCornerShape(8.dp),
                interactionSource = interactionSource,
                content = buttonContent
            )
        }
    }
}

// 按钮颜色数据类
private data class ButtonColors(
    val containerColor: Color,
    val contentColor: Color
)

// 获取按钮颜色
@Composable
private fun getButtonColors(style: ButtonStyle): ButtonColors {
    return when (style) {
        ButtonStyle.Primary -> ButtonColors(
            containerColor = PrimaryColors.Primary,
            contentColor = PrimaryColors.OnPrimary
        )
        ButtonStyle.Secondary -> ButtonColors(
            containerColor = SecondaryColors.Secondary,
            contentColor = SecondaryColors.OnSecondary
        )
        ButtonStyle.Tertiary, ButtonStyle.Ghost -> ButtonColors(
            containerColor = Color.Transparent,
            contentColor = PrimaryColors.Primary
        )
        ButtonStyle.Danger -> ButtonColors(
            containerColor = FunctionalColors.Error,
            contentColor = FunctionalColors.OnError
        )
        ButtonStyle.Success -> ButtonColors(
            containerColor = FunctionalColors.Success,
            contentColor = FunctionalColors.OnSuccess
        )
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun QuesticleButtonPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 不同样式的按钮
            QuesticleButton(
                text = "主要按钮",
                onClick = { },
                style = ButtonStyle.Primary
            )
            
            QuesticleButton(
                text = "次要按钮",
                onClick = { },
                style = ButtonStyle.Secondary
            )
            
            QuesticleButton(
                text = "带图标",
                onClick = { },
                icon = Icons.Default.Add,
                style = ButtonStyle.Primary
            )
            
            QuesticleButton(
                text = "加载中",
                onClick = { },
                loading = true,
                style = ButtonStyle.Primary
            )
            
            // 不同尺寸的按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                QuesticleButton(
                    text = "小",
                    onClick = { },
                    size = ButtonSize.Small
                )
                QuesticleButton(
                    text = "中",
                    onClick = { },
                    size = ButtonSize.Medium
                )
                QuesticleButton(
                    text = "大",
                    onClick = { },
                    size = ButtonSize.Large
                )
            }
        }
    }
}
