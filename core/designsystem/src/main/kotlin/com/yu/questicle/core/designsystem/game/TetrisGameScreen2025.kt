package com.yu.questicle.core.designsystem.game

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.components.QuesticleButton
import com.yu.questicle.core.designsystem.components.ButtonStyle
import com.yu.questicle.core.designsystem.effects.*
import com.yu.questicle.core.designsystem.layouts.GameInfoBentoGrid
import com.yu.questicle.core.designsystem.theme.*

/**
 * 2025年现代化俄罗斯方块游戏界面
 * 融合最新设计趋势的游戏体验
 */

// 游戏状态枚举
enum class TetrisGameStatus {
    READY,      // 准备就绪
    PLAYING,    // 游戏中
    PAUSED,     // 已暂停
    GAME_OVER   // 游戏结束
}

// 游戏动作枚举
enum class TetrisAction {
    START,      // 开始游戏
    PAUSE,      // 暂停游戏
    RESUME,     // 继续游戏
    RESTART,    // 重新开始
    MOVE_LEFT,  // 左移
    MOVE_RIGHT, // 右移
    ROTATE,     // 旋转
    SOFT_DROP,  // 软降
    HARD_DROP,  // 硬降
    HOLD        // 暂存
}

// 游戏状态数据类
data class TetrisGameState(
    val status: TetrisGameStatus = TetrisGameStatus.READY,
    val score: Int = 0,
    val level: Int = 1,
    val lines: Int = 0,
    val time: String = "00:00",
    val currentPiece: TetrisPieceType = TetrisPieceType.T,
    val nextPiece: TetrisPieceType = TetrisPieceType.I,
    val holdPiece: TetrisPieceType? = null,
    val board: Array<Array<TetrisPieceType>> = Array(20) { Array(10) { TetrisPieceType.EMPTY } }
)

/**
 * 2025年俄罗斯方块游戏主界面
 */
@Composable
fun TetrisGameScreen2025(
    gameState: TetrisGameState,
    onAction: (TetrisAction) -> Unit,
    modifier: Modifier = Modifier,
    useLowLightMode: Boolean = false
) {
    // 使用低光模式主题
    if (useLowLightMode) {
        QuesticleTheme(themeMode = ThemeMode.LOW_LIGHT) {
            TetrisGameContent(gameState, onAction, modifier)
        }
    } else {
        TetrisGameContent(gameState, onAction, modifier)
    }
}

@Composable
private fun TetrisGameContent(
    gameState: TetrisGameState,
    onAction: (TetrisAction) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.background,
                        MaterialTheme.colorScheme.surface
                    )
                )
            )
    ) {
        // 顶部信息面板 - Bento网格风格
        GameInfoBentoGrid(
            score = gameState.score,
            level = gameState.level,
            lines = gameState.lines,
            time = gameState.time,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
        
        // 游戏区域
        Row(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 左侧预览区
            Column(
                modifier = Modifier.width(100.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 下一个方块预览
                NextPiecePreview(
                    piece = gameState.nextPiece,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 暂存方块预览
                HoldPiecePreview(
                    piece = gameState.holdPiece,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 游戏状态指示器
                GameStatusIndicator(
                    status = gameState.status,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // 中央游戏板
            TetrisBoard3DContainer(
                gameState = gameState,
                modifier = Modifier.weight(1f)
            )
            
            // 右侧统计区
            Column(
                modifier = Modifier.width(100.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 统计面板
                StatisticsPanel(
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 成就进度
                AchievementProgress(
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        // 底部控制面板 - 毛玻璃效果
        TetrisControlPanel2025(
            gameState = gameState,
            onAction = onAction,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
    }
}

/**
 * 下一个方块预览
 */
@Composable
private fun NextPiecePreview(
    piece: TetrisPieceType,
    modifier: Modifier = Modifier
) {
    GameGlassPanel(
        modifier = modifier,
        alpha = 0.08f
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "下一个",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            TetrisPiece3D(
                pieceType = piece,
                blockSize = 16.dp,
                isAnimated = true
            )
        }
    }
}

/**
 * 暂存方块预览
 */
@Composable
private fun HoldPiecePreview(
    piece: TetrisPieceType?,
    modifier: Modifier = Modifier
) {
    GameGlassPanel(
        modifier = modifier,
        alpha = 0.08f
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "暂存",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            if (piece != null) {
                TetrisPiece3D(
                    pieceType = piece,
                    blockSize = 16.dp,
                    isAnimated = false
                )
            } else {
                Box(
                    modifier = Modifier.size(48.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "空",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

/**
 * 游戏状态指示器
 */
@Composable
private fun GameStatusIndicator(
    status: TetrisGameStatus,
    modifier: Modifier = Modifier
) {
    val statusColor = when (status) {
        TetrisGameStatus.READY -> MaterialTheme.colorScheme.primary
        TetrisGameStatus.PLAYING -> FunctionalColors.Success
        TetrisGameStatus.PAUSED -> FunctionalColors.Warning
        TetrisGameStatus.GAME_OVER -> FunctionalColors.Error
    }
    
    val statusText = when (status) {
        TetrisGameStatus.READY -> "准备"
        TetrisGameStatus.PLAYING -> "游戏中"
        TetrisGameStatus.PAUSED -> "暂停"
        TetrisGameStatus.GAME_OVER -> "结束"
    }
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = statusColor.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(statusColor, RoundedCornerShape(3.dp))
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = statusText,
                style = MaterialTheme.typography.labelSmall,
                color = statusColor
            )
        }
    }
}

/**
 * 3D游戏板容器
 */
@Composable
private fun TetrisBoard3DContainer(
    gameState: TetrisGameState,
    modifier: Modifier = Modifier
) {
    GameGlassPanel(
        modifier = modifier,
        alpha = 0.05f
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            GameBoard3D(
                board = gameState.board,
                blockSize = 18.dp,
                showGrid = true
            )
        }
    }
}

/**
 * 统计面板
 */
@Composable
private fun StatisticsPanel(
    modifier: Modifier = Modifier
) {
    GameGlassPanel(
        modifier = modifier,
        alpha = 0.08f
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "统计",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "📊",
                style = MaterialTheme.typography.titleMedium
            )
            
            Text(
                text = "详情",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * 成就进度
 */
@Composable
private fun AchievementProgress(
    modifier: Modifier = Modifier
) {
    GameGlassPanel(
        modifier = modifier,
        alpha = 0.08f
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "成就",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "🏆",
                style = MaterialTheme.typography.titleMedium
            )

            LinearProgressIndicator(
            progress = { 0.6f },
            modifier = Modifier
                                .fillMaxWidth()
                                .height(4.dp),
            color = MaterialTheme.colorScheme.tertiary,
            trackColor = ProgressIndicatorDefaults.linearTrackColor,
            strokeCap = ProgressIndicatorDefaults.LinearStrokeCap,
            )
        }
    }
}

/**
 * 2025年控制面板
 */
@Composable
private fun TetrisControlPanel2025(
    gameState: TetrisGameState,
    onAction: (TetrisAction) -> Unit,
    modifier: Modifier = Modifier
) {
    GlassCard(
        modifier = modifier,
        alpha = 0.12f
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 主要控制按钮
            when (gameState.status) {
                TetrisGameStatus.READY -> {
                    QuesticleButton(
                        text = "开始游戏",
                        onClick = { onAction(TetrisAction.START) },
                        style = ButtonStyle.Primary
                    )
                }
                TetrisGameStatus.PLAYING -> {
                    QuesticleButton(
                        text = "暂停",
                        onClick = { onAction(TetrisAction.PAUSE) },
                        style = ButtonStyle.Secondary
                    )
                }
                TetrisGameStatus.PAUSED -> {
                    QuesticleButton(
                        text = "继续",
                        onClick = { onAction(TetrisAction.RESUME) },
                        style = ButtonStyle.Primary
                    )
                }
                TetrisGameStatus.GAME_OVER -> {
                    QuesticleButton(
                        text = "重新开始",
                        onClick = { onAction(TetrisAction.RESTART) },
                        style = ButtonStyle.Success
                    )
                }
            }
            
            // 重置按钮
            QuesticleButton(
                text = "重置",
                onClick = { onAction(TetrisAction.RESTART) },
                style = ButtonStyle.Tertiary
            )
        }
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun TetrisGameScreen2025Preview() {
    QuesticleTheme {
        TetrisGameScreen2025(
            gameState = TetrisGameState(
                status = TetrisGameStatus.PLAYING,
                score = 12345,
                level = 5,
                lines = 23,
                time = "05:42"
            ),
            onAction = { }
        )
    }
}
