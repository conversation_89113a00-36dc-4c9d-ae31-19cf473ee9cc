package com.yu.questicle.core.designsystem.accessibility

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.components.QuesticleButton
import com.yu.questicle.core.designsystem.components.ButtonStyle
import com.yu.questicle.core.designsystem.game.TetrisAction
import com.yu.questicle.core.designsystem.performance.PerformanceMonitorPanel
import com.yu.questicle.core.designsystem.performance.PerformanceSettingsPanel
import com.yu.questicle.core.designsystem.performance.rememberPerformanceMonitor
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * 无障碍和性能测试界面
 * 用于测试和验证无障碍功能和性能优化
 */

@Composable
fun AccessibilityTestScreen(
    modifier: Modifier = Modifier
) {
    val accessibilityManager = rememberAccessibilityManager()
    val keyboardNavigationManager = rememberKeyboardNavigationManager()
    val performanceMonitor = rememberPerformanceMonitor()
    
    var accessibilitySettings by remember { mutableStateOf(AccessibilitySettings()) }
    var performanceSettings by remember { mutableStateOf(com.yu.questicle.core.designsystem.performance.PerformanceSettings()) }
    var gameEventMessage by remember { mutableStateOf("") }
    var shouldAnnounceEvent by remember { mutableStateOf(false) }
    
    // 监听无障碍状态变化
    AccessibilityStateListener { newSettings ->
        accessibilitySettings = newSettings
    }
    
    // 性能监控
    LaunchedEffect(Unit) {
        while (true) {
            performanceMonitor.recordFrame()
            kotlinx.coroutines.delay(16) // 60fps
        }
    }
    
    FocusManagement {
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 标题
                Text(
                    text = "♿ 无障碍与性能测试",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            item {
                // 无障碍设置面板
                AccessibilitySettingsPanel(
                    settings = accessibilitySettings,
                    onSettingsChange = { accessibilitySettings = it }
                )
            }
            
            item {
                // 键盘导航测试
                GameKeyboardControlPanel(
                    onAction = { action ->
                        gameEventMessage = "执行游戏动作: ${getActionDescription(action)}"
                        shouldAnnounceEvent = true
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            item {
                // 键盘快捷键帮助
                KeyboardShortcutsHelp(
                    shortcuts = keyboardNavigationManager.getKeyboardShortcuts(),
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            item {
                // 无障碍测试组件
                AccessibilityTestComponents(
                    onTestAction = { testName ->
                        gameEventMessage = "执行无障碍测试: $testName"
                        shouldAnnounceEvent = true
                    }
                )
            }
            
            item {
                // 性能监控面板
                PerformanceMonitorPanel(
                    performanceMonitor = performanceMonitor,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            item {
                // 性能设置面板
                PerformanceSettingsPanel(
                    settings = performanceSettings,
                    onSettingsChange = { performanceSettings = it },
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            item {
                // 测试结果和建议
                AccessibilityTestResults(
                    accessibilitySettings = accessibilitySettings,
                    performanceMonitor = performanceMonitor
                )
            }
        }
    }
    
    // 游戏事件语音播报
    GameEventAnnouncement(
        event = gameEventMessage,
        shouldAnnounce = shouldAnnounceEvent
    )
    
    // 重置播报状态
    LaunchedEffect(shouldAnnounceEvent) {
        if (shouldAnnounceEvent) {
            kotlinx.coroutines.delay(100)
            shouldAnnounceEvent = false
        }
    }
}

@Composable
private fun AccessibilityTestComponents(
    onTestAction: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "无障碍组件测试",
                style = MaterialTheme.typography.titleMedium
            )
            
            // TalkBack测试按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "TalkBack测试",
                    onClick = { onTestAction("TalkBack语音播报") },
                    modifier = Modifier
                        .weight(1f)
                        .then(
                            AccessibilityModifiers.gameControlButton(
                                action = "TalkBack测试",
                                hint = "测试语音播报功能"
                            )
                        )
                )
                
                KeyboardNavigableButton(
                    text = "焦点测试",
                    onClick = { onTestAction("键盘焦点导航") },
                    modifier = Modifier
                        .weight(1f)
                        .then(
                            AccessibilityModifiers.gameControlButton(
                                action = "焦点测试",
                                hint = "测试键盘焦点导航"
                            )
                        )
                )
            }
            
            // 语义化测试
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                KeyboardNavigableButton(
                    text = "语义化测试",
                    onClick = { onTestAction("语义化标签") },
                    modifier = Modifier
                        .weight(1f)
                        .then(
                            AccessibilityModifiers.gameStatusInfo(
                                label = "测试按钮",
                                value = "语义化测试",
                                importance = true
                            )
                        )
                )
                
                KeyboardNavigableButton(
                    text = "对比度测试",
                    onClick = { onTestAction("颜色对比度") },
                    modifier = Modifier
                        .weight(1f)
                        .then(
                            AccessibilityModifiers.gameControlButton(
                                action = "对比度测试",
                                hint = "测试颜色对比度"
                            )
                        )
                )
            }
            
            // 游戏专用无障碍测试
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "游戏无障碍测试",
                    style = MaterialTheme.typography.titleSmall
                )
                
                // 模拟游戏板
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp)
                        .then(
                            AccessibilityModifiers.gameBoard(
                                width = 10,
                                height = 20,
                                filledCells = 15
                            )
                        )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "模拟游戏板 (10x20)\n已填充15个方块",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
                
                // 游戏方块测试
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    repeat(4) { index ->
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = when (index) {
                                    0 -> MaterialTheme.colorScheme.primary
                                    1 -> MaterialTheme.colorScheme.secondary
                                    2 -> MaterialTheme.colorScheme.tertiary
                                    else -> MaterialTheme.colorScheme.error
                                }
                            ),
                            modifier = Modifier
                                .size(40.dp)
                                .then(
                                    AccessibilityModifiers.gameBlock(
                                        pieceType = when (index) {
                                            0 -> "I型方块"
                                            1 -> "T型方块"
                                            2 -> "L型方块"
                                            else -> "O型方块"
                                        },
                                        position = "第${index + 1}个位置",
                                        isActive = index == 0
                                    )
                                )
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = when (index) {
                                        0 -> "I"
                                        1 -> "T"
                                        2 -> "L"
                                        else -> "O"
                                    },
                                    color = MaterialTheme.colorScheme.onPrimary
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun AccessibilityTestResults(
    accessibilitySettings: AccessibilitySettings,
    performanceMonitor: com.yu.questicle.core.designsystem.performance.PerformanceMonitor,
    modifier: Modifier = Modifier
) {
    val performanceMetrics by performanceMonitor.metrics.collectAsState()
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "📊 测试结果与建议",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            // 无障碍评分
            val accessibilityScore = calculateAccessibilityScore(accessibilitySettings)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "无障碍评分",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "$accessibilityScore/100",
                    color = when {
                        accessibilityScore >= 90 -> MaterialTheme.colorScheme.primary
                        accessibilityScore >= 70 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }
            
            // 性能评分
            val performanceScore = calculatePerformanceScore(performanceMetrics)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "性能评分",
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "$performanceScore/100",
                    color = when {
                        performanceScore >= 90 -> MaterialTheme.colorScheme.primary
                        performanceScore >= 70 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 改进建议
            val recommendations = getImprovementRecommendations(
                accessibilitySettings,
                performanceMetrics
            )
            
            if (recommendations.isNotEmpty()) {
                Text(
                    text = "改进建议",
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                recommendations.forEach { recommendation ->
                    Text(
                        text = "• $recommendation",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            } else {
                Text(
                    text = "✅ 所有测试通过，无障碍和性能表现优秀！",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

// 辅助函数
private fun getActionDescription(action: TetrisAction): String {
    return when (action) {
        TetrisAction.MOVE_LEFT -> "向左移动"
        TetrisAction.MOVE_RIGHT -> "向右移动"
        TetrisAction.ROTATE -> "旋转方块"
        TetrisAction.SOFT_DROP -> "软降"
        TetrisAction.HARD_DROP -> "硬降"
        TetrisAction.HOLD -> "暂存方块"
        TetrisAction.PAUSE -> "暂停游戏"
        TetrisAction.RESUME -> "继续游戏"
        TetrisAction.RESTART -> "重新开始"
        TetrisAction.START -> "开始游戏"
    }
}

private fun calculateAccessibilityScore(settings: AccessibilitySettings): Int {
    var score = 60 // 基础分
    
    if (settings.talkBackEnabled) score += 20
    if (settings.highContrastEnabled) score += 10
    if (settings.largeTextEnabled) score += 5
    if (settings.touchExplorationEnabled) score += 5
    
    return score.coerceAtMost(100)
}

private fun calculatePerformanceScore(metrics: com.yu.questicle.core.designsystem.performance.PerformanceMetrics): Int {
    var score = 0
    
    // FPS评分 (40分)
    score += when {
        metrics.averageFPS >= 55 -> 40
        metrics.averageFPS >= 45 -> 30
        metrics.averageFPS >= 30 -> 20
        else -> 10
    }
    
    // 内存使用评分 (30分)
    val memoryUsagePercent = (metrics.memoryUsage.toFloat() / metrics.maxMemoryUsage) * 100
    score += when {
        memoryUsagePercent <= 50 -> 30
        memoryUsagePercent <= 70 -> 20
        memoryUsagePercent <= 85 -> 10
        else -> 5
    }
    
    // 掉帧评分 (30分)
    score += when {
        metrics.droppedFrames <= 2 -> 30
        metrics.droppedFrames <= 5 -> 20
        metrics.droppedFrames <= 10 -> 10
        else -> 5
    }
    
    return score.coerceAtMost(100)
}

private fun getImprovementRecommendations(
    accessibilitySettings: AccessibilitySettings,
    performanceMetrics: com.yu.questicle.core.designsystem.performance.PerformanceMetrics
): List<String> {
    val recommendations = mutableListOf<String>()
    
    // 无障碍建议
    if (!accessibilitySettings.talkBackEnabled) {
        recommendations.add("建议启用TalkBack以测试语音播报功能")
    }
    
    if (!accessibilitySettings.highContrastEnabled) {
        recommendations.add("建议测试高对比度模式以确保视觉可访问性")
    }
    
    // 性能建议
    if (performanceMetrics.averageFPS < 50) {
        recommendations.add("帧率较低，建议优化渲染性能")
    }
    
    if (performanceMetrics.droppedFrames > 5) {
        recommendations.add("检测到较多掉帧，建议减少动画复杂度")
    }
    
    val memoryUsagePercent = (performanceMetrics.memoryUsage.toFloat() / performanceMetrics.maxMemoryUsage) * 100
    if (memoryUsagePercent > 80) {
        recommendations.add("内存使用率过高，建议优化内存管理")
    }
    
    return recommendations
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun AccessibilityTestScreenPreview() {
    QuesticleTheme {
        AccessibilityTestScreen()
    }
}
