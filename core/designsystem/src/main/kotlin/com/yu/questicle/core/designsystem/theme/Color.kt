package com.yu.questicle.core.designsystem.theme

import androidx.compose.ui.graphics.Color

/**
 * Questicle color palette based on Material Design 3
 */
object QuesticleColors {
    
    // Light theme colors
    val Primary = Color(0xFF6750A4)
    val OnPrimary = Color(0xFFFFFFFF)
    val PrimaryContainer = Color(0xFFEADDFF)
    val OnPrimaryContainer = Color(0xFF21005D)
    
    val Secondary = Color(0xFF625B71)
    val OnSecondary = Color(0xFFFFFFFF)
    val SecondaryContainer = Color(0xFFE8DEF8)
    val OnSecondaryContainer = Color(0xFF1D192B)
    
    val Tertiary = Color(0xFF7D5260)
    val OnTertiary = Color(0xFFFFFFFF)
    val TertiaryContainer = Color(0xFFFFD8E4)
    val OnTertiaryContainer = Color(0xFF31111D)
    
    val Error = Color(0xFFBA1A1A)
    val OnError = Color(0xFFFFFFFF)
    val ErrorContainer = Color(0xFFFFDAD6)
    val OnErrorContainer = Color(0xFF410002)
    
    val Background = Color(0xFFFFFBFE)
    val OnBackground = Color(0xFF1C1B1F)
    val Surface = Color(0xFFFFFBFE)
    val OnSurface = Color(0xFF1C1B1F)
    val SurfaceVariant = Color(0xFFE7E0EC)
    val OnSurfaceVariant = Color(0xFF49454F)
    
    val Outline = Color(0xFF79747E)
    val OutlineVariant = Color(0xFFCAC4D0)
    
    // Dark theme colors
    val DarkPrimary = Color(0xFFD0BCFF)
    val DarkOnPrimary = Color(0xFF381E72)
    val DarkPrimaryContainer = Color(0xFF4F378B)
    val DarkOnPrimaryContainer = Color(0xFFEADDFF)
    
    val DarkSecondary = Color(0xFFCCC2DC)
    val DarkOnSecondary = Color(0xFF332D41)
    val DarkSecondaryContainer = Color(0xFF4A4458)
    val DarkOnSecondaryContainer = Color(0xFFE8DEF8)
    
    val DarkTertiary = Color(0xFFEFB8C8)
    val DarkOnTertiary = Color(0xFF492532)
    val DarkTertiaryContainer = Color(0xFF633B48)
    val DarkOnTertiaryContainer = Color(0xFFFFD8E4)
    
    val DarkError = Color(0xFFFFB4AB)
    val DarkOnError = Color(0xFF690005)
    val DarkErrorContainer = Color(0xFF93000A)
    val DarkOnErrorContainer = Color(0xFFFFDAD6)
    
    val DarkBackground = Color(0xFF1C1B1F)
    val DarkOnBackground = Color(0xFFE6E1E5)
    val DarkSurface = Color(0xFF1C1B1F)
    val DarkOnSurface = Color(0xFFE6E1E5)
    val DarkSurfaceVariant = Color(0xFF49454F)
    val DarkOnSurfaceVariant = Color(0xFFCAC4D0)
    
    val DarkOutline = Color(0xFF938F99)
    val DarkOutlineVariant = Color(0xFF49454F)
    
    // Game-specific colors
    val TetrisI = Color(0xFF00FFFF) // Cyan
    val TetrisO = Color(0xFFFFFF00) // Yellow
    val TetrisT = Color(0xFF800080) // Purple
    val TetrisS = Color(0xFF00FF00) // Green
    val TetrisZ = Color(0xFFFF0000) // Red
    val TetrisJ = Color(0xFF0000FF) // Blue
    val TetrisL = Color(0xFFFFA500) // Orange
    
    // Success and warning colors
    val Success = Color(0xFF4CAF50)
    val Warning = Color(0xFFFF9800)
    val Info = Color(0xFF2196F3)
    
    // Gradient colors
    val GradientStart = Color(0xFF6750A4)
    val GradientEnd = Color(0xFF7D5260)
}
