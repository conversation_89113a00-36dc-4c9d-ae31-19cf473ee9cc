package com.yu.questicle.core.designsystem.layouts

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * Bento网格布局系统 - 2025年设计趋势
 * 模块化的界面组织，灵感来自日式便当盒
 */

// Bento网格项目大小 - 重构为统一高度系统
enum class BentoSize(val widthSpan: Int, val heightSpan: Int) {
    Small(1, 1),        // 1x1 - 小卡片
    Medium(2, 1),       // 2x1 - 中等卡片
    Large(2, 2),        // 2x2 - 大卡片
    Wide(3, 1),         // 3x1 - 宽卡片
    Tall(1, 2),         // 1x2 - 高卡片
    ExtraWide(4, 1),    // 4x1 - 超宽卡片
    ExtraLarge(3, 2)    // 3x2 - 超大卡片
}

// Bento网格设计令牌
object BentoGridTokens {
    val BaseCardHeight = 80.dp
    val CardSpacing = 8.dp
    val CardCornerRadius = 16.dp
    val CardElevation = 2.dp
    val ContentPadding = 16.dp
    val MinColumns = 2
    val MaxColumns = 4
}

// Bento网格项目数据类
@Stable
data class BentoItem(
    val id: String,
    val size: BentoSize,
    val isClickable: Boolean = false,
    val onClick: (() -> Unit)? = null,
    val content: @Composable BoxScope.() -> Unit
)

// Bento网格作用域
interface BentoGridScope {
    fun item(
        id: String,
        size: BentoSize = BentoSize.Small,
        isClickable: Boolean = false,
        onClick: (() -> Unit)? = null,
        content: @Composable BoxScope.() -> Unit
    )
}

private class BentoGridScopeImpl : BentoGridScope {
    val items = mutableListOf<BentoItem>()

    override fun item(
        id: String,
        size: BentoSize,
        isClickable: Boolean,
        onClick: (() -> Unit)?,
        content: @Composable BoxScope.() -> Unit
    ) {
        items.add(BentoItem(id, size, isClickable, onClick, content))
    }
}

/**
 * 高质量Bento网格组件 - 支持真正的网格布局
 */
@Composable
fun BentoGrid(
    modifier: Modifier = Modifier,
    columns: Int = BentoGridTokens.MaxColumns,
    spacing: Dp = BentoGridTokens.CardSpacing,
    content: BentoGridScope.() -> Unit
) {
    val scope = remember { BentoGridScopeImpl() }
    scope.content()

    // 计算网格布局
    val gridLayout = remember(scope.items, columns) {
        calculateBentoGridLayout(scope.items, columns)
    }

    Column(
        modifier = modifier.padding(spacing),
        verticalArrangement = Arrangement.spacedBy(spacing)
    ) {
        gridLayout.rows.forEach { row ->
            BentoGridRow(
                row = row,
                columns = columns,
                spacing = spacing
            )
        }
    }
}

/**
 * Bento网格行组件
 */
@Composable
private fun BentoGridRow(
    row: BentoGridRow,
    columns: Int,
    spacing: Dp
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(BentoGridTokens.BaseCardHeight * row.heightSpan),
        horizontalArrangement = Arrangement.spacedBy(spacing)
    ) {
        row.cells.forEach { cell ->
            when (cell) {
                is BentoGridCell.Item -> {
                    BentoCard(
                        item = cell.item,
                        widthWeight = cell.widthSpan.toFloat(),
                        modifier = Modifier.weight(cell.widthSpan.toFloat())
                    )
                }
                is BentoGridCell.Empty -> {
                    Spacer(modifier = Modifier.weight(cell.widthSpan.toFloat()))
                }
            }
        }
    }
}

// 网格布局数据结构
@Stable
data class BentoGridLayout(
    val rows: List<BentoGridRow>
)

@Stable
data class BentoGridRow(
    val cells: List<BentoGridCell>,
    val heightSpan: Int
)

@Stable
sealed interface BentoGridCell {
    val widthSpan: Int

    data class Item(
        val item: BentoItem,
        override val widthSpan: Int
    ) : BentoGridCell

    data class Empty(
        override val widthSpan: Int
    ) : BentoGridCell
}

/**
 * 计算Bento网格布局算法
 */
private fun calculateBentoGridLayout(
    items: List<BentoItem>,
    columns: Int
): BentoGridLayout {
    val rows = mutableListOf<BentoGridRow>()
    var currentRowCells = mutableListOf<BentoGridCell>()
    var currentRowWidth = 0
    var currentRowHeight = 1

    for (item in items) {
        val itemWidth = item.size.widthSpan
        val itemHeight = item.size.heightSpan

        // 检查当前行是否能容纳该项目
        if (currentRowWidth + itemWidth <= columns) {
            // 可以放在当前行
            currentRowCells.add(BentoGridCell.Item(item, itemWidth))
            currentRowWidth += itemWidth
            currentRowHeight = maxOf(currentRowHeight, itemHeight)
        } else {
            // 需要新行，先完成当前行
            if (currentRowCells.isNotEmpty()) {
                // 填充剩余空间
                if (currentRowWidth < columns) {
                    currentRowCells.add(BentoGridCell.Empty(columns - currentRowWidth))
                }
                rows.add(BentoGridRow(currentRowCells.toList(), currentRowHeight))
            }

            // 开始新行
            currentRowCells = mutableListOf(BentoGridCell.Item(item, itemWidth))
            currentRowWidth = itemWidth
            currentRowHeight = itemHeight
        }
    }

    // 处理最后一行
    if (currentRowCells.isNotEmpty()) {
        if (currentRowWidth < columns) {
            currentRowCells.add(BentoGridCell.Empty(columns - currentRowWidth))
        }
        rows.add(BentoGridRow(currentRowCells.toList(), currentRowHeight))
    }

    return BentoGridLayout(rows)
}

/**
 * 高质量Bento卡片组件
 */
@Composable
private fun BentoCard(
    item: BentoItem,
    widthWeight: Float,
    modifier: Modifier = Modifier
) {
    val cardModifier = if (item.isClickable && item.onClick != null) {
        modifier.clickable { item.onClick.invoke() }
    } else {
        modifier
    }

    Card(
        modifier = cardModifier.fillMaxSize(),
        shape = RoundedCornerShape(BentoGridTokens.CardCornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = BentoGridTokens.CardElevation
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(BentoGridTokens.ContentPadding),
            contentAlignment = Alignment.Center
        ) {
            item.content(this)
        }
    }
}

/**
 * 游戏信息Bento网格
 */
@Composable
fun GameInfoBentoGrid(
    score: Int,
    level: Int,
    lines: Int,
    time: String,
    modifier: Modifier = Modifier
) {
    BentoGrid(
        modifier = modifier,
        columns = 4
    ) {
        // 分数卡片 - 占据2列
        item(
            id = "score",
            size = BentoSize.Medium
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "分数",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = score.toString(),
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        // 等级卡片 - 占据1列
        item(
            id = "level",
            size = BentoSize.Small
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "等级",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = level.toString(),
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }
        
        // 行数卡片 - 占据1列
        item(
            id = "lines",
            size = BentoSize.Small
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "行数",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = lines.toString(),
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.tertiary
                )
            }
        }
        
        // 时间卡片 - 占据3列
        item(
            id = "time",
            size = BentoSize.Wide
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "⏱️",
                    style = MaterialTheme.typography.titleLarge
                )
                Column {
                    Text(
                        text = "游戏时间",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = time,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
        
        // 统计卡片 - 占据1列
        item(
            id = "stats",
            size = BentoSize.Small
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = "📊",
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = "统计",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 优化的主页Bento网格 - 单行布局，提升美观和空间感
 */
@Composable
fun HomeBentoGrid(
    modifier: Modifier = Modifier,
    onWelcomeClick: () -> Unit = {},
    onQuickStartClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    onAchievementsClick: () -> Unit = {}
) {
    BentoGrid(
        modifier = modifier,
        columns = 4
    ) {
        // 欢迎卡片 - 调整为1x1小卡片，与其他保持一致
        item(
            id = "welcome",
            size = BentoSize.Small,
            isClickable = true,
            onClick = onWelcomeClick
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "🎮",
                    style = MaterialTheme.typography.headlineMedium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "欢迎",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }

        // 快速开始 - 1x1小卡片，第一行第二列
        item(
            id = "quick_start",
            size = BentoSize.Small,
            isClickable = true,
            onClick = onQuickStartClick
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "⚡",
                    style = MaterialTheme.typography.headlineMedium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "快速开始",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.secondary
                )
            }
        }

        // 设置 - 1x1小卡片，第一行第三列
        item(
            id = "settings",
            size = BentoSize.Small,
            isClickable = true,
            onClick = onSettingsClick
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "⚙️",
                    style = MaterialTheme.typography.headlineMedium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "设置",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 成就 - 1x1小卡片，第一行第四列
        item(
            id = "achievements",
            size = BentoSize.Small,
            isClickable = true,
            onClick = onAchievementsClick
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "🏆",
                    style = MaterialTheme.typography.headlineMedium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "成就",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// 预览组件
@Preview(showBackground = true, name = "主页Bento网格 - 单行布局")
@Composable
private fun HomeBentoGridPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "主页Bento网格 - 单行布局，提升空间感",
                style = MaterialTheme.typography.headlineMedium
            )

            HomeBentoGrid(
                modifier = Modifier.fillMaxWidth(),
                onWelcomeClick = { /* Preview action */ },
                onQuickStartClick = { /* Preview action */ },
                onSettingsClick = { /* Preview action */ },
                onAchievementsClick = { /* Preview action */ }
            )
        }
    }
}

@Preview(showBackground = true, name = "游戏信息网格")
@Composable
private fun GameInfoBentoGridPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "游戏信息面板",
                style = MaterialTheme.typography.headlineMedium
            )

            GameInfoBentoGrid(
                score = 12345,
                level = 5,
                lines = 23,
                time = "05:42"
            )
        }
    }
}

@Preview(showBackground = true, name = "暗色主题 - 单行布局", uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun HomeBentoGridDarkPreview() {
    QuesticleTheme {
        HomeBentoGrid(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.background)
                .padding(16.dp),
            onWelcomeClick = { /* Preview action */ },
            onQuickStartClick = { /* Preview action */ },
            onSettingsClick = { /* Preview action */ },
            onAchievementsClick = { /* Preview action */ }
        )
    }
}
