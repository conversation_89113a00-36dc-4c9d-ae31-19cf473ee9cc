package com.yu.questicle.core.designsystem.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.*


/**
 * Questicle 卡片组件
 * 支持多种样式，融合2025年设计趋势
 */

// 卡片样式枚举
enum class CardStyle {
    Elevated,       // 阴影卡片
    Filled,         // 填充卡片
    Outlined,       // 边框卡片
    Glass           // 毛玻璃效果 (2025年趋势)
}

@Composable
fun QuesticleCard(
    modifier: Modifier = Modifier,
    style: CardStyle = CardStyle.Elevated,
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    // 微交互动画状态
    var isPressed by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    
    // 卡片缩放动画
    val scale by animateFloatAsState(
        targetValue = if (isPressed && onClick != null) 0.98f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "card_scale"
    )
    
    when (style) {
        CardStyle.Elevated -> {
            ElevatedCard(
                modifier = modifier
                    .scale(scale)
                    .then(
                        if (onClick != null) {
                            Modifier.clickable(
                                interactionSource = interactionSource,
                                indication = null
                            ) {
                                isPressed = true
                                onClick()
                                isPressed = false
                            }
                        } else Modifier
                    ),
                elevation = CardDefaults.elevatedCardElevation(
                    defaultElevation = 3.dp
                ),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.elevatedCardColors(),
                content = content
            )
        }
        
        CardStyle.Filled -> {
            Card(
                modifier = modifier
                    .scale(scale)
                    .then(
                        if (onClick != null) {
                            Modifier.clickable(
                                interactionSource = interactionSource,
                                indication = null
                            ) {
                                isPressed = true
                                onClick()
                                isPressed = false
                            }
                        } else Modifier
                    ),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                ),
                content = content
            )
        }
        
        CardStyle.Outlined -> {
            OutlinedCard(
                modifier = modifier
                    .scale(scale)
                    .then(
                        if (onClick != null) {
                            Modifier.clickable(
                                interactionSource = interactionSource,
                                indication = null
                            ) {
                                isPressed = true
                                onClick()
                                isPressed = false
                            }
                        } else Modifier
                    ),
                shape = RoundedCornerShape(12.dp),
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.outline
                ),
                colors = CardDefaults.outlinedCardColors(),
                content = content
            )
        }
        
        CardStyle.Glass -> {
            GlassCard(
                modifier = modifier
                    .scale(scale)
                    .then(
                        if (onClick != null) {
                            Modifier.clickable(
                                interactionSource = interactionSource,
                                indication = null
                            ) {
                                isPressed = true
                                onClick()
                                isPressed = false
                            }
                        } else Modifier
                    ),
                content = content
            )
        }
    }
}

/**
 * 毛玻璃效果卡片 - 2025年设计趋势
 */
@Composable
private fun GlassCard(
    modifier: Modifier = Modifier,
    blurRadius: androidx.compose.ui.unit.Dp = 16.dp,
    alpha: Float = 0.1f,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.5f)
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        border = BorderStroke(
            width = 1.dp,
            brush = Brush.linearGradient(
                colors = listOf(
                    Color.White.copy(alpha = 0.3f),
                    Color.White.copy(alpha = 0.1f)
                )
            )
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Color.White.copy(alpha = 0.05f)
                )
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                content = content
            )
        }
    }
}

/**
 * 游戏信息卡片 - 专用于游戏数据显示
 */
@Composable
fun GameInfoCard(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
    icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    trend: TrendDirection? = null,
    subtitle: String? = null,
    onClick: (() -> Unit)? = null
) {
    QuesticleCard(
        modifier = modifier,
        style = CardStyle.Elevated,
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                if (icon != null) {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            // 数值显示
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = value,
                    style = GameTypography.StatDisplay,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                if (trend != null) {
                    TrendIndicator(trend = trend)
                }
            }
            
            // 副标题
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

// 趋势方向枚举
enum class TrendDirection {
    Up,             // 上升趋势 - 绿色箭头
    Down,           // 下降趋势 - 红色箭头
    Stable          // 稳定趋势 - 水平线
}

@Composable
private fun TrendIndicator(trend: TrendDirection) {
    val color = when (trend) {
        TrendDirection.Up -> FunctionalColors.Success
        TrendDirection.Down -> FunctionalColors.Error
        TrendDirection.Stable -> MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    val icon = when (trend) {
        TrendDirection.Up -> "↗"
        TrendDirection.Down -> "↘"
        TrendDirection.Stable -> "→"
    }
    
    Text(
        text = icon,
        style = MaterialTheme.typography.labelSmall,
        color = color
    )
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun QuesticleCardPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 不同样式的卡片
            QuesticleCard(style = CardStyle.Elevated) {
                Text(
                    text = "阴影卡片",
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            QuesticleCard(style = CardStyle.Filled) {
                Text(
                    text = "填充卡片",
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            QuesticleCard(style = CardStyle.Outlined) {
                Text(
                    text = "边框卡片",
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            QuesticleCard(style = CardStyle.Glass) {
                Text(
                    text = "毛玻璃卡片",
                    modifier = Modifier.padding(16.dp)
                )
            }
            
            // 游戏信息卡片
            GameInfoCard(
                title = "分数",
                value = "12,345",
                trend = TrendDirection.Up,
                subtitle = "本局最高"
            )
        }
    }
}
