package com.yu.questicle.core.designsystem.effects

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * 动态字体系统 - 2025年设计趋势
 * 智能调整字体以适应内容重要性和用户交互
 */

// 文本重要性枚举
enum class TextImportance {
    Critical,       // 关键信息 - 1.5x
    High,           // 重要信息 - 1.2x
    Normal,         // 普通信息 - 1.0x
    Low             // 次要信息 - 0.9x
}

// 动态字体状态
enum class DynamicState {
    Idle,           // 静止状态
    Hover,          // 悬停状态
    Pressed,        // 按压状态
    Focused,        // 焦点状态
    Highlighted     // 高亮状态
}

@Composable
fun DynamicText(
    text: String,
    importance: TextImportance = TextImportance.Normal,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.bodyLarge,
    color: Color = MaterialTheme.colorScheme.onSurface,
    interactive: Boolean = false,
    onImportanceChange: ((TextImportance) -> Unit)? = null
) {
    var currentImportance by remember { mutableStateOf(importance) }
    var dynamicState by remember { mutableStateOf(DynamicState.Idle) }
    val interactionSource = remember { MutableInteractionSource() }
    
    // 字体大小动画
    val animatedFontSize by animateFloatAsState(
        targetValue = when (currentImportance) {
            TextImportance.Critical -> 1.5f
            TextImportance.High -> 1.2f
            TextImportance.Normal -> 1.0f
            TextImportance.Low -> 0.9f
        } * when (dynamicState) {
            DynamicState.Hover -> 1.05f
            DynamicState.Pressed -> 0.95f
            DynamicState.Focused -> 1.1f
            DynamicState.Highlighted -> 1.15f
            else -> 1.0f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "font_size_animation"
    )
    
    // 字体权重动画
    val animatedFontWeight by animateIntAsState(
        targetValue = when (currentImportance) {
            TextImportance.Critical -> 900
            TextImportance.High -> 700
            TextImportance.Normal -> 400
            TextImportance.Low -> 300
        },
        animationSpec = tween(300),
        label = "font_weight_animation"
    )
    
    // 颜色动画
    val animatedColor by animateColorAsState(
        targetValue = when (currentImportance) {
            TextImportance.Critical -> MaterialTheme.colorScheme.error
            TextImportance.High -> MaterialTheme.colorScheme.primary
            TextImportance.Normal -> color
            TextImportance.Low -> color.copy(alpha = 0.7f)
        },
        animationSpec = tween<Color>(300),
        label = "color_animation"
    )
    
    Text(
        text = text,
        style = style.copy(
            fontSize = style.fontSize * animatedFontSize,
            fontWeight = FontWeight(animatedFontWeight),
            color = animatedColor
        ),
        modifier = modifier
            .then(
                if (interactive) {
                    Modifier.clickable(
                        interactionSource = interactionSource,
                        indication = null
                    ) {
                        // 循环切换重要性
                        currentImportance = when (currentImportance) {
                            TextImportance.Low -> TextImportance.Normal
                            TextImportance.Normal -> TextImportance.High
                            TextImportance.High -> TextImportance.Critical
                            TextImportance.Critical -> TextImportance.Low
                        }
                        onImportanceChange?.invoke(currentImportance)
                    }
                } else Modifier
            )
    )
}

/**
 * 智能分数显示 - 根据分数变化动态调整
 */
@Composable
fun SmartScoreDisplay(
    score: Int,
    previousScore: Int = 0,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.headlineLarge
) {
    val scoreDiff = score - previousScore
    var importance by remember { mutableStateOf(TextImportance.Normal) }
    
    // 根据分数变化调整重要性
    LaunchedEffect(score) {
        importance = when {
            scoreDiff >= 1000 -> TextImportance.Critical
            scoreDiff >= 500 -> TextImportance.High
            scoreDiff > 0 -> TextImportance.Normal
            else -> TextImportance.Low
        }
        
        // 重要性逐渐回归正常
        if (scoreDiff > 0) {
            kotlinx.coroutines.delay(2000)
            importance = TextImportance.Normal
        }
    }
    
    DynamicText(
        text = score.toString(),
        importance = importance,
        modifier = modifier,
        style = style
    )
}

/**
 * 响应式标题 - 根据内容长度调整
 */
@Composable
fun ResponsiveTitle(
    text: String,
    modifier: Modifier = Modifier,
    maxLength: Int = 20
) {
    val importance = when {
        text.length <= 10 -> TextImportance.High
        text.length <= maxLength -> TextImportance.Normal
        else -> TextImportance.Low
    }
    
    DynamicText(
        text = text,
        importance = importance,
        modifier = modifier,
        style = MaterialTheme.typography.headlineMedium
    )
}

/**
 * 游戏状态文本 - 根据游戏状态动态变化
 */
@Composable
fun GameStatusText(
    status: String,
    isUrgent: Boolean = false,
    isPositive: Boolean = true,
    modifier: Modifier = Modifier
) {
    val importance = when {
        isUrgent -> TextImportance.Critical
        isPositive -> TextImportance.High
        else -> TextImportance.Normal
    }
    
    DynamicText(
        text = status,
        importance = importance,
        modifier = modifier,
        style = MaterialTheme.typography.titleLarge
    )
}

/**
 * 智能提示文本 - 根据用户交互调整
 */
@Composable
fun SmartHintText(
    hint: String,
    isActive: Boolean = false,
    modifier: Modifier = Modifier
) {
    val importance = if (isActive) TextImportance.High else TextImportance.Low
    
    DynamicText(
        text = hint,
        importance = importance,
        modifier = modifier,
        style = MaterialTheme.typography.bodyMedium,
        interactive = true
    )
}

/**
 * 动态列表项 - 支持重要性排序
 */
@Composable
fun DynamicListItem(
    title: String,
    subtitle: String? = null,
    importance: TextImportance = TextImportance.Normal,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else Modifier
            ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = when (importance) {
                TextImportance.Critical -> 8.dp
                TextImportance.High -> 4.dp
                TextImportance.Normal -> 2.dp
                TextImportance.Low -> 1.dp
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            DynamicText(
                text = title,
                importance = importance,
                style = MaterialTheme.typography.titleMedium
            )
            
            if (subtitle != null) {
                DynamicText(
                    text = subtitle,
                    importance = TextImportance.Low,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * 实时通知文本 - 自动调整重要性
 */
@Composable
fun LiveNotificationText(
    message: String,
    timestamp: Long = System.currentTimeMillis(),
    modifier: Modifier = Modifier
) {
    val currentTime = System.currentTimeMillis()
    val age = currentTime - timestamp
    
    val importance = when {
        age < 5000 -> TextImportance.Critical    // 5秒内 - 关键
        age < 30000 -> TextImportance.High       // 30秒内 - 重要
        age < 300000 -> TextImportance.Normal    // 5分钟内 - 普通
        else -> TextImportance.Low               // 超过5分钟 - 次要
    }
    
    DynamicText(
        text = message,
        importance = importance,
        modifier = modifier,
        style = MaterialTheme.typography.bodyLarge
    )
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun DynamicTypographyPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 不同重要性的文本
            Text(
                text = "动态字体系统演示",
                style = MaterialTheme.typography.headlineMedium
            )
            
            DynamicText(
                text = "关键信息 - 点击切换",
                importance = TextImportance.Critical,
                interactive = true
            )
            
            DynamicText(
                text = "重要信息",
                importance = TextImportance.High
            )
            
            DynamicText(
                text = "普通信息",
                importance = TextImportance.Normal
            )
            
            DynamicText(
                text = "次要信息",
                importance = TextImportance.Low
            )
            
            // 智能分数显示
            SmartScoreDisplay(
                score = 12345,
                previousScore = 10000
            )
            
            // 响应式标题
            ResponsiveTitle(
                text = "这是一个很长的标题用来测试响应式调整"
            )
            
            // 游戏状态文本
            GameStatusText(
                status = "游戏暂停",
                isUrgent = true,
                isPositive = false
            )
            
            // 动态列表项
            DynamicListItem(
                title = "高优先级任务",
                subtitle = "需要立即处理",
                importance = TextImportance.High,
                onClick = { }
            )
        }
    }
}
