package com.yu.questicle.core.designsystem.effects

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * 毛玻璃效果组件 - 2025年设计趋势
 * Glassmorphism 效果实现
 */

@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    blurRadius: Dp = 16.dp,
    alpha: Float = 0.1f,
    borderAlpha: Float = 0.2f,
    cornerRadius: Dp = 16.dp,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.5f)
                    )
                ),
                shape = RoundedCornerShape(cornerRadius)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        border = BorderStroke(
            width = 1.dp,
            brush = Brush.linearGradient(
                colors = listOf(
                    Color.White.copy(alpha = borderAlpha),
                    Color.White.copy(alpha = borderAlpha * 0.5f)
                )
            )
        ),
        shape = RoundedCornerShape(cornerRadius)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Color.White.copy(alpha = 0.05f)
                )
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                content = content
            )
        }
    }
}

@Composable
fun GlassButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    alpha: Float = 0.15f,
    cornerRadius: Dp = 12.dp
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.7f)
                    )
                ),
                shape = RoundedCornerShape(cornerRadius)
            ),
        enabled = enabled,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            contentColor = MaterialTheme.colorScheme.onSurface
        ),
        border = BorderStroke(
            width = 1.dp,
            color = Color.White.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(cornerRadius)
    ) {
        Text(text = text)
    }
}

@Composable
fun GlassBottomNavigation(
    modifier: Modifier = Modifier,
    alpha: Float = 0.1f,
    blurRadius: Dp = 20.dp,
    content: @Composable RowScope.() -> Unit
) {
    BottomAppBar(
        modifier = modifier
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.5f)
                    )
                )
            ),
        containerColor = Color.Transparent,
        contentColor = MaterialTheme.colorScheme.onSurface,
        tonalElevation = 0.dp
    ) {
        content()
    }
}

@Composable
fun GlassDialog(
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    alpha: Float = 0.12f,
    cornerRadius: Dp = 20.dp,
    content: @Composable ColumnScope.() -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        confirmButton = { },
        modifier = modifier,
        containerColor = Color.Transparent,
        shape = RoundedCornerShape(cornerRadius),
        text = {
            GlassCard(
                alpha = alpha,
                cornerRadius = cornerRadius,
                content = content
            )
        }
    )
}

/**
 * 游戏专用毛玻璃面板
 */
@Composable
fun GameGlassPanel(
    modifier: Modifier = Modifier,
    alpha: Float = 0.08f,
    cornerRadius: Dp = 12.dp,
    content: @Composable ColumnScope.() -> Unit
) {
    GlassCard(
        modifier = modifier,
        alpha = alpha,
        cornerRadius = cornerRadius,
        borderAlpha = 0.15f,
        content = content
    )
}

/**
 * 毛玻璃浮动操作按钮
 */
@Composable
fun GlassFloatingActionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    alpha: Float = 0.2f,
    content: @Composable () -> Unit
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color.White.copy(alpha = alpha),
                        Color.White.copy(alpha = alpha * 0.6f)
                    )
                ),
                shape = RoundedCornerShape(16.dp)
            ),
        containerColor = Color.Transparent,
        contentColor = MaterialTheme.colorScheme.onSurface,
        elevation = FloatingActionButtonDefaults.elevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp
        )
    ) {
        Box(
            modifier = Modifier
                .background(
                    Color.White.copy(alpha = 0.1f),
                    RoundedCornerShape(16.dp)
                )
        ) {
            content()
        }
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun GlassmorphismPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF1565C0),
                            Color(0xFF0D47A1)
                        )
                    )
                )
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 毛玻璃卡片
            GlassCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "毛玻璃卡片",
                        style = MaterialTheme.typography.titleLarge
                    )
                    Text(
                        text = "这是一个使用毛玻璃效果的卡片组件，展现了2025年最新的设计趋势。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            // 毛玻璃按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                GlassButton(
                    text = "毛玻璃按钮",
                    onClick = { }
                )
                
                GlassButton(
                    text = "透明按钮",
                    onClick = { },
                    alpha = 0.08f
                )
            }
            
            // 游戏面板
            GameGlassPanel(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "游戏控制面板",
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = "分数: 12,345",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Text(
                        text = "等级: 5",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
        }
    }
}
