package com.yu.questicle.core.designsystem.theme

import androidx.compose.ui.graphics.Color

/**
 * Questicle 2025年色彩系统
 * 基于2025年最新设计趋势，融合现代化配色方案
 */

// ============================================================================
// 主色调 (Primary Colors) - 深度蓝色系
// ============================================================================
object PrimaryColors {
    val Primary = Color(0xFF1565C0)           // 深邃科技蓝
    val PrimaryVariant = Color(0xFF0D47A1)    // 主色变体
    val OnPrimary = Color(0xFFFFFFFF)         // 主色上的文字
    
    // 主色容器
    val PrimaryContainer = Color(0xFFE3F2FD)  // 主色容器
    val OnPrimaryContainer = Color(0xFF0D47A1) // 主色容器上的文字
}

// ============================================================================
// 辅助色调 (Secondary Colors) - 活力橙色系
// ============================================================================
object SecondaryColors {
    val Secondary = Color(0xFFFF7043)         // 活力橙
    val SecondaryVariant = Color(0xFFE64A19)  // 辅助色变体
    val OnSecondary = Color(0xFFFFFFFF)       // 辅助色上的文字
    
    // 辅助色容器
    val SecondaryContainer = Color(0xFFFFE0B2) // 辅助色容器
    val OnSecondaryContainer = Color(0xFFE64A19) // 辅助色容器上的文字
}

// ============================================================================
// 第三色调 (Tertiary Colors) - 智能紫色系
// ============================================================================
object TertiaryColors {
    val Tertiary = Color(0xFF7B1FA2)          // 智能紫
    val TertiaryVariant = Color(0xFF4A148C)   // 第三色变体
    val OnTertiary = Color(0xFFFFFFFF)        // 第三色上的文字
    
    // 第三色容器
    val TertiaryContainer = Color(0xFFF3E5F5) // 第三色容器
    val OnTertiaryContainer = Color(0xFF4A148C) // 第三色容器上的文字
}

// ============================================================================
// 功能色彩 (Functional Colors)
// ============================================================================
object FunctionalColors {
    // 成功色 - 自然绿
    val Success = Color(0xFF2E7D32)
    val OnSuccess = Color(0xFFFFFFFF)
    val SuccessContainer = Color(0xFFE8F5E8)
    val OnSuccessContainer = Color(0xFF1B5E20)
    
    // 警告色 - 阳光黄
    val Warning = Color(0xFFF57C00)
    val OnWarning = Color(0xFFFFFFFF)
    val WarningContainer = Color(0xFFFFF3E0)
    val OnWarningContainer = Color(0xFFE65100)
    
    // 错误色 - 温和红
    val Error = Color(0xFFD32F2F)
    val OnError = Color(0xFFFFFFFF)
    val ErrorContainer = Color(0xFFFFEBEE)
    val OnErrorContainer = Color(0xFFB71C1C)
    
    // 信息色 - 清新蓝
    val Info = Color(0xFF1976D2)
    val OnInfo = Color(0xFFFFFFFF)
    val InfoContainer = Color(0xFFE3F2FD)
    val OnInfoContainer = Color(0xFF0D47A1)
}

// ============================================================================
// 游戏专用色彩 (Game-Specific Colors)
// ============================================================================
object TetrisColors {
    // 俄罗斯方块标准色彩 (符合国际标准)
    val IPiece = Color(0xFF00FFFF)    // I型 - 天蓝色
    val JPiece = Color(0xFF0000FF)    // J型 - 深蓝色
    val LPiece = Color(0xFFFFA500)    // L型 - 橙色
    val OPiece = Color(0xFFFFFF00)    // O型 - 黄色
    val SPiece = Color(0xFF00FF00)    // S型 - 绿色
    val TPiece = Color(0xFF800080)    // T型 - 紫色
    val ZPiece = Color(0xFFFF0000)    // Z型 - 红色
    
    // 游戏界面色彩
    val GameBoard = Color(0xFF1A1A1A)      // 游戏板背景
    val GridLine = Color(0xFF333333)       // 网格线
    val Ghost = Color(0x4DFFFFFF)          // 幽灵方块
    val Preview = Color(0xFF2A2A2A)        // 预览区背景
    val Highlight = Color(0xFFFFFFFF)      // 高亮色
    val Shadow = Color(0xFF000000)         // 阴影色
}

// ============================================================================
// 低光模式色彩 (Low Light Mode) - 2025年趋势
// ============================================================================
object LowLightColors {
    // 低光模式专用色彩 - 减少视觉疲劳
    val Background = Color(0xFF0A0A0A)     // 极深背景
    val Surface = Color(0xFF1A1A1A)        // 表面色
    val Primary = Color(0xFF4FC3F7)        // 柔和主色
    val Secondary = Color(0xFFFFB74D)      // 柔和辅助色
    val OnSurface = Color(0xFFE0E0E0)      // 柔和文字色
    val Outline = Color(0xFF404040)        // 柔和边框
    val SurfaceVariant = Color(0xFF2A2A2A) // 表面变体
    val OnSurfaceVariant = Color(0xFFB0B0B0) // 表面变体文字
}

// ============================================================================
// 情感化配色 (Emotional Color Palettes) - 2025年趋势
// ============================================================================
object EmotionalColors {
    // 平静色系 - 用于放松场景
    val CalmPalette = listOf(
        Color(0xFF81C784), // 柔和绿
        Color(0xFF64B5F6), // 天空蓝
        Color(0xFFAED581), // 清新绿
        Color(0xFF4FC3F7)  // 浅蓝
    )
    
    // 活力色系 - 用于游戏场景
    val EnergeticPalette = listOf(
        Color(0xFFFF7043), // 活力橙
        Color(0xFFEC407A), // 粉红
        Color(0xFFAB47BC), // 紫色
        Color(0xFF42A5F5)  // 蓝色
    )
    
    // 专注色系 - 用于学习场景
    val FocusedPalette = listOf(
        Color(0xFF5C6BC0), // 深蓝
        Color(0xFF7E57C2), // 深紫
        Color(0xFF26A69A), // 青绿
        Color(0xFF66BB6A)  // 绿色
    )
}

// ============================================================================
// 渐变色彩 (Gradient Colors) - 2025年趋势
// ============================================================================
object GradientColors {
    // 主品牌渐变
    val PrimaryGradient = listOf(
        Color(0xFF1565C0),
        Color(0xFF1976D2)
    )
    
    // 游戏背景渐变
    val GameBackgroundGradient = listOf(
        Color(0xFF0A0A0A),
        Color(0xFF1A1A1A),
        Color(0xFF2A2A2A)
    )
    
    // 成功状态渐变
    val SuccessGradient = listOf(
        Color(0xFF2E7D32),
        Color(0xFF388E3C)
    )
    
    // 警告状态渐变
    val WarningGradient = listOf(
        Color(0xFFF57C00),
        Color(0xFFFF9800)
    )
}

// ============================================================================
// 透明度系统 (Alpha System)
// ============================================================================
object AlphaValues {
    const val Invisible = 0.0f
    const val Disabled = 0.38f
    const val Medium = 0.6f
    const val High = 0.87f
    const val Opaque = 1.0f
    
    // 毛玻璃效果透明度
    const val GlassLight = 0.1f
    const val GlassMedium = 0.2f
    const val GlassHeavy = 0.3f
}

// ============================================================================
// 色彩工具函数
// ============================================================================
object ColorUtils {
    /**
     * 根据背景色自动选择合适的文字颜色
     */
    fun getOnColor(backgroundColor: Color): Color {
        val luminance = calculateLuminance(backgroundColor)
        return if (luminance > 0.5f) Color.Black else Color.White
    }
    
    /**
     * 计算颜色的亮度
     */
    private fun calculateLuminance(color: Color): Float {
        val red = color.red
        val green = color.green
        val blue = color.blue
        return 0.299f * red + 0.587f * green + 0.114f * blue
    }
    
    /**
     * 创建带透明度的颜色
     */
    fun withAlpha(color: Color, alpha: Float): Color {
        return color.copy(alpha = alpha)
    }
}

// ============================================================================
// 色彩主题枚举
// ============================================================================
enum class ColorTheme {
    LIGHT,          // 浅色主题
    DARK,           // 深色主题
    LOW_LIGHT,      // 低光主题
    CALM,           // 平静主题
    ENERGETIC,      // 活力主题
    FOCUSED         // 专注主题
}
