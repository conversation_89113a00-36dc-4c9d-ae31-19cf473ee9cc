package com.yu.questicle.core.designsystem.accessibility

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.state.ToggleableState
import androidx.compose.ui.unit.dp
import android.content.Context
import android.view.accessibility.AccessibilityManager
import androidx.compose.material3.HorizontalDivider

/**
 * 无障碍支持系统 - 2025年设计规范
 * 提供完整的TalkBack和无障碍功能支持
 */

// 无障碍设置数据类
data class AccessibilitySettings(
    val talkBackEnabled: Boolean = false,
    val highContrastEnabled: Boolean = false,
    val largeTextEnabled: Boolean = false,
    val reducedMotionEnabled: Boolean = false,
    val touchExplorationEnabled: Boolean = false
)

/**
 * 无障碍管理器
 */
class QuesticleAccessibilityManager(private val context: Context) {
    private val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
    
    /**
     * 检查TalkBack是否启用
     */
    fun isTalkBackEnabled(): Boolean {
        return accessibilityManager.isEnabled && accessibilityManager.isTouchExplorationEnabled
    }
    
    /**
     * 检查是否启用了高对比度
     */
    fun isHighContrastEnabled(): Boolean {
        // Android 系统级高对比度检查
        return try {
            val resolver = context.contentResolver
            android.provider.Settings.Secure.getInt(
                resolver,
                "high_text_contrast_enabled",
                0
            ) == 1
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查是否启用了大字体
     */
    fun isLargeTextEnabled(): Boolean {
        val fontScale = context.resources.configuration.fontScale
        return fontScale > 1.3f
    }
    
    /**
     * 检查是否启用了减少动画
     */
    fun isReducedMotionEnabled(): Boolean {
        return try {
            val resolver = context.contentResolver
            android.provider.Settings.Global.getFloat(
                resolver,
                android.provider.Settings.Global.ANIMATOR_DURATION_SCALE,
                1.0f
            ) == 0.0f
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取当前无障碍设置
     */
    fun getCurrentSettings(): AccessibilitySettings {
        return AccessibilitySettings(
            talkBackEnabled = isTalkBackEnabled(),
            highContrastEnabled = isHighContrastEnabled(),
            largeTextEnabled = isLargeTextEnabled(),
            reducedMotionEnabled = isReducedMotionEnabled(),
            touchExplorationEnabled = accessibilityManager.isTouchExplorationEnabled
        )
    }
}

/**
 * 无障碍语义化修饰符
 */
object AccessibilityModifiers {
    
    /**
     * 游戏方块的无障碍描述
     */
    fun gameBlock(
        pieceType: String,
        position: String,
        isActive: Boolean = false
    ): Modifier {
        return Modifier.semantics {
            contentDescription = if (isActive) {
                "当前活动方块：$pieceType，位置：$position"
            } else {
                "游戏方块：$pieceType，位置：$position"
            }
            
            if (isActive) {
                stateDescription = "活动状态"
            }
            
            role = Role.Image
        }
    }
    
    /**
     * 游戏控制按钮的无障碍描述
     */
    fun gameControlButton(
        action: String,
        isEnabled: Boolean = true,
        hint: String? = null
    ): Modifier {
        return Modifier.semantics {
            contentDescription = "$action 按钮"
            
            if (!isEnabled) {
                stateDescription = "不可用"
            }
            
            hint?.let {
                // onClickLabel = it // 暂时注释，等待Compose更新
            }
            
            role = Role.Button
        }
    }
    
    /**
     * 游戏状态信息的无障碍描述
     */
    fun gameStatusInfo(
        label: String,
        value: String,
        importance: Boolean = false
    ): Modifier {
        return Modifier.semantics {
            contentDescription = "$label：$value"
            
            if (importance) {
                liveRegion = LiveRegionMode.Polite
            }
            
            role = Role.Image
        }
    }
    
    /**
     * 游戏板区域的无障碍描述
     */
    fun gameBoard(
        width: Int,
        height: Int,
        filledCells: Int
    ): Modifier {
        return Modifier.semantics {
            contentDescription = "游戏板，大小 $width 乘 $height，已填充 $filledCells 个方块"
            role = Role.Image
            
            // 添加自定义动作
            customActions = listOf(
                CustomAccessibilityAction("查看游戏状态") {
                    // 触发游戏状态播报
                    true
                },
                CustomAccessibilityAction("查看下一个方块") {
                    // 触发下一个方块信息播报
                    true
                }
            )
        }
    }
    
    /**
     * 设置项的无障碍描述
     */
    fun settingItem(
        title: String,
        value: String,
        isToggle: Boolean = false
    ): Modifier {
        return Modifier.semantics {
            contentDescription = "$title：$value"
            
            if (isToggle) {
                role = Role.Switch
                toggleableState = if (value == "开启") {
                    ToggleableState.On
                } else {
                    ToggleableState.Off
                }
            } else {
                role = Role.Button
            }
        }
    }
}

/**
 * 无障碍语音播报组件
 */
@Composable
fun AccessibilityAnnouncement(
    message: String,
    priority: LiveRegionMode = LiveRegionMode.Polite
) {
    Box(
        modifier = Modifier
            .size(0.dp)
            .semantics {
                liveRegion = priority
                contentDescription = message
            }
    )
}

/**
 * 游戏状态语音播报
 */
@Composable
fun GameStatusAnnouncement(
    score: Int,
    level: Int,
    lines: Int,
    shouldAnnounce: Boolean
) {
    if (shouldAnnounce) {
        AccessibilityAnnouncement(
            message = "游戏状态更新：分数 $score，等级 $level，消除行数 $lines",
            priority = LiveRegionMode.Polite
        )
    }
}

/**
 * 游戏事件语音播报
 */
@Composable
fun GameEventAnnouncement(
    event: String,
    shouldAnnounce: Boolean
) {
    if (shouldAnnounce) {
        AccessibilityAnnouncement(
            message = event,
            priority = LiveRegionMode.Assertive
        )
    }
}

/**
 * 无障碍设置面板
 */
@Composable
fun AccessibilitySettingsPanel(
    settings: AccessibilitySettings,
    onSettingsChange: (AccessibilitySettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "无障碍设置",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            // TalkBack状态显示
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        AccessibilityModifiers.settingItem(
                            "TalkBack状态",
                            if (settings.talkBackEnabled) "已启用" else "未启用"
                        )
                    ),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("TalkBack状态")
                Text(
                    text = if (settings.talkBackEnabled) "已启用" else "未启用",
                    color = if (settings.talkBackEnabled) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            
            // 高对比度设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        AccessibilityModifiers.settingItem(
                            "高对比度",
                            if (settings.highContrastEnabled) "开启" else "关闭",
                            isToggle = true
                        )
                    ),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("高对比度")
                Switch(
                    checked = settings.highContrastEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(highContrastEnabled = it))
                    }
                )
            }
            
            // 大字体设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        AccessibilityModifiers.settingItem(
                            "大字体",
                            if (settings.largeTextEnabled) "开启" else "关闭",
                            isToggle = true
                        )
                    ),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("大字体")
                Switch(
                    checked = settings.largeTextEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(largeTextEnabled = it))
                    }
                )
            }
            
            // 减少动画设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .then(
                        AccessibilityModifiers.settingItem(
                            "减少动画",
                            if (settings.reducedMotionEnabled) "开启" else "关闭",
                            isToggle = true
                        )
                    ),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("减少动画")
                Switch(
                    checked = settings.reducedMotionEnabled,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(reducedMotionEnabled = it))
                    }
                )
            }

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            // 无障碍使用提示
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "无障碍使用提示",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.semantics {
                        heading()
                    }
                )
                
                Text(
                    text = "• 启用TalkBack后，可以通过语音获得游戏状态信息",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = "• 双击屏幕可以执行操作，滑动可以浏览界面",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = "• 游戏支持键盘操作和外接控制器",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * Compose无障碍Hook
 */
@Composable
fun rememberAccessibilityManager(): QuesticleAccessibilityManager {
    val context = LocalContext.current
    return remember { QuesticleAccessibilityManager(context) }
}

/**
 * 无障碍状态监听
 */
@Composable
fun AccessibilityStateListener(
    onAccessibilityStateChanged: (AccessibilitySettings) -> Unit
) {
    val accessibilityManager = rememberAccessibilityManager()

    LaunchedEffect(Unit) {
        // 定期检查无障碍状态变化
        while (true) {
            val currentSettings = accessibilityManager.getCurrentSettings()
            onAccessibilityStateChanged(currentSettings)
            kotlinx.coroutines.delay(1000) // 每秒检查一次
        }
    }
}
