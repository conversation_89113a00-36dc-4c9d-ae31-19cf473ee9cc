package com.yu.questicle.core.designsystem.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight

import androidx.compose.ui.unit.sp
import com.yu.questicle.core.designsystem.R

/**
 * 鸿蒙字体家族定义
 * HarmonyOS Sans 字体系统，专为中文优化
 */
val HarmonyOSFontFamily = FontFamily(
    Font(R.font.harmonyos_sans_thin, FontWeight.Thin),        // 100
    Font(R.font.harmonyos_sans_light, FontWeight.Light),      // 300
    Font(R.font.harmonyos_sans_regular, FontWeight.Normal),   // 400
    Font(R.font.harmonyos_sans_medium, FontWeight.Medium),    // 500
    Font(R.font.harmonyos_sans_bold, FontWeight.Bold),        // 700
    Font(R.font.harmonyos_sans_black, FontWeight.Black)       // 900
)

/**
 * 游戏专用等宽字体 - 用于分数和数据显示
 * 使用系统等宽字体作为备选方案
 */
val GameMonoFontFamily = FontFamily.Monospace

/**
 * 鸿蒙字体Typography系统
 * 基于Material 3规范，使用鸿蒙字体
 */
val HarmonyOSTypography = Typography(
    // 显示级别 - 用于大标题和品牌展示
    displayLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Black,
        fontSize = 57.sp,
        lineHeight = 64.sp,
        letterSpacing = (-0.25).sp
    ),
    displayMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 45.sp,
        lineHeight = 52.sp,
        letterSpacing = 0.sp,
    ),
    displaySmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 36.sp,
        lineHeight = 44.sp,
        letterSpacing = 0.sp,
    ),
    
    // 标题级别 - 用于页面标题和重要信息
    headlineLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 0.sp,
    ),
    headlineMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp,
    ),
    headlineSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 0.sp,
    ),
    
    // 标题级别 - 用于卡片标题和组件标题
    titleLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp,
    ),
    titleMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp,
    ),
    titleSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
    ),
    
    // 正文级别 - 用于主要内容
    bodyLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp,
    ),
    bodyMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp,
    ),
    bodySmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp,
    ),
    
    // 标签级别 - 用于按钮和小标签
    labelLarge = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
    ),
    labelMedium = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
    ),
    labelSmall = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
    )
)

/**
 * 游戏专用字体样式
 * 用于游戏界面的特殊显示需求
 */
object GameTypography {
    // 分数显示 - 大号等宽字体，突出显示
    val ScoreDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 2.sp,
    )
    
    // 等级显示 - 中号等宽字体
    val LevelDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 1.sp,
    )
    
    // 统计数据 - 小号等宽字体
    val StatDisplay = TextStyle(
        fontFamily = GameMonoFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp,
    )
    
    // 游戏提示文字 - 鸿蒙字体，清晰易读
    val GameHint = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp,
    )
    
    // 游戏标题 - 鸿蒙字体，醒目大标题
    val GameTitle = TextStyle(
        fontFamily = HarmonyOSFontFamily,
        fontWeight = FontWeight.Black,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp,
    )
}
