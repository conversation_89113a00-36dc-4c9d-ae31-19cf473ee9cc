package com.yu.questicle.core.designsystem.audio

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.components.QuesticleButton
import com.yu.questicle.core.designsystem.components.ButtonStyle
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * 音效测试界面
 * 用于测试和调试游戏音效系统
 */

@Composable
fun AudioTestScreen(
    modifier: Modifier = Modifier
) {
    val audioManager = rememberGameAudioManager()
    var audioSettings by remember { mutableStateOf(AudioSettings()) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔊 音效系统测试",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.primary
        )
        
        // 音效设置面板
        AudioSettingsPanel(
            settings = audioSettings,
            onSettingsChange = { newSettings ->
                audioSettings = newSettings
                audioManager.updateSettings(newSettings)
            }
        )
        
        // 音效测试区域
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "音效测试",
                    style = MaterialTheme.typography.titleLarge
                )
                
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.height(400.dp)
                ) {
                    items(getSoundTestItems()) { testItem ->
                        SoundTestItem(
                            testItem = testItem,
                            onPlay = { soundType ->
                                audioManager.playSound(soundType)
                            }
                        )
                    }
                }
            }
        }
        
        // 游戏动作测试
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "游戏动作音效测试",
                    style = MaterialTheme.typography.titleLarge
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    QuesticleButton(
                        text = "移动",
                        onClick = { 
                            audioManager.playGameAction(com.yu.questicle.core.designsystem.game.TetrisAction.MOVE_LEFT)
                        },
                        style = ButtonStyle.Secondary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    QuesticleButton(
                        text = "旋转",
                        onClick = { 
                            audioManager.playGameAction(com.yu.questicle.core.designsystem.game.TetrisAction.ROTATE)
                        },
                        style = ButtonStyle.Secondary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    QuesticleButton(
                        text = "下降",
                        onClick = { 
                            audioManager.playGameAction(com.yu.questicle.core.designsystem.game.TetrisAction.SOFT_DROP)
                        },
                        style = ButtonStyle.Secondary,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    QuesticleButton(
                        text = "1行消除",
                        onClick = { audioManager.playLineClear(1) },
                        style = ButtonStyle.Tertiary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    QuesticleButton(
                        text = "2行消除",
                        onClick = { audioManager.playLineClear(2) },
                        style = ButtonStyle.Tertiary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    QuesticleButton(
                        text = "Tetris!",
                        onClick = { audioManager.playLineClear(4) },
                        style = ButtonStyle.Success,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
        
        // 音效状态信息
        AudioStatusInfo(
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun SoundTestItem(
    testItem: SoundTestItem,
    onPlay: (GameSoundType) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = testItem.name,
                    style = MaterialTheme.typography.titleSmall
                )
                Text(
                    text = testItem.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            QuesticleButton(
                text = "播放",
                onClick = { onPlay(testItem.soundType) },
                style = ButtonStyle.Primary
            )
        }
    }
}

@Composable
private fun AudioStatusInfo(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "📋 音效状态信息",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = "• 音效文件位置: core/designsystem/src/main/res/raw/",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = "• 如果听不到声音，请运行: ./scripts/setup-audio-resources.sh",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Text(
                text = "• 音效资源指南: docs/design/audio-resources-guide.md",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
        }
    }
}

// 音效测试项目数据类
data class SoundTestItem(
    val soundType: GameSoundType,
    val name: String,
    val description: String,
    val category: String
)

// 获取音效测试项目列表
private fun getSoundTestItems(): List<SoundTestItem> {
    return listOf(
        // UI音效
        SoundTestItem(
            GameSoundType.BUTTON_CLICK,
            "按钮点击",
            "用户点击按钮时的反馈音效",
            "UI"
        ),
        SoundTestItem(
            GameSoundType.MENU_NAVIGATE,
            "菜单导航",
            "在菜单间切换时的音效",
            "UI"
        ),
        SoundTestItem(
            GameSoundType.SUCCESS,
            "成功提示",
            "操作成功时的确认音效",
            "反馈"
        ),
        SoundTestItem(
            GameSoundType.ERROR,
            "错误提示",
            "操作失败时的警告音效",
            "反馈"
        ),
        SoundTestItem(
            GameSoundType.WARNING,
            "警告提示",
            "需要注意时的提醒音效",
            "反馈"
        ),
        
        // 游戏音效
        SoundTestItem(
            GameSoundType.PIECE_MOVE,
            "方块移动",
            "俄罗斯方块左右移动音效",
            "游戏"
        ),
        SoundTestItem(
            GameSoundType.PIECE_ROTATE,
            "方块旋转",
            "俄罗斯方块旋转音效",
            "游戏"
        ),
        SoundTestItem(
            GameSoundType.PIECE_DROP,
            "方块下降",
            "俄罗斯方块软降音效",
            "游戏"
        ),
        SoundTestItem(
            GameSoundType.PIECE_LOCK,
            "方块锁定",
            "俄罗斯方块锁定到位音效",
            "游戏"
        ),
        
        // 特效音效
        SoundTestItem(
            GameSoundType.LINE_CLEAR_SINGLE,
            "单行消除",
            "消除一行时的音效",
            "特效"
        ),
        SoundTestItem(
            GameSoundType.LINE_CLEAR_TETRIS,
            "四行消除",
            "Tetris四行消除音效",
            "特效"
        ),
        SoundTestItem(
            GameSoundType.LEVEL_UP,
            "等级提升",
            "游戏等级提升音效",
            "特效"
        ),
        SoundTestItem(
            GameSoundType.GAME_OVER,
            "游戏结束",
            "游戏结束时的音效",
            "特效"
        ),
        SoundTestItem(
            GameSoundType.ACHIEVEMENT_UNLOCK,
            "成就解锁",
            "获得成就时的庆祝音效",
            "特效"
        )
    )
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun AudioTestScreenPreview() {
    QuesticleTheme {
        AudioTestScreen()
    }
}
