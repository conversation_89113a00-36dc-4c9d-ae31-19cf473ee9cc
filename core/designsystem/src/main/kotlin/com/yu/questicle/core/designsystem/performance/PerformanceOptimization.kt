package com.yu.questicle.core.designsystem.performance

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.system.measureTimeMillis

/**
 * 性能优化系统 - 2025年设计规范
 * 提供60fps稳定渲染和内存优化
 */

// 性能设置数据类
data class PerformanceSettings(
    val targetFPS: Int = 60,
    val enableVSync: Boolean = true,
    val enableGPUAcceleration: Boolean = true,
    val maxMemoryUsage: Long = 100 * 1024 * 1024, // 100MB
    val enableLazyLoading: Boolean = true,
    val enableImageCaching: Boolean = true,
    val reducedAnimations: Boolean = false,
    val enablePerformanceMonitoring: Boolean = false
)

// 性能指标数据类
data class PerformanceMetrics(
    val currentFPS: Float = 0f,
    val averageFPS: Float = 0f,
    val frameTime: Long = 0L,
    val memoryUsage: Long = 0L,
    val maxMemoryUsage: Long = 0L,
    val droppedFrames: Int = 0,
    val renderTime: Long = 0L,
    val cpuUsage: Float = 0f
)

/**
 * 性能监控管理器
 */
class PerformanceMonitor {
    private val _metrics = MutableStateFlow(PerformanceMetrics())
    val metrics: StateFlow<PerformanceMetrics> = _metrics
    
    private val frameTimeHistory = mutableListOf<Long>()
    private val maxHistorySize = 60 // 保存60帧的历史数据
    private var lastFrameTime = System.nanoTime()
    private var droppedFrameCount = 0
    
    /**
     * 记录帧渲染时间
     */
    fun recordFrame() {
        val currentTime = System.nanoTime()
        val frameTime = currentTime - lastFrameTime
        lastFrameTime = currentTime
        
        // 添加到历史记录
        frameTimeHistory.add(frameTime)
        if (frameTimeHistory.size > maxHistorySize) {
            frameTimeHistory.removeAt(0)
        }
        
        // 计算FPS
        val currentFPS = if (frameTime > 0) {
            1_000_000_000f / frameTime
        } else {
            0f
        }
        
        val averageFPS = if (frameTimeHistory.isNotEmpty()) {
            val averageFrameTime = frameTimeHistory.average()
            1_000_000_000f / averageFrameTime.toFloat()
        } else {
            0f
        }
        
        // 检测掉帧
        if (frameTime > 16_666_667) { // 超过16.67ms (60fps)
            droppedFrameCount++
        }
        
        // 获取内存使用情况
        val runtime = Runtime.getRuntime()
        val memoryUsage = runtime.totalMemory() - runtime.freeMemory()
        val maxMemoryUsage = runtime.maxMemory()
        
        // 更新指标
        _metrics.value = PerformanceMetrics(
            currentFPS = currentFPS,
            averageFPS = averageFPS,
            frameTime = frameTime / 1_000_000, // 转换为毫秒
            memoryUsage = memoryUsage,
            maxMemoryUsage = maxMemoryUsage,
            droppedFrames = droppedFrameCount,
            renderTime = frameTime / 1_000_000
        )
    }
    
    /**
     * 重置统计数据
     */
    fun reset() {
        frameTimeHistory.clear()
        droppedFrameCount = 0
        _metrics.value = PerformanceMetrics()
    }
    
    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val currentMetrics = _metrics.value
        
        if (currentMetrics.averageFPS < 50) {
            recommendations.add("帧率较低，建议降低画质设置")
        }
        
        if (currentMetrics.droppedFrames > 10) {
            recommendations.add("检测到掉帧，建议关闭部分动画效果")
        }
        
        val memoryUsagePercent = (currentMetrics.memoryUsage.toFloat() / currentMetrics.maxMemoryUsage) * 100
        if (memoryUsagePercent > 80) {
            recommendations.add("内存使用率过高，建议清理缓存")
        }
        
        if (currentMetrics.renderTime > 16) {
            recommendations.add("渲染时间过长，建议优化UI复杂度")
        }
        
        return recommendations
    }
}

/**
 * 内存管理器
 */
class MemoryManager {
    private val imageCache = mutableMapOf<String, Any>()
    private val maxCacheSize = 50 * 1024 * 1024 // 50MB
    private var currentCacheSize = 0L
    
    /**
     * 清理内存缓存
     */
    fun clearCache() {
        imageCache.clear()
        currentCacheSize = 0L
        System.gc() // 建议垃圾回收
    }
    
    /**
     * 获取内存使用情况
     */
    fun getMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        return MemoryInfo(
            totalMemory = runtime.totalMemory(),
            freeMemory = runtime.freeMemory(),
            maxMemory = runtime.maxMemory(),
            usedMemory = runtime.totalMemory() - runtime.freeMemory(),
            cacheSize = currentCacheSize
        )
    }
    
    /**
     * 检查是否需要清理内存
     */
    fun shouldCleanMemory(): Boolean {
        val memoryInfo = getMemoryInfo()
        val usagePercent = (memoryInfo.usedMemory.toFloat() / memoryInfo.maxMemory) * 100
        return usagePercent > 85 || currentCacheSize > maxCacheSize
    }
}

// 内存信息数据类
data class MemoryInfo(
    val totalMemory: Long,
    val freeMemory: Long,
    val maxMemory: Long,
    val usedMemory: Long,
    val cacheSize: Long
)

/**
 * 性能优化的Composable修饰符
 */
object PerformanceModifiers {
    
    /**
     * 懒加载修饰符
     */
    fun Modifier.lazyRendering(
        isVisible: Boolean,
        placeholder: @Composable () -> Unit = { }
    ): Modifier {
        return this.then(
            if (isVisible) {
                Modifier
            } else {
                Modifier.size(0.dp)
            }
        )
    }
    
    /**
     * 减少重组的修饰符
     */
    fun Modifier.stableComposition(): Modifier {
        return this // 在实际实现中可以添加稳定性优化
    }
    
    /**
     * 内存优化修饰符
     */
    fun Modifier.memoryOptimized(): Modifier {
        return this // 在实际实现中可以添加内存优化
    }
}

/**
 * 性能监控面板
 */
@Composable
fun PerformanceMonitorPanel(
    performanceMonitor: PerformanceMonitor,
    modifier: Modifier = Modifier
) {
    val metrics by performanceMonitor.metrics.collectAsState()
    val density = LocalDensity.current
    
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "性能监控",
                style = MaterialTheme.typography.titleMedium
            )
            
            // FPS显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("当前FPS")
                Text(
                    text = "${metrics.currentFPS.toInt()}",
                    color = when {
                        metrics.currentFPS >= 55 -> MaterialTheme.colorScheme.primary
                        metrics.currentFPS >= 30 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }
            
            // 平均FPS
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("平均FPS")
                Text("${metrics.averageFPS.toInt()}")
            }
            
            // 帧时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("帧时间")
                Text("${metrics.frameTime}ms")
            }
            
            // 内存使用
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("内存使用")
                Text("${metrics.memoryUsage / 1024 / 1024}MB")
            }
            
            // 掉帧数
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("掉帧数")
                Text(
                    text = "${metrics.droppedFrames}",
                    color = if (metrics.droppedFrames > 5) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
            }
            
            // 性能建议
            val recommendations = performanceMonitor.getPerformanceRecommendations()
            if (recommendations.isNotEmpty()) {
                HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)
                Text(
                    text = "性能建议",
                    style = MaterialTheme.typography.titleSmall
                )
                
                recommendations.forEach { recommendation ->
                    Text(
                        text = "• $recommendation",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { performanceMonitor.reset() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("重置")
                }
                
                Button(
                    onClick = { System.gc() },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清理内存")
                }
            }
        }
    }
}

/**
 * 性能设置面板
 */
@Composable
fun PerformanceSettingsPanel(
    settings: PerformanceSettings,
    onSettingsChange: (PerformanceSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "性能设置",
                style = MaterialTheme.typography.titleMedium
            )
            
            // 目标FPS设置
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("目标FPS")
                Text("${settings.targetFPS}")
            }
            
            Slider(
                value = settings.targetFPS.toFloat(),
                onValueChange = { 
                    onSettingsChange(settings.copy(targetFPS = it.toInt()))
                },
                valueRange = 30f..120f,
                steps = 8
            )
            
            // VSync开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("垂直同步")
                Switch(
                    checked = settings.enableVSync,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(enableVSync = it))
                    }
                )
            }
            
            // GPU加速开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("GPU加速")
                Switch(
                    checked = settings.enableGPUAcceleration,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(enableGPUAcceleration = it))
                    }
                )
            }
            
            // 懒加载开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("懒加载")
                Switch(
                    checked = settings.enableLazyLoading,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(enableLazyLoading = it))
                    }
                )
            }
            
            // 减少动画开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("减少动画")
                Switch(
                    checked = settings.reducedAnimations,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(reducedAnimations = it))
                    }
                )
            }
            
            // 性能监控开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("性能监控")
                Switch(
                    checked = settings.enablePerformanceMonitoring,
                    onCheckedChange = { 
                        onSettingsChange(settings.copy(enablePerformanceMonitoring = it))
                    }
                )
            }
        }
    }
}

/**
 * 性能优化Hook
 */
@Composable
fun rememberPerformanceMonitor(): PerformanceMonitor {
    return remember { PerformanceMonitor() }
}

@Composable
fun rememberMemoryManager(): MemoryManager {
    return remember { MemoryManager() }
}

/**
 * 性能优化的LaunchedEffect
 */
@Composable
fun PerformanceOptimizedEffect(
    key: Any?,
    enabled: Boolean = true,
    block: suspend () -> Unit
) {
    if (enabled) {
        LaunchedEffect(key) {
            val executionTime = measureTimeMillis {
                block()
            }
            // 记录执行时间用于性能分析
        }
    }
}
