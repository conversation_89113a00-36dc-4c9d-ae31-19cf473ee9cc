package com.yu.questicle.core.designsystem.effects

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yu.questicle.core.designsystem.theme.QuesticleTheme

/**
 * 微交互动效系统 - 2025年设计趋势
 * 精致的细节动效提升用户体验
 */

// 触觉反馈强度
enum class HapticIntensity {
    Light,      // 轻微反馈 - 移动操作
    Medium,     // 中等反馈 - 旋转操作
    Strong      // 强烈反馈 - 消除行、游戏结束
}

/**
 * 微交互按钮 - 带有精致的按压动效
 */
@Composable
fun MicroInteractionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    hapticIntensity: HapticIntensity = HapticIntensity.Medium,
    content: @Composable () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }
    var isHovered by remember { mutableStateOf(false) }
    val hapticFeedback = LocalHapticFeedback.current
    
    val scale by animateFloatAsState(
        targetValue = when {
            isPressed -> 0.95f
            isHovered -> 1.05f
            else -> 1.0f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "button_scale"
    )
    
    val elevation by animateDpAsState(
        targetValue = if (isPressed) 2.dp else 8.dp,
        animationSpec = tween(150),
        label = "button_elevation"
    )
    
    Button(
        onClick = {
            // 触觉反馈
            when (hapticIntensity) {
                HapticIntensity.Light -> hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.TextHandleMove)
                HapticIntensity.Medium -> hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.LongPress)
                HapticIntensity.Strong -> hapticFeedback.performHapticFeedback(androidx.compose.ui.hapticfeedback.HapticFeedbackType.LongPress)
            }
            onClick()
        },
        modifier = modifier
            .scale(scale)
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    }
                )
            },
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = elevation
        )
    ) {
        content()
    }
}

/**
 * 脉冲加载指示器 - 呼吸式动画
 */
@Composable
fun PulsingLoadingIndicator(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    size: androidx.compose.ui.unit.Dp = 40.dp
) {
    val infiniteTransition = rememberInfiniteTransition(label = "loading")
    
    val scale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale"
    )
    
    val alpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "alpha"
    )
    
    Box(
        modifier = modifier
            .size(size)
            .scale(scale)
            .background(
                color = color.copy(alpha = alpha),
                shape = CircleShape
            )
    )
}

/**
 * 波纹点击效果 - 自定义波纹动画
 */
@Composable
fun RippleClickEffect(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    rippleColor: Color = MaterialTheme.colorScheme.primary,
    content: @Composable () -> Unit
) {
    var isClicked by remember { mutableStateOf(false) }
    
    val rippleScale by animateFloatAsState(
        targetValue = if (isClicked) 3.0f else 0f,
        animationSpec = tween(300, easing = FastOutSlowInEasing),
        finishedListener = { isClicked = false },
        label = "ripple_scale"
    )
    
    val rippleAlpha by animateFloatAsState(
        targetValue = if (isClicked) 0f else 0.3f,
        animationSpec = tween(300, easing = FastOutSlowInEasing),
        label = "ripple_alpha"
    )
    
    Box(
        modifier = modifier
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                isClicked = true
                onClick()
            }
    ) {
        content()
        
        // 波纹效果
        if (isClicked) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .scale(rippleScale)
                    .background(
                        color = rippleColor.copy(alpha = rippleAlpha),
                        shape = CircleShape
                    )
            )
        }
    }
}

/**
 * 悬浮卡片 - 鼠标悬停时的微妙动效
 */
@Composable
fun HoverCard(
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var isHovered by remember { mutableStateOf(false) }
    
    val elevation by animateDpAsState(
        targetValue = if (isHovered) 12.dp else 4.dp,
        animationSpec = tween(200),
        label = "card_elevation"
    )
    
    val scale by animateFloatAsState(
        targetValue = if (isHovered) 1.02f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "card_scale"
    )
    
    Card(
        modifier = modifier
            .scale(scale)
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else Modifier
            ),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation)
    ) {
        content()
    }
}

/**
 * 弹性按钮 - 按压时的弹性反馈
 */
@Composable
fun BouncyButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "bouncy_scale"
    )
    
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier
            .scale(scale)
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = {
                        isPressed = true
                        tryAwaitRelease()
                        isPressed = false
                    }
                )
            }
    ) {
        Text(text = text)
    }
}

/**
 * 旋转加载器 - 平滑的旋转动画
 */
@Composable
fun RotatingLoader(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    size: androidx.compose.ui.unit.Dp = 32.dp
) {
    val infiniteTransition = rememberInfiniteTransition(label = "rotation")
    
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )
    
    Box(
        modifier = modifier
            .size(size)
            .graphicsLayer {
                rotationZ = rotation
            }
            .background(
                color = color,
                shape = RoundedCornerShape(4.dp)
            )
    )
}

/**
 * 成功动画 - 勾选标记动画
 */
@Composable
fun SuccessAnimation(
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    color: Color = Color.Green
) {
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1.0f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "success_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isVisible) 1.0f else 0f,
        animationSpec = tween(300),
        label = "success_alpha"
    )
    
    Box(
        modifier = modifier
            .size(48.dp)
            .scale(scale)
            .background(
                color = color.copy(alpha = alpha),
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "✓",
            color = Color.White,
            style = MaterialTheme.typography.headlineSmall
        )
    }
}

/**
 * 游戏方块掉落动画
 */
@Composable
fun BlockDropAnimation(
    isDropping: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val offsetY by animateFloatAsState(
        targetValue = if (isDropping) 100f else 0f,
        animationSpec = tween(500, easing = FastOutSlowInEasing),
        label = "drop_offset"
    )
    
    val rotation by animateFloatAsState(
        targetValue = if (isDropping) 180f else 0f,
        animationSpec = tween(500, easing = FastOutSlowInEasing),
        label = "drop_rotation"
    )
    
    Box(
        modifier = modifier
            .graphicsLayer {
                translationY = offsetY
                rotationZ = rotation
            }
    ) {
        content()
    }
}

// 预览组件
@Preview(showBackground = true)
@Composable
private fun MicroInteractionsPreview() {
    QuesticleTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "微交互动效演示",
                style = MaterialTheme.typography.headlineMedium
            )
            
            // 微交互按钮
            MicroInteractionButton(
                onClick = { }
            ) {
                Text("微交互按钮")
            }
            
            // 弹性按钮
            BouncyButton(
                text = "弹性按钮",
                onClick = { }
            )
            
            // 波纹点击效果
            RippleClickEffect(
                onClick = { }
            ) {
                Card(
                    modifier = Modifier
                        .size(100.dp)
                        .padding(8.dp)
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text("点击我")
                    }
                }
            }
            
            // 加载指示器
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                PulsingLoadingIndicator()
                RotatingLoader()
            }
            
            // 悬浮卡片
            HoverCard(
                onClick = { }
            ) {
                Box(
                    modifier = Modifier
                        .size(120.dp, 80.dp)
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text("悬浮卡片")
                }
            }
            
            // 成功动画
            var showSuccess by remember { mutableStateOf(false) }
            
            Button(
                onClick = { showSuccess = !showSuccess }
            ) {
                Text("切换成功动画")
            }
            
            SuccessAnimation(isVisible = showSuccess)
        }
    }
}
