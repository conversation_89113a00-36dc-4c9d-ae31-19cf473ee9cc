package com.yu.questicle.core.testing.performance

import kotlinx.serialization.Serializable

/**
 * 高级性能监控配置
 * 
 * 用于配置详细的性能监控参数
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Serializable
data class AdvancedPerformanceConfig(
    val enableFrameRateMonitoring: Boolean = true,
    val enableMemoryMonitoring: Boolean = true,
    val enableCpuMonitoring: Boolean = true,
    val enableNetworkMonitoring: Boolean = false,
    val snapshotIntervalMs: Long = 5000L,
    val frameRateThreshold: Int = 55,
    val memoryThreshold: Double = 0.8,
    val cpuThreshold: Double = 80.0,
    val enableDetailedLogging: Boolean = false,
    val enableAlerts: Boolean = true,
    val maxSnapshotsInMemory: Int = 100,
    val enableAutoOptimization: Boolean = false
) {
    /**
     * 验证配置有效性
     */
    fun validate(): Boolean {
        return snapshotIntervalMs > 0 &&
                frameRateThreshold > 0 &&
                memoryThreshold in 0.0..1.0 &&
                cpuThreshold in 0.0..100.0 &&
                maxSnapshotsInMemory > 0
    }
    
    /**
     * 获取监控间隔（秒）
     */
    fun getSnapshotIntervalSeconds(): Double = snapshotIntervalMs / 1000.0
    
    /**
     * 是否启用任何监控
     */
    fun isAnyMonitoringEnabled(): Boolean {
        return enableFrameRateMonitoring || 
               enableMemoryMonitoring || 
               enableCpuMonitoring || 
               enableNetworkMonitoring
    }
    
    /**
     * 获取启用的监控类型数量
     */
    fun getEnabledMonitoringCount(): Int {
        var count = 0
        if (enableFrameRateMonitoring) count++
        if (enableMemoryMonitoring) count++
        if (enableCpuMonitoring) count++
        if (enableNetworkMonitoring) count++
        return count
    }
    
    companion object {
        /**
         * 默认配置
         */
        val DEFAULT = AdvancedPerformanceConfig()
        
        /**
         * 轻量级配置
         */
        val LIGHTWEIGHT = AdvancedPerformanceConfig(
            enableFrameRateMonitoring = true,
            enableMemoryMonitoring = true,
            enableCpuMonitoring = false,
            enableNetworkMonitoring = false,
            snapshotIntervalMs = 10000L,
            enableDetailedLogging = false,
            maxSnapshotsInMemory = 50
        )
        
        /**
         * 详细监控配置
         */
        val DETAILED = AdvancedPerformanceConfig(
            enableFrameRateMonitoring = true,
            enableMemoryMonitoring = true,
            enableCpuMonitoring = true,
            enableNetworkMonitoring = true,
            snapshotIntervalMs = 1000L,
            enableDetailedLogging = true,
            enableAlerts = true,
            maxSnapshotsInMemory = 200,
            enableAutoOptimization = true
        )
        
        /**
         * 调试配置
         */
        val DEBUG = AdvancedPerformanceConfig(
            enableFrameRateMonitoring = true,
            enableMemoryMonitoring = true,
            enableCpuMonitoring = true,
            enableNetworkMonitoring = true,
            snapshotIntervalMs = 500L,
            frameRateThreshold = 30,
            memoryThreshold = 0.9,
            cpuThreshold = 90.0,
            enableDetailedLogging = true,
            enableAlerts = true,
            maxSnapshotsInMemory = 500,
            enableAutoOptimization = false
        )
    }
}
