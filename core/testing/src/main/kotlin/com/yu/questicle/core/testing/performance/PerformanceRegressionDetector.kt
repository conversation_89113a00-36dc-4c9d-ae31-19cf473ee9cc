package com.yu.questicle.core.testing.performance

import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * Performance regression detection system that compares current performance
 * against baseline measurements and detects significant changes.
 */
class PerformanceRegressionDetector(
    private val baselineDirectory: File,
    private val alertThreshold: Double = 10.0, // 10% degradation threshold
    private val improvementThreshold: Double = 5.0 // 5% improvement threshold
) {
    
    private val json = Json { prettyPrint = true }
    
    init {
        baselineDirectory.mkdirs()
    }
    
    /**
     * Create or update baseline performance measurements
     */
    fun createBaseline(
        testName: String,
        results: List<PerformanceResult>
    ): BaselineData {
        val baseline = BaselineData(
            testName = testName,
            createdAt = LocalDateTime.now(),
            results = results.map { result ->
                BaselineResult(
                    operationName = result.testName,
                    averageTime = result.averageTime,
                    operationsPerSecond = result.operationsPerSecond,
                    standardDeviation = result.standardDeviation,
                    iterations = result.iterations
                )
            }
        )
        
        saveBaseline(baseline)
        return baseline
    }
    
    /**
     * Detect performance regressions by comparing current results against baseline
     */
    fun detectRegressions(
        testName: String,
        currentResults: List<PerformanceResult>
    ): RegressionAnalysis {
        val baseline = loadBaseline(testName)
            ?: return RegressionAnalysis(
                testName = testName,
                hasBaseline = false,
                regressions = emptyList(),
                improvements = emptyList(),
                overallStatus = RegressionStatus.NO_BASELINE
            )
        
        val regressions = mutableListOf<PerformanceRegression>()
        val improvements = mutableListOf<PerformanceImprovement>()
        
        currentResults.forEach { current ->
            val baselineResult = baseline.results.find { it.operationName == current.testName }
            if (baselineResult != null) {
                val changePercent = calculatePerformanceChange(baselineResult.averageTime, current.averageTime)
                
                when {
                    changePercent > alertThreshold -> {
                        regressions.add(
                            PerformanceRegression(
                                operationName = current.testName,
                                baselineTime = baselineResult.averageTime,
                                currentTime = current.averageTime,
                                degradationPercent = changePercent,
                                severity = calculateSeverity(changePercent)
                            )
                        )
                    }
                    changePercent < -improvementThreshold -> {
                        improvements.add(
                            PerformanceImprovement(
                                operationName = current.testName,
                                baselineTime = baselineResult.averageTime,
                                currentTime = current.averageTime,
                                improvementPercent = -changePercent
                            )
                        )
                    }
                }
            }
        }
        
        val overallStatus = when {
            regressions.any { it.severity == RegressionSeverity.CRITICAL } -> RegressionStatus.CRITICAL_REGRESSION
            regressions.isNotEmpty() -> RegressionStatus.REGRESSION_DETECTED
            improvements.isNotEmpty() -> RegressionStatus.IMPROVEMENT_DETECTED
            else -> RegressionStatus.NO_CHANGE
        }
        
        return RegressionAnalysis(
            testName = testName,
            hasBaseline = true,
            regressions = regressions,
            improvements = improvements,
            overallStatus = overallStatus,
            baselineDate = baseline.createdAt
        )
    }
    
    /**
     * Generate automated alerts for performance degradation
     */
    fun generateAlerts(analysis: RegressionAnalysis): List<PerformanceAlert> {
        val alerts = mutableListOf<PerformanceAlert>()
        
        // Critical regression alerts
        analysis.regressions.filter { it.severity == RegressionSeverity.CRITICAL }.forEach { regression ->
            alerts.add(
                PerformanceAlert(
                    metricName = regression.operationName,
                    currentValue = regression.currentTime,
                    threshold = regression.baselineTime,
                    severity = AlertSeverity.CRITICAL,
                    timestamp = System.currentTimeMillis(),
                    message = "CRITICAL: ${regression.operationName} performance degraded by ${String.format("%.1f", regression.degradationPercent)}%"
                )
            )
        }
        
        // Major regression alerts
        analysis.regressions.filter { it.severity == RegressionSeverity.MAJOR }.forEach { regression ->
            alerts.add(
                PerformanceAlert(
                    metricName = regression.operationName,
                    currentValue = regression.currentTime,
                    threshold = regression.baselineTime,
                    severity = AlertSeverity.HIGH,
                    timestamp = System.currentTimeMillis(),
                    message = "MAJOR: ${regression.operationName} performance degraded by ${String.format("%.1f", regression.degradationPercent)}%"
                )
            )
        }
        
        // Improvement notifications
        analysis.improvements.forEach { improvement ->
            alerts.add(
                PerformanceAlert(
                    metricName = improvement.operationName,
                    currentValue = improvement.currentTime,
                    threshold = improvement.baselineTime,
                    severity = AlertSeverity.INFO,
                    timestamp = System.currentTimeMillis(),
                    message = "IMPROVEMENT: ${improvement.operationName} performance improved by ${String.format("%.1f", improvement.improvementPercent)}%"
                )
            )
        }
        
        return alerts
    }
    
    /**
     * Create performance trend analysis over time
     */
    fun analyzeTrends(testName: String, historicalResults: List<PerformanceResult>): TrendAnalysis {
        if (historicalResults.size < 2) {
            return TrendAnalysis(
                testName = testName,
                trend = PerformanceTrend.INSUFFICIENT_DATA,
                trendStrength = 0.0,
                dataPoints = historicalResults.size
            )
        }
        
        // Simple linear regression to detect trends
        val times = historicalResults.mapIndexed { index, result -> index.toDouble() to result.averageTime }
        val slope = calculateLinearRegressionSlope(times)
        
        val trend = when {
            slope > 1.0 -> PerformanceTrend.DEGRADING
            slope < -1.0 -> PerformanceTrend.IMPROVING
            else -> PerformanceTrend.STABLE
        }
        
        return TrendAnalysis(
            testName = testName,
            trend = trend,
            trendStrength = kotlin.math.abs(slope),
            dataPoints = historicalResults.size,
            averageChange = slope
        )
    }
    
    private fun calculatePerformanceChange(baselineTime: Double, currentTime: Double): Double {
        return ((currentTime - baselineTime) / baselineTime) * 100.0
    }
    
    private fun calculateSeverity(changePercent: Double): RegressionSeverity {
        return when {
            changePercent >= 50.0 -> RegressionSeverity.CRITICAL
            changePercent >= 25.0 -> RegressionSeverity.MAJOR
            changePercent >= 10.0 -> RegressionSeverity.MINOR
            else -> RegressionSeverity.NEGLIGIBLE
        }
    }
    
    private fun calculateLinearRegressionSlope(points: List<Pair<Double, Double>>): Double {
        val n = points.size
        val sumX = points.sumOf { it.first }
        val sumY = points.sumOf { it.second }
        val sumXY = points.sumOf { it.first * it.second }
        val sumXX = points.sumOf { it.first * it.first }
        
        return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    }
    
    private fun saveBaseline(baseline: BaselineData) {
        val file = File(baselineDirectory, "${baseline.testName}.json")
        file.writeText(json.encodeToString(baseline))
    }
    
    private fun loadBaseline(testName: String): BaselineData? {
        val file = File(baselineDirectory, "$testName.json")
        return if (file.exists()) {
            try {
                json.decodeFromString<BaselineData>(file.readText())
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
}

/**
 * Baseline performance data
 */
@Serializable
data class BaselineData(
    val testName: String,
    val createdAt: LocalDateTime,
    val results: List<BaselineResult>
)

@Serializable
data class BaselineResult(
    val operationName: String,
    val averageTime: Double,
    val operationsPerSecond: Double,
    val standardDeviation: Double,
    val iterations: Int
)

/**
 * Regression analysis result
 */
data class RegressionAnalysis(
    val testName: String,
    val hasBaseline: Boolean,
    val regressions: List<PerformanceRegression>,
    val improvements: List<PerformanceImprovement>,
    val overallStatus: RegressionStatus,
    val baselineDate: LocalDateTime? = null
)

data class PerformanceRegression(
    val operationName: String,
    val baselineTime: Double,
    val currentTime: Double,
    val degradationPercent: Double,
    val severity: RegressionSeverity
)

data class PerformanceImprovement(
    val operationName: String,
    val baselineTime: Double,
    val currentTime: Double,
    val improvementPercent: Double
)



/**
 * Trend analysis
 */
data class TrendAnalysis(
    val testName: String,
    val trend: PerformanceTrend,
    val trendStrength: Double,
    val dataPoints: Int,
    val averageChange: Double = 0.0
)

/**
 * Enums for classification
 */
enum class RegressionStatus {
    NO_BASELINE,
    NO_CHANGE,
    IMPROVEMENT_DETECTED,
    REGRESSION_DETECTED,
    CRITICAL_REGRESSION
}

enum class RegressionSeverity {
    NEGLIGIBLE,
    MINOR,
    MAJOR,
    CRITICAL
}

enum class AlertType {
    CRITICAL_REGRESSION,
    MAJOR_REGRESSION,
    MINOR_REGRESSION,
    PERFORMANCE_IMPROVEMENT,
    BASELINE_CREATED
}

enum class AlertSeverity {
    INFO,
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

enum class PerformanceTrend {
    INSUFFICIENT_DATA,
    IMPROVING,
    STABLE,
    DEGRADING
}