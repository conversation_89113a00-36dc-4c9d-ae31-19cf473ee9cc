package com.yu.questicle.core.testing.mock

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.factory.TestDataFactory
import kotlinx.coroutines.delay
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt
import kotlin.random.Random

/**
 * Advanced Mock TetrisStatisticsCalculator with comprehensive calculation logic
 * 
 * Provides realistic statistical calculations including advanced metrics
 * like efficiency ratings, performance trends, and piece distribution analysis.
 */
class AdvancedMockTetrisStatisticsCalculator : TetrisStatisticsCalculator {
    
    private val calculationHistory = mutableListOf<StatisticsSnapshot>()
    
    override fun calculatePiecesPerMinute(piecesPlaced: Int, gameTimeMs: Long): Double {
        if (gameTimeMs <= 0) return 0.0
        val minutes = gameTimeMs / 60000.0
        return piecesPlaced / minutes
    }
    
    override fun calculateLinesPerMinute(linesCleared: Int, gameTimeMs: Long): Double {
        if (gameTimeMs <= 0) return 0.0
        val minutes = gameTimeMs / 60000.0
        return linesCleared / minutes
    }
    
    override fun calculateEfficiency(linesCleared: Int, piecesPlaced: Int): Double {
        if (piecesPlaced <= 0) return 0.0
        return linesCleared.toDouble() / piecesPlaced
    }
    
    override fun updateStatistics(current: GameStatistics, action: TetrisAction): GameStatistics {
        val updated = when (action) {
            is TetrisAction.Drop -> {
                if (action.hard) {
                    current.copy(piecesPlaced = current.piecesPlaced + 1)
                } else {
                    current
                }
            }
            is TetrisAction.Move -> current
            is TetrisAction.Rotate -> current
            is TetrisAction.Hold -> current
            is TetrisAction.Pause -> current
        }
        
        // Record snapshot for trend analysis
        recordSnapshot(updated)
        
        return updated.calculateEfficiency()
    }
    
    /**
     * Calculate advanced efficiency rating (0-100 scale)
     */
    fun calculateEfficiencyRating(statistics: GameStatistics): Double {
        val baseEfficiency = calculateEfficiency(statistics.linesCleared, statistics.piecesPlaced)
        val tSpinBonus = statistics.tSpins * 0.1
        val tetrisBonus = statistics.tetrises * 0.05
        val comboBonus = statistics.maxCombo * 0.02
        
        val rating = (baseEfficiency + tSpinBonus + tetrisBonus + comboBonus) * 100
        return min(100.0, max(0.0, rating))
    }
    
    /**
     * Calculate piece distribution balance (0-1 scale, 1 = perfectly balanced)
     */
    fun calculatePieceDistributionBalance(pieceStats: Map<TetrisPieceType, Int>): Double {
        if (pieceStats.isEmpty()) return 1.0
        
        val totalPieces = pieceStats.values.sum()
        if (totalPieces == 0) return 1.0
        
        val expectedPerPiece = totalPieces / 7.0
        val variance = pieceStats.values.map { count ->
            val deviation = count - expectedPerPiece
            deviation * deviation
        }.average()
        
        val standardDeviation = sqrt(variance)
        val balanceScore = max(0.0, 1.0 - (standardDeviation / expectedPerPiece))
        
        return balanceScore
    }
    
    /**
     * Calculate performance trend over time
     */
    fun calculatePerformanceTrend(): PerformanceTrend {
        if (calculationHistory.size < 2) {
            return PerformanceTrend.STABLE
        }
        
        val recent = calculationHistory.takeLast(5)
        val older = calculationHistory.dropLast(5).takeLast(5)
        
        if (older.isEmpty()) return PerformanceTrend.STABLE
        
        val recentAvg = recent.map { it.efficiency }.average()
        val olderAvg = older.map { it.efficiency }.average()
        
        return when {
            recentAvg > olderAvg * 1.1 -> PerformanceTrend.IMPROVING
            recentAvg < olderAvg * 0.9 -> PerformanceTrend.DECLINING
            else -> PerformanceTrend.STABLE
        }
    }
    
    /**
     * Get detailed performance analysis
     */
    fun getPerformanceAnalysis(statistics: GameStatistics): PerformanceAnalysis {
        return PerformanceAnalysis(
            efficiencyRating = calculateEfficiencyRating(statistics),
            pieceDistributionBalance = calculatePieceDistributionBalance(statistics.pieceStats),
            performanceTrend = calculatePerformanceTrend(),
            strongPoints = identifyStrongPoints(statistics),
            improvementAreas = identifyImprovementAreas(statistics)
        )
    }
    
    private fun recordSnapshot(statistics: GameStatistics) {
        val snapshot = StatisticsSnapshot(
            timestamp = System.currentTimeMillis(),
            efficiency = calculateEfficiency(statistics.linesCleared, statistics.piecesPlaced),
            ppm = calculatePiecesPerMinute(statistics.piecesPlaced, statistics.gameTime),
            lpm = calculateLinesPerMinute(statistics.linesCleared, statistics.gameTime)
        )
        
        calculationHistory.add(snapshot)
        
        // Keep only last 50 snapshots
        if (calculationHistory.size > 50) {
            calculationHistory.removeAt(0)
        }
    }
    
    private fun identifyStrongPoints(statistics: GameStatistics): List<String> {
        val strongPoints = mutableListOf<String>()
        
        if (statistics.tetrises > statistics.piecesPlaced * 0.1) {
            strongPoints.add("Excellent Tetris rate")
        }
        if (statistics.tSpins > 0) {
            strongPoints.add("T-Spin execution")
        }
        if (statistics.maxCombo > 5) {
            strongPoints.add("Strong combo building")
        }
        if (calculateEfficiency(statistics.linesCleared, statistics.piecesPlaced) > 0.6) {
            strongPoints.add("High efficiency")
        }
        
        return strongPoints
    }
    
    private fun identifyImprovementAreas(statistics: GameStatistics): List<String> {
        val improvements = mutableListOf<String>()
        
        if (statistics.tetrises == 0 && statistics.piecesPlaced > 50) {
            improvements.add("Focus on Tetris setups")
        }
        if (statistics.tSpins == 0 && statistics.piecesPlaced > 100) {
            improvements.add("Learn T-Spin techniques")
        }
        if (statistics.maxCombo < 3) {
            improvements.add("Work on combo building")
        }
        if (calculateEfficiency(statistics.linesCleared, statistics.piecesPlaced) < 0.4) {
            improvements.add("Improve line clearing efficiency")
        }
        
        return improvements
    }
}

/**
 * Advanced Mock TetrisPerformanceManager with comprehensive monitoring
 * 
 * Provides detailed performance monitoring including operation timing,
 * memory usage tracking, and performance regression detection.
 */
class AdvancedMockTetrisPerformanceManager : TetrisPerformanceManager {
    
    private val operationMetrics = ConcurrentHashMap<String, OperationMetrics>()
    private val performanceHistory = mutableListOf<PerformanceSnapshot>()
    private var isMonitoring = false
    private var startTime = 0L
    private val memoryUsage = AtomicLong(0)
    
    override fun startPerformanceMonitoring() {
        isMonitoring = true
        startTime = System.currentTimeMillis()
        operationMetrics.clear()
        recordMemoryUsage()
    }
    
    override fun stopPerformanceMonitoring() {
        if (isMonitoring) {
            recordPerformanceSnapshot()
            isMonitoring = false
        }
    }
    
    override fun recordOperation(operationName: String, durationMs: Long) {
        if (!isMonitoring) return
        
        val metrics = operationMetrics.getOrPut(operationName) { OperationMetrics() }
        metrics.addMeasurement(durationMs)
        
        // Simulate memory usage changes
        memoryUsage.addAndGet(Random.nextLong(-1000, 2000))
    }
    
    override fun getPerformanceMetrics(): Map<String, Double> {
        return operationMetrics.mapValues { (_, metrics) -> metrics.getAverageTime() }
    }
    
    override fun resetMetrics() {
        operationMetrics.clear()
        performanceHistory.clear()
        memoryUsage.set(0)
    }
    
    /**
     * Get detailed performance report
     */
    fun getDetailedPerformanceReport(): PerformanceReport {
        val totalOperations = operationMetrics.values.sumOf { it.getCallCount() }
        val averageResponseTime = operationMetrics.values
            .map { it.getAverageTime() }
            .takeIf { it.isNotEmpty() }
            ?.average() ?: 0.0
        
        return PerformanceReport(
            totalOperations = totalOperations,
            averageResponseTime = averageResponseTime,
            slowestOperation = findSlowestOperation(),
            fastestOperation = findFastestOperation(),
            memoryUsage = memoryUsage.get(),
            performanceTrend = calculatePerformanceTrend(),
            bottlenecks = identifyBottlenecks(),
            recommendations = generateRecommendations()
        )
    }
    
    /**
     * Check for performance regressions
     */
    fun checkForRegressions(thresholdPercent: Double = 20.0): List<PerformanceRegression> {
        if (performanceHistory.size < 2) return emptyList()
        
        val current = performanceHistory.last()
        val baseline = performanceHistory.dropLast(1).last()
        
        val regressions = mutableListOf<PerformanceRegression>()
        
        current.operationMetrics.forEach { (operation, currentTime) ->
            val baselineTime = baseline.operationMetrics[operation]
            if (baselineTime != null) {
                val increase = ((currentTime - baselineTime) / baselineTime) * 100
                if (increase > thresholdPercent) {
                    regressions.add(
                        PerformanceRegression(
                            operation = operation,
                            baselineTime = baselineTime,
                            currentTime = currentTime,
                            regressionPercent = increase
                        )
                    )
                }
            }
        }
        
        return regressions
    }
    
    /**
     * Simulate realistic performance monitoring
     */
    suspend fun simulateRealisticPerformance(operationName: String, baseTimeMs: Long) {
        val variance = Random.nextDouble(0.8, 1.2) // ±20% variance
        val actualTime = (baseTimeMs * variance).toLong()
        
        delay(actualTime)
        recordOperation(operationName, actualTime)
    }
    
    private fun recordMemoryUsage() {
        // Simulate initial memory usage
        memoryUsage.set(Random.nextLong(50_000, 100_000))
    }
    
    private fun recordPerformanceSnapshot() {
        val snapshot = PerformanceSnapshot(
            timestamp = System.currentTimeMillis(),
            operationMetrics = operationMetrics.mapValues { it.value.getAverageTime() },
            memoryUsage = memoryUsage.get()
        )
        
        performanceHistory.add(snapshot)
        
        // Keep only last 20 snapshots
        if (performanceHistory.size > 20) {
            performanceHistory.removeAt(0)
        }
    }
    
    private fun findSlowestOperation(): String? {
        return operationMetrics.maxByOrNull { it.value.getAverageTime() }?.key
    }
    
    private fun findFastestOperation(): String? {
        return operationMetrics.minByOrNull { it.value.getAverageTime() }?.key
    }
    
    private fun calculatePerformanceTrend(): PerformanceTrend {
        if (performanceHistory.size < 3) return PerformanceTrend.STABLE
        
        val recent = performanceHistory.takeLast(3).map { it.getAverageResponseTime() }
        val older = performanceHistory.dropLast(3).takeLast(3).map { it.getAverageResponseTime() }
        
        if (older.isEmpty()) return PerformanceTrend.STABLE
        
        val recentAvg = recent.average()
        val olderAvg = older.average()
        
        return when {
            recentAvg > olderAvg * 1.2 -> PerformanceTrend.DECLINING
            recentAvg < olderAvg * 0.8 -> PerformanceTrend.IMPROVING
            else -> PerformanceTrend.STABLE
        }
    }
    
    private fun identifyBottlenecks(): List<String> {
        val avgTime = operationMetrics.values.map { it.getAverageTime() }.average()
        return operationMetrics.filter { it.value.getAverageTime() > avgTime * 2 }.keys.toList()
    }
    
    private fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        
        val bottlenecks = identifyBottlenecks()
        if (bottlenecks.isNotEmpty()) {
            recommendations.add("Optimize bottleneck operations: ${bottlenecks.joinToString()}")
        }
        
        if (memoryUsage.get() > 500_000) {
            recommendations.add("Consider memory optimization - usage is high")
        }
        
        val trend = calculatePerformanceTrend()
        if (trend == PerformanceTrend.DECLINING) {
            recommendations.add("Performance is declining - investigate recent changes")
        }
        
        return recommendations
    }
}

/**
 * Advanced Mock TetrisCacheManager with intelligent caching strategies
 * 
 * Provides sophisticated caching with LRU eviction, cache statistics,
 * and performance optimization features.
 */
class AdvancedMockTetrisCacheManager(
    private val maxCacheSize: Int = 100,
    private val ttlMs: Long = 300_000 // 5 minutes
) : TetrisCacheManager {
    
    private val cache = LinkedHashMap<String, CacheEntry>(maxCacheSize, 0.75f, true)
    private val cacheStats = CacheStatistics()
    
    override fun cacheGameState(gameId: String, gameState: TetrisGameState) {
        synchronized(cache) {
            // Remove expired entries
            cleanupExpiredEntries()
            
            // Evict oldest entry if cache is full
            if (cache.size >= maxCacheSize && !cache.containsKey(gameId)) {
                val oldestKey = cache.keys.first()
                cache.remove(oldestKey)
                cacheStats.recordEviction()
            }
            
            val entry = CacheEntry(
                gameState = gameState,
                timestamp = System.currentTimeMillis(),
                accessCount = 0
            )
            
            cache[gameId] = entry
            cacheStats.recordWrite()
        }
    }
    
    override fun getCachedGameState(gameId: String): TetrisGameState? {
        synchronized(cache) {
            val entry = cache[gameId]
            
            if (entry == null) {
                cacheStats.recordMiss()
                return null
            }
            
            // Check if entry is expired
            if (isExpired(entry)) {
                cache.remove(gameId)
                cacheStats.recordMiss()
                cacheStats.recordExpiration()
                return null
            }
            
            // Update access statistics
            entry.accessCount++
            entry.lastAccessTime = System.currentTimeMillis()
            cacheStats.recordHit()
            
            return entry.gameState
        }
    }
    
    override fun clearCache() {
        synchronized(cache) {
            cache.clear()
            cacheStats.reset()
        }
    }
    
    override fun getCacheSize(): Int {
        return cache.size
    }
    
    /**
     * Get cache hit ratio (0.0 to 1.0)
     */
    fun getCacheHitRatio(): Double {
        return cacheStats.getHitRatio()
    }
    
    /**
     * Get detailed cache statistics
     */
    fun getCacheStatistics(): CacheStatistics {
        return cacheStats.copy()
    }
    
    /**
     * Get cache performance metrics
     */
    fun getCachePerformanceMetrics(): CachePerformanceMetrics {
        val totalEntries = cache.size
        val averageAccessCount = cache.values.map { it.accessCount }.average()
        val oldestEntryAge = cache.values.minOfOrNull { 
            System.currentTimeMillis() - it.timestamp 
        } ?: 0L
        
        return CachePerformanceMetrics(
            totalEntries = totalEntries,
            hitRatio = getCacheHitRatio(),
            averageAccessCount = averageAccessCount,
            oldestEntryAgeMs = oldestEntryAge,
            memoryUsageEstimate = estimateMemoryUsage()
        )
    }
    
    /**
     * Preload frequently accessed game states
     */
    fun preloadFrequentStates() {
        // Simulate preloading common game states
        val commonStates = listOf(
            "initial_state" to TestDataFactory.createTetrisGameState(),
            "mid_game_state" to TestDataFactory.createMidGameState(),
            "near_game_over" to TestDataFactory.createNearGameOverState()
        )
        
        commonStates.forEach { (id, state) ->
            cacheGameState(id, state)
        }
    }
    
    /**
     * Optimize cache by removing least accessed entries
     */
    fun optimizeCache() {
        synchronized(cache) {
            if (cache.size < maxCacheSize * 0.8) return
            
            // Remove entries with lowest access count
            val sortedEntries = cache.entries.sortedBy { it.value.accessCount }
            val toRemove = sortedEntries.take(cache.size / 4) // Remove 25%
            
            toRemove.forEach { (key, _) ->
                cache.remove(key)
                cacheStats.recordEviction()
            }
        }
    }
    
    private fun cleanupExpiredEntries() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = cache.filter { (_, entry) ->
            currentTime - entry.timestamp > ttlMs
        }.keys
        
        expiredKeys.forEach { key ->
            cache.remove(key)
            cacheStats.recordExpiration()
        }
    }
    
    private fun isExpired(entry: CacheEntry): Boolean {
        return System.currentTimeMillis() - entry.timestamp > ttlMs
    }
    
    private fun estimateMemoryUsage(): Long {
        // Rough estimate: each game state ~2KB
        return cache.size * 2048L
    }
}

/**
 * Supporting data classes for advanced mock components
 */

data class StatisticsSnapshot(
    val timestamp: Long,
    val efficiency: Double,
    val ppm: Double,
    val lpm: Double
)

data class PerformanceAnalysis(
    val efficiencyRating: Double,
    val pieceDistributionBalance: Double,
    val performanceTrend: PerformanceTrend,
    val strongPoints: List<String>,
    val improvementAreas: List<String>
)

enum class PerformanceTrend {
    IMPROVING, STABLE, DECLINING
}

data class PerformanceSnapshot(
    val timestamp: Long,
    val operationMetrics: Map<String, Double>,
    val memoryUsage: Long
) {
    fun getAverageResponseTime(): Double {
        return operationMetrics.values.takeIf { it.isNotEmpty() }?.average() ?: 0.0
    }
}

data class PerformanceReport(
    val totalOperations: Long,
    val averageResponseTime: Double,
    val slowestOperation: String?,
    val fastestOperation: String?,
    val memoryUsage: Long,
    val performanceTrend: PerformanceTrend,
    val bottlenecks: List<String>,
    val recommendations: List<String>
)

data class PerformanceRegression(
    val operation: String,
    val baselineTime: Double,
    val currentTime: Double,
    val regressionPercent: Double
)

class OperationMetrics {
    private val measurements = mutableListOf<Long>()
    
    fun addMeasurement(durationMs: Long) {
        measurements.add(durationMs)
        // Keep only last 100 measurements
        if (measurements.size > 100) {
            measurements.removeAt(0)
        }
    }
    
    fun getAverageTime(): Double {
        return measurements.takeIf { it.isNotEmpty() }?.average() ?: 0.0
    }
    
    fun getCallCount(): Long {
        return measurements.size.toLong()
    }
}

data class CacheEntry(
    val gameState: TetrisGameState,
    val timestamp: Long,
    var accessCount: Int,
    var lastAccessTime: Long = timestamp
)

data class CacheStatistics(
    private var hits: Long = 0,
    private var misses: Long = 0,
    private var writes: Long = 0,
    private var evictions: Long = 0,
    private var expirations: Long = 0
) {
    fun recordHit() { hits++ }
    fun recordMiss() { misses++ }
    fun recordWrite() { writes++ }
    fun recordEviction() { evictions++ }
    fun recordExpiration() { expirations++ }
    
    fun getHitRatio(): Double {
        val total = hits + misses
        return if (total > 0) hits.toDouble() / total else 0.0
    }
    
    fun reset() {
        hits = 0
        misses = 0
        writes = 0
        evictions = 0
        expirations = 0
    }
    
    fun copy(): CacheStatistics {
        return CacheStatistics(hits, misses, writes, evictions, expirations)
    }
}

data class CachePerformanceMetrics(
    val totalEntries: Int,
    val hitRatio: Double,
    val averageAccessCount: Double,
    val oldestEntryAgeMs: Long,
    val memoryUsageEstimate: Long
)