package com.yu.questicle.core.testing.performance

import kotlinx.serialization.Serializable
import java.time.Instant

/**
 * 性能快照
 * 
 * 包含某个时间点的完整性能指标
 */
@Serializable
data class PerformanceSnapshot(
    val timestamp: Long,
    val frameRate: FrameRateMetric? = null,
    val memory: MemoryMetric? = null,
    val cpu: CPUMetric? = null,
    val network: NetworkMetric? = null,
    val overall: OverallPerformance? = null
) {
    fun getOverallScore(): Double {
        return overall?.score ?: 0.0
    }
    
    fun hasIssues(): Boolean {
        return overall?.hasIssues ?: false
    }
}

/**
 * 帧率指标
 */
@Serializable
data class FrameRateMetric(
    val timestamp: Long,
    val fps: Int,
    val avgFrameTime: Double, // 毫秒
    val droppedFrameRate: Double, // 0.0 - 1.0
    val isSmooth: Boolean,
    val frameTimeVariance: Double = 0.0,
    val maxFrameTime: Double = 0.0,
    val minFrameTime: Double = 0.0
) {
    fun getPerformanceRating(): PerformanceRating {
        return when {
            fps >= 60 && droppedFrameRate < 0.01 -> PerformanceRating.EXCELLENT
            fps >= 55 && droppedFrameRate < 0.05 -> PerformanceRating.GOOD
            fps >= 45 && droppedFrameRate < 0.1 -> PerformanceRating.FAIR
            fps >= 30 && droppedFrameRate < 0.2 -> PerformanceRating.POOR
            else -> PerformanceRating.CRITICAL
        }
    }
}

/**
 * 内存指标
 */
@Serializable
data class MemoryMetric(
    val timestamp: Long,
    val heapUtilization: Double, // 0.0 - 1.0
    val allocatedMemory: Long, // bytes
    val freeMemory: Long, // bytes
    val maxMemory: Long, // bytes
    val gcCount: Int = 0,
    val gcTime: Long = 0, // 毫秒
    val memoryLeakSuspected: Boolean = false
) {
    fun getMemoryPressure(): MemoryPressure {
        return when {
            heapUtilization < 0.5 -> MemoryPressure.LOW
            heapUtilization < 0.7 -> MemoryPressure.MODERATE
            heapUtilization < 0.85 -> MemoryPressure.HIGH
            else -> MemoryPressure.CRITICAL
        }
    }
    
    fun getAvailableMemory(): Long = maxMemory - allocatedMemory
}

/**
 * CPU 指标
 */
@Serializable
data class CPUMetric(
    val timestamp: Long,
    val cpuUsage: Double, // 0.0 - 1.0
    val userTime: Double, // 0.0 - 1.0
    val systemTime: Double, // 0.0 - 1.0
    val idleTime: Double, // 0.0 - 1.0
    val threadCount: Int = 0,
    val contextSwitches: Long = 0,
    val loadAverage: Double = 0.0
) {
    fun getCpuLoad(): CpuLoad {
        return when {
            cpuUsage < 0.3 -> CpuLoad.LOW
            cpuUsage < 0.6 -> CpuLoad.MODERATE
            cpuUsage < 0.8 -> CpuLoad.HIGH
            else -> CpuLoad.CRITICAL
        }
    }
}

/**
 * 网络指标
 */
@Serializable
data class NetworkMetric(
    val timestamp: Long,
    val requestCount: Long,
    val avgResponseTime: Double, // 毫秒
    val errorRate: Double, // 0.0 - 1.0
    val throughput: Double, // bytes/second
    val connectionCount: Int = 0,
    val timeoutCount: Long = 0,
    val retryCount: Long = 0
) {
    fun getNetworkHealth(): NetworkHealth {
        return when {
            errorRate < 0.01 && avgResponseTime < 100 -> NetworkHealth.EXCELLENT
            errorRate < 0.05 && avgResponseTime < 300 -> NetworkHealth.GOOD
            errorRate < 0.1 && avgResponseTime < 1000 -> NetworkHealth.FAIR
            errorRate < 0.2 && avgResponseTime < 3000 -> NetworkHealth.POOR
            else -> NetworkHealth.CRITICAL
        }
    }
}

/**
 * 整体性能评估
 */
@Serializable
data class OverallPerformance(
    val score: Double, // 0.0 - 100.0
    val rating: PerformanceRating,
    val issues: List<PerformanceIssue> = emptyList(),
    val recommendations: List<String> = emptyList(),
    val trend: PerformanceTrend = PerformanceTrend.STABLE
) {
    val hasIssues: Boolean get() = issues.isNotEmpty()
    
    fun getCriticalIssues(): List<PerformanceIssue> {
        return issues.filter { it.severity == IssueSeverity.CRITICAL }
    }
}

/**
 * 性能问题
 */
@Serializable
data class PerformanceIssue(
    val type: IssueType,
    val severity: IssueSeverity,
    val description: String,
    val metric: String,
    val value: Double,
    val threshold: Double,
    val recommendation: String
)

/**
 * 性能评级
 */
enum class PerformanceRating {
    EXCELLENT, GOOD, FAIR, POOR, CRITICAL
}

/**
 * 内存压力等级
 */
enum class MemoryPressure {
    LOW, MODERATE, HIGH, CRITICAL
}

/**
 * CPU 负载等级
 */
enum class CpuLoad {
    LOW, MODERATE, HIGH, CRITICAL
}

/**
 * 网络健康状态
 */
enum class NetworkHealth {
    EXCELLENT, GOOD, FAIR, POOR, CRITICAL
}

/**
 * 性能趋势
 */
enum class PerformanceTrend {
    INSUFFICIENT_DATA, IMPROVING, STABLE, DECLINING, DEGRADING
}

/**
 * 问题类型
 */
enum class IssueType {
    FRAME_RATE, MEMORY, CPU, NETWORK, BATTERY, STORAGE, UI, CONFIGURATION, THIRD_PARTY, UNKNOWN
}

/**
 * 问题严重程度
 */
enum class IssueSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}
