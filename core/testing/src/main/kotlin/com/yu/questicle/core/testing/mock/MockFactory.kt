package com.yu.questicle.core.testing.mock

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.exception.BusinessException
import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.factory.TestDataFactory
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * 统一Mock工厂 - 简化版本
 * 
 * 提供基础的Mock对象创建功能，专注于实际存在的域模型。
 * 避免模拟不存在的接口，确保测试的稳定性和可维护性。
 * 
 * <AUTHOR> Team
 * @version 2.0 (JUnit 5 + MockK + 简化版本)
 */
object MockFactory {
    
    // ========================================
    // 基础Mock创建方法
    // ========================================
    
    /**
     * 创建基础的mock - 简化版本，仅提供测试数据
     * 注意：这里不使用MockK，而是通过TestDataFactory提供测试数据
     */
    fun createBasicMock(): String = "This is a basic mock placeholder"
    
    // ========================================
    // 测试数据工厂支持
    // ========================================
    
    /**
     * 创建测试用的TetrisGameState
     */
    fun createMockTetrisGameState(
        status: TetrisStatus = TetrisStatus.READY,
        score: Int = 0,
        level: Int = 1
    ): TetrisGameState = TestDataFactory.createTetrisGameState(
        status = status,
        score = score,
        level = level
    )
    
    /**
     * 创建测试用的User
     */
    fun createMockUser(
        id: String = "mock_user_id",
        username: String = "mock_user",
        email: String? = "<EMAIL>"
    ): User = TestDataFactory.createUser(
        id = id,
        username = username,
        email = email
    )
    
    /**
     * 创建测试用的Game
     */
    fun createMockGame(
        id: String = "mock_game_id",
        playerId: String = "mock_player_id",
        score: Int = 1000
    ): Game = TestDataFactory.createGame(
        id = id,
        playerId = playerId,
        score = score
    )
    
    /**
     * 创建测试用的Achievement
     */
    fun createMockAchievement(
        id: String = "mock_achievement_id",
        title: String = "Mock Achievement"
    ): Achievement = TestDataFactory.createAchievement(
        id = id,
        title = title
    )
    
    // ========================================
    // 结果类型Mock支持
    // ========================================
    
    /**
     * 创建成功的Result
     */
    fun <T> createSuccessResult(data: T): Result<T> = Result.Success(data)
    
    /**
     * 创建失败的Result
     */
    fun <T> createErrorResult(message: String = "Mock error"): Result<T> = 
        Result.Error(BusinessException(message))
    
    /**
     * 创建加载中的Result
     */
    fun <T> createLoadingResult(): Result<T> = Result.Loading
    
    // ========================================
    // 验证辅助方法
    // ========================================
    
    /**
     * 验证mock对象的方法调用
     */
    fun verifyMockCall(mock: Any, methodName: String) {
        // 这里可以根据需要添加特定的验证逻辑
        // 目前保持简单，主要用于演示
    }
    
    /**
     * 重置mock对象 - 简化版本
     */
    fun resetMock(mock: Any) {
        // 简化版本，不使用MockK
        // 在实际项目中，这里可以根据需要添加重置逻辑
    }
    
    /**
     * 重置所有mock对象 - 简化版本
     */
    fun resetAllMocks() {
        // 简化版本，不使用MockK
        // 在实际项目中，这里可以根据需要添加重置逻辑
    }
    
    // ========================================
    // 便捷方法
    // ========================================
    
    /**
     * 创建包含多个测试数据的列表
     */
    fun createMockGameList(count: Int = 5): List<Game> = 
        TestDataFactory.createLeaderboardGames(count)
    
    /**
     * 创建用户游戏历史
     */
    fun createMockUserGameHistory(
        playerId: String = "mock_player",
        gameCount: Int = 3
    ): List<Game> = TestDataFactory.createUserGameHistory(playerId, gameCount)
    
    /**
     * 创建TetrisStatistics
     */
    fun createMockTetrisStatistics(
        piecesPlaced: Int = 100,
        linesCleared: Int = 50
    ): GameStatistics = TestDataFactory.createTetrisStatistics(
        piecesPlaced = piecesPlaced,
        linesCleared = linesCleared
    )
    
    /**
     * 创建Mock GameRepository - 完整实现
     */
    fun createMockGameRepository(): com.yu.questicle.core.domain.repository.GameRepository {
        return object : com.yu.questicle.core.domain.repository.GameRepository {
            override suspend fun saveGame(game: Game): Result<Unit> = createSuccessResult(Unit)
            override suspend fun loadGame(gameId: String): Result<Game> = createSuccessResult(createMockGame(id = gameId))
            override fun getGamesForPlayer(playerId: String): kotlinx.coroutines.flow.Flow<List<Game>> = 
                kotlinx.coroutines.flow.flowOf(createMockUserGameHistory(playerId))
            override fun getGamesByType(playerId: String, gameType: GameType): kotlinx.coroutines.flow.Flow<List<Game>> = 
                kotlinx.coroutines.flow.flowOf(emptyList())
            override suspend fun deleteGame(gameId: String): Result<Unit> = createSuccessResult(Unit)
            override suspend fun saveGameSession(session: GameSession): Result<Unit> = createSuccessResult(Unit)
            override fun getGameSessions(gameId: String): kotlinx.coroutines.flow.Flow<List<GameSession>> = 
                kotlinx.coroutines.flow.flowOf(emptyList())
            override suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats> = 
                createSuccessResult(GameStats(playerId = playerId, gameType = gameType, totalGames = 0, totalScore = 0))
            override suspend fun updateGameStats(stats: GameStats): Result<Unit> = createSuccessResult(Unit)
            override suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>> = 
                createSuccessResult(emptyList())
            override suspend fun searchGames(
                playerId: String?, 
                gameType: GameType?, 
                minScore: Int?, 
                maxScore: Int?, 
                limit: Int
            ): Result<List<Game>> = createSuccessResult(emptyList())
        }
    }
}
