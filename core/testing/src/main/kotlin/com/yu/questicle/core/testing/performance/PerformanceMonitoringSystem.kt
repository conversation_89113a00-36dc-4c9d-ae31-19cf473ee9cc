package com.yu.questicle.core.testing.performance

import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.*

/**
 * Comprehensive performance monitoring system that integrates regression detection,
 * trend analysis, and automated alerting.
 */
class PerformanceMonitoringSystem(
    private val testRunner: PerformanceTestRunner,
    private val regressionDetector: PerformanceRegressionDetector,
    private val reportsDirectory: File,
    private val alertHandlers: List<AlertHandler> = emptyList()
) {
    
    init {
        reportsDirectory.mkdirs()
    }
    
    /**
     * Run comprehensive performance monitoring for a test suite
     */
    suspend fun runPerformanceMonitoring(
        testSuite: PerformanceTestSuite,
        createBaseline: Boolean = false
    ): PerformanceMonitoringResult {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<PerformanceResult>()
        val errors = mutableListOf<PerformanceTestError>()
        
        // Execute all performance tests
        testSuite.tests.forEach { test ->
            try {
                val result = testRunner.runPerformanceTest(
                    testName = test.name,
                    iterations = test.iterations,
                    operation = test.operation
                )
                results.add(result)
            } catch (e: Exception) {
                errors.add(
                    PerformanceTestError(
                        testName = test.name,
                        error = e.message ?: "Unknown error",
                        timestamp = LocalDateTime.now()
                    )
                )
            }
        }
        
        val executionTime = System.currentTimeMillis() - startTime
        
        // Handle baseline creation or regression detection
        val regressionAnalysis = if (createBaseline) {
            regressionDetector.createBaseline(testSuite.name, results)
            null
        } else {
            regressionDetector.detectRegressions(testSuite.name, results)
        }
        
        // Generate alerts if regressions detected
        val alerts = regressionAnalysis?.let { analysis ->
            regressionDetector.generateAlerts(analysis)
        } ?: emptyList()
        
        // Send alerts through configured handlers
        alerts.forEach { alert ->
            alertHandlers.forEach { handler ->
                try {
                    handler.handleAlert(alert)
                } catch (e: Exception) {
                    // Log alert handler errors but don't fail the monitoring
                    println("Alert handler error: ${e.message}")
                }
            }
        }
        
        // Generate monitoring report
        val report = generateMonitoringReport(
            testSuite = testSuite,
            results = results,
            regressionAnalysis = regressionAnalysis,
            alerts = alerts,
            errors = errors,
            executionTime = executionTime
        )
        
        return PerformanceMonitoringResult(
            testSuiteName = testSuite.name,
            results = results,
            regressionAnalysis = regressionAnalysis,
            alerts = alerts,
            errors = errors,
            executionTimeMs = executionTime,
            reportFile = report
        )
    }
    
    /**
     * Run continuous performance monitoring with scheduled execution
     */
    fun startContinuousMonitoring(
        testSuite: PerformanceTestSuite,
        intervalMinutes: Long = 60,
        scope: CoroutineScope = GlobalScope
    ): Job {
        return scope.launch {
            while (isActive) {
                try {
                    runPerformanceMonitoring(testSuite, createBaseline = false)
                    delay(intervalMinutes * 60 * 1000) // Convert minutes to milliseconds
                } catch (e: Exception) {
                    println("Continuous monitoring error: ${e.message}")
                    delay(5 * 60 * 1000) // Wait 5 minutes before retry
                }
            }
        }
    }
    
    /**
     * Analyze performance trends over historical data
     */
    fun analyzeHistoricalTrends(
        testName: String,
        historicalResults: List<PerformanceResult>
    ): TrendAnalysis {
        return regressionDetector.analyzeTrends(testName, historicalResults)
    }
    
    /**
     * Generate comprehensive monitoring report
     */
    private fun generateMonitoringReport(
        testSuite: PerformanceTestSuite,
        results: List<PerformanceResult>,
        regressionAnalysis: RegressionAnalysis?,
        alerts: List<PerformanceAlert>,
        errors: List<PerformanceTestError>,
        executionTime: Long
    ): File {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))
        val reportFile = File(reportsDirectory, "performance-monitoring-${testSuite.name}-$timestamp.html")
        
        val html = generateHtmlReport(
            testSuite, results, regressionAnalysis, alerts, errors, executionTime
        )
        
        reportFile.writeText(html)
        return reportFile
    }
    
    private fun generateHtmlReport(
        testSuite: PerformanceTestSuite,
        results: List<PerformanceResult>,
        regressionAnalysis: RegressionAnalysis?,
        alerts: List<PerformanceAlert>,
        errors: List<PerformanceTestError>,
        executionTime: Long
    ): String {
        val statusColor = when {
            alerts.any { it.severity == AlertSeverity.CRITICAL } -> "#dc3545"
            alerts.any { it.severity == AlertSeverity.HIGH } -> "#fd7e14"
            alerts.any { it.severity == AlertSeverity.MEDIUM } -> "#ffc107"
            else -> "#28a745"
        }
        
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Monitoring Report - ${testSuite.name}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, $statusColor 0%, ${adjustColor(statusColor)} 100%); color: white; padding: 40px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .card { background: white; border-radius: 12px; padding: 30px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center; }
        .metric-value { font-size: 2.5rem; font-weight: bold; margin: 10px 0; }
        .metric-label { font-size: 0.9rem; opacity: 0.9; }
        .alert { padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid; }
        .alert-critical { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .alert-high { background: #fff3cd; border-color: #fd7e14; color: #856404; }
        .alert-info { background: #d1ecf1; border-color: #17a2b8; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 15px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background: #f8f9fa; font-weight: 600; color: #495057; }
        tr:hover { background: #f8f9fa; }
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-danger { color: #dc3545; font-weight: bold; }
        .footer { text-align: center; color: #6c757d; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Performance Monitoring Report</h1>
            <p>Test Suite: ${testSuite.name} | Generated: ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMMM dd, yyyy 'at' HH:mm"))}</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Tests Executed</div>
                <div class="metric-value">${results.size}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Execution Time</div>
                <div class="metric-value">${executionTime / 1000}s</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Alerts Generated</div>
                <div class="metric-value">${alerts.size}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Errors</div>
                <div class="metric-value">${errors.size}</div>
            </div>
        </div>

        ${generateAlertsSection(alerts)}
        ${generateRegressionSection(regressionAnalysis)}
        ${generateResultsSection(results)}
        ${generateErrorsSection(errors)}

        <div class="footer">
            <p>Performance Monitoring System - Automated Report</p>
        </div>
    </div>
</body>
</html>
        """.trimIndent()
    }
    
    private fun generateAlertsSection(alerts: List<PerformanceAlert>): String {
        if (alerts.isEmpty()) return ""
        
        return """
        <div class="card">
            <h2>🚨 Performance Alerts</h2>
            ${alerts.joinToString("") { alert ->
                val alertClass = when (alert.severity) {
                    AlertSeverity.CRITICAL -> "alert-critical"
                    AlertSeverity.HIGH -> "alert-high"
                    else -> "alert-info"
                }
                """<div class="alert $alertClass">${alert.message}</div>"""
            }}
        </div>
        """.trimIndent()
    }
    
    private fun generateRegressionSection(analysis: RegressionAnalysis?): String {
        if (analysis == null) return ""
        
        return """
        <div class="card">
            <h2>📈 Regression Analysis</h2>
            <p><strong>Status:</strong> ${analysis.overallStatus}</p>
            <p><strong>Baseline Date:</strong> ${analysis.baselineDate?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) ?: "N/A"}</p>
            
            ${if (analysis.regressions.isNotEmpty()) """
            <h3>Performance Regressions</h3>
            <table>
                <thead>
                    <tr><th>Operation</th><th>Baseline (ms)</th><th>Current (ms)</th><th>Change</th><th>Severity</th></tr>
                </thead>
                <tbody>
                    ${analysis.regressions.joinToString("") { reg ->
                        """<tr>
                            <td>${reg.operationName}</td>
                            <td>${String.format("%.3f", reg.baselineTime)}</td>
                            <td>${String.format("%.3f", reg.currentTime)}</td>
                            <td class="status-danger">+${String.format("%.1f", reg.degradationPercent)}%</td>
                            <td>${reg.severity}</td>
                        </tr>"""
                    }}
                </tbody>
            </table>
            """ else ""}
            
            ${if (analysis.improvements.isNotEmpty()) """
            <h3>Performance Improvements</h3>
            <table>
                <thead>
                    <tr><th>Operation</th><th>Baseline (ms)</th><th>Current (ms)</th><th>Improvement</th></tr>
                </thead>
                <tbody>
                    ${analysis.improvements.joinToString("") { imp ->
                        """<tr>
                            <td>${imp.operationName}</td>
                            <td>${String.format("%.3f", imp.baselineTime)}</td>
                            <td>${String.format("%.3f", imp.currentTime)}</td>
                            <td class="status-good">-${String.format("%.1f", imp.improvementPercent)}%</td>
                        </tr>"""
                    }}
                </tbody>
            </table>
            """ else ""}
        </div>
        """.trimIndent()
    }
    
    private fun generateResultsSection(results: List<PerformanceResult>): String {
        return """
        <div class="card">
            <h2>📊 Performance Results</h2>
            <table>
                <thead>
                    <tr><th>Test Name</th><th>Avg Time (ms)</th><th>Ops/sec</th><th>Std Dev</th><th>Iterations</th></tr>
                </thead>
                <tbody>
                    ${results.joinToString("") { result ->
                        """<tr>
                            <td>${result.testName}</td>
                            <td>${String.format("%.3f", result.averageTime)}</td>
                            <td>${String.format("%.1f", result.operationsPerSecond)}</td>
                            <td>${String.format("%.3f", result.standardDeviation)}</td>
                            <td>${result.iterations}</td>
                        </tr>"""
                    }}
                </tbody>
            </table>
        </div>
        """.trimIndent()
    }
    
    private fun generateErrorsSection(errors: List<PerformanceTestError>): String {
        if (errors.isEmpty()) return ""
        
        return """
        <div class="card">
            <h2>❌ Test Errors</h2>
            <table>
                <thead>
                    <tr><th>Test Name</th><th>Error</th><th>Timestamp</th></tr>
                </thead>
                <tbody>
                    ${errors.joinToString("") { error ->
                        """<tr>
                            <td>${error.testName}</td>
                            <td>${error.error}</td>
                            <td>${error.timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))}</td>
                        </tr>"""
                    }}
                </tbody>
            </table>
        </div>
        """.trimIndent()
    }
    
    private fun adjustColor(color: String): String {
        // Simple color adjustment for gradient
        return when (color) {
            "#dc3545" -> "#c82333"
            "#fd7e14" -> "#e66100"
            "#ffc107" -> "#e0a800"
            else -> "#1e7e34"
        }
    }
}

/**
 * Performance test suite definition
 */
data class PerformanceTestSuite(
    val name: String,
    val tests: List<PerformanceTest>
)

data class PerformanceTest(
    val name: String,
    val iterations: Int,
    val operation: suspend () -> Unit
)

/**
 * Monitoring result
 */
data class PerformanceMonitoringResult(
    val testSuiteName: String,
    val results: List<PerformanceResult>,
    val regressionAnalysis: RegressionAnalysis?,
    val alerts: List<PerformanceAlert>,
    val errors: List<PerformanceTestError>,
    val executionTimeMs: Long,
    val reportFile: File
)

data class PerformanceTestError(
    val testName: String,
    val error: String,
    val timestamp: LocalDateTime
)

/**
 * Alert handler interface for different notification systems
 */
interface AlertHandler {
    suspend fun handleAlert(alert: PerformanceAlert)
}

/**
 * Console alert handler for development
 */
class ConsoleAlertHandler : AlertHandler {
    override suspend fun handleAlert(alert: PerformanceAlert) {
        val severityIcon = when (alert.severity) {
            AlertSeverity.CRITICAL -> "🔴"
            AlertSeverity.HIGH -> "🟠"
            AlertSeverity.MEDIUM -> "🟡"
            AlertSeverity.LOW -> "🔵"
            AlertSeverity.INFO -> "ℹ️"
        }
        
        println("$severityIcon [${alert.severity}] ${alert.message}")
    }
}