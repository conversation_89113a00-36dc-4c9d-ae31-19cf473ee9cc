package com.yu.questicle.core.testing.benchmarks

import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Production-grade benchmark reporting system.
 */
class BenchmarkReportSystem(
    private val reportsDirectory: File
) {
    private val parser = JmhResultParser()

    init {
        reportsDirectory.mkdirs()
    }

    fun generateComprehensiveReport(
        resultsFile: File,
        baselineFile: File? = null,
        title: String = "Benchmark Report"
    ): File {
        val results = parser.parseResults(resultsFile)
        val analysis = parser.analyzeResults(results)
        val comparison = baselineFile?.let { baseline ->
            val baselineResults = parser.parseResults(baseline)
            parser.compareResults(baselineResults, results)
        }

        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))
        val reportFile = File(reportsDirectory, "benchmark-report-$timestamp.html")

        val html = generateHtmlReport(title, analysis, comparison)
        reportFile.writeText(html)

        return reportFile
    }

    private fun generateHtmlReport(
        title: String,
        analysis: BenchmarkAnalysis,
        comparison: ComparisonResult?
    ): String {
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .card { background: white; border-radius: 12px; padding: 30px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; border-radius: 12px; text-align: center; }
        .metric-value { font-size: 2.5rem; font-weight: bold; margin: 10px 0; }
        .metric-label { font-size: 0.9rem; opacity: 0.9; }
        .alert { padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid; }
        .alert-danger { background: #f8d7da; border-color: #dc3545; color: #721c24; }
        .alert-success { background: #d4edda; border-color: #28a745; color: #155724; }
        .alert-warning { background: #fff3cd; border-color: #ffc107; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 15px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background: #f8f9fa; font-weight: 600; color: #495057; }
        tr:hover { background: #f8f9fa; }
        .regression { background: #ffebee !important; }
        .improvement { background: #e8f5e8 !important; }
        .category-section { margin: 30px 0; }
        .category-header { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
        .progress-bar { width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); }
        .footer { text-align: center; color: #6c757d; margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>$title</h1>
            <p>Generated on ${LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMMM dd, yyyy 'at' HH:mm"))}</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Total Benchmarks</div>
                <div class="metric-value">${analysis.totalBenchmarks}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Average Score</div>
                <div class="metric-value">${String.format("%.2f", analysis.averageScore)}</div>
                <div class="metric-label">${analysis.bestResult?.scoreUnit ?: "ops/s"}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Best Performance</div>
                <div class="metric-value">${String.format("%.2f", analysis.bestResult?.score ?: 0.0)}</div>
                <div class="metric-label">${analysis.bestResult?.benchmark?.substringAfterLast('.') ?: "N/A"}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Categories</div>
                <div class="metric-value">${analysis.categoryBreakdown.size}</div>
            </div>
        </div>

        ${generateComparisonSection(comparison)}
        ${generateCategorySection(analysis)}
        ${generateDetailedResultsSection(analysis)}

        <div class="footer">
            <p>Report generated by JMH Benchmark Report System</p>
        </div>
    </div>
</body>
</html>
        """.trimIndent()
    }

    private fun generateComparisonSection(comparison: ComparisonResult?): String {
        if (comparison == null) return ""

        val alertClass = when {
            comparison.regressions.isNotEmpty() -> "alert-danger"
            comparison.improvements.isNotEmpty() -> "alert-success"
            else -> "alert-warning"
        }

        val alertMessage = when {
            comparison.regressions.isNotEmpty() -> "⚠️ ${comparison.regressions.size} performance regressions detected!"
            comparison.improvements.isNotEmpty() -> "✅ ${comparison.improvements.size} performance improvements detected!"
            else -> "ℹ️ No significant performance changes detected."
        }

        return """
        <div class="card">
            <h2>📈 Performance Comparison</h2>
            <div class="alert $alertClass">
                $alertMessage
            </div>
            <p><strong>Overall Performance Change:</strong> ${String.format("%.2f", comparison.overallPerformanceChange)}%</p>
            
            ${if (comparison.regressions.isNotEmpty()) generateRegressionTable(comparison.regressions) else ""}
            ${if (comparison.improvements.isNotEmpty()) generateImprovementTable(comparison.improvements) else ""}
        </div>
        """.trimIndent()
    }

    private fun generateRegressionTable(regressions: List<BenchmarkComparison>): String {
        return """
        <h3>🔴 Performance Regressions</h3>
        <table>
            <thead>
                <tr><th>Benchmark</th><th>Baseline</th><th>Current</th><th>Change</th><th>Unit</th></tr>
            </thead>
            <tbody>
                ${regressions.joinToString("") { reg ->
                    """<tr class="regression">
                        <td>${reg.benchmarkName.substringAfterLast('.')}</td>
                        <td>${String.format("%.3f", reg.baselineScore)}</td>
                        <td>${String.format("%.3f", reg.currentScore)}</td>
                        <td>+${String.format("%.2f", reg.percentageChange)}%</td>
                        <td>${reg.scoreUnit}</td>
                    </tr>"""
                }}
            </tbody>
        </table>
        """.trimIndent()
    }

    private fun generateImprovementTable(improvements: List<BenchmarkComparison>): String {
        return """
        <h3>🟢 Performance Improvements</h3>
        <table>
            <thead>
                <tr><th>Benchmark</th><th>Baseline</th><th>Current</th><th>Change</th><th>Unit</th></tr>
            </thead>
            <tbody>
                ${improvements.joinToString("") { imp ->
                    """<tr class="improvement">
                        <td>${imp.benchmarkName.substringAfterLast('.')}</td>
                        <td>${String.format("%.3f", imp.baselineScore)}</td>
                        <td>${String.format("%.3f", imp.currentScore)}</td>
                        <td>${String.format("%.2f", imp.percentageChange)}%</td>
                        <td>${imp.scoreUnit}</td>
                    </tr>"""
                }}
            </tbody>
        </table>
        """.trimIndent()
    }

    private fun generateCategorySection(analysis: BenchmarkAnalysis): String {
        return """
        <div class="card">
            <h2>🏷️ Category Breakdown</h2>
            ${analysis.categoryBreakdown.entries.joinToString("") { (category, stats) ->
                val percentage = (stats.count.toDouble() / analysis.totalBenchmarks * 100).toInt()
                """
                <div class="category-section">
                    <div class="category-header">
                        <h3>$category (${stats.count} benchmarks)</h3>
                        <p>Average: ${String.format("%.3f", stats.averageScore)} | Best: ${String.format("%.3f", stats.bestScore)} | Worst: ${String.format("%.3f", stats.worstScore)}</p>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: $percentage%"></div>
                    </div>
                </div>
                """
            }}
        </div>
        """.trimIndent()
    }

    private fun generateDetailedResultsSection(analysis: BenchmarkAnalysis): String {
        // Simplified implementation for now
        val allResults = emptyList<BenchmarkResult>()

        return """
        <div class="card">
            <h2>📋 Detailed Results</h2>
            <p>Detailed benchmark results would be displayed here in a production implementation.</p>
        </div>
        """.trimIndent()
    }

    fun generateJsonReport(resultsFile: File, baselineFile: File? = null): File {
        val results = parser.parseResults(resultsFile)
        val analysis = parser.analyzeResults(results)
        val comparison = baselineFile?.let { baseline ->
            val baselineResults = parser.parseResults(baseline)
            parser.compareResults(baselineResults, results)
        }

        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))
        val reportFile = File(reportsDirectory, "benchmark-report-$timestamp.json")

        val jsonReport = """
{
  "metadata": {
    "generatedAt": "${LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)}",
    "version": "1.0"
  },
  "analysis": {
    "totalBenchmarks": ${analysis.totalBenchmarks},
    "averageScore": ${analysis.averageScore}
  },
  "comparison": ${if (comparison != null) """
  {
    "overallPerformanceChange": ${comparison.overallPerformanceChange},
    "regressionCount": ${comparison.regressions.size},
    "improvementCount": ${comparison.improvements.size}
  }""" else "null"}
}
        """.trimIndent()

        reportFile.writeText(jsonReport)
        return reportFile
    }
}