package com.yu.questicle.core.testing.performance

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 性能监控器
 * 
 * 负责监控应用性能指标，包括帧率、内存使用、CPU使用等
 */
@Singleton
class PerformanceMonitor @Inject constructor() {
    
    private val _metrics = MutableSharedFlow<PerformanceMetric>()
    val metrics: Flow<PerformanceMetric> = _metrics.asSharedFlow()
    
    private var isMonitoring = false
    
    /**
     * 开始监控
     */
    fun startMonitoring() {
        isMonitoring = true
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring() {
        isMonitoring = false
    }
    
    /**
     * 记录性能指标
     */
    suspend fun recordMetric(metric: PerformanceMetric) {
        if (isMonitoring) {
            _metrics.emit(metric)
        }
    }
    
    /**
     * 记录帧率
     */
    suspend fun recordFrameRate(fps: Double) {
        recordMetric(
            PerformanceMetric(
                name = "frame_rate",
                value = fps,
                unit = "fps",
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录内存使用
     */
    suspend fun recordMemoryUsage(memoryMB: Double) {
        recordMetric(
            PerformanceMetric(
                name = "memory_usage",
                value = memoryMB,
                unit = "MB",
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录CPU使用率
     */
    suspend fun recordCpuUsage(cpuPercent: Double) {
        recordMetric(
            PerformanceMetric(
                name = "cpu_usage",
                value = cpuPercent,
                unit = "%",
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录网络延迟
     */
    suspend fun recordNetworkLatency(latencyMs: Double) {
        recordMetric(
            PerformanceMetric(
                name = "network_latency",
                value = latencyMs,
                unit = "ms",
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录游戏引擎性能
     */
    suspend fun recordGameEngineMetric(name: String, value: Double, unit: String = "ms") {
        recordMetric(
            PerformanceMetric(
                name = "game_engine_$name",
                value = value,
                unit = unit,
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录渲染性能
     */
    suspend fun recordRenderingMetric(name: String, value: Double, unit: String = "ms") {
        recordMetric(
            PerformanceMetric(
                name = "rendering_$name",
                value = value,
                unit = unit,
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 记录用户交互延迟
     */
    suspend fun recordUserInteractionLatency(interactionType: String, latencyMs: Double) {
        recordMetric(
            PerformanceMetric(
                name = "user_interaction_${interactionType}_latency",
                value = latencyMs,
                unit = "ms",
                timestamp = System.currentTimeMillis()
            )
        )
    }
    
    /**
     * 获取当前监控状态
     */
    fun isMonitoring(): Boolean = isMonitoring
}

/**
 * 性能指标数据类
 */
data class PerformanceMetric(
    val name: String,
    val value: Double,
    val unit: String,
    val timestamp: Long,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * 性能监控配置
 */
data class PerformanceMonitorConfig(
    val enableFrameRateMonitoring: Boolean = true,
    val enableMemoryMonitoring: Boolean = true,
    val enableCpuMonitoring: Boolean = true,
    val enableNetworkMonitoring: Boolean = true,
    val enableGameEngineMonitoring: Boolean = true,
    val enableRenderingMonitoring: Boolean = true,
    val enableUserInteractionMonitoring: Boolean = true,
    val samplingIntervalMs: Long = 1000L,
    val maxMetricsInMemory: Int = 1000
)

/**
 * 性能监控扩展函数
 */
suspend inline fun <T> PerformanceMonitor.measureTime(
    metricName: String,
    unit: String = "ms",
    block: () -> T
): T {
    val startTime = System.currentTimeMillis()
    return try {
        block()
    } finally {
        val endTime = System.currentTimeMillis()
        recordMetric(
            PerformanceMetric(
                name = metricName,
                value = (endTime - startTime).toDouble(),
                unit = unit,
                timestamp = endTime
            )
        )
    }
}

/**
 * 性能监控注解
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class MonitorPerformance(
    val metricName: String = "",
    val unit: String = "ms",
    val enabled: Boolean = true
)

/**
 * 性能阈值配置
 */
data class PerformanceThresholds(
    val maxFrameTime: Double = 16.67, // 60 FPS
    val maxMemoryUsage: Double = 512.0, // 512 MB
    val maxCpuUsage: Double = 80.0, // 80%
    val maxNetworkLatency: Double = 1000.0, // 1 second
    val maxGameEngineTime: Double = 10.0, // 10 ms
    val maxRenderingTime: Double = 8.0, // 8 ms
    val maxUserInteractionLatency: Double = 100.0 // 100 ms
)

/**
 * 性能警报
 */
data class PerformanceAlert(
    val metricName: String,
    val currentValue: Double,
    val threshold: Double,
    val severity: AlertSeverity,
    val timestamp: Long = System.currentTimeMillis(),
    val message: String = "Performance threshold exceeded for $metricName: $currentValue > $threshold"
)

/**
 * 性能监控统计
 */
data class PerformanceStats(
    val metricName: String,
    val count: Long,
    val average: Double,
    val min: Double,
    val max: Double,
    val standardDeviation: Double,
    val percentile95: Double,
    val percentile99: Double
)
