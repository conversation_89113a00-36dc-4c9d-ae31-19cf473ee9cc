package com.yu.questicle.core.testing.performance

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.factory.TestDataFactory
import kotlinx.coroutines.delay

/**
 * Comprehensive performance test suite for Tetris engine operations
 */
class TetrisPerformanceTestSuite(
    private val performanceTestRunner: PerformanceTestRunner = DefaultPerformanceTestRunner()
) {
    
    /**
     * Run all performance tests and return comprehensive results
     */
    suspend fun runAllTests(): TetrisPerformanceResults {
        val results = mutableMapOf<String, PerformanceResult>()
        val memoryResults = mutableMapOf<String, MemoryUsageResult>()
        val benchmarkResults = mutableMapOf<String, BenchmarkResult>()
        
        // Piece movement tests
        results["piece_movement_left"] = testPieceMovement(Direction.LEFT)
        results["piece_movement_right"] = testPieceMovement(Direction.RIGHT)
        results["piece_movement_down"] = testPieceMovement(Direction.DOWN)
        
        // Collision detection tests
        results["collision_detection_empty"] = testCollisionDetectionEmpty()
        results["collision_detection_full"] = testCollisionDetectionFull()
        
        // Line clearing tests
        results["line_clearing_single"] = testLineClearingSingle()
        results["line_clearing_tetris"] = testLineClearingTetris()
        
        // Game state operations
        results["game_state_creation"] = testGameStateCreation()
        results["game_state_update"] = testGameStateUpdate()
        
        // Memory usage tests
        memoryResults["piece_generation"] = testPieceGenerationMemory()
        memoryResults["board_operations"] = testBoardOperationsMemory()
        memoryResults["statistics_calculation"] = testStatisticsCalculationMemory()
        
        // Benchmark tests
        benchmarkResults["piece_rotation"] = testPieceRotationBenchmark()
        benchmarkResults["board_validation"] = testBoardValidationBenchmark()
        benchmarkResults["score_calculation"] = testScoreCalculationBenchmark()
        
        return TetrisPerformanceResults(
            performanceResults = results,
            memoryResults = memoryResults,
            benchmarkResults = benchmarkResults
        )
    }
    
    /**
     * Test piece movement performance
     */
    suspend fun testPieceMovement(direction: Direction): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "piece_movement_${direction.name.lowercase()}",
            iterations = 1000
        ) {
            val piece = TestDataFactory.createTetrisPiece(x = 4, y = 5)
            val board = TetrisBoard.empty()
            
            // Simulate piece movement
            val newPiece = when (direction) {
                Direction.LEFT -> piece.copy(x = piece.x - 1)
                Direction.RIGHT -> piece.copy(x = piece.x + 1)
                Direction.DOWN -> piece.copy(y = piece.y + 1)
            }
            
            // Validate position
            board.isValidPosition(newPiece)
        }
    }
    
    /**
     * Test collision detection on empty board
     */
    suspend fun testCollisionDetectionEmpty(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "collision_detection_empty",
            iterations = 2000
        ) {
            val piece = TestDataFactory.createTetrisPiece(x = 4, y = 10)
            val board = TetrisBoard.empty()
            
            // Check collision on empty board
            board.isValidPosition(piece)
        }
    }
    
    /**
     * Test collision detection on full board
     */
    suspend fun testCollisionDetectionFull(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "collision_detection_full",
            iterations = 2000
        ) {
            val piece = TestDataFactory.createTetrisPiece(x = 4, y = 15)
            val board = TestDataFactory.createBoardWithFullLines(4)
            
            // Check collision on board with obstacles
            board.isValidPosition(piece)
        }
    }
    
    /**
     * Test single line clearing performance
     */
    suspend fun testLineClearingSingle(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "line_clearing_single",
            iterations = 500
        ) {
            val board = TestDataFactory.createBoardWithFullLines(1)
            
            // Clear single line
            board.clearLines()
        }
    }
    
    /**
     * Test tetris (4 lines) clearing performance
     */
    suspend fun testLineClearingTetris(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "line_clearing_tetris",
            iterations = 500
        ) {
            val board = TestDataFactory.createBoardWithFullLines(4)
            
            // Clear tetris
            board.clearLines()
        }
    }
    
    /**
     * Test game state creation performance
     */
    suspend fun testGameStateCreation(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "game_state_creation",
            iterations = 1000
        ) {
            TestDataFactory.createTetrisGameState(
                status = TetrisStatus.PLAYING,
                currentPiece = TestDataFactory.createTetrisPiece(),
                nextPiece = TestDataFactory.createTetrisPiece(TetrisPieceType.T)
            )
        }
    }
    
    /**
     * Test game state update performance
     */
    suspend fun testGameStateUpdate(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "game_state_update",
            iterations = 1000
        ) {
            val gameState = TestDataFactory.createTetrisGameState()
            
            // Update game state
            gameState.copy(
                score = gameState.score + 100,
                lines = gameState.lines + 1,
                level = gameState.level + 1
            )
        }
    }
    
    /**
     * Test piece generation memory usage
     */
    suspend fun testPieceGenerationMemory(): MemoryUsageResult {
        return performanceTestRunner.measureMemoryUsage {
            val pieces = mutableListOf<TetrisPiece>()
            
            // Generate many pieces
            repeat(1000) {
                val pieceType = TetrisPieceType.values().random()
                pieces.add(TestDataFactory.createTetrisPiece(pieceType))
            }
            
            // Use pieces to prevent optimization
            pieces.size
        }
    }
    
    /**
     * Test board operations memory usage
     */
    suspend fun testBoardOperationsMemory(): MemoryUsageResult {
        return performanceTestRunner.measureMemoryUsage {
            val boards = mutableListOf<TetrisBoard>()
            
            // Create and modify boards
            repeat(100) {
                val board = TetrisBoard.empty()
                val piece = TestDataFactory.createTetrisPiece(x = 4, y = 15)
                val newBoard = board.placePiece(piece)
                boards.add(newBoard)
            }
            
            // Use boards to prevent optimization
            boards.size
        }
    }
    
    /**
     * Test statistics calculation memory usage
     */
    suspend fun testStatisticsCalculationMemory(): MemoryUsageResult {
        return performanceTestRunner.measureMemoryUsage {
            val statisticsList = mutableListOf<GameStatistics>()
            
            // Create and calculate statistics
            repeat(500) {
                val stats = TestDataFactory.createTetrisStatistics(
                    piecesPlaced = it * 2,
                    linesCleared = it,
                    tetrises = it / 10
                )
                statisticsList.add(stats.calculateEfficiency())
            }
            
            // Use statistics to prevent optimization
            statisticsList.size
        }
    }
    
    /**
     * Benchmark piece rotation
     */
    suspend fun testPieceRotationBenchmark(): BenchmarkResult {
        return performanceTestRunner.benchmarkOperation {
            val piece = TestDataFactory.createTetrisPiece(TetrisPieceType.T)
            
            // Rotate piece through all orientations
            var rotatedPiece = piece
            repeat(4) {
                rotatedPiece = rotatedPiece.rotate()
            }
        }
    }
    
    /**
     * Benchmark board validation
     */
    suspend fun testBoardValidationBenchmark(): BenchmarkResult {
        return performanceTestRunner.benchmarkOperation {
            val board = TestDataFactory.createBoardWithFullLines(2)
            val piece = TestDataFactory.createTetrisPiece(x = 4, y = 10)
            
            // Validate multiple positions
            repeat(10) { x ->
                val testPiece = piece.copy(x = x)
                board.isValidPosition(testPiece)
            }
        }
    }
    
    /**
     * Benchmark score calculation
     */
    suspend fun testScoreCalculationBenchmark(): BenchmarkResult {
        return performanceTestRunner.benchmarkOperation {
            val statistics = TestDataFactory.createTetrisStatistics()
            
            // Calculate various metrics
            statistics.calculateEfficiency()
            statistics.getSpecialClears()
            statistics.getAttackPower()
        }
    }
    
    /**
     * Run stress test with high load
     */
    suspend fun runStressTest(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "stress_test_high_load",
            iterations = 100
        ) {
            // Simulate intensive game operations
            val gameState = TestDataFactory.createTetrisGameState()
            val actions = listOf(
                TetrisAction.Move(Direction.LEFT, "test"),
                TetrisAction.Move(Direction.RIGHT, "test"),
                TetrisAction.Rotate(true, "test"),
                TetrisAction.Drop(false, "test")
            )
            
            // Process multiple actions
            actions.forEach { action ->
                when (action) {
                    is TetrisAction.Move -> {
                        val piece = gameState.currentPiece
                        if (piece != null) {
                            val newPiece = when (action.direction) {
                                Direction.LEFT -> piece.copy(x = piece.x - 1)
                                Direction.RIGHT -> piece.copy(x = piece.x + 1)
                                Direction.DOWN -> piece.copy(y = piece.y + 1)
                            }
                            gameState.board.isValidPosition(newPiece)
                        }
                    }
                    is TetrisAction.Rotate -> {
                        val piece = gameState.currentPiece
                        if (piece != null) {
                            val rotatedPiece = piece.rotate()
                            gameState.board.isValidPosition(rotatedPiece)
                        }
                    }
                    is TetrisAction.Drop -> {
                        val piece = gameState.currentPiece
                        if (piece != null) {
                            gameState.board.isValidPosition(piece)
                        }
                    }
                    else -> { /* Handle other actions */ }
                }
            }
        }
    }
    
    /**
     * Run concurrent performance test
     */
    suspend fun runConcurrentTest(): PerformanceResult {
        return performanceTestRunner.runPerformanceTest(
            testName = "concurrent_operations",
            iterations = 200
        ) {
            // Simulate concurrent operations
            val gameStates = (1..5).map {
                TestDataFactory.createTetrisGameState(id = "game_$it")
            }
            
            // Process each game state
            gameStates.forEach { gameState ->
                val piece = gameState.currentPiece
                if (piece != null) {
                    // Simulate multiple operations per game state
                    gameState.board.isValidPosition(piece)
                    val movedPiece = piece.copy(x = piece.x + 1)
                    gameState.board.isValidPosition(movedPiece)
                }
            }
        }
    }
}

/**
 * Results container for all Tetris performance tests
 */
data class TetrisPerformanceResults(
    val performanceResults: Map<String, PerformanceResult>,
    val memoryResults: Map<String, MemoryUsageResult>,
    val benchmarkResults: Map<String, BenchmarkResult>
) {
    
    /**
     * Get summary of all performance results
     */
    fun getSummary(): String {
        val sb = StringBuilder()
        sb.appendLine("=== Tetris Performance Test Results ===")
        sb.appendLine()
        
        sb.appendLine("Performance Tests:")
        performanceResults.forEach { (name, result) ->
            sb.appendLine("  $name: ${String.format("%.2f", result.averageTime)}ms avg, ${String.format("%.2f", result.operationsPerSecond)} ops/sec")
        }
        sb.appendLine()
        
        sb.appendLine("Memory Tests:")
        memoryResults.forEach { (name, result) ->
            sb.appendLine("  $name: ${formatBytes(result.allocatedMemory)} allocated")
        }
        sb.appendLine()
        
        sb.appendLine("Benchmark Tests:")
        benchmarkResults.forEach { (name, result) ->
            sb.appendLine("  $name: ${String.format("%.3f", result.score)} ${result.scoreUnit}")
        }
        
        return sb.toString()
    }
    
    /**
     * Check if all tests meet performance thresholds
     */
    fun meetsPerformanceThresholds(thresholds: PerformanceThresholds): Boolean {
        // Check performance thresholds
        performanceResults.forEach { (name, result) ->
            val threshold = thresholds.performanceThresholds[name]
            if (threshold != null && result.averageTime > threshold) {
                return false
            }
        }
        
        // Check memory thresholds
        memoryResults.forEach { (name, result) ->
            val threshold = thresholds.memoryThresholds[name]
            if (threshold != null && result.allocatedMemory > threshold) {
                return false
            }
        }
        
        return true
    }
    
    private fun formatBytes(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        return when {
            mb >= 1.0 -> String.format("%.2f MB", mb)
            kb >= 1.0 -> String.format("%.2f KB", kb)
            else -> "$bytes bytes"
        }
    }
}

/**
 * Performance thresholds for validation
 */
data class PerformanceThresholds(
    val performanceThresholds: Map<String, Double> = mapOf(
        "piece_movement_left" to 1.0,
        "piece_movement_right" to 1.0,
        "piece_movement_down" to 1.0,
        "collision_detection_empty" to 0.5,
        "collision_detection_full" to 1.0,
        "line_clearing_single" to 2.0,
        "line_clearing_tetris" to 5.0,
        "game_state_creation" to 1.0,
        "game_state_update" to 0.5
    ),
    val memoryThresholds: Map<String, Long> = mapOf(
        "piece_generation" to 1024 * 1024, // 1MB
        "board_operations" to 2 * 1024 * 1024, // 2MB
        "statistics_calculation" to 512 * 1024 // 512KB
    )
)