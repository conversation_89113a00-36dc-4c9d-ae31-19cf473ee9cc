package com.yu.questicle.core.testing.benchmarks

import org.openjdk.jmh.annotations.*
import org.openjdk.jmh.infra.Blackhole
import java.util.concurrent.TimeUnit

/**
 * Simple JMH benchmark to verify JMH configuration is working
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MICROSECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
open class SimpleBenchmark {

    private lateinit var testData: IntArray

    @Setup
    fun setup() {
        testData = IntArray(1000) { it }
    }

    @Benchmark
    fun benchmarkArraySum(blackhole: Blackhole) {
        var sum = 0
        for (i in testData.indices) {
            sum += testData[i]
        }
        blackhole.consume(sum)
    }

    @Benchmark
    fun benchmarkArraySumWithStream(blackhole: Blackhole) {
        val sum = testData.sum()
        blackhole.consume(sum)
    }

    @Benchmark
    fun benchmarkStringConcatenation(blackhole: Blackhole) {
        val sb = StringBuilder()
        for (i in 0 until 100) {
            sb.append("test_$i")
        }
        blackhole.consume(sb.toString())
    }

    @Benchmark
    fun benchmarkListOperations(blackhole: Blackhole) {
        val list = mutableListOf<Int>()
        for (i in 0 until 100) {
            list.add(i)
        }
        val filtered = list.filter { it % 2 == 0 }
        blackhole.consume(filtered.size)
    }

    @Benchmark
    fun benchmarkMapOperations(blackhole: Blackhole) {
        val map = mutableMapOf<String, Int>()
        for (i in 0 until 100) {
            map["key_$i"] = i
        }
        val sum = map.values.sum()
        blackhole.consume(sum)
    }
}