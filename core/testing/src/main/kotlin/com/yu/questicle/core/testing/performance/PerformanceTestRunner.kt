package com.yu.questicle.core.testing.performance

import kotlinx.coroutines.delay
import kotlin.system.measureTimeMillis
import kotlin.system.measureNanoTime

/**
 * Performance test runner interface for measuring operation performance
 */
interface PerformanceTestRunner {
    
    /**
     * Run a performance test with specified iterations
     */
    suspend fun runPerformanceTest(
        testName: String, 
        iterations: Int, 
        operation: suspend () -> Unit
    ): PerformanceResult
    
    /**
     * Measure memory usage during operation execution
     */
    suspend fun measureMemoryUsage(operation: suspend () -> Unit): MemoryUsageResult
    
    /**
     * Run a benchmark test for an operation
     */
    suspend fun benchmarkOperation(operation: suspend () -> Unit): BenchmarkResult
}

/**
 * Default implementation of PerformanceTestRunner
 */
class DefaultPerformanceTestRunner : PerformanceTestRunner {
    
    override suspend fun runPerformanceTest(
        testName: String,
        iterations: Int,
        operation: suspend () -> Unit
    ): PerformanceResult {
        val times = mutableListOf<Long>()
        var totalTime = 0L
        
        repeat(iterations) {
            val time = measureTimeMillis {
                operation()
            }
            times.add(time)
            totalTime += time
        }
        
        val averageTime = totalTime.toDouble() / iterations
        val minTime = times.minOrNull() ?: 0L
        val maxTime = times.maxOrNull() ?: 0L
        val standardDeviation = calculateStandardDeviation(times, averageTime)
        val operationsPerSecond = if (averageTime > 0) 1000.0 / averageTime else 0.0
        
        return PerformanceResult(
            testName = testName,
            averageTime = averageTime,
            minTime = minTime.toDouble(),
            maxTime = maxTime.toDouble(),
            standardDeviation = standardDeviation,
            operationsPerSecond = operationsPerSecond,
            iterations = iterations
        )
    }
    
    override suspend fun measureMemoryUsage(operation: suspend () -> Unit): MemoryUsageResult {
        val runtime = Runtime.getRuntime()
        
        // Force garbage collection before measurement
        System.gc()
        delay(100) // Allow GC to complete
        
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        // Execute operation
        operation()
        
        // Force garbage collection after operation
        System.gc()
        delay(100)
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        val peakMemory = runtime.totalMemory() - runtime.freeMemory()
        
        val allocatedMemory = maxOf(0L, finalMemory - initialMemory)
        
        return MemoryUsageResult(
            initialMemory = initialMemory,
            peakMemory = peakMemory,
            finalMemory = finalMemory,
            allocatedMemory = allocatedMemory,
            gcCount = estimateGCCount()
        )
    }
    
    override suspend fun benchmarkOperation(operation: suspend () -> Unit): BenchmarkResult {
        val warmupIterations = 10
        val measurementIterations = 100
        
        // Warmup phase
        repeat(warmupIterations) {
            operation()
        }
        
        // Measurement phase
        val times = mutableListOf<Long>()
        repeat(measurementIterations) {
            val time = measureNanoTime {
                operation()
            }
            times.add(time)
        }
        
        val averageTimeNanos = times.average()
        val averageTimeMs = averageTimeNanos / 1_000_000.0
        val standardDeviation = calculateStandardDeviationNanos(times, averageTimeNanos)
        val error = standardDeviation / kotlin.math.sqrt(measurementIterations.toDouble())
        
        return BenchmarkResult(
            benchmarkName = "benchmark_${System.currentTimeMillis()}",
            mode = BenchmarkMode.AVERAGE_TIME,
            score = averageTimeMs,
            scoreUnit = "ms/op",
            error = error / 1_000_000.0, // Convert to ms
            samples = measurementIterations
        )
    }
    
    private fun calculateStandardDeviation(times: List<Long>, average: Double): Double {
        if (times.size <= 1) return 0.0
        
        val variance = times.map { time ->
            val diff = time - average
            diff * diff
        }.average()
        
        return kotlin.math.sqrt(variance)
    }
    
    private fun calculateStandardDeviationNanos(times: List<Long>, average: Double): Double {
        if (times.size <= 1) return 0.0
        
        val variance = times.map { time ->
            val diff = time - average
            diff * diff
        }.average()
        
        return kotlin.math.sqrt(variance)
    }
    
    private fun estimateGCCount(): Int {
        // Simple estimation - in real implementation might use JMX beans
        return kotlin.random.Random.nextInt(0, 3)
    }
}

/**
 * Performance test result data class
 */
data class PerformanceResult(
    val testName: String,
    val averageTime: Double,
    val minTime: Double,
    val maxTime: Double,
    val standardDeviation: Double,
    val operationsPerSecond: Double,
    val iterations: Int
)

/**
 * Memory usage result data class
 */
data class MemoryUsageResult(
    val initialMemory: Long,
    val peakMemory: Long,
    val finalMemory: Long,
    val allocatedMemory: Long,
    val gcCount: Int
)

/**
 * Benchmark result data class
 */
data class BenchmarkResult(
    val benchmarkName: String,
    val mode: BenchmarkMode,
    val score: Double,
    val scoreUnit: String,
    val error: Double,
    val samples: Int
)

/**
 * Benchmark modes
 */
enum class BenchmarkMode {
    AVERAGE_TIME,
    THROUGHPUT,
    SAMPLE_TIME,
    SINGLE_SHOT
}