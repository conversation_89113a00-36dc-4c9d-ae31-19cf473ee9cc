package com.yu.questicle.core.testing

import io.kotest.core.spec.style.BehaviorSpec
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.core.spec.style.FunSpec
import io.kotest.core.spec.style.StringSpec
import io.mockk.MockKAnnotations
import io.mockk.clearAllMocks
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

/**
 * 基础测试类 - JUnit 5风格
 */
abstract class BaseUnitTest {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    protected val testDispatcher: TestDispatcher = StandardTestDispatcher()
    
    @OptIn(ExperimentalCoroutinesApi::class)
    @BeforeEach
    open fun setUp() {
        MockKAnnotations.init(this)
        Dispatchers.setMain(testDispatcher)
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    @AfterEach
    open fun tearDown() {
        clearAllMocks()
        Dispatchers.resetMain()
    }
}

/**
 * 协程测试基类
 */
@OptIn(ExperimentalCoroutinesApi::class)
abstract class BaseCoroutineTest : BaseUnitTest() {
    
    protected fun runTest(block: suspend () -> Unit) {
        kotlinx.coroutines.test.runTest(testDispatcher) {
            block()
        }
    }
}

/**
 * Kotest风格的基础测试类
 */
abstract class BaseKotestSpec : StringSpec() {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    protected val testDispatcher: TestDispatcher = StandardTestDispatcher()
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun beforeEach(testCase: io.kotest.core.test.TestCase) {
        MockKAnnotations.init(this)
        Dispatchers.setMain(testDispatcher)
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun afterEach(testCase: io.kotest.core.test.TestCase, result: io.kotest.core.test.TestResult) {
        clearAllMocks()
        Dispatchers.resetMain()
    }
}

/**
 * BDD风格测试基类
 */
abstract class BaseBehaviorSpec : BehaviorSpec() {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    protected val testDispatcher: TestDispatcher = StandardTestDispatcher()
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun beforeEach(testCase: io.kotest.core.test.TestCase) {
        MockKAnnotations.init(this)
        Dispatchers.setMain(testDispatcher)
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun afterEach(testCase: io.kotest.core.test.TestCase, result: io.kotest.core.test.TestResult) {
        clearAllMocks()
        Dispatchers.resetMain()
    }
}

/**
 * 描述式测试基类
 */
abstract class BaseDescribeSpec : DescribeSpec() {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    protected val testDispatcher: TestDispatcher = StandardTestDispatcher()
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun beforeEach(testCase: io.kotest.core.test.TestCase) {
        MockKAnnotations.init(this)
        Dispatchers.setMain(testDispatcher)
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun afterEach(testCase: io.kotest.core.test.TestCase, result: io.kotest.core.test.TestResult) {
        clearAllMocks()
        Dispatchers.resetMain()
    }
}