package com.yu.questicle.core.testing.model

import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.*

/**
 * 测试用户数据模型
 */
@Serializable
data class User(
    val id: String,
    val name: String,
    val email: String?,
    val level: Int = 1,
    val experience: Long = 0L,
    val createdAt: String, // ISO 8601 格式
    val lastLoginAt: String? = null,
    val isActive: Boolean = true,
    val preferences: UserPreferences = UserPreferences(),
    val statistics: UserStatistics = UserStatistics()
) {
    fun getDisplayName(): String = name.ifBlank { "Player $id" }
    
    fun isNewUser(): Boolean = experience < 100L
    
    fun canLevelUp(): Boolean {
        val requiredExp = level * 1000L
        return experience >= requiredExp
    }
}

/**
 * 用户偏好设置
 */
@Serializable
data class UserPreferences(
    val soundEnabled: Boolean = true,
    val musicEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val theme: String = "default",
    val language: String = "en",
    val difficulty: String = "normal",
    val autoSave: Boolean = true,
    val notifications: Boolean = true
)

/**
 * 用户统计数据
 */
@Serializable
data class UserStatistics(
    val totalGamesPlayed: Long = 0L,
    val totalPlayTime: Long = 0L, // 毫秒
    val highestScore: Long = 0L,
    val totalLinesCleared: Long = 0L,
    val totalPiecesPlaced: Long = 0L,
    val averageGameDuration: Long = 0L, // 毫秒
    val winRate: Double = 0.0,
    val favoriteGameMode: String? = null,
    val achievements: List<String> = emptyList()
) {
    fun getAverageScore(): Double {
        return if (totalGamesPlayed > 0) highestScore.toDouble() / totalGamesPlayed else 0.0
    }
    
    fun getPiecesPerMinute(): Double {
        return if (totalPlayTime > 0) {
            (totalPiecesPlaced * 60000.0) / totalPlayTime
        } else 0.0
    }
}

/**
 * 游戏会话数据模型
 */
@Serializable
data class GameSession(
    val id: String,
    val userId: String,
    val startTime: String, // ISO 8601 格式
    val endTime: String? = null,
    val score: Long = 0L,
    val level: Int = 1,
    val duration: Long = 0L, // 毫秒
    val gameMode: String = "classic",
    val status: GameSessionStatus = GameSessionStatus.ACTIVE,
    val statistics: GameSessionStatistics = GameSessionStatistics(),
    val events: List<GameEvent> = emptyList()
) {
    fun isActive(): Boolean = status == GameSessionStatus.ACTIVE
    
    fun isCompleted(): Boolean = status == GameSessionStatus.COMPLETED
    
    fun getDurationInMinutes(): Double = duration / 60000.0
    
    fun getScorePerMinute(): Double {
        return if (duration > 0) (score * 60000.0) / duration else 0.0
    }
}

/**
 * 游戏会话状态
 */
enum class GameSessionStatus {
    ACTIVE, PAUSED, COMPLETED, ABANDONED, ERROR
}

/**
 * 游戏会话统计
 */
@Serializable
data class GameSessionStatistics(
    val linesCleared: Int = 0,
    val piecesPlaced: Int = 0,
    val tetrisCount: Int = 0, // 四行消除次数
    val comboCount: Int = 0,
    val maxCombo: Int = 0,
    val holdUsed: Int = 0,
    val softDrops: Int = 0,
    val hardDrops: Int = 0,
    val rotations: Int = 0,
    val perfectClears: Int = 0,
    val tSpins: Int = 0
) {
    fun getEfficiency(): Double {
        return if (piecesPlaced > 0) linesCleared.toDouble() / piecesPlaced else 0.0
    }
    
    fun getTetrisRate(): Double {
        return if (linesCleared > 0) (tetrisCount * 4.0) / linesCleared else 0.0
    }
}

/**
 * 游戏事件
 */
@Serializable
data class GameEvent(
    val id: String = UUID.randomUUID().toString(),
    val timestamp: Long,
    val type: GameEventType,
    val data: Map<String, String> = emptyMap(),
    val playerId: String
)

/**
 * 游戏事件类型
 */
enum class GameEventType {
    GAME_START,
    GAME_END,
    GAME_PAUSE,
    GAME_RESUME,
    PIECE_PLACED,
    LINE_CLEARED,
    LEVEL_UP,
    SCORE_UPDATE,
    HOLD_USED,
    TETRIS_ACHIEVED,
    COMBO_ACHIEVED,
    PERFECT_CLEAR,
    T_SPIN,
    ERROR_OCCURRED
}

/**
 * 测试场景配置
 */
@Serializable
data class TestScenario(
    val name: String,
    val description: String,
    val users: List<User>,
    val sessions: List<GameSession>,
    val expectedOutcomes: List<String>,
    val performanceThresholds: Map<String, Double> = emptyMap(),
    val tags: List<String> = emptyList()
)

/**
 * 测试结果
 */
@Serializable
data class TestResult(
    val scenarioName: String,
    val success: Boolean,
    val executionTime: Long, // 毫秒
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList(),
    val metrics: Map<String, Double> = emptyMap(),
    val timestamp: Long = System.currentTimeMillis()
) {
    fun hasErrors(): Boolean = errors.isNotEmpty()
    
    fun hasWarnings(): Boolean = warnings.isNotEmpty()
    
    fun getOverallScore(): Double {
        return if (success && !hasErrors()) {
            val warningPenalty = warnings.size * 0.1
            maxOf(0.0, 1.0 - warningPenalty)
        } else 0.0
    }
}
