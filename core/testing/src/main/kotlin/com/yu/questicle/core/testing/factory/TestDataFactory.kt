package com.yu.questicle.core.testing.factory

import com.yu.questicle.core.domain.model.*
import com.yu.questicle.core.domain.model.tetris.*
import java.util.*

/**
 * 统一测试数据工厂
 * 
 * 提供所有核心实体的测试数据创建方法，确保测试数据的一致性和可维护性。
 * 基于Builder模式和工厂模式，支持灵活的测试数据定制。
 * 
 * <AUTHOR> Team
 * @version 2.0 (JUnit 5 + 2025 最佳实践)
 */
object TestDataFactory {
    
    /**
     * 测试常量
     */
    object Constants {
        const val TEST_PLAYER_ID = "test_player_123"
        const val TEST_EMAIL = "<EMAIL>"
        const val TEST_USERNAME = "testuser"
        const val TEST_DISPLAY_NAME = "Test User"
        const val TEST_SCORE = 1000
        const val TEST_LEVEL = 5
        const val TEST_LINES = 50
    }
    
    // ========================================
    // Tetris 游戏相关测试数据
    // ========================================
    
    /**
     * 创建Tetris方块测试数据
     */
    fun createTetrisPiece(
        type: TetrisPieceType = TetrisPieceType.I,
        x: Int = 4,
        y: Int = 0,
        rotation: Int = 0
    ): TetrisPiece = TetrisPiece(
        type = type,
        x = x,
        y = y,
        rotation = rotation
    )
    
    /**
     * 创建Tetris游戏状态测试数据
     */
    fun createTetrisGameState(
        id: String = UUID.randomUUID().toString(),
        status: TetrisStatus = TetrisStatus.READY,
        score: Int = 0,
        level: Int = 1,
        lines: Int = 0,
        currentPiece: TetrisPiece? = null,
        nextPiece: TetrisPiece? = createTetrisPiece(TetrisPieceType.T),
        holdPiece: TetrisPiece? = null,
        board: TetrisBoard = TetrisBoard.empty(),
        canHold: Boolean = true,
        lastDropTime: Long = System.currentTimeMillis()
    ): TetrisGameState = TetrisGameState(
        id = id,
        status = status,
        score = score,
        level = level,
        lines = lines,
        currentPiece = currentPiece,
        nextPiece = nextPiece,
        holdPiece = holdPiece,
        board = board,
        canHold = canHold,
        lastDropTime = lastDropTime
    )
    
    /**
     * 创建Tetris统计数据
     */
    fun createTetrisStatistics(
        piecesPlaced: Int = 100,
        linesCleared: Int = 50,
        singles: Int = 20,
        doubles: Int = 15,
        triples: Int = 10,
        tetrises: Int = 5,
        maxCombo: Int = 3,
        totalDrops: Int = 100,
        totalRotations: Int = 50,
        totalMoves: Int = 200,
        pieceStats: Map<TetrisPieceType, Int> = emptyMap(),
        gameTime: Long = 3600000L, // 1小时
        piecesPerMinute: Double = 20.0,
        linesPerMinute: Double = 10.0,
        efficiency: Double = 0.5,
        perfectClears: Int = 1,
        tSpins: Int = 3,
        tSpinSingles: Int = 1,
        tSpinDoubles: Int = 1,
        tSpinTriples: Int = 1,
        holdUsed: Int = 10,
        softDrops: Int = 30,
        hardDrops: Int = 20,
        maxLevel: Int = 5,
        averageHeight: Double = 5.0,
        maxHeight: Int = 10,
        droughtLength: Int = 2,
        maxDroughtLength: Int = 5,
        backToBackCount: Int = 0,
        maxBackToBack: Int = 2
    ): GameStatistics = GameStatistics(
        piecesPlaced = piecesPlaced,
        linesCleared = linesCleared,
        singles = singles,
        doubles = doubles,
        triples = triples,
        tetrises = tetrises,
        maxCombo = maxCombo,
        pieceStats = pieceStats,
        gameTime = gameTime,
        piecesPerMinute = piecesPerMinute,
        linesPerMinute = linesPerMinute,
        efficiency = efficiency,
        perfectClears = perfectClears,
        tSpins = tSpins,
        tSpinSingles = tSpinSingles,
        tSpinDoubles = tSpinDoubles,
        tSpinTriples = tSpinTriples,
        backToBackCount = backToBackCount,
        maxBackToBack = maxBackToBack
    )
    
    // ========================================
    // 用户相关测试数据
    // ========================================
    
    /**
     * 创建用户测试数据
     */
    fun createUser(
        id: String = Constants.TEST_PLAYER_ID,
        username: String = Constants.TEST_USERNAME,
        email: String? = Constants.TEST_EMAIL,
        displayName: String = Constants.TEST_DISPLAY_NAME,
        isActive: Boolean = true,
        createdAt: Long = System.currentTimeMillis() - 86400000L, // 1天前
        lastLoginAt: Long = System.currentTimeMillis(),
        avatarUrl: String? = null
    ): User = User(
        id = id,
        username = username,
        email = email,
        displayName = displayName,
        isActive = isActive,
        createdAt = createdAt,
        lastLoginAt = lastLoginAt,
        avatarUrl = avatarUrl
    )
    
    /**
     * 创建游客用户
     */
    fun createGuestUser(
        id: String = "guest_${UUID.randomUUID()}",
        username: String = "Guest_${System.currentTimeMillis() % 10000}"
    ): User = createUser(
        id = id,
        username = username,
        email = null,
        displayName = "游客用户"
    )
    
    /**
     * 创建用户偏好设置测试数据
     */
    fun createUserPreferences(
        theme: Theme = Theme.SYSTEM,
        language: String = "zh-CN",
        soundEnabled: Boolean = true,
        musicEnabled: Boolean = true,
        vibrationEnabled: Boolean = true,
        masterVolume: Float = 0.7f,
        soundEffectsVolume: Float = 1.0f,
        musicVolume: Float = 0.5f,
        notificationsEnabled: Boolean = true,
        autoSaveEnabled: Boolean = true,
        showHintsEnabled: Boolean = true,
        animationSpeed: Float = 0.5f,
        difficulty: GameDifficulty = GameDifficulty.MEDIUM
    ): UserPreferences = UserPreferences(
        theme = theme,
        language = language,
        soundEnabled = soundEnabled,
        musicEnabled = musicEnabled,
        vibrationEnabled = vibrationEnabled,
        masterVolume = masterVolume,
        soundEffectsVolume = soundEffectsVolume,
        musicVolume = musicVolume,
        notificationsEnabled = notificationsEnabled,
        autoSaveEnabled = autoSaveEnabled,
        showHintsEnabled = showHintsEnabled,
        animationSpeed = animationSpeed,
        difficulty = difficulty
    )
    
    // ========================================
    // 游戏记录相关测试数据
    // ========================================
    
    /**
     * 创建游戏记录测试数据
     */
    fun createGame(
        id: String = UUID.randomUUID().toString(),
        type: GameType = GameType.TETRIS,
        status: GameStatus = GameStatus.COMPLETED,
        playerId: String = Constants.TEST_PLAYER_ID,
        score: Int = Constants.TEST_SCORE,
        level: Int = Constants.TEST_LEVEL,
        startTime: Long = System.currentTimeMillis() / 1000 - 3600,
        endTime: Long? = System.currentTimeMillis() / 1000,
        duration: Long = 3600,
        metadata: Map<String, String> = mapOf(
            "lines" to Constants.TEST_LINES.toString(),
            "pieces" to "150"
        )
    ): Game = Game(
        id = id,
        type = type,
        status = status,
        playerId = playerId,
        score = score,
        level = level,
        startTime = startTime,
        endTime = endTime,
        duration = duration,
        metadata = metadata
    )
    
    /**
     * 创建进行中的游戏记录
     */
    fun createActiveGame(
        playerId: String = Constants.TEST_PLAYER_ID,
        currentScore: Int = 500
    ): Game = createGame(
        status = GameStatus.PLAYING,
        playerId = playerId,
        score = currentScore,
        endTime = null,
        duration = 0L
    )
    
    // ========================================
    // 成就和统计相关测试数据
    // ========================================
    
    /**
     * 创建成就测试数据
     */
    fun createAchievement(
        id: String = "achievement_${UUID.randomUUID()}",
        title: String = "Test Achievement",
        description: String = "Test achievement description",
        category: AchievementCategory = AchievementCategory.GAMEPLAY,
        type: AchievementType = AchievementType.SINGLE,
        requirement: AchievementRequirement = AchievementRequirement.ScoreRequirement(10000),
        reward: AchievementReward? = null,
        isHidden: Boolean = false,
        sortOrder: Int = 0,
        iconUrl: String? = null
    ): Achievement = Achievement(
        id = id,
        title = title,
        description = description,
        category = category,
        type = type,
        requirement = requirement,
        reward = reward,
        isHidden = isHidden,
        sortOrder = sortOrder,
        iconUrl = iconUrl
    )
    
    /**
     * 创建用户成就进度
     */
    fun createUserAchievement(
        userId: String = Constants.TEST_PLAYER_ID,
        achievementId: String = "test_achievement",
        isUnlocked: Boolean = false,
        progress: Float = 0.5f,
        currentValue: Long = 5000L,
        unlockedAt: Long? = null
    ): UserAchievement = UserAchievement(
        userId = userId,
        achievementId = achievementId,
        isUnlocked = isUnlocked,
        progress = progress,
        currentValue = currentValue,
        unlockedAt = unlockedAt
    )
    
    // ========================================
    // Tetris特定游戏数据
    // ========================================
    
    /**
     * 创建满行的游戏板（用于测试行消除）
     */
    fun createBoardWithFullLines(lineCount: Int = 2): TetrisBoard {
        val board = TetrisBoard.empty()
        // 在底部创建满行
        val newCells = board.cells.mapIndexed { rowIndex, row ->
            if (rowIndex >= TetrisBoard.STANDARD_HEIGHT - lineCount) {
                // 填充底部行
                row.mapIndexed { _, _ -> TetrisCellType.I_PIECE }
            } else {
                row
            }
        }
        return board.copy(cells = newCells)
    }
    
    /**
     * 创建即将结束的游戏状态（顶部有方块）
     */
    fun createNearGameOverState(): TetrisGameState {
        val board = TetrisBoard.empty()
        // 在顶部添加一些方块
        val newCells = board.cells.mapIndexed { rowIndex, row ->
            if (rowIndex < 3) {
                // 填充顶部行的中间部分
                row.mapIndexed { colIndex, cell ->
                    if (colIndex in 2 until 8) TetrisCellType.I_PIECE else cell
                }
            } else {
                row
            }
        }
        val nearFullBoard = board.copy(cells = newCells)
        
        return createTetrisGameState(
            status = TetrisStatus.PLAYING,
            board = nearFullBoard,
            currentPiece = createTetrisPiece(y = 0), // 在顶部的方块
            score = 5000,
            level = 3,
            lines = 25
        )
    }
    
    /**
     * 创建标准游戏中状态
     */
    fun createMidGameState(): TetrisGameState {
        return createTetrisGameState(
            status = TetrisStatus.PLAYING,
            currentPiece = createTetrisPiece(TetrisPieceType.T, x = 4, y = 10),
            nextPiece = createTetrisPiece(TetrisPieceType.L),
            score = 2500,
            level = 2,
            lines = 15
        )
    }
    
    // ========================================
    // 批量数据创建方法
    // ========================================
    
    /**
     * 创建多个用户的游戏记录（用于排行榜测试）
     */
    fun createLeaderboardGames(count: Int = 10): List<Game> {
        return (1..count).map { index ->
            createGame(
                playerId = "player_$index",
                score = 1000 * index,
                level = index / 2 + 1
            )
        }
    }
    
    /**
     * 创建用户的游戏历史记录
     */
    fun createUserGameHistory(
        playerId: String = Constants.TEST_PLAYER_ID,
        gameCount: Int = 5
    ): List<Game> {
        return (1..gameCount).map { index ->
            createGame(
                playerId = playerId,
                score = 800 + index * 200,
                startTime = System.currentTimeMillis() / 1000 - (gameCount - index) * 3600
            )
        }
    }
    
    // ========================================
    // 构建器模式支持
    // ========================================
    
    /**
     * TetrisGameState构建器
     */
    class TetrisGameStateBuilder {
        private var id = UUID.randomUUID().toString()
        private var status = TetrisStatus.READY
        private var score = 0
        private var level = 1
        private var lines = 0
        private var currentPiece: TetrisPiece? = null
        private var nextPiece: TetrisPiece? = createTetrisPiece(TetrisPieceType.T)
        private var holdPiece: TetrisPiece? = null
        private var board = TetrisBoard.empty()
        private var canHold = true
        private var lastDropTime = System.currentTimeMillis()
        
        fun withId(id: String) = apply { this.id = id }
        fun withStatus(status: TetrisStatus) = apply { this.status = status }
        fun withScore(score: Int) = apply { this.score = score }
        fun withLevel(level: Int) = apply { this.level = level }
        fun withLines(lines: Int) = apply { this.lines = lines }
        fun withCurrentPiece(piece: TetrisPiece?) = apply { this.currentPiece = piece }
        fun withNextPiece(piece: TetrisPiece?) = apply { this.nextPiece = piece }
        fun withHoldPiece(piece: TetrisPiece?) = apply { this.holdPiece = piece }
        fun withBoard(board: TetrisBoard) = apply { this.board = board }
        fun withCanHold(canHold: Boolean) = apply { this.canHold = canHold }
        fun withLastDropTime(time: Long) = apply { this.lastDropTime = time }
        
        fun build(): TetrisGameState = createTetrisGameState(
            id = id,
            status = status,
            score = score,
            level = level,
            lines = lines,
            currentPiece = currentPiece,
            nextPiece = nextPiece,
            holdPiece = holdPiece,
            board = board,
            canHold = canHold,
            lastDropTime = lastDropTime
        )
    }
    
    /**
     * User构建器
     */
    class UserBuilder {
        private var id = Constants.TEST_PLAYER_ID
        private var username = Constants.TEST_USERNAME
        private var email: String? = Constants.TEST_EMAIL
        private var displayName = Constants.TEST_DISPLAY_NAME
        private var isActive = true
        private var createdAt = System.currentTimeMillis() - 86400000L
        private var lastLoginAt = System.currentTimeMillis()
        private var avatarUrl: String? = null
        
        fun withId(id: String) = apply { this.id = id }
        fun withUsername(username: String) = apply { this.username = username }
        fun withEmail(email: String?) = apply { this.email = email }
        fun withDisplayName(displayName: String) = apply { this.displayName = displayName }
        fun asGuest() = apply { this.username = "Guest_${System.currentTimeMillis()}" }
        fun asRegularUser() = apply { this.username = Constants.TEST_USERNAME }
        fun withActive(active: Boolean) = apply { this.isActive = active }
        fun withCreatedAt(createdAt: Long) = apply { this.createdAt = createdAt }
        fun withLastLoginAt(lastLoginAt: Long) = apply { this.lastLoginAt = lastLoginAt }
        fun withAvatarUrl(avatarUrl: String?) = apply { this.avatarUrl = avatarUrl }
        
        fun build(): User = createUser(
            id = id,
            username = username,
            email = email,
            displayName = displayName,
            isActive = isActive,
            createdAt = createdAt,
            lastLoginAt = lastLoginAt,
            avatarUrl = avatarUrl
        )
    }
    
    // ========================================
    // 便捷方法
    // ========================================
    
    /**
     * 快速创建游戏状态构建器
     */
    fun gameStateBuilder() = TetrisGameStateBuilder()
    
    /**
     * 快速创建用户构建器
     */
    fun userBuilder() = UserBuilder()
} 