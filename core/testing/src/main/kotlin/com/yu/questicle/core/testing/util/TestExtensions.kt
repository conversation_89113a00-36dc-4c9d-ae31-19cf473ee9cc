package com.yu.questicle.core.testing.util

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * 测试扩展函数集合
 * 提供常用的测试工具方法
 */

/**
 * 在测试作用域中运行协程测试
 */
fun runTestWithTimeout(
    timeout: Duration = 10.seconds,
    testBody: suspend TestScope.() -> Unit
) = runTest(timeout = timeout) {
    testBody()
}

/**
 * 等待Flow发射第一个值，带超时
 */
suspend fun <T> Flow<T>.awaitFirst(timeout: Duration = 5.seconds): T {
    return withTimeout(timeout) {
        first()
    }
}

/**
 * 验证Flow是否发射了预期的值序列
 */
suspend fun <T> Flow<T>.collectValues(count: Int): List<T> {
    val values = mutableListOf<T>()
    var collected = 0
    
    collect { value ->
        values.add(value)
        collected++
        if (collected >= count) {
            return@collect
        }
    }
    
    return values
}

/**
 * 创建测试用的协程作用域
 */
fun createTestScope() = TestScope()

/**
 * 验证异常类型和消息
 */
inline fun <reified T : Throwable> assertThrows(
    expectedMessage: String? = null,
    block: () -> Unit
): T {
    try {
        block()
        throw AssertionError("Expected ${T::class.simpleName} to be thrown")
    } catch (e: Throwable) {
        if (e !is T) {
            throw AssertionError("Expected ${T::class.simpleName}, but got ${e::class.simpleName}")
        }
        if (expectedMessage != null && !e.message.orEmpty().contains(expectedMessage)) {
            throw AssertionError("Expected message to contain '$expectedMessage', but was '${e.message}'")
        }
        return e
    }
}

/**
 * 验证挂起函数抛出异常
 */
suspend inline fun <reified T : Throwable> assertThrowsSuspend(
    expectedMessage: String? = null,
    crossinline block: suspend () -> Unit
): T {
    try {
        block()
        throw AssertionError("Expected ${T::class.simpleName} to be thrown")
    } catch (e: Throwable) {
        if (e !is T) {
            throw AssertionError("Expected ${T::class.simpleName}, but got ${e::class.simpleName}")
        }
        if (expectedMessage != null && !e.message.orEmpty().contains(expectedMessage)) {
            throw AssertionError("Expected message to contain '$expectedMessage', but was '${e.message}'")
        }
        return e
    }
}

/**
 * 测试数据验证扩展
 */
fun <T> T.shouldNotBeNull(): T {
    if (this == null) {
        throw AssertionError("Expected value to not be null")
    }
    return this
}

fun <T> T?.shouldBeNull(): T? {
    if (this != null) {
        throw AssertionError("Expected value to be null, but was $this")
    }
    return this
}

fun <T> T.shouldBe(expected: T): T {
    if (this != expected) {
        throw AssertionError("Expected $expected, but was $this")
    }
    return this
}

fun <T> T.shouldNotBe(unexpected: T): T {
    if (this == unexpected) {
        throw AssertionError("Expected value to not be $unexpected")
    }
    return this
}

fun <T : Collection<*>> T.shouldHaveSize(expectedSize: Int): T {
    if (this.size != expectedSize) {
        throw AssertionError("Expected collection to have size $expectedSize, but was ${this.size}")
    }
    return this
}

fun <T : Collection<*>> T.shouldBeEmpty(): T {
    if (this.isNotEmpty()) {
        throw AssertionError("Expected collection to be empty, but had ${this.size} elements")
    }
    return this
}

fun <T : Collection<*>> T.shouldNotBeEmpty(): T {
    if (this.isEmpty()) {
        throw AssertionError("Expected collection to not be empty")
    }
    return this
}

fun <T> Collection<T>.shouldContain(element: T): Collection<T> {
    if (!this.contains(element)) {
        throw AssertionError("Expected collection to contain $element, but it didn't")
    }
    return this
}

fun <T> Collection<T>.shouldNotContain(element: T): Collection<T> {
    if (this.contains(element)) {
        throw AssertionError("Expected collection to not contain $element, but it did")
    }
    return this
}

/**
 * 字符串验证扩展
 */
fun String.shouldContain(substring: String): String {
    if (!this.contains(substring)) {
        throw AssertionError("Expected string to contain '$substring', but was '$this'")
    }
    return this
}

fun String.shouldNotContain(substring: String): String {
    if (this.contains(substring)) {
        throw AssertionError("Expected string to not contain '$substring', but was '$this'")
    }
    return this
}

fun String.shouldStartWith(prefix: String): String {
    if (!this.startsWith(prefix)) {
        throw AssertionError("Expected string to start with '$prefix', but was '$this'")
    }
    return this
}

fun String.shouldEndWith(suffix: String): String {
    if (!this.endsWith(suffix)) {
        throw AssertionError("Expected string to end with '$suffix', but was '$this'")
    }
    return this
}

/**
 * 数值验证扩展
 */
fun <T : Comparable<T>> T.shouldBeGreaterThan(other: T): T {
    if (this <= other) {
        throw AssertionError("Expected $this to be greater than $other")
    }
    return this
}

fun <T : Comparable<T>> T.shouldBeLessThan(other: T): T {
    if (this >= other) {
        throw AssertionError("Expected $this to be less than $other")
    }
    return this
}

fun <T : Comparable<T>> T.shouldBeGreaterThanOrEqual(other: T): T {
    if (this < other) {
        throw AssertionError("Expected $this to be greater than or equal to $other")
    }
    return this
}

fun <T : Comparable<T>> T.shouldBeLessThanOrEqual(other: T): T {
    if (this > other) {
        throw AssertionError("Expected $this to be less than or equal to $other")
    }
    return this
}

fun <T : Number> T.shouldBeCloseTo(expected: T, tolerance: Double): T {
    val actualDouble = this.toDouble()
    val expectedDouble = expected.toDouble()
    val diff = kotlin.math.abs(actualDouble - expectedDouble)
    
    if (diff > tolerance) {
        throw AssertionError("Expected $this to be close to $expected within $tolerance, but difference was $diff")
    }
    return this
}

/**
 * 布尔验证扩展
 */
fun Boolean.shouldBeTrue(): Boolean {
    if (!this) {
        throw AssertionError("Expected value to be true, but was false")
    }
    return this
}

fun Boolean.shouldBeFalse(): Boolean {
    if (this) {
        throw AssertionError("Expected value to be false, but was true")
    }
    return this
}
