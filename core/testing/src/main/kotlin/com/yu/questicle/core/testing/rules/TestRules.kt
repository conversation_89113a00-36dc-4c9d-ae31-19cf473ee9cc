package com.yu.questicle.core.testing.rules

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*

/**
 * 统一测试规则集合
 * 
 * 提供各种测试场景下的规则和辅助方法，简化测试编写
 * 
 * <AUTHOR> Team
 * @version 2.0 (JUnit 5 + 协程测试最佳实践)
 */

/**
 * Tetris测试规则
 * 
 * 专门为Tetris游戏测试提供的便捷方法
 */
class TetrisTestRule {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest(testBody = testBody)
    
    /**
     * 运行游戏循环测试
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runGameLoopTest(
        tickCount: Int = 10,
        testBody: suspend TestScope.(tick: Int) -> Unit
    ) = kotlinx.coroutines.test.runTest {
        repeat(tickCount) { tick ->
            testBody(tick)
        }
    }
    
    /**
     * 运行性能测试
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runPerformanceTest(
        iterations: Int = 100,
        testBody: suspend TestScope.() -> Unit
    ) = kotlinx.coroutines.test.runTest {
        val startTime = System.nanoTime()
        repeat(iterations) {
            testBody()
        }
        val endTime = System.nanoTime()
        val averageTime = (endTime - startTime) / iterations / 1_000_000
        println("⏱️ 平均执行时间: ${averageTime}ms (${iterations}次迭代)")
    }
}

/**
 * 用户测试规则
 * 
 * 专门为用户相关测试提供的便捷方法
 */
class UserTestRule {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest(testBody = testBody)
    
    /**
     * 运行认证流程测试
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runAuthTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 清理认证状态
            cleanupAuthState()
            testBody()
        }
    
    private fun cleanupAuthState() {
        // 清理认证相关的状态
        System.clearProperty("user.auth.token")
        System.clearProperty("user.id")
    }
}

/**
 * 性能测试规则
 * 
 * 用于监控和验证性能指标
 */
class PerformanceTestRule {
    
    private var maxDurationMs: Long = 5000L
    private var warmupIterations: Int = 0
    
    fun withMaxDuration(durationMs: Long) = apply {
        this.maxDurationMs = durationMs
    }
    
    fun withWarmup(iterations: Int) = apply {
        this.warmupIterations = iterations
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 预热
            repeat(warmupIterations) {
                testBody()
            }
            
            // 实际测试
            val startTime = System.currentTimeMillis()
            testBody()
            val endTime = System.currentTimeMillis()
            
            val duration = endTime - startTime
            if (duration > maxDurationMs) {
                throw AssertionError(
                    "性能测试失败: 执行时间 ${duration}ms 超过限制 ${maxDurationMs}ms"
                )
            }
            
            println("✅ 性能测试通过: ${duration}ms (限制: ${maxDurationMs}ms)")
        }
}

/**
 * 内存测试规则
 * 
 * 用于监控内存使用情况
 */
class MemoryTestRule {
    
    private var maxMemoryMB: Long = 100L
    
    fun withMaxMemory(memoryMB: Long) = apply {
        this.maxMemoryMB = memoryMB
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 初始内存清理
            System.gc()
            Thread.sleep(100)
            val initialMemory = getUsedMemory()
            
            // 执行测试
            testBody()
            
            // 最终内存检查
            System.gc()
            Thread.sleep(100)
            val finalMemory = getUsedMemory()
            
            val memoryUsed = finalMemory - initialMemory
            if (memoryUsed > maxMemoryMB) {
                throw AssertionError(
                    "内存测试失败: 使用内存 ${memoryUsed}MB 超过限制 ${maxMemoryMB}MB"
                )
            }
            
            println("✅ 内存测试通过: ${memoryUsed}MB (限制: ${maxMemoryMB}MB)")
        }
    
    private fun getUsedMemory(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
    }
}

/**
 * 并发测试规则
 * 
 * 用于测试并发场景
 */
class ConcurrencyTestRule {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runConcurrentTest(
        threadCount: Int = 10,
        testBody: suspend TestScope.(threadId: Int) -> Unit
    ) = kotlinx.coroutines.test.runTest {
        val results = mutableListOf<Result<Unit>>()
        
        // 并发执行测试
        repeat(threadCount) { threadId ->
            try {
                testBody(threadId)
                results.add(Result.success(Unit))
            } catch (e: Exception) {
                results.add(Result.failure(e))
            }
        }
        
        // 检查结果
        val failures = results.filterIsInstance<Result<Unit>>().filter { it.isFailure }
        if (failures.isNotEmpty()) {
            throw AssertionError(
                "并发测试失败: ${failures.size}/${threadCount} 个线程失败"
            )
        }
        
        println("✅ 并发测试通过: ${threadCount}个线程全部成功")
    }
}

/**
 * 数据库测试规则
 * 
 * 用于数据库相关测试
 */
class DatabaseTestRule {
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runTransactionTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 开启事务
            beginTransaction()
            
            try {
                testBody()
                // 如果测试成功，回滚事务以保持测试隔离
                rollbackTransaction()
            } catch (e: Exception) {
                // 如果测试失败，回滚事务
                rollbackTransaction()
                throw e
            }
        }
    
    private fun beginTransaction() {
        // 开启数据库事务的实现
        println("🔄 开启数据库事务")
    }
    
    private fun rollbackTransaction() {
        // 回滚数据库事务的实现
        println("↩️ 回滚数据库事务")
    }
}

/**
 * API测试规则
 * 
 * 用于API相关测试
 */
class ApiTestRule {
    
    private var timeout: Long = 30000L // 30秒
    
    fun withTimeout(timeoutMs: Long) = apply {
        this.timeout = timeoutMs
    }
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runApiTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 设置API测试环境
            setupApiTestEnvironment()
            
            try {
                testBody()
            } finally {
                // 清理API测试环境
                cleanupApiTestEnvironment()
            }
        }
    
    private fun setupApiTestEnvironment() {
        // 设置API测试环境的实现
        System.setProperty("api.test.mode", "true")
        System.setProperty("api.base.url", "http://localhost:8080")
    }
    
    private fun cleanupApiTestEnvironment() {
        // 清理API测试环境的实现
        System.clearProperty("api.test.mode")
        System.clearProperty("api.base.url")
    }
}

/**
 * 综合测试规则
 * 
 * 组合多个测试规则，提供完整的测试环境
 */
class ComprehensiveTestRule {
    
    private val tetrisRule = TetrisTestRule()
    private val userRule = UserTestRule()
    private val performanceRule = PerformanceTestRule()
    private val memoryRule = MemoryTestRule()
    
    fun configureTetris() = tetrisRule
    fun configureUser() = userRule
    fun configurePerformance() = performanceRule
    fun configureMemory() = memoryRule
    
    @OptIn(ExperimentalCoroutinesApi::class)
    fun runComprehensiveTest(testBody: suspend TestScope.() -> Unit) = 
        kotlinx.coroutines.test.runTest {
            // 综合测试环境设置
            setupComprehensiveEnvironment()
            
            try {
                testBody()
            } finally {
                // 综合测试环境清理
                cleanupComprehensiveEnvironment()
            }
        }
    
    private fun setupComprehensiveEnvironment() {
        System.setProperty("test.comprehensive.mode", "true")
        System.setProperty("test.log.level", "DEBUG")
        println("🚀 综合测试环境启动")
    }
    
    private fun cleanupComprehensiveEnvironment() {
        System.clearProperty("test.comprehensive.mode")
        System.clearProperty("test.log.level")
        println("🏁 综合测试环境清理完成")
    }
}

/**
 * 便捷方法集合
 */
object TestRules {
    
    /**
     * 创建Tetris测试规则
     */
    fun tetris() = TetrisTestRule()
    
    /**
     * 创建用户测试规则
     */
    fun user() = UserTestRule()
    
    /**
     * 创建性能测试规则
     */
    fun performance() = PerformanceTestRule()
    
    /**
     * 创建内存测试规则
     */
    fun memory() = MemoryTestRule()
    
    /**
     * 创建并发测试规则
     */
    fun concurrency() = ConcurrencyTestRule()
    
    /**
     * 创建数据库测试规则
     */
    fun database() = DatabaseTestRule()
    
    /**
     * 创建API测试规则
     */
    fun api() = ApiTestRule()
    
    /**
     * 创建综合测试规则
     */
    fun comprehensive() = ComprehensiveTestRule()
} 