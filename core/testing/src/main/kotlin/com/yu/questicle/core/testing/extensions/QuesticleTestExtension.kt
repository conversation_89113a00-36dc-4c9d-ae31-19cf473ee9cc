package com.yu.questicle.core.testing.extensions

import io.mockk.clearAllMocks
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.extension.AfterEachCallback
import org.junit.jupiter.api.extension.BeforeEachCallback
import org.junit.jupiter.api.extension.Extension
import org.junit.jupiter.api.extension.ExtensionContext
import org.junit.jupiter.api.extension.ParameterContext
import org.junit.jupiter.api.extension.ParameterResolver
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler

/**
 * Questicle统一测试扩展
 * 
 * 提供统一的测试环境设置，包括：
 * - 协程测试调度器管理
 * - Mock对象清理
 * - 测试参数注入
 * - 测试环境初始化和清理
 * 
 * <AUTHOR> Team
 * @version 2.0 (JUnit 5 + 协程测试最佳实践)
 */
@ExperimentalCoroutinesApi
class QuesticleTestExtension : BeforeEachCallback, AfterEachCallback, ParameterResolver {
    
    companion object {
        private val testDispatcher = StandardTestDispatcher()
    }
    
    /**
     * 测试前设置
     */
    override fun beforeEach(context: ExtensionContext) {
        try {
            // 设置主调度器为测试调度器
            Dispatchers.setMain(testDispatcher)
            
            // 清理所有Mock对象
            clearAllMocks()
            
            // 打印测试开始信息
            val testName = context.displayName
            val className = context.testClass.map { it.simpleName }.orElse("Unknown")
            println("🚀 开始测试: $className.$testName")
        } catch (e: Exception) {
            println("⚠️ 测试设置失败: ${e.message}")
            throw e
        }
    }
    
    /**
     * 测试后清理
     */
    override fun afterEach(context: ExtensionContext) {
        try {
            // 重置主调度器
            Dispatchers.resetMain()
            
            // 打印测试结束信息
            val testName = context.displayName
            val className = context.testClass.map { it.simpleName }.orElse("Unknown")
            println("✅ 完成测试: $className.$testName")
        } catch (e: Exception) {
            println("⚠️ 测试清理失败: ${e.message}")
            // 不抛出异常，避免影响测试结果
        }
    }
    
    /**
     * 支持的参数类型
     */
    override fun supportsParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext
    ): Boolean {
        return parameterContext.parameter.type == TestDispatcher::class.java
    }
    
    /**
     * 解析参数
     */
    override fun resolveParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext
    ): Any {
        return testDispatcher
    }
}

/**
 * 性能测试扩展
 * 
 * 监控测试执行时间，确保测试性能符合要求
 */
class PerformanceTestExtension : BeforeEachCallback, AfterEachCallback {
    
    companion object {
        private const val DEFAULT_MAX_DURATION_MS = 5000L // 5秒
        private const val SLOW_TEST_THRESHOLD_MS = 1000L  // 1秒
    }
    
    private var startTime: Long = 0
    
    override fun beforeEach(context: ExtensionContext) {
        startTime = System.currentTimeMillis()
    }
    
    override fun afterEach(context: ExtensionContext) {
        val duration = System.currentTimeMillis() - startTime
        val testName = context.displayName
        val className = context.testClass.map { it.simpleName }.orElse("Unknown")
        
        // 检查性能测试注解
        val annotation = context.requiredTestMethod.getAnnotation(PerformanceTest::class.java)
        if (annotation != null) {
            val maxDuration = annotation.maxDurationMs
            if (duration > maxDuration) {
                throw AssertionError(
                    "⚠️ 性能测试失败: $className.$testName\n" +
                    "描述: ${annotation.description}\n" +
                    "执行时间: ${duration}ms 超过限制: ${maxDuration}ms"
                )
            }
        }
        
        // 标记慢测试
        if (duration > SLOW_TEST_THRESHOLD_MS) {
            println("🐌 慢测试警告: $className.$testName - ${duration}ms")
        }
        
        // 记录测试执行时间
        println("⏱️  测试耗时: $className.$testName - ${duration}ms")
    }
}

/**
 * 性能测试注解
 * 
 * 用于标记需要性能监控的测试方法
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class PerformanceTest(
    val maxDurationMs: Long = 5000L,
    val description: String = ""
)

/**
 * 内存测试扩展
 * 
 * 监控测试内存使用情况，检测内存泄漏
 */
class MemoryTestExtension : BeforeEachCallback, AfterEachCallback {
    
    companion object {
        private const val MEMORY_THRESHOLD_MB = 100L // 100MB
    }
    
    private var initialMemory: Long = 0
    
    override fun beforeEach(context: ExtensionContext) {
        // 强制垃圾回收
        System.gc()
        Thread.sleep(100)
        
        initialMemory = getUsedMemory()
    }
    
    override fun afterEach(context: ExtensionContext) {
        // 强制垃圾回收
        System.gc()
        Thread.sleep(100)
        
        val finalMemory = getUsedMemory()
        val memoryDiff = finalMemory - initialMemory
        val testName = context.displayName
        val className = context.testClass.map { it.simpleName }.orElse("Unknown")
        
        if (memoryDiff > MEMORY_THRESHOLD_MB) {
            println("⚠️ 内存使用警告: $className.$testName - 增加了 ${memoryDiff}MB")
        }
        
        println("🧠 内存使用: $className.$testName - ${memoryDiff}MB")
    }
    
    private fun getUsedMemory(): Long {
        val runtime = Runtime.getRuntime()
        return (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
    }
}

/**
 * 异常测试扩展
 * 
 * 捕获和记录测试中的异常信息
 */
class ExceptionTestExtension : TestExecutionExceptionHandler {
    
    override fun handleTestExecutionException(
        context: ExtensionContext,
        throwable: Throwable
    ) {
        val testName = context.displayName
        val className = context.testClass.map { it.simpleName }.orElse("Unknown")
        
        println("❌ 测试异常: $className.$testName")
        println("   异常类型: ${throwable.javaClass.simpleName}")
        println("   异常消息: ${throwable.message}")
        
        // 记录异常堆栈的前5行
        val stackTrace = throwable.stackTrace.take(5).joinToString("\n   ") { "at $it" }
        println("   堆栈跟踪:\n   $stackTrace")
        
        // 重新抛出异常
        throw throwable
    }
}

/**
 * 测试数据清理扩展
 * 
 * 确保测试数据不会在测试间产生干扰
 */
class TestDataCleanupExtension : BeforeEachCallback, AfterEachCallback {
    
    override fun beforeEach(context: ExtensionContext) {
        // 清理测试数据
        clearTestData()
    }
    
    override fun afterEach(context: ExtensionContext) {
        // 清理测试数据
        clearTestData()
    }
    
    private fun clearTestData() {
        // 清理Mock对象
        clearAllMocks()
        
        // 清理静态变量等
        System.setProperty("test.mode", "true")
    }
}

/**
 * 组合测试扩展
 * 
 * 将多个扩展组合在一起，提供完整的测试环境
 */
@ExperimentalCoroutinesApi
class CompositeTestExtension : Extension, BeforeEachCallback, AfterEachCallback, ParameterResolver, TestExecutionExceptionHandler {
    
    private val extensions = listOf(
        QuesticleTestExtension(),
        PerformanceTestExtension(),
        MemoryTestExtension(),
        ExceptionTestExtension(),
        TestDataCleanupExtension()
    )
    
    override fun beforeEach(context: ExtensionContext) {
        extensions.filterIsInstance<BeforeEachCallback>().forEach { extension ->
            extension.beforeEach(context)
        }
    }
    
    override fun afterEach(context: ExtensionContext) {
        extensions.filterIsInstance<AfterEachCallback>().forEach { extension ->
            extension.afterEach(context)
        }
    }
    
    override fun supportsParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext
    ): Boolean {
        return extensions.filterIsInstance<ParameterResolver>().any { extension ->
            extension.supportsParameter(parameterContext, extensionContext)
        }
    }
    
    override fun resolveParameter(
        parameterContext: ParameterContext,
        extensionContext: ExtensionContext
    ): Any {
        return extensions.filterIsInstance<ParameterResolver>().first { extension ->
            extension.supportsParameter(parameterContext, extensionContext)
        }.resolveParameter(parameterContext, extensionContext)
    }
    
    override fun handleTestExecutionException(
        context: ExtensionContext,
        throwable: Throwable
    ) {
        extensions.filterIsInstance<TestExecutionExceptionHandler>().forEach { extension ->
            extension.handleTestExecutionException(context, throwable)
        }
    }
}
