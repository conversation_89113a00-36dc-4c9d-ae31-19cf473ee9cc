package com.yu.questicle.core.testing

import kotlin.random.Random

/**
 * 测试数据生成器
 * 用于生成各种测试场景的模拟数据
 */
object TestDataGenerator {
    
    /**
     * 游戏相关测试数据
     */
    object Game {
        fun randomScore(min: Int = 0, max: Int = 999999) = Random.nextInt(min, max)
        
        fun randomLevel(min: Int = 1, max: Int = 20) = Random.nextInt(min, max)
        
        fun randomLines(min: Int = 0, max: Int = 1000) = Random.nextInt(min, max)
        
        fun randomGameTime(min: Long = 0L, max: Long = 3600000L) = Random.nextLong(min, max)
        
        fun randomPlayerName() = listOf(
            "TestPlayer", "GameMaster", "TetrisKing", "BlockMover", "LineClearer"
        ).random()
    }
    
    /**
     * 俄罗斯方块相关测试数据
     */
    object Tetris {
        enum class BlockType { I, O, T, S, Z, J, L }
        
        fun randomBlockType() = BlockType.values().random()
        
        fun randomPosition(maxX: Int = 10, maxY: Int = 20) = Pair(
            Random.nextInt(0, maxX),
            Random.nextInt(0, maxY)
        )
        
        fun randomRotation() = Random.nextInt(0, 4)
        
        fun randomBoard(width: Int = 10, height: Int = 20): Array<IntArray> {
            return Array(height) { IntArray(width) { Random.nextInt(0, 2) } }
        }
        
        fun emptyBoard(width: Int = 10, height: Int = 20): Array<IntArray> {
            return Array(height) { IntArray(width) { 0 } }
        }
        
        fun fullLine(width: Int = 10): IntArray {
            return IntArray(width) { 1 }
        }
    }
    
    /**
     * 用户相关测试数据
     */
    object User {
        fun randomUserId() = "user_${Random.nextInt(1000, 9999)}"
        
        fun randomEmail() = "${randomString(8)}@test.com"
        
        fun randomUsername() = "user${Random.nextInt(100, 999)}"
        
        private fun randomString(length: Int): String {
            val chars = "abcdefghijklmnopqrstuvwxyz0123456789"
            return (1..length)
                .map { chars.random() }
                .joinToString("")
        }
    }
    
    /**
     * 时间相关测试数据
     */
    object Time {
        fun currentTimeMillis() = System.currentTimeMillis()
        
        fun randomTimestamp(
            start: Long = 1640995200000L, // 2022-01-01
            end: Long = System.currentTimeMillis()
        ) = Random.nextLong(start, end)
        
        fun randomDuration(min: Long = 1000L, max: Long = 3600000L) = Random.nextLong(min, max)
    }
    
    /**
     * 网络相关测试数据
     */
    object Network {
        fun randomLatency(min: Int = 10, max: Int = 500) = Random.nextInt(min, max)
        
        fun randomHttpStatusCode() = listOf(200, 201, 400, 401, 403, 404, 500, 502, 503).random()
        
        fun randomApiResponse(success: Boolean = true) = if (success) {
            """{"status": "success", "data": {"id": ${Random.nextInt(1, 1000)}}}"""
        } else {
            """{"status": "error", "message": "Test error"}"""
        }
    }
}