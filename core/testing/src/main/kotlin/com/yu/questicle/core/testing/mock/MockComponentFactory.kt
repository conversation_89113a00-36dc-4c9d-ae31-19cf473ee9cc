package com.yu.questicle.core.testing.mock

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.engine.TetrisEngine
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.factory.TestDataFactory
import kotlinx.coroutines.delay

/**
 * Mock Component Factory for Tetris Testing Framework
 * 
 * Provides mock implementations of core Tetris engine components for testing.
 * These mocks have realistic behavior to support comprehensive testing scenarios.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
interface MockComponentFactory {
    
    /**
     * Create mock TetrisEngine with realistic behavior
     */
    fun createMockTetrisEngine(): TetrisEngine
    
    /**
     * Create mock collision detector
     */
    fun createMockCollisionDetector(): TetrisCollisionDetector
    
    /**
     * Create mock game logic processor
     */
    fun createMockGameLogicProcessor(): TetrisGameLogicProcessor
    
    /**
     * Create mock statistics calculator
     */
    fun createMockStatisticsCalculator(): TetrisStatisticsCalculator
    
    /**
     * Create mock performance manager
     */
    fun createMockPerformanceManager(): TetrisPerformanceManager
    
    /**
     * Create mock cache manager
     */
    fun createMockCacheManager(): TetrisCacheManager
}

/**
 * Default implementation of MockComponentFactory
 */
class DefaultMockComponentFactory : MockComponentFactory {
    
    override fun createMockTetrisEngine(): TetrisEngine {
        return MockTetrisEngine()
    }
    
    override fun createMockCollisionDetector(): TetrisCollisionDetector {
        return MockTetrisCollisionDetector()
    }
    
    override fun createMockGameLogicProcessor(): TetrisGameLogicProcessor {
        return MockTetrisGameLogicProcessor()
    }
    
    override fun createMockStatisticsCalculator(): TetrisStatisticsCalculator {
        return MockTetrisStatisticsCalculator()
    }
    
    override fun createMockPerformanceManager(): TetrisPerformanceManager {
        return MockTetrisPerformanceManager()
    }
    
    override fun createMockCacheManager(): TetrisCacheManager {
        return MockTetrisCacheManager()
    }
}

/**
 * Advanced implementation of MockComponentFactory with sophisticated mock behavior
 */
class AdvancedMockComponentFactory : MockComponentFactory {
    
    override fun createMockTetrisEngine(): TetrisEngine {
        return MockTetrisEngine()
    }
    
    override fun createMockCollisionDetector(): TetrisCollisionDetector {
        return MockTetrisCollisionDetector()
    }
    
    override fun createMockGameLogicProcessor(): TetrisGameLogicProcessor {
        return MockTetrisGameLogicProcessor()
    }
    
    override fun createMockStatisticsCalculator(): TetrisStatisticsCalculator {
        return AdvancedMockTetrisStatisticsCalculator()
    }
    
    override fun createMockPerformanceManager(): TetrisPerformanceManager {
        return AdvancedMockTetrisPerformanceManager()
    }
    
    override fun createMockCacheManager(): TetrisCacheManager {
        return AdvancedMockTetrisCacheManager()
    }
}

/**
 * Mock TetrisEngine with realistic behavior
 */
class MockTetrisEngine : TetrisEngine {
    
    private val pieceSequence = listOf(
        TetrisPieceType.I, TetrisPieceType.O, TetrisPieceType.T,
        TetrisPieceType.S, TetrisPieceType.Z, TetrisPieceType.J, TetrisPieceType.L
    )
    private var pieceIndex = 0
    private var currentGameState: TetrisGameState? = null
    
    override suspend fun initializeGame(playerId: String): Result<TetrisGameState> {
        val initialState = TestDataFactory.createTetrisGameState(
            status = TetrisStatus.READY,
            currentPiece = generateNextPiece(),
            nextPiece = generateNextPiece()
        )
        currentGameState = initialState
        return Result.Success(initialState)
    }
    
    override suspend fun startGame(gameState: TetrisGameState): Result<TetrisGameState> {
        val startedState = gameState.copy(status = TetrisStatus.PLAYING)
        currentGameState = startedState
        return Result.Success(startedState)
    }
    
    override suspend fun processAction(action: TetrisAction, gameState: TetrisGameState): Result<TetrisGameState> {
        delay(10) // Simulate processing time
        
        return when (action) {
            is TetrisAction.Move -> movePiece(action.direction, gameState)
            is TetrisAction.Rotate -> rotatePiece(true, gameState)
            is TetrisAction.Drop -> dropPiece(action.hard, gameState)
            is TetrisAction.Hold -> holdPiece(gameState)
            is TetrisAction.Pause -> Result.Success(gameState.copy(status = TetrisStatus.PAUSED))
        }
    }
    
    override suspend fun pauseGame(gameState: TetrisGameState): Result<TetrisGameState> {
        val pausedState = gameState.copy(status = TetrisStatus.PAUSED)
        currentGameState = pausedState
        return Result.Success(pausedState)
    }
    
    override suspend fun resumeGame(gameState: TetrisGameState): Result<TetrisGameState> {
        val resumedState = gameState.copy(status = TetrisStatus.PLAYING)
        currentGameState = resumedState
        return Result.Success(resumedState)
    }
    
    override suspend fun endGame(gameState: TetrisGameState): Result<com.yu.questicle.core.domain.model.Game> {
        val endedState = gameState.copy(status = TetrisStatus.GAME_OVER)
        currentGameState = endedState
        
        val game = TestDataFactory.createGame(
            score = gameState.score,
            level = gameState.level,
            metadata = mapOf(
                "lines" to gameState.lines.toString(),
                "pieces" to gameState.statistics.piecesPlaced.toString()
            )
        )
        return Result.Success(game)
    }
    
    override fun observeGameState(): kotlinx.coroutines.flow.Flow<TetrisGameState> {
        return kotlinx.coroutines.flow.flowOf(currentGameState ?: TestDataFactory.createTetrisGameState())
    }
    
    override fun isValidAction(action: TetrisAction, currentState: TetrisGameState): Boolean {
        return when (action) {
            is TetrisAction.Move -> currentState.currentPiece != null
            is TetrisAction.Rotate -> currentState.currentPiece != null
            is TetrisAction.Drop -> currentState.currentPiece != null
            is TetrisAction.Hold -> currentState.canHold && currentState.currentPiece != null
            is TetrisAction.Pause -> currentState.status == TetrisStatus.PLAYING
        }
    }
    
    override fun calculateScore(gameState: TetrisGameState): Int {
        return gameState.score
    }
    
    override fun isGameOver(gameState: TetrisGameState): Boolean {
        return gameState.status == TetrisStatus.GAME_OVER
    }
    
    override fun getGameStatistics(gameState: TetrisGameState): Map<String, Any> {
        return mapOf(
            "score" to gameState.score,
            "level" to gameState.level,
            "lines" to gameState.lines,
            "piecesPlaced" to gameState.statistics.piecesPlaced,
            "linesCleared" to gameState.statistics.linesCleared,
            "gameTime" to gameState.statistics.gameTime
        )
    }
    
    override suspend fun saveGameState(gameState: TetrisGameState): Result<Unit> {
        currentGameState = gameState
        return Result.Success(Unit)
    }
    
    override suspend fun loadGameState(gameId: String): Result<TetrisGameState> {
        return Result.Success(currentGameState ?: TestDataFactory.createTetrisGameState())
    }
    
    override fun generateNextPiece(): TetrisPiece {
        val piece = TestDataFactory.createTetrisPiece(
            type = pieceSequence[pieceIndex % pieceSequence.size],
            x = 4,
            y = 0
        )
        pieceIndex++
        return piece
    }
    
    override suspend fun movePiece(direction: Direction, gameState: TetrisGameState): Result<TetrisGameState> {
        val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)
        
        val (dx, dy) = when (direction) {
            Direction.LEFT -> -1 to 0
            Direction.RIGHT -> 1 to 0
            Direction.DOWN -> 0 to 1
        }
        
        val newPiece = currentPiece.move(dx, dy)
        
        // Simple boundary check
        if (isValidPosition(newPiece, gameState)) {
            return Result.Success(gameState.copy(currentPiece = newPiece))
        }
        
        return Result.Success(gameState)
    }
    
    override suspend fun rotatePiece(clockwise: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)
        val rotatedPiece = currentPiece.rotate()
        
        if (isValidPosition(rotatedPiece, gameState)) {
            return Result.Success(gameState.copy(currentPiece = rotatedPiece))
        }
        
        return Result.Success(gameState)
    }
    
    override suspend fun dropPiece(hard: Boolean, gameState: TetrisGameState): Result<TetrisGameState> {
        val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)
        
        if (hard) {
            // Hard drop - move piece to bottom
            var testPiece = currentPiece
            while (isValidPosition(testPiece.move(0, 1), gameState)) {
                testPiece = testPiece.move(0, 1)
            }
            
            val newBoard = gameState.board.placePiece(testPiece)
            val (clearedBoard, linesCleared) = newBoard.clearLines()
            
            return Result.Success(gameState.copy(
                board = clearedBoard,
                currentPiece = generateNextPiece(),
                nextPiece = generateNextPiece(),
                score = gameState.score + calculateLineScore(linesCleared, gameState.level, 0),
                lines = gameState.lines + linesCleared
            ))
        } else {
            // Soft drop - move piece down one row
            return movePiece(Direction.DOWN, gameState)
        }
    }
    
    override suspend fun holdPiece(gameState: TetrisGameState): Result<TetrisGameState> {
        if (!gameState.canHold) return Result.Success(gameState)
        
        val currentPiece = gameState.currentPiece ?: return Result.Success(gameState)
        val holdPiece = gameState.holdPiece
        
        val newCurrentPiece = holdPiece ?: generateNextPiece()
        val newNextPiece = if (holdPiece == null) generateNextPiece() else gameState.nextPiece
        
        return Result.Success(gameState.copy(
            currentPiece = newCurrentPiece,
            nextPiece = newNextPiece,
            holdPiece = currentPiece,
            canHold = false
        ))
    }
    
    override suspend fun checkLineClear(gameState: TetrisGameState): Result<TetrisGameState> {
        val (clearedBoard, linesCleared) = gameState.board.clearLines()
        
        if (linesCleared > 0) {
            val newScore = gameState.score + calculateLineScore(linesCleared, gameState.level, 0)
            return Result.Success(gameState.copy(
                board = clearedBoard,
                score = newScore,
                lines = gameState.lines + linesCleared
            ))
        }
        
        return Result.Success(gameState)
    }
    
    override fun calculateDropInterval(level: Int): Long {
        return maxOf(50L, 1000L - (level * 50L))
    }
    
    override fun calculateLineScore(linesCleared: Int, level: Int, combo: Int): Int {
        val baseScores = mapOf(1 to 100, 2 to 300, 3 to 500, 4 to 800)
        val baseScore = baseScores[linesCleared] ?: 0
        return baseScore * (level + 1) + (combo * 50)
    }
    
    override fun getGhostPiece(gameState: TetrisGameState): TetrisPiece? {
        val currentPiece = gameState.currentPiece ?: return null
        
        var ghostPiece = currentPiece
        while (isValidPosition(ghostPiece.move(0, 1), gameState)) {
            ghostPiece = ghostPiece.move(0, 1)
        }
        
        return ghostPiece
    }
    
    override fun isValidPosition(piece: TetrisPiece, gameState: TetrisGameState): Boolean {
        return gameState.board.isValidPosition(piece)
    }
    
    override fun getPossibleRotations(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        return (0..3).map { rotation ->
            currentPiece.copy(rotation = rotation)
        }.filter { isValidPosition(it, gameState) }
    }
    
    override fun getPossiblePositions(gameState: TetrisGameState): List<TetrisPiece> {
        val currentPiece = gameState.currentPiece ?: return emptyList()
        val positions = mutableListOf<TetrisPiece>()
        
        for (x in 0 until gameState.board.width) {
            for (y in 0 until gameState.board.height) {
                val testPiece = currentPiece.copy(x = x, y = y)
                if (isValidPosition(testPiece, gameState)) {
                    positions.add(testPiece)
                }
            }
        }
        
        return positions
    }
    
    override suspend fun autoDrop(gameState: TetrisGameState): Result<TetrisGameState> {
        return movePiece(Direction.DOWN, gameState)
    }
    
    override fun checkTSpin(gameState: TetrisGameState): Boolean {
        val currentPiece = gameState.currentPiece ?: return false
        return currentPiece.type == TetrisPieceType.T && currentPiece.rotation > 0
    }
    
    override fun calculateLevel(lines: Int): Int {
        return (lines / 10) + 1
    }
}
/*
*
 * Mock interfaces for Tetris components
 */

/**
 * Tetris collision detection interface
 */
interface TetrisCollisionDetector {
    fun checkCollision(piece: TetrisPiece, board: TetrisBoard): Boolean
    fun checkBoundaryCollision(piece: TetrisPiece, boardWidth: Int, boardHeight: Int): Boolean
    fun checkPieceCollision(piece: TetrisPiece, board: TetrisBoard): Boolean
}

/**
 * Tetris game logic processor interface
 */
interface TetrisGameLogicProcessor {
    suspend fun processLineClear(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun processLevelUp(gameState: TetrisGameState): Result<TetrisGameState>
    suspend fun processScoring(gameState: TetrisGameState, linesCleared: Int): Result<TetrisGameState>
    fun calculateNextLevel(currentLines: Int): Int
}

/**
 * Tetris statistics calculator interface
 */
interface TetrisStatisticsCalculator {
    fun calculatePiecesPerMinute(piecesPlaced: Int, gameTimeMs: Long): Double
    fun calculateLinesPerMinute(linesCleared: Int, gameTimeMs: Long): Double
    fun calculateEfficiency(linesCleared: Int, piecesPlaced: Int): Double
    fun updateStatistics(current: GameStatistics, action: TetrisAction): GameStatistics
}

/**
 * Tetris performance manager interface
 */
interface TetrisPerformanceManager {
    fun startPerformanceMonitoring()
    fun stopPerformanceMonitoring()
    fun recordOperation(operationName: String, durationMs: Long)
    fun getPerformanceMetrics(): Map<String, Double>
    fun resetMetrics()
}

/**
 * Tetris cache manager interface
 */
interface TetrisCacheManager {
    fun cacheGameState(gameId: String, gameState: TetrisGameState)
    fun getCachedGameState(gameId: String): TetrisGameState?
    fun clearCache()
    fun getCacheSize(): Int
}

/**
 * Mock implementations
 */

/**
 * Mock collision detector with realistic behavior
 */
class MockTetrisCollisionDetector : TetrisCollisionDetector {
    
    override fun checkCollision(piece: TetrisPiece, board: TetrisBoard): Boolean {
        return checkBoundaryCollision(piece, board.width, board.height) || 
               checkPieceCollision(piece, board)
    }
    
    override fun checkBoundaryCollision(piece: TetrisPiece, boardWidth: Int, boardHeight: Int): Boolean {
        val positions = piece.getOccupiedPositions()
        return positions.any { (x, y) ->
            x < 0 || x >= boardWidth || y < 0 || y >= boardHeight
        }
    }
    
    override fun checkPieceCollision(piece: TetrisPiece, board: TetrisBoard): Boolean {
        val positions = piece.getOccupiedPositions()
        return positions.any { (x, y) ->
            x in 0 until board.width && 
            y in 0 until board.height && 
            board.cells[y][x] != TetrisCellType.EMPTY
        }
    }
}

/**
 * Mock game logic processor with basic functionality
 */
class MockTetrisGameLogicProcessor : TetrisGameLogicProcessor {
    
    override suspend fun processLineClear(gameState: TetrisGameState): Result<TetrisGameState> {
        val (clearedBoard, linesCleared) = gameState.board.clearLines()
        
        if (linesCleared > 0) {
            val newScore = gameState.score + (linesCleared * 100 * (gameState.level + 1))
            val newLines = gameState.lines + linesCleared
            val newLevel = calculateNextLevel(newLines)
            
            return Result.Success(gameState.copy(
                board = clearedBoard,
                score = newScore,
                lines = newLines,
                level = newLevel
            ))
        }
        
        return Result.Success(gameState)
    }
    
    override suspend fun processLevelUp(gameState: TetrisGameState): Result<TetrisGameState> {
        val newLevel = calculateNextLevel(gameState.lines)
        if (newLevel > gameState.level) {
            return Result.Success(gameState.copy(level = newLevel))
        }
        return Result.Success(gameState)
    }
    
    override suspend fun processScoring(gameState: TetrisGameState, linesCleared: Int): Result<TetrisGameState> {
        val baseScores = mapOf(1 to 100, 2 to 300, 3 to 500, 4 to 800)
        val scoreIncrease = (baseScores[linesCleared] ?: 0) * (gameState.level + 1)
        
        return Result.Success(gameState.copy(score = gameState.score + scoreIncrease))
    }
    
    override fun calculateNextLevel(currentLines: Int): Int {
        return (currentLines / 10) + 1
    }
}

/**
 * Mock statistics calculator with calculation logic
 */
class MockTetrisStatisticsCalculator : TetrisStatisticsCalculator {
    
    override fun calculatePiecesPerMinute(piecesPlaced: Int, gameTimeMs: Long): Double {
        if (gameTimeMs <= 0) return 0.0
        val minutes = gameTimeMs / 60000.0
        return piecesPlaced / minutes
    }
    
    override fun calculateLinesPerMinute(linesCleared: Int, gameTimeMs: Long): Double {
        if (gameTimeMs <= 0) return 0.0
        val minutes = gameTimeMs / 60000.0
        return linesCleared / minutes
    }
    
    override fun calculateEfficiency(linesCleared: Int, piecesPlaced: Int): Double {
        if (piecesPlaced <= 0) return 0.0
        return linesCleared.toDouble() / piecesPlaced
    }
    
    override fun updateStatistics(current: GameStatistics, action: TetrisAction): GameStatistics {
        return when (action) {
            is TetrisAction.Drop -> current.copy(piecesPlaced = current.piecesPlaced + 1)
            else -> current
        }
    }
}

/**
 * Mock performance manager with monitoring capabilities
 */
class MockTetrisPerformanceManager : TetrisPerformanceManager {
    
    private val metrics = mutableMapOf<String, MutableList<Long>>()
    private var isMonitoring = false
    
    override fun startPerformanceMonitoring() {
        isMonitoring = true
        metrics.clear()
    }
    
    override fun stopPerformanceMonitoring() {
        isMonitoring = false
    }
    
    override fun recordOperation(operationName: String, durationMs: Long) {
        if (isMonitoring) {
            metrics.getOrPut(operationName) { mutableListOf() }.add(durationMs)
        }
    }
    
    override fun getPerformanceMetrics(): Map<String, Double> {
        return metrics.mapValues { (_, durations) ->
            if (durations.isNotEmpty()) durations.average() else 0.0
        }
    }
    
    override fun resetMetrics() {
        metrics.clear()
    }
}

/**
 * Mock cache manager with basic caching behavior
 */
class MockTetrisCacheManager : TetrisCacheManager {
    
    private val cache = mutableMapOf<String, TetrisGameState>()
    
    override fun cacheGameState(gameId: String, gameState: TetrisGameState) {
        cache[gameId] = gameState
    }
    
    override fun getCachedGameState(gameId: String): TetrisGameState? {
        return cache[gameId]
    }
    
    override fun clearCache() {
        cache.clear()
    }
    
    override fun getCacheSize(): Int {
        return cache.size
    }
}

/**
 * Companion object for easy access to mock factory
 */
object MockComponents {
    
    private val basicFactory: MockComponentFactory = DefaultMockComponentFactory()
    private val advancedFactory: MockComponentFactory = AdvancedMockComponentFactory()
    
    // Basic mock components (simple behavior)
    fun tetrisEngine(): TetrisEngine = basicFactory.createMockTetrisEngine()
    fun collisionDetector(): TetrisCollisionDetector = basicFactory.createMockCollisionDetector()
    fun gameLogicProcessor(): TetrisGameLogicProcessor = basicFactory.createMockGameLogicProcessor()
    fun statisticsCalculator(): TetrisStatisticsCalculator = basicFactory.createMockStatisticsCalculator()
    fun performanceManager(): TetrisPerformanceManager = basicFactory.createMockPerformanceManager()
    fun cacheManager(): TetrisCacheManager = basicFactory.createMockCacheManager()
    
    // Advanced mock components (sophisticated behavior)
    object Advanced {
        fun tetrisEngine(): TetrisEngine = advancedFactory.createMockTetrisEngine()
        fun collisionDetector(): TetrisCollisionDetector = advancedFactory.createMockCollisionDetector()
        fun gameLogicProcessor(): TetrisGameLogicProcessor = advancedFactory.createMockGameLogicProcessor()
        fun statisticsCalculator(): TetrisStatisticsCalculator = advancedFactory.createMockStatisticsCalculator()
        fun performanceManager(): TetrisPerformanceManager = advancedFactory.createMockPerformanceManager()
        fun cacheManager(): TetrisCacheManager = advancedFactory.createMockCacheManager()
    }
    
    // Factory access
    fun basicFactory(): MockComponentFactory = basicFactory
    fun advancedFactory(): MockComponentFactory = advancedFactory
}