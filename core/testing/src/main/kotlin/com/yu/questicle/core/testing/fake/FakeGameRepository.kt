package com.yu.questicle.core.testing.fake

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.common.result.toQuesticleException
import com.yu.questicle.core.domain.model.Game
import com.yu.questicle.core.domain.model.GameSession
import com.yu.questicle.core.domain.model.GameStats
import com.yu.questicle.core.domain.model.GameType
import com.yu.questicle.core.domain.repository.GameRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map

/**
 * Fake implementation of GameRepository for testing
 */
class FakeGameRepository : GameRepository {
    
    private val games = mutableMapOf<String, Game>()
    private val sessions = mutableMapOf<String, List<GameSession>>()
    private val stats = mutableMapOf<String, GameStats>()
    
    private val gamesFlow = MutableStateFlow<Map<String, Game>>(emptyMap())
    
    var shouldReturnError = false
    var errorMessage = "Test error"
    
    override suspend fun saveGame(game: Game): Result<Unit> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            games[game.id] = game
            gamesFlow.value = games.toMap()
            Result.Success(Unit)
        }
    }

    override suspend fun loadGame(gameId: String): Result<Game> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            games[gameId]?.let { game ->
                Result.Success(game)
            } ?: Result.Error(Exception("Game not found").toQuesticleException())
        }
    }
    
    override fun getGamesForPlayer(playerId: String): Flow<List<Game>> {
        return gamesFlow.map { gamesMap ->
            gamesMap.values.filter { it.playerId == playerId }
        }
    }
    
    override fun getGamesByType(playerId: String, gameType: GameType): Flow<List<Game>> {
        return gamesFlow.map { gamesMap ->
            gamesMap.values.filter { it.playerId == playerId && it.type == gameType }
        }
    }
    
    override suspend fun deleteGame(gameId: String): Result<Unit> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            games.remove(gameId)
            gamesFlow.value = games.toMap()
            Result.Success(Unit)
        }
    }

    override suspend fun saveGameSession(session: GameSession): Result<Unit> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            val existingSessions = sessions[session.gameId] ?: emptyList()
            sessions[session.gameId] = existingSessions + session
            Result.Success(Unit)
        }
    }
    
    override fun getGameSessions(gameId: String): Flow<List<GameSession>> {
        return MutableStateFlow(sessions[gameId] ?: emptyList())
    }
    
    override suspend fun getGameStats(playerId: String, gameType: GameType): Result<GameStats> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            val key = "${playerId}_${gameType.name}"
            stats[key]?.let { gameStats ->
                Result.Success(gameStats)
            } ?: Result.Success(
                GameStats(
                    gameType = gameType,
                    playerId = playerId
                )
            )
        }
    }

    override suspend fun updateGameStats(stats: GameStats): Result<Unit> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            val key = "${stats.playerId}_${stats.gameType.name}"
            this.stats[key] = stats
            Result.Success(Unit)
        }
    }

    override suspend fun getLeaderboard(gameType: GameType, limit: Int): Result<List<GameStats>> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            val leaderboard = stats.values
                .filter { it.gameType == gameType }
                .sortedByDescending { it.bestScore }
                .take(limit)
            Result.Success(leaderboard)
        }
    }
    
    override suspend fun searchGames(
        playerId: String?,
        gameType: GameType?,
        minScore: Int?,
        maxScore: Int?,
        limit: Int
    ): Result<List<Game>> {
        return if (shouldReturnError) {
            Result.Error(Exception(errorMessage).toQuesticleException())
        } else {
            val filteredGames = games.values.filter { game ->
                (playerId == null || game.playerId == playerId) &&
                (gameType == null || game.type == gameType) &&
                (minScore == null || game.score >= minScore) &&
                (maxScore == null || game.score <= maxScore)
            }.take(limit)
            Result.Success(filteredGames)
        }
    }
    
    // Test helper methods
    fun addGame(game: Game) {
        games[game.id] = game
        gamesFlow.value = games.toMap()
    }
    
    fun addGameStats(stats: GameStats) {
        val key = "${stats.playerId}_${stats.gameType.name}"
        this.stats[key] = stats
    }
    
    fun clear() {
        games.clear()
        sessions.clear()
        stats.clear()
        gamesFlow.value = emptyMap()
    }
    
    fun getGameCount(): Int = games.size
    
    fun hasGame(gameId: String): Boolean = games.containsKey(gameId)
}
