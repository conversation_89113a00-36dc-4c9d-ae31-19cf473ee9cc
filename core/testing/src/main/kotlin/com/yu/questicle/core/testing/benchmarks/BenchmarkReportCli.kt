package com.yu.questicle.core.testing.benchmarks

import java.io.File

/**
 * Command line interface for benchmark reporting.
 */
object BenchmarkReportCli {
    
    @JvmStatic
    fun main(args: Array<String>) {
        if (args.isEmpty()) {
            printUsage()
            return
        }
        
        val command = args[0]
        when (command) {
            "generate" -> generateReport(args)
            "compare" -> compareReports(args)
            else -> {
                println("Unknown command: $command")
                printUsage()
            }
        }
    }
    
    private fun generateReport(args: Array<String>) {
        val resultsFile = if (args.size > 1) File(args[1]) else File("build/reports/jmh/results.json")
        val outputDir = if (args.size > 2) File(args[2]) else File("build/reports/benchmark")
        
        try {
            val reportSystem = BenchmarkReportSystem(outputDir)
            val reportFile = reportSystem.generateComprehensiveReport(resultsFile)
            println("✅ Report generated: ${reportFile.absolutePath}")
        } catch (e: Exception) {
            println("❌ Error generating report: ${e.message}")
        }
    }
    
    private fun compareReports(args: Array<String>) {
        if (args.size < 3) {
            println("❌ Compare command requires baseline and current files")
            println("Usage: compare <baseline.json> <current.json> [output-dir]")
            return
        }
        
        val baselineFile = File(args[1])
        val currentFile = File(args[2])
        val outputDir = if (args.size > 3) File(args[3]) else File("build/reports/benchmark")
        
        try {
            val reportSystem = BenchmarkReportSystem(outputDir)
            val reportFile = reportSystem.generateComprehensiveReport(currentFile, baselineFile, "Benchmark Comparison")
            println("✅ Comparison report generated: ${reportFile.absolutePath}")
        } catch (e: Exception) {
            println("❌ Error generating comparison: ${e.message}")
        }
    }
    
    private fun printUsage() {
        println("""
            Benchmark Report CLI
            
            Usage:
              generate [results-file] [output-dir]    Generate HTML report from JMH results
              compare <baseline> <current> [output-dir]  Compare two benchmark results
            
            Examples:
              generate
              generate results.json reports/
              compare baseline.json current.json
        """.trimIndent())
    }
}