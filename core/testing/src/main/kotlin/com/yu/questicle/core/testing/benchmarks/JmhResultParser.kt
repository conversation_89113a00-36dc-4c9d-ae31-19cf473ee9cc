package com.yu.questicle.core.testing.benchmarks

import org.json.JSONArray
import org.json.JSONObject
import java.io.File

/**
 * Production-grade JMH result parser and analyzer.
 */
class JmhResultParser {

    fun parseResults(resultsFile: File): List<BenchmarkResult> {
        if (!resultsFile.exists()) {
            throw IllegalArgumentException("Results file not found: ${resultsFile.absolutePath}")
        }

        val jsonContent = resultsFile.readText()
        val jsonArray = JSONArray(jsonContent)
        val results = mutableListOf<BenchmarkResult>()

        for (i in 0 until jsonArray.length()) {
            val jsonObject = jsonArray.getJSONObject(i)
            results.add(parseBenchmarkResult(jsonObject))
        }

        return results
    }

    private fun parseBenchmarkResult(json: JSONObject): BenchmarkResult {
        val primaryMetric = json.getJSONObject("primaryMetric")
        
        return BenchmarkResult(
            benchmark = json.getString("benchmark"),
            mode = json.getString("mode"),
            score = primaryMetric.getDouble("score"),
            scoreError = primaryMetric.getDouble("scoreError"),
            scoreUnit = primaryMetric.getString("scoreUnit"),
            samples = primaryMetric.getJSONArray("rawData").getJSONArray(0).length(),
            forks = json.getInt("forks"),
            warmupIterations = json.getInt("warmupIterations"),
            measurementIterations = json.getInt("measurementIterations")
        )
    }

    fun analyzeResults(results: List<BenchmarkResult>): BenchmarkAnalysis {
        if (results.isEmpty()) {
            return BenchmarkAnalysis(
                totalBenchmarks = 0,
                averageScore = 0.0,
                bestResult = null,
                worstResult = null,
                categoryBreakdown = emptyMap()
            )
        }

        val categoryBreakdown = results.groupBy { extractCategory(it.benchmark) }
            .mapValues { (_, categoryResults) ->
                CategoryStats(
                    count = categoryResults.size,
                    averageScore = categoryResults.map { it.score }.average(),
                    bestScore = categoryResults.minByOrNull { it.score }?.score ?: 0.0,
                    worstScore = categoryResults.maxByOrNull { it.score }?.score ?: 0.0
                )
            }

        return BenchmarkAnalysis(
            totalBenchmarks = results.size,
            averageScore = results.map { it.score }.average(),
            bestResult = results.minByOrNull { it.score },
            worstResult = results.maxByOrNull { it.score },
            categoryBreakdown = categoryBreakdown
        )
    }

    fun compareResults(baseline: List<BenchmarkResult>, current: List<BenchmarkResult>): ComparisonResult {
        val baselineMap = baseline.associateBy { it.benchmark }
        val currentMap = current.associateBy { it.benchmark }
        val commonBenchmarks = baselineMap.keys.intersect(currentMap.keys)

        val comparisons = commonBenchmarks.map { benchmarkName ->
            val baselineResult = baselineMap[benchmarkName]!!
            val currentResult = currentMap[benchmarkName]!!
            val percentageChange = ((currentResult.score - baselineResult.score) / baselineResult.score) * 100

            BenchmarkComparison(
                benchmarkName = benchmarkName,
                baselineScore = baselineResult.score,
                currentScore = currentResult.score,
                percentageChange = percentageChange,
                isRegression = percentageChange > 5.0,
                isImprovement = percentageChange < -5.0,
                scoreUnit = baselineResult.scoreUnit
            )
        }

        return ComparisonResult(
            comparisons = comparisons,
            regressions = comparisons.filter { it.isRegression },
            improvements = comparisons.filter { it.isImprovement },
            newBenchmarks = currentMap.keys - baselineMap.keys,
            removedBenchmarks = baselineMap.keys - currentMap.keys,
            overallPerformanceChange = comparisons.map { it.percentageChange }.average()
        )
    }

    private fun extractCategory(benchmarkName: String): String {
        return when {
            benchmarkName.contains("Engine") -> "Engine"
            benchmarkName.contains("Rendering") -> "Rendering"
            benchmarkName.contains("Memory") -> "Memory"
            benchmarkName.contains("Network") -> "Network"
            else -> "Other"
        }
    }
}

data class BenchmarkResult(
    val benchmark: String,
    val mode: String,
    val score: Double,
    val scoreError: Double,
    val scoreUnit: String,
    val samples: Int,
    val forks: Int,
    val warmupIterations: Int,
    val measurementIterations: Int
)

data class BenchmarkAnalysis(
    val totalBenchmarks: Int,
    val averageScore: Double,
    val bestResult: BenchmarkResult?,
    val worstResult: BenchmarkResult?,
    val categoryBreakdown: Map<String, CategoryStats>
)

data class CategoryStats(
    val count: Int,
    val averageScore: Double,
    val bestScore: Double,
    val worstScore: Double
)

data class ComparisonResult(
    val comparisons: List<BenchmarkComparison>,
    val regressions: List<BenchmarkComparison>,
    val improvements: List<BenchmarkComparison>,
    val newBenchmarks: Set<String>,
    val removedBenchmarks: Set<String>,
    val overallPerformanceChange: Double
)

data class BenchmarkComparison(
    val benchmarkName: String,
    val baselineScore: Double,
    val currentScore: Double,
    val percentageChange: Double,
    val isRegression: Boolean,
    val isImprovement: Boolean,
    val scoreUnit: String
)