package com.yu.questicle.core.testing

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest

/**
 * 测试工具集合
 */
object TestUtils {
    
    /**
     * Flow测试工具
     */
    suspend fun <T> Flow<T>.testCollect(): List<T> {
        return this.toList()
    }
    
    /**
     * 协程测试工具
     */
    fun runTestBlocking(block: suspend () -> Unit) = runTest {
        block()
    }
    
    /**
     * 断言工具
     */
    fun assertGameState(
        actual: Map<String, Any>,
        expectedScore: Int? = null,
        expectedLevel: Int? = null
    ) {
        expectedScore?.let { 
            assert(actual["score"] == it) { "Score mismatch" }
        }
        expectedLevel?.let {
            assert(actual["level"] == it) { "Level mismatch" }
        }
    }
}