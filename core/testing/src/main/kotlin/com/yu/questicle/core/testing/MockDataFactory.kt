package com.yu.questicle.core.testing

import com.yu.questicle.core.common.exception.*
import com.yu.questicle.core.common.performance.*
import com.yu.questicle.core.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import java.time.Instant
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 测试数据工厂
 * 
 * 提供各种测试数据的创建和管理
 * 支持建造者模式和自定义配置
 */
@Singleton
class MockDataFactory @Inject constructor() {
    
    private val random = Random()
    
    // ==================== 性能监控测试数据 ====================
    
    /**
     * 创建性能快照
     */
    fun createPerformanceSnapshot(
        customizer: PerformanceSnapshotBuilder.() -> Unit = {}
    ): PerformanceSnapshot {
        return PerformanceSnapshotBuilder()
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建帧率指标
     */
    fun createFrameRateMetric(
        fps: Int = random.nextInt(30, 120),
        customizer: FrameRateMetricBuilder.() -> Unit = {}
    ): FrameRateMetric {
        return FrameRateMetricBuilder(fps)
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建内存指标
     */
    fun createMemoryMetric(
        heapUtilization: Double = random.nextDouble(0.3, 0.9),
        customizer: MemoryMetricBuilder.() -> Unit = {}
    ): MemoryMetric {
        return MemoryMetricBuilder(heapUtilization)
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建CPU指标
     */
    fun createCpuMetric(
        cpuUsage: Double = random.nextDouble(10.0, 90.0),
        customizer: CpuMetricBuilder.() -> Unit = {}
    ): CPUMetric {
        return CpuMetricBuilder(cpuUsage)
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建性能快照流
     */
    fun createPerformanceSnapshotFlow(
        count: Int = 10,
        customizer: PerformanceSnapshotBuilder.() -> Unit = {}
    ): Flow<PerformanceSnapshot> {
        val snapshots = (1..count).map { index ->
            createPerformanceSnapshot {
                withTimestamp(System.currentTimeMillis() + index * 1000L)
                customizer()
            }
        }
        return flowOf(*snapshots.toTypedArray())
    }
    
    // ==================== 异常处理测试数据 ====================
    
    /**
     * 创建 Questicle 异常
     */
    fun createQuesticleException(
        type: ExceptionType = ExceptionType.BUSINESS_LOGIC,
        customizer: ExceptionBuilder.() -> Unit = {}
    ): QuesticleException {
        return ExceptionBuilder(type)
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建崩溃事件
     */
    fun createCrashEvent(
        exception: QuesticleException = createQuesticleException(),
        customizer: CrashEventBuilder.() -> Unit = {}
    ): CrashEvent {
        return CrashEventBuilder(exception)
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建异常上下文
     */
    fun createExceptionContext(
        customizer: ExceptionContextBuilder.() -> Unit = {}
    ): ExceptionContext {
        return ExceptionContextBuilder()
            .apply(customizer)
            .build()
    }
    
    // ==================== 用户和游戏数据 ====================
    
    /**
     * 创建用户
     */
    fun createUser(
        customizer: UserBuilder.() -> Unit = {}
    ): User {
        return UserBuilder()
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建游戏会话
     */
    fun createGameSession(
        customizer: GameSessionBuilder.() -> Unit = {}
    ): GameSession {
        return GameSessionBuilder()
            .apply(customizer)
            .build()
    }
    
    // ==================== 配置数据 ====================
    
    /**
     * 创建高级性能监控配置
     */
    fun createAdvancedPerformanceConfig(
        customizer: AdvancedPerformanceConfigBuilder.() -> Unit = {}
    ): AdvancedPerformanceConfig {
        return AdvancedPerformanceConfigBuilder()
            .apply(customizer)
            .build()
    }
    
    /**
     * 创建崩溃报告配置
     */
    fun createCrashReportingConfig(
        customizer: CrashReportingConfigBuilder.() -> Unit = {}
    ): CrashReportingConfig {
        return CrashReportingConfigBuilder()
            .apply(customizer)
            .build()
    }
}

// ==================== 建造者类 ====================

/**
 * 性能快照建造者
 */
class PerformanceSnapshotBuilder {
    private var timestamp: Long = System.currentTimeMillis()
    private var frameRate: FrameRateMetric? = null
    private var memory: MemoryMetric? = null
    private var cpu: CPUMetric? = null
    private var network: NetworkMetric? = null
    
    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    
    fun withFrameRate(fps: Int, avgFrameTime: Double = 16.67) = apply {
        this.frameRate = FrameRateMetric(
            timestamp = timestamp,
            fps = fps,
            avgFrameTime = avgFrameTime,
            droppedFrameRate = if (fps < 55) 0.1 else 0.0,
            isSmooth = fps >= 55
        )
    }
    
    fun withMemory(heapUtilization: Double) = apply {
        this.memory = MemoryMetric(
            timestamp = timestamp,
            usedHeap = (heapUtilization * 1024 * 1024 * 100).toLong(),
            totalHeap = 1024 * 1024 * 100,
            maxHeap = 1024 * 1024 * 200,
            heapUtilization = heapUtilization,
            usedMemory = (heapUtilization * 1024 * 1024 * 500).toLong(),
            totalMemory = 1024 * 1024 * 1000,
            availableMemory = 1024 * 1024 * (1000 - 500 * heapUtilization).toLong(),
            memoryPressure = heapUtilization > 0.9,
            gcCount = 10,
            gcRate = if (heapUtilization > 0.8) 5.0 else 2.0
        )
    }
    
    fun withCpu(cpuUsage: Double) = apply {
        this.cpu = CPUMetric(
            timestamp = timestamp,
            cpuUsage = cpuUsage,
            threadCount = 20,
            loadAverage = cpuUsage / 100.0 * 4.0,
            isHighUsage = cpuUsage > 80.0
        )
    }
    
    fun withNetwork(requestCount: Long = 100, avgResponseTime: Double = 200.0) = apply {
        this.network = NetworkMetric(
            timestamp = timestamp,
            requestCount = requestCount,
            avgResponseTime = avgResponseTime,
            errorRate = 0.05,
            throughput = 1024.0 * 1024.0 // 1MB/s
        )
    }
    
    fun build(): PerformanceSnapshot {
        val overall = calculateOverallPerformance()
        return PerformanceSnapshot(
            timestamp = timestamp,
            frameRate = frameRate,
            memory = memory,
            cpu = cpu,
            network = network,
            overall = overall
        )
    }
    
    private fun calculateOverallPerformance(): OverallPerformance {
        var score = 100.0
        val issues = mutableListOf<String>()
        
        frameRate?.let { if (it.fps < 30) { score -= 30; issues.add("Low FPS: ${it.fps}") } }
        memory?.let { if (it.heapUtilization > 0.9) { score -= 25; issues.add("High Memory: ${(it.heapUtilization * 100).toInt()}%") } }
        cpu?.let { if (it.cpuUsage > 80) { score -= 20; issues.add("High CPU: ${it.cpuUsage.toInt()}%") } }
        
        return OverallPerformance(
            score = score.coerceAtLeast(0.0),
            grade = when {
                score >= 90 -> PerformanceGrade.EXCELLENT
                score >= 75 -> PerformanceGrade.GOOD
                score >= 60 -> PerformanceGrade.FAIR
                score >= 40 -> PerformanceGrade.POOR
                else -> PerformanceGrade.CRITICAL
            },
            issues = issues
        )
    }
}

/**
 * 帧率指标建造者
 */
class FrameRateMetricBuilder(private val fps: Int) {
    private var timestamp: Long = System.currentTimeMillis()
    private var avgFrameTime: Double = 1000.0 / fps
    private var droppedFrameRate: Double = if (fps < 55) 0.1 else 0.0
    
    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    fun withAvgFrameTime(avgFrameTime: Double) = apply { this.avgFrameTime = avgFrameTime }
    fun withDroppedFrameRate(droppedFrameRate: Double) = apply { this.droppedFrameRate = droppedFrameRate }
    
    fun build(): FrameRateMetric {
        return FrameRateMetric(
            timestamp = timestamp,
            fps = fps,
            avgFrameTime = avgFrameTime,
            droppedFrameRate = droppedFrameRate,
            isSmooth = fps >= 55 && droppedFrameRate < 0.1
        )
    }
}

/**
 * 内存指标建造者
 */
class MemoryMetricBuilder(private val heapUtilization: Double) {
    private var timestamp: Long = System.currentTimeMillis()
    private var maxHeap: Long = 1024 * 1024 * 200 // 200MB
    private var memoryPressure: Boolean = heapUtilization > 0.9
    
    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    fun withMaxHeap(maxHeap: Long) = apply { this.maxHeap = maxHeap }
    fun withMemoryPressure(memoryPressure: Boolean) = apply { this.memoryPressure = memoryPressure }
    
    fun build(): MemoryMetric {
        val usedHeap = (heapUtilization * maxHeap).toLong()
        val totalHeap = (maxHeap * 0.8).toLong()
        
        return MemoryMetric(
            timestamp = timestamp,
            usedHeap = usedHeap,
            totalHeap = totalHeap,
            maxHeap = maxHeap,
            heapUtilization = heapUtilization,
            usedMemory = usedHeap * 5, // 假设系统内存是堆内存的5倍
            totalMemory = maxHeap * 10, // 假设总内存是最大堆内存的10倍
            availableMemory = maxHeap * 10 - usedHeap * 5,
            memoryPressure = memoryPressure,
            gcCount = if (heapUtilization > 0.8) 20 else 10,
            gcRate = if (heapUtilization > 0.8) 5.0 else 2.0
        )
    }
}

/**
 * CPU指标建造者
 */
class CpuMetricBuilder(private val cpuUsage: Double) {
    private var timestamp: Long = System.currentTimeMillis()
    private var threadCount: Int = 20
    private var loadAverage: Double = cpuUsage / 100.0 * 4.0

    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    fun withThreadCount(threadCount: Int) = apply { this.threadCount = threadCount }
    fun withLoadAverage(loadAverage: Double) = apply { this.loadAverage = loadAverage }

    fun build(): CPUMetric {
        return CPUMetric(
            timestamp = timestamp,
            cpuUsage = cpuUsage,
            threadCount = threadCount,
            loadAverage = loadAverage,
            isHighUsage = cpuUsage > 80.0
        )
    }
}

/**
 * 异常建造者
 */
class ExceptionBuilder(private val type: ExceptionType) {
    private var message: String = "Test exception message"
    private var errorCode: String = "TEST_ERROR"
    private var severity: ErrorSeverity = ErrorSeverity.MEDIUM
    private var cause: Throwable? = null
    private var context: Map<String, Any?> = emptyMap()

    fun withMessage(message: String) = apply { this.message = message }
    fun withErrorCode(errorCode: String) = apply { this.errorCode = errorCode }
    fun withSeverity(severity: ErrorSeverity) = apply { this.severity = severity }
    fun withCause(cause: Throwable) = apply { this.cause = cause }
    fun withContext(context: Map<String, Any?>) = apply { this.context = context }

    fun build(): QuesticleException {
        return when (type) {
            ExceptionType.BUSINESS_LOGIC -> BusinessLogicException(message, errorCode, severity, cause)
            ExceptionType.NETWORK -> NetworkException(message, errorCode, severity, cause)
            ExceptionType.DATABASE -> DatabaseException(message, errorCode, severity, cause)
            ExceptionType.SYSTEM -> SystemException(message, errorCode, severity, cause)
            ExceptionType.PERMISSION -> PermissionException(message, errorCode, severity, cause)
            ExceptionType.VALIDATION -> ValidationException(message, errorCode, severity, cause)
        }.apply {
            this.context.putAll(<EMAIL>)
        }
    }
}

/**
 * 崩溃事件建造者
 */
class CrashEventBuilder(private val exception: QuesticleException) {
    private var timestamp: Long = System.currentTimeMillis()
    private var thread: String = Thread.currentThread().name
    private var stackTrace: String = exception.stackTraceToString()
    private var context: ExceptionContext = ExceptionContext.EMPTY
    private var isFatal: Boolean = exception.severity == ErrorSeverity.CRITICAL

    fun withTimestamp(timestamp: Long) = apply { this.timestamp = timestamp }
    fun withThread(thread: String) = apply { this.thread = thread }
    fun withStackTrace(stackTrace: String) = apply { this.stackTrace = stackTrace }
    fun withContext(context: ExceptionContext) = apply { this.context = context }
    fun withFatal(isFatal: Boolean) = apply { this.isFatal = isFatal }

    fun build(): CrashEvent {
        return CrashEvent(
            timestamp = timestamp,
            exception = exception,
            thread = thread,
            stackTrace = stackTrace,
            context = context,
            isFatal = isFatal
        )
    }
}

/**
 * 异常上下文建造者
 */
class ExceptionContextBuilder {
    private val contextMap = mutableMapOf<String, Any?>()

    fun withUserId(userId: String) = apply { contextMap["userId"] = userId }
    fun withSessionId(sessionId: String) = apply { contextMap["sessionId"] = sessionId }
    fun withScreenName(screenName: String) = apply { contextMap["screenName"] = screenName }
    fun withUserAction(action: String) = apply { contextMap["userAction"] = action }
    fun withDeviceInfo(deviceInfo: Map<String, Any?>) = apply { contextMap["deviceInfo"] = deviceInfo }
    fun withAppVersion(appVersion: String) = apply { contextMap["appVersion"] = appVersion }
    fun withCustomData(key: String, value: Any?) = apply { contextMap[key] = value }

    fun build(): ExceptionContext {
        return ExceptionContext(contextMap.toMap())
    }
}

/**
 * 用户建造者
 */
class UserBuilder {
    private var id: String = UUID.randomUUID().toString()
    private var name: String = "Test User"
    private var email: String = "<EMAIL>"
    private var level: Int = 1
    private var experience: Long = 0L
    private var createdAt: Instant = Instant.now()

    fun withId(id: String) = apply { this.id = id }
    fun withName(name: String) = apply { this.name = name }
    fun withEmail(email: String) = apply { this.email = email }
    fun withLevel(level: Int) = apply { this.level = level }
    fun withExperience(experience: Long) = apply { this.experience = experience }
    fun withCreatedAt(createdAt: Instant) = apply { this.createdAt = createdAt }

    fun build(): User {
        return User(
            id = id,
            name = name,
            email = email,
            level = level,
            experience = experience,
            createdAt = createdAt
        )
    }
}

/**
 * 游戏会话建造者
 */
class GameSessionBuilder {
    private var id: String = UUID.randomUUID().toString()
    private var userId: String = UUID.randomUUID().toString()
    private var startTime: Instant = Instant.now()
    private var endTime: Instant? = null
    private var score: Long = 0L
    private var level: Int = 1
    private var duration: Long = 0L

    fun withId(id: String) = apply { this.id = id }
    fun withUserId(userId: String) = apply { this.userId = userId }
    fun withStartTime(startTime: Instant) = apply { this.startTime = startTime }
    fun withEndTime(endTime: Instant?) = apply { this.endTime = endTime }
    fun withScore(score: Long) = apply { this.score = score }
    fun withLevel(level: Int) = apply { this.level = level }
    fun withDuration(duration: Long) = apply { this.duration = duration }

    fun build(): GameSession {
        return GameSession(
            id = id,
            userId = userId,
            startTime = startTime,
            endTime = endTime,
            score = score,
            level = level,
            duration = duration
        )
    }
}

/**
 * 高级性能监控配置建造者
 */
class AdvancedPerformanceConfigBuilder {
    private var enableFrameRateMonitoring: Boolean = true
    private var enableMemoryMonitoring: Boolean = true
    private var enableCpuMonitoring: Boolean = true
    private var enableNetworkMonitoring: Boolean = false
    private var snapshotIntervalMs: Long = 5000L
    private var frameRateThreshold: Int = 55
    private var memoryThreshold: Double = 0.8
    private var cpuThreshold: Double = 80.0

    fun withFrameRateMonitoring(enabled: Boolean) = apply { this.enableFrameRateMonitoring = enabled }
    fun withMemoryMonitoring(enabled: Boolean) = apply { this.enableMemoryMonitoring = enabled }
    fun withCpuMonitoring(enabled: Boolean) = apply { this.enableCpuMonitoring = enabled }
    fun withNetworkMonitoring(enabled: Boolean) = apply { this.enableNetworkMonitoring = enabled }
    fun withSnapshotInterval(intervalMs: Long) = apply { this.snapshotIntervalMs = intervalMs }
    fun withFrameRateThreshold(threshold: Int) = apply { this.frameRateThreshold = threshold }
    fun withMemoryThreshold(threshold: Double) = apply { this.memoryThreshold = threshold }
    fun withCpuThreshold(threshold: Double) = apply { this.cpuThreshold = threshold }

    fun build(): AdvancedPerformanceConfig {
        return AdvancedPerformanceConfig(
            enableFrameRateMonitoring = enableFrameRateMonitoring,
            enableMemoryMonitoring = enableMemoryMonitoring,
            enableCpuMonitoring = enableCpuMonitoring,
            enableNetworkMonitoring = enableNetworkMonitoring,
            snapshotIntervalMs = snapshotIntervalMs,
            frameRateThreshold = frameRateThreshold,
            memoryThreshold = memoryThreshold,
            cpuThreshold = cpuThreshold
        )
    }
}

/**
 * 崩溃报告配置建造者
 */
class CrashReportingConfigBuilder {
    private var enableSupabaseCrashlytics: Boolean = true
    private var enableCustomReporting: Boolean = true
    private var enableLocalReporting: Boolean = true
    private var enableUserFeedback: Boolean = true
    private var enableAutoUpload: Boolean = true
    private var maxLocalReports: Int = 100
    private var reportRetentionDays: Int = 30

    fun withSupabaseCrashlytics(enabled: Boolean) = apply { this.enableSupabaseCrashlytics = enabled }
    fun withCustomReporting(enabled: Boolean) = apply { this.enableCustomReporting = enabled }
    fun withLocalReporting(enabled: Boolean) = apply { this.enableLocalReporting = enabled }
    fun withUserFeedback(enabled: Boolean) = apply { this.enableUserFeedback = enabled }
    fun withAutoUpload(enabled: Boolean) = apply { this.enableAutoUpload = enabled }
    fun withMaxLocalReports(max: Int) = apply { this.maxLocalReports = max }
    fun withReportRetentionDays(days: Int) = apply { this.reportRetentionDays = days }

    fun build(): CrashReportingConfig {
        return CrashReportingConfig(
            enableSupabaseCrashlytics = enableSupabaseCrashlytics,
            enableCustomReporting = enableCustomReporting,
            enableLocalReporting = enableLocalReporting,
            enableUserFeedback = enableUserFeedback,
            enableAutoUpload = enableAutoUpload,
            maxLocalReports = maxLocalReports,
            reportRetentionDays = reportRetentionDays
        )
    }
}
