package com.yu.questicle.core.testing

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import io.kotest.matchers.shouldBe

/**
 * 简单测试用于验证 JaCoCo 兼容性
 */
@DisplayName("JaCoCo Compatibility Tests")
class JacocoCompatibilityTest {

    @Test
    @DisplayName("Should verify JaCoCo works with Java 21")
    fun shouldVerifyJacocoWorksWithJava21() {
        // 简单的测试，确保 JaCoCo 能够正常工作
        val result = performSimpleOperation()
        result shouldBe "JaCoCo Compatible"
    }

    @Test
    @DisplayName("Should test basic arithmetic operations")
    fun shouldTestBasicArithmeticOperations() {
        val calculator = SimpleCalculator()
        
        calculator.add(2, 3) shouldBe 5
        calculator.subtract(10, 4) shouldBe 6
        calculator.multiply(3, 4) shouldBe 12
        calculator.divide(15, 3) shouldBe 5
    }

    @Test
    @DisplayName("Should test string operations")
    fun shouldTestStringOperations() {
        val processor = StringProcessor()
        
        processor.concatenate("Hello", "World") shouldBe "HelloWorld"
        processor.reverse("test") shouldBe "tset"
        processor.uppercase("hello") shouldBe "HELLO"
    }

    private fun performSimpleOperation(): String {
        return "JaCoCo Compatible"
    }
}

/**
 * 简单计算器类用于测试覆盖率
 */
class SimpleCalculator {
    fun add(a: Int, b: Int): Int = a + b
    fun subtract(a: Int, b: Int): Int = a - b
    fun multiply(a: Int, b: Int): Int = a * b
    fun divide(a: Int, b: Int): Int = if (b != 0) a / b else throw IllegalArgumentException("Division by zero")
}

/**
 * 字符串处理器类用于测试覆盖率
 */
class StringProcessor {
    fun concatenate(str1: String, str2: String): String = str1 + str2
    fun reverse(str: String): String = str.reversed()
    fun uppercase(str: String): String = str.uppercase()
    fun lowercase(str: String): String = str.lowercase()
}