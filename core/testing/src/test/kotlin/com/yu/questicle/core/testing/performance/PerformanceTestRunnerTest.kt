package com.yu.questicle.core.testing.performance

import com.yu.questicle.core.testing.BaseCoroutineTest
import io.kotest.matchers.doubles.shouldBeGreaterThan
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.longs.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

/**
 * Tests for PerformanceTestRunner implementation
 */
@DisplayName("PerformanceTestRunner Tests")
class PerformanceTestRunnerTest : BaseCoroutineTest() {
    
    private lateinit var performanceTestRunner: PerformanceTestRunner
    
    @BeforeEach
    override fun setUp() {
        super.setUp()
        performanceTestRunner = DefaultPerformanceTestRunner()
    }
    
    @Nested
    @DisplayName("Performance Test Execution")
    inner class PerformanceTestExecution {
        
        @Test
        @DisplayName("Should run performance test with correct metrics")
        fun shouldRunPerformanceTestWithCorrectMetrics() = runTest {
            val testName = "test_operation"
            val iterations = 10
            
            val result = performanceTestRunner.runPerformanceTest(
                testName = testName,
                iterations = iterations
            ) {
                // Simulate work with actual computation instead of delay
                var sum = 0
                repeat(1000) { sum += it }
            }
            
            result.testName shouldBe testName
            result.iterations shouldBe iterations
            // In test environment, times might be very small but should be >= 0
            result.averageTime shouldBeGreaterThan -1.0
            result.minTime shouldBeGreaterThan -1.0
            result.maxTime shouldBeGreaterThan -1.0
            result.operationsPerSecond shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should calculate standard deviation correctly")
        fun shouldCalculateStandardDeviationCorrectly() = runTest {
            val result = performanceTestRunner.runPerformanceTest(
                testName = "variable_time_test",
                iterations = 20
            ) {
                // Variable execution time
                val randomDelay = kotlin.random.Random.nextLong(5, 15)
                delay(randomDelay)
            }
            
            result.standardDeviation shouldBeGreaterThan 0.0
            result.minTime shouldNotBe result.maxTime
        }
        
        @Test
        @DisplayName("Should handle zero-time operations")
        fun shouldHandleZeroTimeOperations() = runTest {
            val result = performanceTestRunner.runPerformanceTest(
                testName = "instant_operation",
                iterations = 100
            ) {
                // No-op operation
            }
            
            result.testName shouldBe "instant_operation"
            result.iterations shouldBe 100
            // Should handle zero or very small times gracefully
        }
    }
    
    @Nested
    @DisplayName("Memory Usage Measurement")
    inner class MemoryUsageMeasurement {
        
        @Test
        @DisplayName("Should measure memory usage during operation")
        fun shouldMeasureMemoryUsageDuringOperation() = runTest {
            val result = performanceTestRunner.measureMemoryUsage {
                // Allocate some memory
                val largeList = mutableListOf<String>()
                repeat(1000) {
                    largeList.add("test_string_$it")
                }
                delay(10)
            }
            
            result.initialMemory shouldBeGreaterThan 0L
            result.finalMemory shouldBeGreaterThan 0L
            result.peakMemory shouldBeGreaterThan 0L
            result.gcCount shouldBeGreaterThan -1 // Can be 0 or more
        }
        
        @Test
        @DisplayName("Should detect memory allocation")
        fun shouldDetectMemoryAllocation() = runTest {
            val result = performanceTestRunner.measureMemoryUsage {
                // Allocate significant memory
                val data = ByteArray(1024 * 1024) // 1MB
                data[0] = 1 // Use the array to prevent optimization
            }
            
            // Memory measurements should be reasonable
            result.initialMemory shouldBeGreaterThan 0L
            result.finalMemory shouldBeGreaterThan 0L
        }
    }
    
    @Nested
    @DisplayName("Benchmark Operations")
    inner class BenchmarkOperations {
        
        @Test
        @DisplayName("Should run benchmark with warmup and measurement phases")
        fun shouldRunBenchmarkWithWarmupAndMeasurementPhases() = runTest {
            val result = performanceTestRunner.benchmarkOperation {
                delay(5) // Simulate work
            }
            
            result.benchmarkName shouldNotBe ""
            result.mode shouldBe BenchmarkMode.AVERAGE_TIME
            result.score shouldBeGreaterThan 0.0
            result.scoreUnit shouldBe "ms/op"
            result.samples shouldBeGreaterThan 0
            result.error shouldBeGreaterThan 0.0
        }
        
        @Test
        @DisplayName("Should provide consistent benchmark results")
        fun shouldProvideConsistentBenchmarkResults() = runTest {
            val operation: suspend () -> Unit = {
                delay(10) // Consistent work
            }
            
            val result1 = performanceTestRunner.benchmarkOperation(operation)
            val result2 = performanceTestRunner.benchmarkOperation(operation)
            
            // Results should be in similar range (within 50% of each other)
            val ratio = result1.score / result2.score
            ratio shouldBeGreaterThan 0.5
            ratio shouldBeLessThan 2.0
        }
        
        private infix fun Double.shouldBeLessThan(expected: Double) {
            if (this >= expected) {
                throw AssertionError("Expected $this to be less than $expected")
            }
        }
    }
    
    @Nested
    @DisplayName("Performance Analysis")
    inner class PerformanceAnalysis {
        
        @Test
        @DisplayName("Should calculate operations per second correctly")
        fun shouldCalculateOperationsPerSecondCorrectly() = runTest {
            val result = performanceTestRunner.runPerformanceTest(
                testName = "ops_per_second_test",
                iterations = 10
            ) {
                // Use actual computation instead of delay for more predictable timing
                var sum = 0
                repeat(10000) { sum += it }
            }
            
            result.operationsPerSecond shouldBeGreaterThan 0.0
            // Should be reasonable for computation-based operation
            result.operationsPerSecond shouldBeLessThan 1000000.0 // Upper bound
        }
        
        @Test
        @DisplayName("Should handle fast operations correctly")
        fun shouldHandleFastOperationsCorrectly() = runTest {
            val result = performanceTestRunner.runPerformanceTest(
                testName = "fast_operation",
                iterations = 1000
            ) {
                // Very fast operation
                val x = 1 + 1
            }
            
            result.operationsPerSecond shouldBeGreaterThan 100.0 // Should be very high
            result.averageTime shouldBeGreaterThan 0.0 // Should still measure some time
        }
        
        private infix fun Double.shouldBeLessThan(expected: Double) {
            if (this >= expected) {
                throw AssertionError("Expected $this to be less than $expected")
            }
        }
    }
    
    @Nested
    @DisplayName("Edge Cases")
    inner class EdgeCases {
        
        @Test
        @DisplayName("Should handle single iteration test")
        fun shouldHandleSingleIterationTest() = runTest {
            val result = performanceTestRunner.runPerformanceTest(
                testName = "single_iteration",
                iterations = 1
            ) {
                delay(50)
            }
            
            result.iterations shouldBe 1
            result.averageTime shouldBe result.minTime
            result.averageTime shouldBe result.maxTime
            result.standardDeviation shouldBe 0.0
        }
        
        @Test
        @DisplayName("Should handle exception in operation gracefully")
        fun shouldHandleExceptionInOperationGracefully() = runTest {
            try {
                performanceTestRunner.runPerformanceTest(
                    testName = "exception_test",
                    iterations = 5
                ) {
                    throw RuntimeException("Test exception")
                }
            } catch (e: RuntimeException) {
                // Expected - operation should propagate exceptions
                e.message shouldBe "Test exception"
            }
        }
    }
}