package com.yu.questicle.core.testing.mock

import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.BaseCoroutineTest
import com.yu.questicle.core.testing.factory.TestDataFactory
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.doubles.shouldBeGreaterThan
import io.kotest.matchers.doubles.shouldBeLessThan
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.longs.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeInstanceOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

/**
 * Comprehensive tests for Advanced Mock Components
 * 
 * Tests all advanced mock implementations to ensure they provide
 * sophisticated and realistic behavior for comprehensive testing scenarios.
 */
@DisplayName("Advanced Mock Components Tests")
class AdvancedMockComponentsTest : BaseCoroutineTest() {
    
    @Nested
    @DisplayName("AdvancedMockTetrisStatisticsCalculator Tests")
    inner class AdvancedStatisticsCalculatorTests {
        
        private lateinit var calculator: AdvancedMockTetrisStatisticsCalculator
        
        @BeforeEach
        fun setup() {
            calculator = AdvancedMockTetrisStatisticsCalculator()
        }
        
        @Test
        @DisplayName("Should calculate efficiency rating correctly")
        fun shouldCalculateEfficiencyRatingCorrectly() {
            val statistics = TestDataFactory.createTetrisStatistics(
                linesCleared = 50,
                piecesPlaced = 100,
                tetrises = 5,
                tSpins = 3,
                maxCombo = 4
            )
            
            val rating = calculator.calculateEfficiencyRating(statistics)
            
            rating shouldBeGreaterThan 0.0
            rating shouldBeLessThan 100.0
        }
        
        @Test
        @DisplayName("Should calculate piece distribution balance")
        fun shouldCalculatePieceDistributionBalance() {
            val balancedStats = mapOf(
                TetrisPieceType.I to 10,
                TetrisPieceType.O to 10,
                TetrisPieceType.T to 10,
                TetrisPieceType.S to 10,
                TetrisPieceType.Z to 10,
                TetrisPieceType.J to 10,
                TetrisPieceType.L to 10
            )
            
            val unbalancedStats = mapOf(
                TetrisPieceType.I to 50,
                TetrisPieceType.O to 1,
                TetrisPieceType.T to 1,
                TetrisPieceType.S to 1,
                TetrisPieceType.Z to 1,
                TetrisPieceType.J to 1,
                TetrisPieceType.L to 1
            )
            
            val balancedScore = calculator.calculatePieceDistributionBalance(balancedStats)
            val unbalancedScore = calculator.calculatePieceDistributionBalance(unbalancedStats)
            
            balancedScore shouldBeGreaterThan unbalancedScore
            balancedScore shouldBeGreaterThan 0.8 // Should be highly balanced
        }
        
        @Test
        @DisplayName("Should track performance trends")
        fun shouldTrackPerformanceTrends() {
            val baseStats = TestDataFactory.createTetrisStatistics()
            
            // Simulate improving performance
            repeat(10) { i ->
                val improvingStats = baseStats.copy(
                    linesCleared = baseStats.linesCleared + i * 5,
                    piecesPlaced = baseStats.piecesPlaced + i * 8
                )
                calculator.updateStatistics(improvingStats, TetrisAction.Drop(false, "test"))
            }
            
            val trend = calculator.calculatePerformanceTrend()
            trend.shouldBeInstanceOf<PerformanceTrend>()
        }
        
        @Test
        @DisplayName("Should provide detailed performance analysis")
        fun shouldProvideDetailedPerformanceAnalysis() {
            val statistics = TestDataFactory.createTetrisStatistics(
                linesCleared = 100,
                piecesPlaced = 150,
                tetrises = 10,
                tSpins = 5,
                maxCombo = 8
            )
            
            val analysis = calculator.getPerformanceAnalysis(statistics)
            
            analysis.efficiencyRating shouldBeGreaterThan 0.0
            analysis.pieceDistributionBalance shouldBeGreaterThan 0.0
            analysis.strongPoints.shouldNotBeEmpty()
            // With good stats, should have fewer improvement areas
        }
        
        @Test
        @DisplayName("Should identify strong points correctly")
        fun shouldIdentifyStrongPointsCorrectly() {
            val excellentStats = TestDataFactory.createTetrisStatistics(
                linesCleared = 200,
                piecesPlaced = 250, // High efficiency
                tetrises = 30, // Excellent Tetris rate
                tSpins = 15, // Good T-Spin execution
                maxCombo = 12 // Strong combo building
            )
            
            val analysis = calculator.getPerformanceAnalysis(excellentStats)
            
            analysis.strongPoints shouldContain "Excellent Tetris rate"
            analysis.strongPoints shouldContain "T-Spin execution"
            analysis.strongPoints shouldContain "Strong combo building"
            analysis.strongPoints shouldContain "High efficiency"
        }
        
        @Test
        @DisplayName("Should identify improvement areas correctly")
        fun shouldIdentifyImprovementAreasCorrectly() {
            val poorStats = TestDataFactory.createTetrisStatistics(
                linesCleared = 20,
                piecesPlaced = 100, // Low efficiency
                tetrises = 0, // No Tetrises
                tSpins = 0, // No T-Spins
                maxCombo = 1 // Poor combo building
            )
            
            val analysis = calculator.getPerformanceAnalysis(poorStats)
            
            analysis.improvementAreas shouldContain "Focus on Tetris setups"
            analysis.improvementAreas shouldContain "Learn T-Spin techniques"
            analysis.improvementAreas shouldContain "Work on combo building"
            analysis.improvementAreas shouldContain "Improve line clearing efficiency"
        }
    }
    
    @Nested
    @DisplayName("AdvancedMockTetrisPerformanceManager Tests")
    inner class AdvancedPerformanceManagerTests {
        
        private lateinit var manager: AdvancedMockTetrisPerformanceManager
        
        @BeforeEach
        fun setup() {
            manager = AdvancedMockTetrisPerformanceManager()
        }
        
        @Test
        @DisplayName("Should provide detailed performance report")
        fun shouldProvideDetailedPerformanceReport() {
            manager.startPerformanceMonitoring()
            
            // Record some operations
            manager.recordOperation("piece_movement", 10L)
            manager.recordOperation("collision_detection", 5L)
            manager.recordOperation("line_clearing", 20L)
            manager.recordOperation("piece_movement", 12L)
            
            manager.stopPerformanceMonitoring()
            
            val report = manager.getDetailedPerformanceReport()
            
            report.totalOperations shouldBeGreaterThan 0L
            report.averageResponseTime shouldBeGreaterThan 0.0
            report.slowestOperation shouldNotBe null
            report.fastestOperation shouldNotBe null
            report.memoryUsage shouldBeGreaterThan 0L
        }
        
        @Test
        @DisplayName("Should detect performance regressions")
        fun shouldDetectPerformanceRegressions() {
            // First monitoring session (baseline)
            manager.startPerformanceMonitoring()
            manager.recordOperation("test_operation", 10L)
            manager.stopPerformanceMonitoring()
            
            // Second monitoring session (regression)
            manager.startPerformanceMonitoring()
            manager.recordOperation("test_operation", 50L) // 5x slower
            manager.stopPerformanceMonitoring()
            
            val regressions = manager.checkForRegressions(20.0)
            
            regressions.shouldNotBeEmpty()
            regressions.first().operation shouldBe "test_operation"
            regressions.first().regressionPercent shouldBeGreaterThan 20.0
        }
        
        @Test
        @DisplayName("Should simulate realistic performance")
        fun shouldSimulateRealisticPerformance() = runTest {
            manager.startPerformanceMonitoring()
            
            manager.simulateRealisticPerformance("test_operation", 100L)
            
            val metrics = manager.getPerformanceMetrics()
            metrics["test_operation"] shouldNotBe null
            metrics["test_operation"]!! shouldBeGreaterThan 0.0
        }
        
        @Test
        @DisplayName("Should identify bottlenecks")
        fun shouldIdentifyBottlenecks() {
            manager.startPerformanceMonitoring()
            
            // Create a clear bottleneck
            manager.recordOperation("fast_operation", 5L)
            manager.recordOperation("slow_operation", 100L) // Much slower
            manager.recordOperation("normal_operation", 15L)
            
            val report = manager.getDetailedPerformanceReport()
            
            report.bottlenecks shouldContain "slow_operation"
        }
        
        @Test
        @DisplayName("Should generate performance recommendations")
        fun shouldGeneratePerformanceRecommendations() {
            manager.startPerformanceMonitoring()
            
            // Create conditions that should trigger recommendations
            manager.recordOperation("bottleneck_operation", 200L)
            manager.recordOperation("normal_operation", 10L)
            
            manager.stopPerformanceMonitoring()
            
            val report = manager.getDetailedPerformanceReport()
            
            report.recommendations.shouldNotBeEmpty()
        }
    }
    
    @Nested
    @DisplayName("AdvancedMockTetrisCacheManager Tests")
    inner class AdvancedCacheManagerTests {
        
        private lateinit var cacheManager: AdvancedMockTetrisCacheManager
        
        @BeforeEach
        fun setup() {
            cacheManager = AdvancedMockTetrisCacheManager(maxCacheSize = 5, ttlMs = 1000L)
        }
        
        @Test
        @DisplayName("Should track cache hit ratio")
        fun shouldTrackCacheHitRatio() {
            val gameState = TestDataFactory.createTetrisGameState()
            
            // Cache a game state
            cacheManager.cacheGameState("test_game", gameState)
            
            // Hit
            cacheManager.getCachedGameState("test_game")
            // Miss
            cacheManager.getCachedGameState("non_existent")
            
            val hitRatio = cacheManager.getCacheHitRatio()
            hitRatio shouldBe 0.5 // 1 hit out of 2 requests
        }
        
        @Test
        @DisplayName("Should provide detailed cache statistics")
        fun shouldProvideDetailedCacheStatistics() {
            val gameState = TestDataFactory.createTetrisGameState()
            
            cacheManager.cacheGameState("game1", gameState)
            cacheManager.getCachedGameState("game1") // Hit
            cacheManager.getCachedGameState("game2") // Miss
            
            val stats = cacheManager.getCacheStatistics()
            
            stats.getHitRatio() shouldBe 0.5
        }
        
        @Test
        @DisplayName("Should evict entries when cache is full")
        fun shouldEvictEntriesWhenCacheIsFull() {
            // Fill cache to capacity
            repeat(5) { i ->
                cacheManager.cacheGameState("game$i", TestDataFactory.createTetrisGameState())
            }
            
            cacheManager.getCacheSize() shouldBe 5
            
            // Add one more - should evict oldest
            cacheManager.cacheGameState("game5", TestDataFactory.createTetrisGameState())
            
            cacheManager.getCacheSize() shouldBe 5
            cacheManager.getCachedGameState("game0") shouldBe null // Should be evicted
        }
        
        @Test
        @DisplayName("Should handle cache expiration")
        fun shouldHandleCacheExpiration() = runTest {
            val gameState = TestDataFactory.createTetrisGameState()
            
            cacheManager.cacheGameState("test_game", gameState)
            
            // Should be available immediately
            cacheManager.getCachedGameState("test_game") shouldNotBe null
            
            // Wait for expiration (TTL is 1000ms in test setup)
            kotlinx.coroutines.delay(1100L)
            
            // Should be expired now
            cacheManager.getCachedGameState("test_game") shouldBe null
        }
        
        @Test
        @DisplayName("Should provide cache performance metrics")
        fun shouldProvideCachePerformanceMetrics() {
            repeat(3) { i ->
                cacheManager.cacheGameState("game$i", TestDataFactory.createTetrisGameState())
            }
            
            // Access some entries multiple times
            cacheManager.getCachedGameState("game0")
            cacheManager.getCachedGameState("game0")
            cacheManager.getCachedGameState("game1")
            
            val metrics = cacheManager.getCachePerformanceMetrics()
            
            metrics.totalEntries shouldBe 3
            metrics.hitRatio shouldBeGreaterThan 0.0
            metrics.averageAccessCount shouldBeGreaterThan 0.0
            metrics.memoryUsageEstimate shouldBeGreaterThan 0L
        }
        
        @Test
        @DisplayName("Should preload frequent states")
        fun shouldPreloadFrequentStates() {
            val initialSize = cacheManager.getCacheSize()
            
            cacheManager.preloadFrequentStates()
            
            cacheManager.getCacheSize() shouldBeGreaterThan initialSize
            cacheManager.getCachedGameState("initial_state") shouldNotBe null
            cacheManager.getCachedGameState("mid_game_state") shouldNotBe null
            cacheManager.getCachedGameState("near_game_over") shouldNotBe null
        }
        
        @Test
        @DisplayName("Should optimize cache by removing least accessed entries")
        fun shouldOptimizeCacheByRemovingLeastAccessedEntries() {
            // Fill cache
            repeat(5) { i ->
                cacheManager.cacheGameState("game$i", TestDataFactory.createTetrisGameState())
            }
            
            // Access some entries more than others
            repeat(5) { cacheManager.getCachedGameState("game0") }
            repeat(3) { cacheManager.getCachedGameState("game1") }
            // game2, game3, game4 have 0 accesses
            
            cacheManager.optimizeCache()
            
            // Should still have the frequently accessed entries
            cacheManager.getCachedGameState("game0") shouldNotBe null
            cacheManager.getCachedGameState("game1") shouldNotBe null
        }
        
        @Test
        @DisplayName("Should estimate memory usage")
        fun shouldEstimateMemoryUsage() {
            repeat(3) { i ->
                cacheManager.cacheGameState("game$i", TestDataFactory.createTetrisGameState())
            }
            
            val metrics = cacheManager.getCachePerformanceMetrics()
            
            // Should estimate ~2KB per entry
            metrics.memoryUsageEstimate shouldBe 3 * 2048L
        }
    }
    
    @Nested
    @DisplayName("Integration Tests")
    inner class IntegrationTests {
        
        @Test
        @DisplayName("Should work together in realistic testing scenario")
        fun shouldWorkTogetherInRealisticTestingScenario() = runTest {
            val statsCalculator = AdvancedMockTetrisStatisticsCalculator()
            val performanceManager = AdvancedMockTetrisPerformanceManager()
            val cacheManager = AdvancedMockTetrisCacheManager()
            
            // Start monitoring
            performanceManager.startPerformanceMonitoring()
            
            // Simulate game session
            val gameState = TestDataFactory.createTetrisGameState()
            cacheManager.cacheGameState("session1", gameState)
            
            // Simulate operations with performance tracking
            performanceManager.simulateRealisticPerformance("cache_read", 5L)
            val cachedState = cacheManager.getCachedGameState("session1")
            
            performanceManager.simulateRealisticPerformance("stats_calculation", 10L)
            val updatedStats = statsCalculator.updateStatistics(
                gameState.statistics,
                TetrisAction.Drop(true, "test_player")
            )
            
            performanceManager.stopPerformanceMonitoring()
            
            // Verify integration
            cachedState shouldNotBe null
            updatedStats.piecesPlaced shouldBeGreaterThan gameState.statistics.piecesPlaced
            
            val performanceReport = performanceManager.getDetailedPerformanceReport()
            performanceReport.totalOperations shouldBeGreaterThan 0L
            
            val cacheMetrics = cacheManager.getCachePerformanceMetrics()
            cacheMetrics.hitRatio shouldBeGreaterThan 0.0
            
            val analysis = statsCalculator.getPerformanceAnalysis(updatedStats)
            analysis.efficiencyRating shouldBeGreaterThan 0.0
        }
    }
}