package com.yu.questicle.core.testing.mock

import com.yu.questicle.core.common.result.Result
import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.domain.model.TetrisAction
import com.yu.questicle.core.domain.model.tetris.*
import com.yu.questicle.core.testing.BaseCoroutineTest
import com.yu.questicle.core.testing.factory.TestDataFactory
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeInstanceOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

/**
 * Unit tests for MockComponentFactory
 * 
 * Tests all mock component implementations to ensure they provide
 * realistic behavior for testing scenarios.
 */
@DisplayName("MockComponentFactory Tests")
class MockComponentFactoryTest : BaseCoroutineTest() {
    
    private lateinit var factory: MockComponentFactory
    
    @BeforeEach
    override fun setUp() {
        super.setUp()
        factory = DefaultMockComponentFactory()
    }
    
    @Nested
    @DisplayName("Factory Creation Tests")
    inner class FactoryCreationTests {
        
        @Test
        @DisplayName("Should create mock TetrisEngine")
        fun shouldCreateMockTetrisEngine() {
            val engine = factory.createMockTetrisEngine()
            
            engine shouldNotBe null
            engine.shouldBeInstanceOf<MockTetrisEngine>()
        }
        
        @Test
        @DisplayName("Should create mock CollisionDetector")
        fun shouldCreateMockCollisionDetector() {
            val detector = factory.createMockCollisionDetector()
            
            detector shouldNotBe null
            detector.shouldBeInstanceOf<MockTetrisCollisionDetector>()
        }
        
        @Test
        @DisplayName("Should create mock GameLogicProcessor")
        fun shouldCreateMockGameLogicProcessor() {
            val processor = factory.createMockGameLogicProcessor()
            
            processor shouldNotBe null
            processor.shouldBeInstanceOf<MockTetrisGameLogicProcessor>()
        }
        
        @Test
        @DisplayName("Should create mock StatisticsCalculator")
        fun shouldCreateMockStatisticsCalculator() {
            val calculator = factory.createMockStatisticsCalculator()
            
            calculator shouldNotBe null
            calculator.shouldBeInstanceOf<MockTetrisStatisticsCalculator>()
        }
        
        @Test
        @DisplayName("Should create mock PerformanceManager")
        fun shouldCreateMockPerformanceManager() {
            val manager = factory.createMockPerformanceManager()
            
            manager shouldNotBe null
            manager.shouldBeInstanceOf<MockTetrisPerformanceManager>()
        }
        
        @Test
        @DisplayName("Should create mock CacheManager")
        fun shouldCreateMockCacheManager() {
            val cacheManager = factory.createMockCacheManager()
            
            cacheManager shouldNotBe null
            cacheManager.shouldBeInstanceOf<MockTetrisCacheManager>()
        }
    }
    
    @Nested
    @DisplayName("MockTetrisEngine Tests")
    inner class MockTetrisEngineTests {
        
        private lateinit var engine: MockTetrisEngine
        
        @BeforeEach
        fun setup() {
            engine = factory.createMockTetrisEngine() as MockTetrisEngine
        }
        
        @Test
        @DisplayName("Should initialize game with valid state")
        fun shouldInitializeGameWithValidState() = runTest {
            val result = engine.initializeGame("test_player")
            
            result.shouldBeInstanceOf<Result.Success<TetrisGameState>>()
            val gameState = (result as Result.Success).data
            
            gameState.status shouldBe TetrisStatus.READY
            gameState.currentPiece shouldNotBe null
            gameState.nextPiece shouldNotBe null
            gameState.score shouldBe 0
            gameState.level shouldBe 1
        }
        
        @Test
        @DisplayName("Should generate different pieces")
        fun shouldGenerateDifferentPieces() {
            val pieces = (1..10).map { engine.generateNextPiece() }
            
            pieces.size shouldBe 10
            // Should have variety in piece types
            pieces.map { it.type }.toSet().size shouldBe 7 // All 7 piece types
        }
        
        @Test
        @DisplayName("Should process move action")
        fun shouldProcessMoveAction() = runTest {
            val gameState = TestDataFactory.createTetrisGameState(
                status = TetrisStatus.PLAYING,
                currentPiece = TestDataFactory.createTetrisPiece(x = 4, y = 5)
            )
            val action = TetrisAction.Move(Direction.LEFT, "test_player")
            
            val result = engine.processAction(action, gameState)
            
            result.shouldBeInstanceOf<Result.Success<TetrisGameState>>()
            val newState = (result as Result.Success).data
            newState.currentPiece?.x shouldBe 3 // Moved left
        }
        
        @Test
        @DisplayName("Should process rotate action")
        fun shouldProcessRotateAction() = runTest {
            val gameState = TestDataFactory.createTetrisGameState(
                status = TetrisStatus.PLAYING,
                currentPiece = TestDataFactory.createTetrisPiece(rotation = 0)
            )
            val action = TetrisAction.Rotate(true, "test_player")
            
            val result = engine.processAction(action, gameState)
            
            result.shouldBeInstanceOf<Result.Success<TetrisGameState>>()
            val newState = (result as Result.Success).data
            newState.currentPiece?.rotation shouldBe 1 // Rotated
        }
        
        @Test
        @DisplayName("Should calculate drop interval based on level")
        fun shouldCalculateDropIntervalBasedOnLevel() {
            engine.calculateDropInterval(1) shouldBe 950L
            engine.calculateDropInterval(5) shouldBe 750L
            engine.calculateDropInterval(10) shouldBe 500L
            engine.calculateDropInterval(20) shouldBe 50L // Minimum
        }
        
        @Test
        @DisplayName("Should calculate line score correctly")
        fun shouldCalculateLineScoreCorrectly() {
            engine.calculateLineScore(1, 1, 0) shouldBe 200 // Single at level 1
            engine.calculateLineScore(2, 1, 0) shouldBe 600 // Double at level 1
            engine.calculateLineScore(4, 2, 0) shouldBe 2400 // Tetris at level 2
            engine.calculateLineScore(1, 1, 2) shouldBe 300 // Single with combo
        }
    }
    
    @Nested
    @DisplayName("MockCollisionDetector Tests")
    inner class MockCollisionDetectorTests {
        
        private lateinit var detector: MockTetrisCollisionDetector
        
        @BeforeEach
        fun setup() {
            detector = factory.createMockCollisionDetector() as MockTetrisCollisionDetector
        }
        
        @Test
        @DisplayName("Should detect boundary collision")
        fun shouldDetectBoundaryCollision() {
            val piece = TestDataFactory.createTetrisPiece(x = -1, y = 0) // Out of bounds
            
            val collision = detector.checkBoundaryCollision(piece, 10, 20)
            
            collision shouldBe true
        }
        
        @Test
        @DisplayName("Should not detect collision for valid position")
        fun shouldNotDetectCollisionForValidPosition() {
            val piece = TestDataFactory.createTetrisPiece(x = 4, y = 0) // Valid position
            val board = TetrisBoard.empty()
            
            val collision = detector.checkCollision(piece, board)
            
            collision shouldBe false
        }
    }
    
    @Nested
    @DisplayName("MockGameLogicProcessor Tests")
    inner class MockGameLogicProcessorTests {
        
        private lateinit var processor: MockTetrisGameLogicProcessor
        
        @BeforeEach
        fun setup() {
            processor = factory.createMockGameLogicProcessor() as MockTetrisGameLogicProcessor
        }
        
        @Test
        @DisplayName("Should calculate next level correctly")
        fun shouldCalculateNextLevelCorrectly() {
            processor.calculateNextLevel(0) shouldBe 1
            processor.calculateNextLevel(9) shouldBe 1
            processor.calculateNextLevel(10) shouldBe 2
            processor.calculateNextLevel(25) shouldBe 3
        }
        
        @Test
        @DisplayName("Should process scoring correctly")
        fun shouldProcessScoringCorrectly() = runTest {
            val gameState = TestDataFactory.createTetrisGameState(score = 1000, level = 2)
            
            val result = processor.processScoring(gameState, 2) // Double line clear
            
            result.shouldBeInstanceOf<Result.Success<TetrisGameState>>()
            val newState = (result as Result.Success).data
            newState.score shouldBe 1900 // 1000 + (300 * 3)
        }
    }
    
    @Nested
    @DisplayName("MockStatisticsCalculator Tests")
    inner class MockStatisticsCalculatorTests {
        
        private lateinit var calculator: MockTetrisStatisticsCalculator
        
        @BeforeEach
        fun setup() {
            calculator = factory.createMockStatisticsCalculator() as MockTetrisStatisticsCalculator
        }
        
        @Test
        @DisplayName("Should calculate pieces per minute")
        fun shouldCalculatePiecesPerMinute() {
            val ppm = calculator.calculatePiecesPerMinute(120, 60000) // 120 pieces in 1 minute
            
            ppm shouldBe 120.0
        }
        
        @Test
        @DisplayName("Should calculate lines per minute")
        fun shouldCalculateLinesPerMinute() {
            val lpm = calculator.calculateLinesPerMinute(30, 120000) // 30 lines in 2 minutes
            
            lpm shouldBe 15.0
        }
        
        @Test
        @DisplayName("Should calculate efficiency")
        fun shouldCalculateEfficiency() {
            val efficiency = calculator.calculateEfficiency(50, 100) // 50 lines from 100 pieces
            
            efficiency shouldBe 0.5
        }
        
        @Test
        @DisplayName("Should handle zero values gracefully")
        fun shouldHandleZeroValuesGracefully() {
            calculator.calculatePiecesPerMinute(100, 0) shouldBe 0.0
            calculator.calculateEfficiency(50, 0) shouldBe 0.0
        }
    }
    
    @Nested
    @DisplayName("MockPerformanceManager Tests")
    inner class MockPerformanceManagerTests {
        
        private lateinit var manager: MockTetrisPerformanceManager
        
        @BeforeEach
        fun setup() {
            manager = factory.createMockPerformanceManager() as MockTetrisPerformanceManager
        }
        
        @Test
        @DisplayName("Should record operations when monitoring")
        fun shouldRecordOperationsWhenMonitoring() {
            manager.startPerformanceMonitoring()
            manager.recordOperation("test_operation", 100L)
            manager.recordOperation("test_operation", 200L)
            
            val metrics = manager.getPerformanceMetrics()
            
            metrics["test_operation"] shouldBe 150.0 // Average of 100 and 200
        }
        
        @Test
        @DisplayName("Should not record operations when not monitoring")
        fun shouldNotRecordOperationsWhenNotMonitoring() {
            manager.recordOperation("test_operation", 100L)
            
            val metrics = manager.getPerformanceMetrics()
            
            metrics.isEmpty() shouldBe true
        }
        
        @Test
        @DisplayName("Should reset metrics")
        fun shouldResetMetrics() {
            manager.startPerformanceMonitoring()
            manager.recordOperation("test_operation", 100L)
            
            manager.resetMetrics()
            val metrics = manager.getPerformanceMetrics()
            
            metrics.isEmpty() shouldBe true
        }
    }
    
    @Nested
    @DisplayName("MockCacheManager Tests")
    inner class MockCacheManagerTests {
        
        private lateinit var cacheManager: MockTetrisCacheManager
        
        @BeforeEach
        fun setup() {
            cacheManager = factory.createMockCacheManager() as MockTetrisCacheManager
        }
        
        @Test
        @DisplayName("Should cache and retrieve game state")
        fun shouldCacheAndRetrieveGameState() {
            val gameState = TestDataFactory.createTetrisGameState()
            
            cacheManager.cacheGameState("test_game", gameState)
            val retrieved = cacheManager.getCachedGameState("test_game")
            
            retrieved shouldBe gameState
        }
        
        @Test
        @DisplayName("Should return null for non-existent cache entry")
        fun shouldReturnNullForNonExistentCacheEntry() {
            val retrieved = cacheManager.getCachedGameState("non_existent")
            
            retrieved shouldBe null
        }
        
        @Test
        @DisplayName("Should track cache size")
        fun shouldTrackCacheSize() {
            cacheManager.getCacheSize() shouldBe 0
            
            cacheManager.cacheGameState("game1", TestDataFactory.createTetrisGameState())
            cacheManager.getCacheSize() shouldBe 1
            
            cacheManager.cacheGameState("game2", TestDataFactory.createTetrisGameState())
            cacheManager.getCacheSize() shouldBe 2
        }
        
        @Test
        @DisplayName("Should clear cache")
        fun shouldClearCache() {
            cacheManager.cacheGameState("game1", TestDataFactory.createTetrisGameState())
            cacheManager.cacheGameState("game2", TestDataFactory.createTetrisGameState())
            
            cacheManager.clearCache()
            
            cacheManager.getCacheSize() shouldBe 0
            cacheManager.getCachedGameState("game1") shouldBe null
        }
    }
    
    @Nested
    @DisplayName("MockComponents Companion Object Tests")
    inner class MockComponentsTests {
        
        @Test
        @DisplayName("Should provide easy access to all mock components")
        fun shouldProvideEasyAccessToAllMockComponents() {
            MockComponents.tetrisEngine().shouldBeInstanceOf<MockTetrisEngine>()
            MockComponents.collisionDetector().shouldBeInstanceOf<MockTetrisCollisionDetector>()
            MockComponents.gameLogicProcessor().shouldBeInstanceOf<MockTetrisGameLogicProcessor>()
            MockComponents.statisticsCalculator().shouldBeInstanceOf<MockTetrisStatisticsCalculator>()
            MockComponents.performanceManager().shouldBeInstanceOf<MockTetrisPerformanceManager>()
            MockComponents.cacheManager().shouldBeInstanceOf<MockTetrisCacheManager>()
        }
    }
}