package com.yu.questicle.core.testing.performance

import com.yu.questicle.core.domain.model.Direction
import com.yu.questicle.core.testing.BaseCoroutineTest
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.doubles.shouldBeGreaterThan
import io.kotest.matchers.ints.shouldBeGreaterThan
import io.kotest.matchers.longs.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.maps.shouldContainKey
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

/**
 * Tests for TetrisPerformanceTestSuite
 */
@DisplayName("TetrisPerformanceTestSuite Tests")
class TetrisPerformanceTestSuiteTest : BaseCoroutineTest() {
    
    private lateinit var performanceTestSuite: TetrisPerformanceTestSuite
    
    @BeforeEach
    override fun setUp() {
        super.setUp()
        performanceTestSuite = TetrisPerformanceTestSuite()
    }
    
    @Nested
    @DisplayName("Individual Performance Tests")
    inner class IndividualPerformanceTests {
        
        @Test
        @DisplayName("Should test piece movement performance")
        fun shouldTestPieceMovementPerformance() = runTest {
            val result = performanceTestSuite.testPieceMovement(Direction.LEFT)
            
            result.testName shouldBe "piece_movement_left"
            result.iterations shouldBe 1000
            result.averageTime shouldBeGreaterThan -1.0
            result.operationsPerSecond shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should test collision detection on empty board")
        fun shouldTestCollisionDetectionEmpty() = runTest {
            val result = performanceTestSuite.testCollisionDetectionEmpty()
            
            result.testName shouldBe "collision_detection_empty"
            result.iterations shouldBe 2000
            result.averageTime shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should test collision detection on full board")
        fun shouldTestCollisionDetectionFull() = runTest {
            val result = performanceTestSuite.testCollisionDetectionFull()
            
            result.testName shouldBe "collision_detection_full"
            result.iterations shouldBe 2000
            result.averageTime shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should test line clearing performance")
        fun shouldTestLineClearingPerformance() = runTest {
            val singleResult = performanceTestSuite.testLineClearingSingle()
            val tetrisResult = performanceTestSuite.testLineClearingTetris()
            
            singleResult.testName shouldBe "line_clearing_single"
            tetrisResult.testName shouldBe "line_clearing_tetris"
            
            singleResult.iterations shouldBe 500
            tetrisResult.iterations shouldBe 500
        }
        
        @Test
        @DisplayName("Should test game state operations")
        fun shouldTestGameStateOperations() = runTest {
            val creationResult = performanceTestSuite.testGameStateCreation()
            val updateResult = performanceTestSuite.testGameStateUpdate()
            
            creationResult.testName shouldBe "game_state_creation"
            updateResult.testName shouldBe "game_state_update"
            
            creationResult.iterations shouldBe 1000
            updateResult.iterations shouldBe 1000
        }
    }
    
    @Nested
    @DisplayName("Memory Usage Tests")
    inner class MemoryUsageTests {
        
        @Test
        @DisplayName("Should test piece generation memory usage")
        fun shouldTestPieceGenerationMemoryUsage() = runTest {
            val result = performanceTestSuite.testPieceGenerationMemory()
            
            result.initialMemory shouldBeGreaterThan 0L
            result.finalMemory shouldBeGreaterThan 0L
            result.peakMemory shouldBeGreaterThan 0L
        }
        
        @Test
        @DisplayName("Should test board operations memory usage")
        fun shouldTestBoardOperationsMemoryUsage() = runTest {
            val result = performanceTestSuite.testBoardOperationsMemory()
            
            result.initialMemory shouldBeGreaterThan 0L
            result.finalMemory shouldBeGreaterThan 0L
            result.peakMemory shouldBeGreaterThan 0L
        }
        
        @Test
        @DisplayName("Should test statistics calculation memory usage")
        fun shouldTestStatisticsCalculationMemoryUsage() = runTest {
            val result = performanceTestSuite.testStatisticsCalculationMemory()
            
            result.initialMemory shouldBeGreaterThan 0L
            result.finalMemory shouldBeGreaterThan 0L
            result.peakMemory shouldBeGreaterThan 0L
        }
    }
    
    @Nested
    @DisplayName("Benchmark Tests")
    inner class BenchmarkTests {
        
        @Test
        @DisplayName("Should run piece rotation benchmark")
        fun shouldRunPieceRotationBenchmark() = runTest {
            val result = performanceTestSuite.testPieceRotationBenchmark()
            
            result.benchmarkName shouldNotBe ""
            result.mode shouldBe BenchmarkMode.AVERAGE_TIME
            result.score shouldBeGreaterThan 0.0
            result.samples shouldBeGreaterThan 0
        }
        
        @Test
        @DisplayName("Should run board validation benchmark")
        fun shouldRunBoardValidationBenchmark() = runTest {
            val result = performanceTestSuite.testBoardValidationBenchmark()
            
            result.benchmarkName shouldNotBe ""
            result.score shouldBeGreaterThan 0.0
            result.samples shouldBeGreaterThan 0
        }
        
        @Test
        @DisplayName("Should run score calculation benchmark")
        fun shouldRunScoreCalculationBenchmark() = runTest {
            val result = performanceTestSuite.testScoreCalculationBenchmark()
            
            result.benchmarkName shouldNotBe ""
            result.score shouldBeGreaterThan 0.0
            result.samples shouldBeGreaterThan 0
        }
    }
    
    @Nested
    @DisplayName("Comprehensive Test Suite")
    inner class ComprehensiveTestSuite {
        
        @Test
        @DisplayName("Should run all performance tests")
        fun shouldRunAllPerformanceTests() = runTest {
            val results = performanceTestSuite.runAllTests()
            
            // Verify performance results
            results.performanceResults.size shouldBeGreaterThan 0
            results.performanceResults shouldContainKey "piece_movement_left"
            results.performanceResults shouldContainKey "collision_detection_empty"
            results.performanceResults shouldContainKey "line_clearing_single"
            
            // Verify memory results
            results.memoryResults.size shouldBeGreaterThan 0
            results.memoryResults shouldContainKey "piece_generation"
            results.memoryResults shouldContainKey "board_operations"
            
            // Verify benchmark results
            results.benchmarkResults.size shouldBeGreaterThan 0
            results.benchmarkResults shouldContainKey "piece_rotation"
            results.benchmarkResults shouldContainKey "board_validation"
        }
        
        @Test
        @DisplayName("Should generate comprehensive summary")
        fun shouldGenerateComprehensiveSummary() = runTest {
            val results = performanceTestSuite.runAllTests()
            val summary = results.getSummary()
            
            summary shouldContain "Tetris Performance Test Results"
            summary shouldContain "Performance Tests:"
            summary shouldContain "Memory Tests:"
            summary shouldContain "Benchmark Tests:"
            summary shouldContain "piece_movement_left"
            summary shouldContain "piece_generation"
            summary shouldContain "piece_rotation"
        }
        
        @Test
        @DisplayName("Should validate performance thresholds")
        fun shouldValidatePerformanceThresholds() = runTest {
            val results = performanceTestSuite.runAllTests()
            
            // Create lenient thresholds for testing
            val thresholds = PerformanceThresholds(
                performanceThresholds = mapOf(
                    "piece_movement_left" to 100.0, // Very lenient
                    "collision_detection_empty" to 100.0,
                    "line_clearing_single" to 100.0
                ),
                memoryThresholds = mapOf(
                    "piece_generation" to 100 * 1024 * 1024L, // 100MB - very lenient
                    "board_operations" to 100 * 1024 * 1024L
                )
            )
            
            val meetsThresholds = results.meetsPerformanceThresholds(thresholds)
            // Should meet lenient thresholds
            meetsThresholds shouldBe true
        }
    }
    
    @Nested
    @DisplayName("Stress and Load Tests")
    inner class StressAndLoadTests {
        
        @Test
        @DisplayName("Should run stress test")
        fun shouldRunStressTest() = runTest {
            val result = performanceTestSuite.runStressTest()
            
            result.testName shouldBe "stress_test_high_load"
            result.iterations shouldBe 100
            result.averageTime shouldBeGreaterThan -1.0
            result.operationsPerSecond shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should run concurrent test")
        fun shouldRunConcurrentTest() = runTest {
            val result = performanceTestSuite.runConcurrentTest()
            
            result.testName shouldBe "concurrent_operations"
            result.iterations shouldBe 200
            result.averageTime shouldBeGreaterThan -1.0
            result.operationsPerSecond shouldBeGreaterThan -1.0
        }
    }
    
    @Nested
    @DisplayName("Performance Analysis")
    inner class PerformanceAnalysis {
        
        @Test
        @DisplayName("Should compare different operation types")
        fun shouldCompareDifferentOperationTypes() = runTest {
            val leftMovement = performanceTestSuite.testPieceMovement(Direction.LEFT)
            val rightMovement = performanceTestSuite.testPieceMovement(Direction.RIGHT)
            val downMovement = performanceTestSuite.testPieceMovement(Direction.DOWN)
            
            // All movement operations should have similar performance characteristics
            leftMovement.iterations shouldBe rightMovement.iterations
            rightMovement.iterations shouldBe downMovement.iterations
            
            // All should complete successfully
            leftMovement.averageTime shouldBeGreaterThan -1.0
            rightMovement.averageTime shouldBeGreaterThan -1.0
            downMovement.averageTime shouldBeGreaterThan -1.0
        }
        
        @Test
        @DisplayName("Should analyze collision detection performance differences")
        fun shouldAnalyzeCollisionDetectionPerformanceDifferences() = runTest {
            val emptyBoardResult = performanceTestSuite.testCollisionDetectionEmpty()
            val fullBoardResult = performanceTestSuite.testCollisionDetectionFull()
            
            // Both should complete successfully
            emptyBoardResult.averageTime shouldBeGreaterThan -1.0
            fullBoardResult.averageTime shouldBeGreaterThan -1.0
            
            // Both should have same number of iterations
            emptyBoardResult.iterations shouldBe fullBoardResult.iterations
        }
        
        @Test
        @DisplayName("Should analyze line clearing performance scaling")
        fun shouldAnalyzeLineClearingPerformanceScaling() = runTest {
            val singleLineResult = performanceTestSuite.testLineClearingSingle()
            val tetrisResult = performanceTestSuite.testLineClearingTetris()
            
            // Both should complete successfully
            singleLineResult.averageTime shouldBeGreaterThan -1.0
            tetrisResult.averageTime shouldBeGreaterThan -1.0
            
            // Same number of iterations for fair comparison
            singleLineResult.iterations shouldBe tetrisResult.iterations
        }
    }
    
    @Nested
    @DisplayName("Results Analysis")
    inner class ResultsAnalysis {
        
        @Test
        @DisplayName("Should format memory usage correctly")
        fun shouldFormatMemoryUsageCorrectly() = runTest {
            val results = performanceTestSuite.runAllTests()
            val summary = results.getSummary()
            
            // Should contain memory formatting
            summary shouldContain "allocated"
            // Should contain either bytes, KB, or MB
            val hasMemoryUnit = summary.contains("bytes") || 
                               summary.contains("KB") || 
                               summary.contains("MB")
            hasMemoryUnit shouldBe true
        }
        
        @Test
        @DisplayName("Should provide detailed benchmark information")
        fun shouldProvideDetailedBenchmarkInformation() = runTest {
            val results = performanceTestSuite.runAllTests()
            val summary = results.getSummary()
            
            // Should contain benchmark details
            summary shouldContain "ms/op"
            results.benchmarkResults.values.forEach { result ->
                result.scoreUnit shouldBe "ms/op"
                result.mode shouldBe BenchmarkMode.AVERAGE_TIME
            }
        }
        
        @Test
        @DisplayName("Should handle threshold validation correctly")
        fun shouldHandleThresholdValidationCorrectly() = runTest {
            val results = performanceTestSuite.runAllTests()
            
            // Create very strict thresholds that should fail
            val strictThresholds = PerformanceThresholds(
                performanceThresholds = mapOf(
                    "piece_movement_left" to 0.001 // 0.001ms - very strict
                ),
                memoryThresholds = mapOf(
                    "piece_generation" to 1L // 1 byte - very strict
                )
            )
            
            val meetsStrictThresholds = results.meetsPerformanceThresholds(strictThresholds)
            // Should not meet very strict thresholds
            meetsStrictThresholds shouldBe false
        }
    }
}