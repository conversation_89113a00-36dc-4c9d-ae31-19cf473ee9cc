package com.yu.questicle.core.testing.benchmarks

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.io.TempDir
import java.io.File

class BenchmarkReportGeneratorTest {

    @Test
    fun `generateHtmlReport should create valid HTML file`(@TempDir tempDir: File) {
        // Given
        val jsonContent = """
            [
                {
                    "benchmark": "com.example.TestBenchmark.testMethod",
                    "mode": "avgt",
                    "threads": 1,
                    "forks": 1,
                    "jvm": "OpenJDK 64-Bit Server VM",
                    "jvmArgs": [],
                    "jdkVersion": "11.0.1",
                    "vmName": "OpenJDK 64-Bit Server VM",
                    "vmVersion": "11.0.1+13",
                    "warmupIterations": 3,
                    "warmupTime": "1 s",
                    "measurementIterations": 5,
                    "measurementTime": "1 s",
                    "primaryMetric": {
                        "score": 100.5,
                        "scoreError": 5.2,
                        "scoreConfidence": [95.3, 105.7],
                        "scorePercentiles": {
                            "0.0": 90.0,
                            "50.0": 100.0,
                            "90.0": 110.0,
                            "95.0": 115.0,
                            "99.0": 120.0,
                            "99.9": 125.0,
                            "99.99": 130.0,
                            "100.0": 135.0
                        },
                        "scoreUnit": "us/op",
                        "rawData": [[100.1, 99.8, 101.2, 100.0, 100.9]]
                    }
                }
            ]
        """.trimIndent()

        val resultsFile = File(tempDir, "results.json")
        resultsFile.writeText(jsonContent)
        
        val outputFile = File(tempDir, "report.html")
        val generator = BenchmarkReportGenerator()

        // When
        generator.generateHtmlReport(resultsFile, outputFile)

        // Then
        assert(outputFile.exists())
        val content = outputFile.readText()
        assert(content.contains("<!DOCTYPE html"))
        assert(content.contains("TestBenchmark.testMethod"))
        assert(content.contains("100.500"))
    }

    @Test
    fun `generateJsonReport should create valid JSON file`(@TempDir tempDir: File) {
        // Given
        val jsonContent = """
            [
                {
                    "benchmark": "com.example.TestBenchmark.testMethod",
                    "mode": "avgt",
                    "threads": 1,
                    "forks": 1,
                    "jvm": "OpenJDK 64-Bit Server VM",
                    "jvmArgs": [],
                    "jdkVersion": "11.0.1",
                    "vmName": "OpenJDK 64-Bit Server VM",
                    "vmVersion": "11.0.1+13",
                    "warmupIterations": 3,
                    "warmupTime": "1 s",
                    "measurementIterations": 5,
                    "measurementTime": "1 s",
                    "primaryMetric": {
                        "score": 100.5,
                        "scoreError": 5.2,
                        "scoreConfidence": [95.3, 105.7],
                        "scorePercentiles": {
                            "0.0": 90.0,
                            "50.0": 100.0,
                            "90.0": 110.0,
                            "95.0": 115.0,
                            "99.0": 120.0,
                            "99.9": 125.0,
                            "99.99": 130.0,
                            "100.0": 135.0
                        },
                        "scoreUnit": "us/op",
                        "rawData": [[100.1, 99.8, 101.2, 100.0, 100.9]]
                    }
                }
            ]
        """.trimIndent()

        val resultsFile = File(tempDir, "results.json")
        resultsFile.writeText(jsonContent)
        
        val outputFile = File(tempDir, "report.json")
        val generator = BenchmarkReportGenerator()

        // When
        generator.generateJsonReport(resultsFile, outputFile)

        // Then
        assert(outputFile.exists())
        val content = outputFile.readText()
        assert(content.contains("\"totalBenchmarks\": 1"))
        assert(content.contains("\"averageScore\": 100.5"))
    }
}