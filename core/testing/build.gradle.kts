plugins {
    id("questicle.android.library")
    alias(libs.plugins.android.junit5)
    kotlin("kapt")
}

android {
    namespace = "com.yu.questicle.core.testing"
}

dependencies {
    // Core testing frameworks (JUnit 5) - 统一使用JUnit 5
    api(libs.junit5.api)
    api(libs.junit5.engine)
    api(libs.junit5.params)

    // Mock frameworks
    api(libs.mockk)
    api(libs.mockk.android)

    // Flow testing
    api(libs.turbine)

    // Assertions - 使用Truth和Kotest作为补充
    api(libs.kotest.assertions.core)
    api(libs.truth)
    api(libs.kotest.runner.junit5)
    api(libs.kotest.property)

    // Android testing
    api(libs.androidx.test.core)
    api(libs.androidx.test.ext.junit)
    api(libs.robolectric)
    api(libs.robolectric.junit5.extension)

    // Coroutines testing
    api(libs.kotlinx.coroutines.test)

    // Compose testing (JUnit 5)
    api(libs.androidx.compose.ui.test.android)
    api(libs.androidx.compose.ui.test.manifest)

    // Hilt testing support
    api("com.google.dagger:hilt-android-testing:2.52")
    api("androidx.test:core:1.6.1")

    // JMH performance testing framework - using implementation to avoid conflicts
    implementation(libs.jmh.core)
    kapt(libs.jmh.generator.annprocess)
    
    // JSON parsing for JMH results
    implementation("org.json:json:20231013")

    // Internal module dependencies for accessing shared domain and common classes used in tests
    api(project(":core:common"))
    api(project(":core:domain"))
}

// Custom JMH benchmark runner task
tasks.register<JavaExec>("runJmhBenchmarks") {
    group = "benchmark"
    description = "Run all JMH benchmarks"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    
    // Ensure output directory exists
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Engine benchmark tasks
tasks.register<JavaExec>("runEngineBenchmarks") {
    group = "benchmark"
    description = "Run Tetris engine benchmarks"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    args = listOf("engine")
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Rendering benchmark tasks
tasks.register<JavaExec>("runRenderingBenchmarks") {
    group = "benchmark"
    description = "Run Tetris rendering benchmarks"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    args = listOf("rendering")
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Memory benchmark tasks
tasks.register<JavaExec>("runMemoryBenchmarks") {
    group = "benchmark"
    description = "Run Tetris memory benchmarks"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    args = listOf("memory")
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Network benchmark tasks
tasks.register<JavaExec>("runNetworkBenchmarks") {
    group = "benchmark"
    description = "Run Tetris network benchmarks"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    args = listOf("network")
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Quick benchmark task for development
tasks.register<JavaExec>("runQuickBenchmarks") {
    group = "benchmark"
    description = "Run quick benchmarks with reduced iterations"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.ComprehensiveBenchmarkRunner")
    args = listOf("quick")
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/jmh").mkdirs()
    }
}

// Production benchmark reporting tasks
tasks.register<JavaExec>("generateBenchmarkReport") {
    group = "benchmark"
    description = "Generate comprehensive benchmark report"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.BenchmarkReportCli")
    
    args = listOf(
        "generate",
        "${project.layout.buildDirectory.get()}/reports/jmh/results.json",
        "${project.layout.buildDirectory.get()}/reports/benchmark"
    )
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/benchmark").mkdirs()
    }
}

tasks.register<JavaExec>("compareBenchmarkResults") {
    group = "benchmark"
    description = "Compare benchmark results for regression detection"
    
    dependsOn("compileDemoDebugKotlin")
    
    classpath = configurations["demoDebugRuntimeClasspath"] + files("${project.layout.buildDirectory.get()}/tmp/kotlin-classes/demoDebug")
    mainClass.set("com.yu.questicle.core.testing.benchmarks.BenchmarkReportCli")
    
    val baselineFile = project.findProperty("baseline") ?: "baseline.json"
    val currentFile = project.findProperty("current") ?: "current.json"
    
    args = listOf(
        "compare",
        baselineFile.toString(),
        currentFile.toString(),
        "${project.layout.buildDirectory.get()}/reports/benchmark"
    )
    
    doFirst {
        file("${project.layout.buildDirectory.get()}/reports/benchmark").mkdirs()
    }
}