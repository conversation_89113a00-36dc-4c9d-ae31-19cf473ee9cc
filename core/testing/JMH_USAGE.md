# JMH Benchmarking Setup

This document describes how to use the JMH (Java Microbenchmark Harness) configuration in the testing module.

## Overview

JMH has been integrated into the core/testing module to enable performance benchmarking of Tetris game components.

## Configuration

### Dependencies
- JMH Core: `org.openjdk.jmh:jmh-core:1.37`
- JMH Annotation Processor: `org.openjdk.jmh:jmh-generator-annprocess:1.37`

### Build Configuration
The build.gradle.kts includes:
- KAPT plugin for annotation processing
- JMH dependencies
- Custom Gradle tasks for running benchmarks

## Available Gradle Tasks

### Benchmark Tasks
- `runAllBenchmarks` - Run all JMH benchmarks (comprehensive suite)
- `runMicroBenchmarks` - Run micro-benchmarks for individual operations
- `runMacroBenchmarks` - Run macro-benchmarks for complete game scenarios  
- `runMemoryBenchmarks` - Run memory allocation benchmarks
- `runThroughputBenchmarks` - Run throughput benchmarks for high-frequency operations
- `runQuickBenchmarks` - Run benchmarks with reduced iterations for development

### Usage Examples
```bash
# Run all benchmarks (comprehensive)
./gradlew :core:testing:runAllBenchmarks

# Run micro-benchmarks (individual operations)
./gradlew :core:testing:runMicroBenchmarks

# Run macro-benchmarks (complete scenarios)
./gradlew :core:testing:runMacroBenchmarks

# Run memory allocation benchmarks
./gradlew :core:testing:runMemoryBenchmarks

# Run throughput benchmarks
./gradlew :core:testing:runThroughputBenchmarks

# Run quick benchmarks for development
./gradlew :core:testing:runQuickBenchmarks
```

## Benchmark Results

### Output Location
Benchmark results are saved as JSON files in:
- `build/reports/jmh/all-benchmarks.json` - All benchmarks
- `build/reports/jmh/micro-benchmarks.json` - Micro-benchmarks
- `build/reports/jmh/macro-benchmarks.json` - Macro-benchmarks
- `build/reports/jmh/memory-benchmarks.json` - Memory allocation benchmarks
- `build/reports/jmh/throughput-benchmarks.json` - Throughput benchmarks
- `build/reports/jmh/quick-results.json` - Quick benchmarks

### Result Analysis
Use the `JmhResultAnalyzer` class to:
- Parse JMH JSON results
- Generate HTML reports
- Compare benchmark results
- Detect performance regressions

```kotlin
val analyzer = JmhResultAnalyzer()
val results = analyzer.parseResults(File("build/reports/jmh/results.json"))
analyzer.generateHtmlReport(results, File("build/reports/jmh/report.html"))
```

## Writing Benchmarks

### Basic Benchmark Structure
```kotlin
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MICROSECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
open class MyBenchmark {

    @Setup
    fun setup() {
        // Initialize test data
    }

    @Benchmark
    fun benchmarkMethod(blackhole: Blackhole) {
        // Benchmark code here
        val result = performOperation()
        blackhole.consume(result)
    }
}
```

### Best Practices
1. Use `@State(Scope.Benchmark)` for shared state
2. Always consume results with `Blackhole.consume()`
3. Use `@Setup` and `@TearDown` for initialization/cleanup
4. Keep benchmarks focused on specific operations
5. Use appropriate benchmark modes (AverageTime, Throughput, etc.)

## Current Benchmarks

### TetrisEngineMicroBenchmarks
Micro-benchmarks for individual engine operations:
- Collision detection (empty, partial, full boards)
- Piece movement (single, multiple positions)
- Piece rotation (single, all rotations, all piece types)
- Line clear detection and execution
- Piece placement operations
- Data structure operations (board/piece copying)

### TetrisGameMacroBenchmarks
Macro-benchmarks for complete game scenarios:
- Complete game rounds (beginner to expert levels)
- Multi-piece sequence processing (10-100 pieces)
- Line clearing scenarios (single, double, triple, tetris)
- High-speed gameplay simulation
- Complex game state handling
- Concurrent gameplay scenarios

### TetrisMemoryAllocationBenchmarks
Memory allocation benchmarks for critical paths:
- Object creation (boards, pieces, game states)
- Object copying (shallow vs deep copy)
- Collection operations (lists, maps, sets)
- String operations and concatenation
- Game logic memory allocation patterns
- Cache and performance monitoring operations
- Memory pressure testing

### TetrisThroughputBenchmarks
Throughput benchmarks for high-frequency operations:
- Collision detection throughput
- Piece movement and rotation throughput
- Line clearing throughput
- Data structure operation throughput
- Statistics calculation throughput
- Cache operation throughput
- Comprehensive game operation throughput

### SimpleBenchmark
A basic benchmark that tests:
- Array sum operations
- String concatenation
- List operations
- Map operations

This benchmark serves as a configuration validation and example.

## Integration with Performance Testing

The JMH configuration integrates with the performance testing framework:
- Results can be analyzed using `PerformanceRegressionDetector`
- Benchmark data feeds into performance trend analysis
- Automated regression detection for CI/CD pipelines

## Troubleshooting

### Common Issues
1. **Compilation Errors**: Ensure KAPT is properly configured
2. **Missing Dependencies**: Check that JMH dependencies are included
3. **Classpath Issues**: Verify Android library configuration compatibility

### Android Library Compatibility
JMH integration with Android libraries has some limitations:
- Use KAPT instead of KSP for annotation processing
- Custom JavaExec tasks are used instead of the JMH Gradle plugin
- Classpath configuration is adapted for Android module structure

## Future Enhancements

Planned improvements:
1. Integration with Tetris domain models
2. Comprehensive game scenario benchmarks
3. Memory allocation profiling
4. Automated performance regression detection in CI
5. Performance trend visualization