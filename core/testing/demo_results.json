[{"benchmark": "com.yu.questicle.core.testing.benchmarks.TetrisEngineBenchmarkSuite.benchmarkGameStateInitialization", "mode": "avgt", "threads": 1, "forks": 1, "jvm": "OpenJDK 64-Bit Server VM", "jvmArgs": [], "jdkVersion": "11.0.1", "vmName": "OpenJDK 64-Bit Server VM", "vmVersion": "11.0.1+13", "warmupIterations": 3, "warmupTime": "1 s", "measurementIterations": 5, "measurementTime": "1 s", "primaryMetric": {"score": 100.5, "scoreError": 5.2, "scoreConfidence": [95.3, 105.7], "scorePercentiles": {"0.0": 90.0, "50.0": 100.0, "90.0": 110.0, "95.0": 115.0, "99.0": 120.0, "99.9": 125.0, "99.99": 130.0, "100.0": 135.0}, "scoreUnit": "us/op", "rawData": [[100.1, 99.8, 101.2, 100.0, 100.9]]}}, {"benchmark": "com.yu.questicle.core.testing.benchmarks.TetrisRenderingBenchmarkSuite.benchmarkRenderEmptyBoard", "mode": "avgt", "threads": 1, "forks": 1, "jvm": "OpenJDK 64-Bit Server VM", "jvmArgs": [], "jdkVersion": "11.0.1", "vmName": "OpenJDK 64-Bit Server VM", "vmVersion": "11.0.1+13", "warmupIterations": 3, "warmupTime": "1 s", "measurementIterations": 5, "measurementTime": "1 s", "primaryMetric": {"score": 50.2, "scoreError": 2.1, "scoreConfidence": [48.1, 52.3], "scorePercentiles": {"0.0": 45.0, "50.0": 50.0, "90.0": 55.0, "95.0": 57.0, "99.0": 60.0, "99.9": 62.0, "99.99": 65.0, "100.0": 68.0}, "scoreUnit": "us/op", "rawData": [[50.1, 49.8, 51.2, 50.0, 49.9]]}}]