plugins {
    id("questicle.android.library.compose")
    alias(libs.plugins.android.junit5)
}

android {
    namespace = "com.yu.questicle.core.ui"
}

dependencies {
    // 依赖designsystem模块获取设计系统组件
    implementation(project(":core:designsystem"))
    
    // Compose核心依赖
    implementation(libs.androidx.compose.runtime)
    implementation(libs.androidx.compose.ui)
    implementation(libs.androidx.compose.ui.graphics)
    implementation(libs.androidx.compose.ui.tooling.preview)
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.compose.material.icons.extended)
    implementation(libs.androidx.compose.animation)
    implementation(libs.androidx.compose.animation.core)
    implementation(libs.androidx.compose.animation.graphics)
    
    // 标准化测试依赖 - 由 convention plugin 统一管理
    testImplementation(project(":core:testing"))
    testImplementation(libs.bundles.unit.testing)

    // Compose测试依赖 - 由 convention plugin 统一管理
    testImplementation(libs.bundles.compose.testing)
    debugImplementation(libs.androidx.compose.ui.test.manifest)
} 