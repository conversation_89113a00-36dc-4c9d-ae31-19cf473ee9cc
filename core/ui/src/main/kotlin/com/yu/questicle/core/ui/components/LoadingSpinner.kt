package com.yu.questicle.core.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * 加载动画尺寸
 */
enum class LoadingSpinnerSize(
    val size: androidx.compose.ui.unit.Dp,
    val strokeWidth: androidx.compose.ui.unit.Dp
) {
    Small(16.dp, 2.dp),
    Medium(24.dp, 3.dp),
    Large(32.dp, 4.dp),
    ExtraLarge(48.dp, 6.dp)
}

/**
 * 简单的加载组件
 */
@Composable
fun LoadingSpinner(
    modifier: Modifier = Modifier,
    size: LoadingSpinnerSize = LoadingSpinnerSize.Medium
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(size.size),
            strokeWidth = size.strokeWidth
        )
    }
}

/**
 * 全屏加载组件
 */
@Composable
fun FullScreenLoading(
    modifier: Modifier = Modifier,
    message: String? = null
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            LoadingSpinner(
                size = LoadingSpinnerSize.Large,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            if (message != null) {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Preview
@Composable
private fun LoadingSpinnerPreview() {
    MaterialTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            LoadingSpinner(size = LoadingSpinnerSize.Small)
            LoadingSpinner(size = LoadingSpinnerSize.Medium)
            LoadingSpinner(size = LoadingSpinnerSize.Large)
            LoadingSpinner(size = LoadingSpinnerSize.ExtraLarge)
        }
    }
}

@Preview
@Composable
private fun FullScreenLoadingPreview() {
    MaterialTheme {
        FullScreenLoading(message = "Loading...")
    }
} 