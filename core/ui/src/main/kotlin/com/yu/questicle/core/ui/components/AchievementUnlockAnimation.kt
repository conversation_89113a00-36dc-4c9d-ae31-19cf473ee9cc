package com.yu.questicle.core.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import androidx.compose.ui.graphics.graphicsLayer

/**
 * 成就解锁动画组件
 * Achievement unlock animation component
 */
@Composable
fun AchievementUnlockAnimation(
    achievement: AchievementUnlockData,
    isVisible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            AchievementUnlockContent(
                achievement = achievement,
                onDismiss = onDismiss,
                modifier = modifier
            )
        }
    }
}

@Composable
private fun AchievementUnlockContent(
    achievement: AchievementUnlockData,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var animationPhase by remember { mutableIntStateOf(0) }
    
    // 动画状态
    val scaleAnimation by animateFloatAsState(
        targetValue = when (animationPhase) {
            0 -> 0f
            1 -> 1.2f
            else -> 1f
        },
        animationSpec = tween(
            durationMillis = when (animationPhase) {
                0 -> 0
                1 -> 300
                else -> 200
            },
            easing = FastOutSlowInEasing
        ),
        label = "scale_animation"
    )
    
    val alphaAnimation by animateFloatAsState(
        targetValue = if (animationPhase >= 1) 1f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "alpha_animation"
    )
    
    val rotationAnimation by animateFloatAsState(
        targetValue = if (animationPhase >= 2) 360f else 0f,
        animationSpec = tween(durationMillis = 800, easing = LinearOutSlowInEasing),
        label = "rotation_animation"
    )
    
    // 启动动画序列
    LaunchedEffect(Unit) {
        animationPhase = 1
        delay(300)
        animationPhase = 2
        delay(2000) // 显示2秒
        onDismiss()
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.7f)),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .padding(32.dp)
                .scale(scaleAnimation)
                .wrapContentSize(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .wrapContentSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 成就图标
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .background(
                            color = achievement.iconBackgroundColor,
                            shape = RoundedCornerShape(40.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = achievement.icon,
                        contentDescription = null,
                        modifier = Modifier
                            .size(48.dp)
                            .graphicsLayer {
                                rotationZ = rotationAnimation
                            },
                        tint = achievement.iconColor
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // "Achievement Unlocked!" 文本
                Text(
                    text = "Achievement Unlocked!",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.alpha(alphaAnimation)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 成就标题
                Text(
                    text = achievement.title,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.alpha(alphaAnimation)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 成就描述
                Text(
                    text = achievement.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.alpha(alphaAnimation)
                )
                
                if (achievement.experienceReward > 0) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 经验值奖励
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.alpha(alphaAnimation)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "+${achievement.experienceReward} XP",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
        
        // 粒子效果（简化版）
        if (animationPhase >= 2) {
            ParticleEffect(
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
private fun ParticleEffect(
    modifier: Modifier = Modifier
) {
    // 简化的粒子效果
    val particles = remember { 
        List(12) { index ->
            ParticleData(
                id = index,
                startAngle = index * 30f,
                color = if (index % 2 == 0) Color.Yellow else Color.White
            )
        }
    }
    
    particles.forEach { particle ->
        ParticleItem(
            particle = particle,
            modifier = modifier
        )
    }
}

@Composable
private fun ParticleItem(
    particle: ParticleData,
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }
    
    val offsetAnimation by animateFloatAsState(
        targetValue = if (isVisible) 100f else 0f,
        animationSpec = tween(durationMillis = 1000, easing = LinearOutSlowInEasing),
        label = "particle_offset"
    )
    
    val alphaAnimation by animateFloatAsState(
        targetValue = if (isVisible) 0f else 1f,
        animationSpec = tween(durationMillis = 1000),
        label = "particle_alpha"
    )
    
    LaunchedEffect(Unit) {
        delay(particle.id * 50L) // 错开启动时间
        isVisible = true
    }
    
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .offset(
                    x = (offsetAnimation * kotlin.math.cos(Math.toRadians(particle.startAngle.toDouble()))).dp,
                    y = (offsetAnimation * kotlin.math.sin(Math.toRadians(particle.startAngle.toDouble()))).dp
                )
                .size(4.dp)
                .background(
                    color = particle.color.copy(alpha = alphaAnimation),
                    shape = RoundedCornerShape(2.dp)
                )
        )
    }
}

/**
 * 成就解锁数据
 */
data class AchievementUnlockData(
    val id: String,
    val title: String,
    val description: String,
    val icon: ImageVector = Icons.Default.Star,
    val iconColor: Color = Color.White,
    val iconBackgroundColor: Color = Color.Blue,
    val experienceReward: Long = 0L
)

/**
 * 粒子数据
 */
private data class ParticleData(
    val id: Int,
    val startAngle: Float,
    val color: Color
)

@Preview
@Composable
private fun AchievementUnlockAnimationPreview() {
    MaterialTheme {
        AchievementUnlockAnimation(
            achievement = AchievementUnlockData(
                id = "first_game",
                title = "First Steps",
                description = "Complete your first game",
                experienceReward = 100L
            ),
            isVisible = true,
            onDismiss = {}
        )
    }
}
