package com.yu.questicle.core.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 通用确认对话框组件
 * Universal confirmation dialog component
 */
@Composable
fun ConfirmationDialog(
    title: String,
    message: String,
    confirmText: String = "确认",
    dismissText: String = "取消",
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    isDestructive: Boolean = false,
    modifier: Modifier = Modifier
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall,
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = if (isDestructive) {
                        MaterialTheme.colorScheme.error
                    } else {
                        MaterialTheme.colorScheme.primary
                    }
                )
            ) {
                Text(confirmText)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(dismissText)
            }
        },
        modifier = modifier
    )
}

/**
 * 删除确认对话框
 */
@Composable
fun DeleteConfirmationDialog(
    title: String = "确认删除",
    message: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        title = title,
        message = message,
        confirmText = "删除",
        dismissText = "取消",
        onConfirm = onConfirm,
        onDismiss = onDismiss,
        isDestructive = true,
        modifier = modifier
    )
}

/**
 * 退出确认对话框
 */
@Composable
fun ExitConfirmationDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    ConfirmationDialog(
        title = "确认退出",
        message = "确定要退出应用吗？未保存的进度将会丢失。",
        confirmText = "退出",
        dismissText = "取消",
        onConfirm = onConfirm,
        onDismiss = onDismiss,
        isDestructive = true,
        modifier = modifier
    )
}

@Preview
@Composable
private fun ConfirmationDialogPreview() {
    MaterialTheme {
        ConfirmationDialog(
            title = "确认操作",
            message = "确定要执行此操作吗？",
            onConfirm = {},
            onDismiss = {}
        )
    }
}

@Preview
@Composable
private fun DeleteConfirmationDialogPreview() {
    MaterialTheme {
        DeleteConfirmationDialog(
            message = "确定要删除这个项目吗？此操作无法撤销。",
            onConfirm = {},
            onDismiss = {}
        )
    }
}

@Preview
@Composable
private fun ExitConfirmationDialogPreview() {
    MaterialTheme {
        ExitConfirmationDialog(
            onConfirm = {},
            onDismiss = {}
        )
    }
} 