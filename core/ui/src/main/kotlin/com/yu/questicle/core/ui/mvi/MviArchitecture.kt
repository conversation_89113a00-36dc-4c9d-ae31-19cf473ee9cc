package com.yu.questicle.core.ui.mvi

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * 标准化的MVI架构模板
 * 
 * 统一所有UI模块的状态管理模式，解决P4 UI Architecture问题
 */

/**
 * 基础UI状态接口
 * 
 * 所有UI状态都应该实现这个接口
 */
interface BaseUiState {
    /**
     * 加载状态
     */
    val isLoading: Boolean
    
    /**
     * 错误状态
     */
    val error: UiError?
    
    /**
     * 是否有错误
     */
    val hasError: Boolean get() = error != null
    
    /**
     * 是否内容准备就绪
     */
    val isContentReady: Boolean get() = !isLoading && !hasError
}

/**
 * 标准化的UI错误状态
 */
data class UiError(
    val id: String = java.util.UUID.randomUUID().toString(),
    val message: String,
    val type: UiErrorType = UiErrorType.GENERAL,
    val canRetry: Boolean = true,
    val retryAction: (() -> Unit)? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * UI错误类型
 */
enum class UiErrorType {
    NETWORK,        // 网络错误
    DATA_LOADING,   // 数据加载错误
    AUTHENTICATION, // 认证错误
    PERMISSION,     // 权限错误
    VALIDATION,     // 验证错误
    BUSINESS,       // 业务逻辑错误
    GENERAL         // 通用错误
}

/**
 * 基础UI事件接口
 * 
 * 所有UI事件都应该实现这个接口
 */
interface BaseUiEvent {
    /**
     * 事件的唯一标识
     */
    val eventId: String get() = java.util.UUID.randomUUID().toString()
    
    /**
     * 事件时间戳
     */
    val timestamp: Long get() = System.currentTimeMillis()
}

/**
 * 基础UI副作用接口
 * 
 * 所有UI副作用都应该实现这个接口
 */
interface BaseSideEffect {
    /**
     * 副作用的唯一标识
     */
    val effectId: String get() = java.util.UUID.randomUUID().toString()
    
    /**
     * 副作用时间戳
     */
    val timestamp: Long get() = System.currentTimeMillis()
}

/**
 * 标准化的加载状态
 */
data class LoadingState(
    val isLoading: Boolean = false,
    val loadingMessage: String? = null,
    val progress: Float? = null // 0.0 to 1.0, null for indeterminate
) {
    val isIndeterminate: Boolean get() = progress == null
    val isComplete: Boolean get() = progress == 1.0f
}

/**
 * 标准化的刷新状态
 */
data class RefreshState(
    val isRefreshing: Boolean = false,
    val lastRefreshTime: Long? = null,
    val canRefresh: Boolean = true,
    val refreshError: UiError? = null
) {
    val hasRefreshError: Boolean get() = refreshError != null
}

/**
 * 标准化的分页状态
 */
data class PaginationState(
    val currentPage: Int = 0,
    val totalPages: Int = 0,
    val itemsPerPage: Int = 20,
    val totalItems: Int = 0,
    val hasNextPage: Boolean = false,
    val hasPreviousPage: Boolean = false,
    val isLoadingMore: Boolean = false
) {
    val isEmpty: Boolean get() = totalItems == 0
    val isFirstPage: Boolean get() = currentPage == 0
    val isLastPage: Boolean get() = currentPage >= totalPages - 1
}

/**
 * 标准化的网络状态
 */
enum class NetworkState {
    CONNECTED,
    DISCONNECTED,
    SLOW,
    UNKNOWN
}

/**
 * MVI Store接口
 * 
 * 定义标准的状态管理接口
 */
interface MviStore<State : BaseUiState, Event : BaseUiEvent, Effect : BaseSideEffect> {
    /**
     * 当前UI状态
     */
    val state: StateFlow<State>
    
    /**
     * 副作用流
     */
    val sideEffects: Flow<Effect>
    
    /**
     * 处理UI事件
     */
    fun handleEvent(event: Event)
    
    /**
     * 触发副作用
     */
    suspend fun triggerSideEffect(effect: Effect)
}

/**
 * 标准化的数据状态
 */
sealed interface DataState<out T> {
    /**
     * 空闲状态
     */
    data object Idle : DataState<Nothing>
    
    /**
     * 加载状态
     */
    data class Loading(val message: String? = null) : DataState<Nothing>
    
    /**
     * 成功状态
     */
    data class Success<T>(val data: T) : DataState<T>
    
    /**
     * 错误状态
     */
    data class Error(val error: UiError) : DataState<Nothing>
    
    /**
     * 检查是否为成功状态
     */
    val isSuccess: Boolean get() = this is Success
    
    /**
     * 检查是否为加载状态
     */
    val isLoading: Boolean get() = this is Loading
    
    /**
     * 检查是否为错误状态
     */
    val isError: Boolean get() = this is Error
    
    /**
     * 获取数据（如果是成功状态）
     */
    fun getDataOrNull(): T? = if (this is Success) data else null
    
    /**
     * 获取错误（如果是错误状态）
     */
    fun getErrorOrNull(): UiError? = if (this is Error) error else null
}

/**
 * 标准化的操作状态
 */
data class OperationState(
    val isInProgress: Boolean = false,
    val operationType: String? = null,
    val progress: Float? = null,
    val error: UiError? = null
) {
    val hasError: Boolean get() = error != null
    val isComplete: Boolean get() = !isInProgress && !hasError
}

/**
 * 通用的UI事件
 */
sealed interface CommonUiEvent : BaseUiEvent {
    /**
     * 初始化事件
     */
    data object Initialize : CommonUiEvent
    
    /**
     * 刷新事件
     */
    data object Refresh : CommonUiEvent
    
    /**
     * 重试事件
     */
    data object Retry : CommonUiEvent
    
    /**
     * 清除错误事件
     */
    data class ClearError(val errorId: String) : CommonUiEvent
    
    /**
     * 返回事件
     */
    data object NavigateBack : CommonUiEvent
}

/**
 * 通用的UI副作用
 */
sealed interface CommonSideEffect : BaseSideEffect {
    /**
     * 显示Toast
     */
    data class ShowToast(val message: String) : CommonSideEffect
    
    /**
     * 显示Snackbar
     */
    data class ShowSnackbar(
        val message: String,
        val actionLabel: String? = null,
        val duration: SnackbarDuration = SnackbarDuration.Short
    ) : CommonSideEffect
    
    /**
     * 导航副作用
     */
    data class Navigate(val route: String) : CommonSideEffect
    
    /**
     * 返回导航
     */
    data object NavigateBack : CommonSideEffect
    
    /**
     * 触觉反馈
     */
    data object HapticFeedback : CommonSideEffect
    
    /**
     * 播放声音
     */
    data class PlaySound(val soundType: SoundType) : CommonSideEffect
}

/**
 * Snackbar持续时间
 */
enum class SnackbarDuration {
    Short,
    Long,
    Indefinite
}

/**
 * 声音类型
 */
enum class SoundType {
    CLICK,
    SUCCESS,
    ERROR,
    WARNING,
    ACHIEVEMENT,
    NOTIFICATION
}

/**
 * 标准化的特性状态
 */
data class FeatureState(
    val enabledFeatures: Set<String> = emptySet(),
    val permissions: Set<String> = emptySet(),
    val experiments: Map<String, Boolean> = emptyMap()
) {
    /**
     * 检查特性是否启用
     */
    fun hasFeature(feature: String): Boolean = enabledFeatures.contains(feature)
    
    /**
     * 检查权限是否已授予
     */
    fun hasPermission(permission: String): Boolean = permissions.contains(permission)
    
    /**
     * 检查实验特性状态
     */
    fun isExperimentEnabled(experiment: String): Boolean = experiments[experiment] == true
}

/**
 * 标准化的用户交互状态
 */
data class InteractionState(
    val isInteractionEnabled: Boolean = true,
    val disabledInteractions: Set<String> = emptySet(),
    val pendingOperations: Set<String> = emptySet()
) {
    /**
     * 检查是否可以执行特定交互
     */
    fun canPerformInteraction(interaction: String): Boolean {
        return isInteractionEnabled && !disabledInteractions.contains(interaction)
    }
    
    /**
     * 检查是否有待处理的操作
     */
    fun hasPendingOperation(operation: String): Boolean {
        return pendingOperations.contains(operation)
    }
} 