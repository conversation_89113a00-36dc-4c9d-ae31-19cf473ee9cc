-- Questicle 崩溃报告系统 Supabase 数据库架构
-- 创建时间: 2025-07-20
-- 版本: 1.0

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建崩溃报告表
CREATE TABLE IF NOT EXISTS crash_reports (
    -- 主键和基本信息
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 应用信息
    app_version TEXT NOT NULL,
    user_id TEXT,
    session_id TEXT,
    
    -- 异常信息
    exception_type TEXT NOT NULL,
    exception_message TEXT NOT NULL,
    stack_trace TEXT NOT NULL,
    error_code TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    
    -- 上下文信息
    context JSONB DEFAULT '{}',
    thread TEXT NOT NULL,
    is_fatal BOOLEAN NOT NULL DEFAULT false,
    
    -- 设备信息
    device_info JSONB DEFAULT '{}',
    
    -- 处理状态
    status TEXT DEFAULT 'NEW' CHECK (status IN ('NEW', 'INVESTIGATING', 'RESOLVED', 'IGNORED')),
    assigned_to TEXT,
    resolution_notes TEXT,
    resolved_at TIMESTAMPTZ,
    
    -- 统计信息
    occurrence_count INTEGER DEFAULT 1,
    first_seen_at TIMESTAMPTZ DEFAULT NOW(),
    last_seen_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建用户操作日志表
CREATE TABLE IF NOT EXISTS user_action_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crash_report_id UUID REFERENCES crash_reports(id) ON DELETE CASCADE,
    user_id TEXT,
    action TEXT NOT NULL,
    parameters JSONB DEFAULT '{}',
    timestamp BIGINT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建异常模式表
CREATE TABLE IF NOT EXISTS exception_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pattern_name TEXT NOT NULL UNIQUE,
    description TEXT,
    exception_types TEXT[] NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    detection_rules JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建异常统计表
CREATE TABLE IF NOT EXISTS exception_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    exception_type TEXT NOT NULL,
    severity TEXT NOT NULL,
    total_count INTEGER NOT NULL DEFAULT 0,
    fatal_count INTEGER NOT NULL DEFAULT 0,
    unique_users INTEGER NOT NULL DEFAULT 0,
    app_version TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 确保每天每个异常类型只有一条记录
    UNIQUE(date, exception_type, severity, app_version)
);

-- 创建性能监控表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp BIGINT NOT NULL,
    user_id TEXT,
    session_id TEXT,
    metric_type TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_crash_reports_timestamp ON crash_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_crash_reports_user_id ON crash_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_crash_reports_exception_type ON crash_reports(exception_type);
CREATE INDEX IF NOT EXISTS idx_crash_reports_severity ON crash_reports(severity);
CREATE INDEX IF NOT EXISTS idx_crash_reports_is_fatal ON crash_reports(is_fatal);
CREATE INDEX IF NOT EXISTS idx_crash_reports_status ON crash_reports(status);
CREATE INDEX IF NOT EXISTS idx_crash_reports_created_at ON crash_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_crash_reports_app_version ON crash_reports(app_version);

-- 为 JSONB 字段创建 GIN 索引
CREATE INDEX IF NOT EXISTS idx_crash_reports_context_gin ON crash_reports USING GIN(context);
CREATE INDEX IF NOT EXISTS idx_crash_reports_device_info_gin ON crash_reports USING GIN(device_info);

-- 用户操作日志索引
CREATE INDEX IF NOT EXISTS idx_user_action_logs_crash_report_id ON user_action_logs(crash_report_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_timestamp ON user_action_logs(timestamp);

-- 异常统计索引
CREATE INDEX IF NOT EXISTS idx_exception_statistics_date ON exception_statistics(date);
CREATE INDEX IF NOT EXISTS idx_exception_statistics_type ON exception_statistics(exception_type);

-- 性能监控索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type ON performance_metrics(metric_type);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_crash_reports_updated_at 
    BEFORE UPDATE ON crash_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exception_patterns_updated_at 
    BEFORE UPDATE ON exception_patterns 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图以便于查询

-- 崩溃报告摘要视图
CREATE OR REPLACE VIEW crash_reports_summary AS
SELECT 
    DATE(created_at) as date,
    exception_type,
    severity,
    COUNT(*) as total_count,
    COUNT(*) FILTER (WHERE is_fatal = true) as fatal_count,
    COUNT(DISTINCT user_id) as unique_users,
    app_version
FROM crash_reports
GROUP BY DATE(created_at), exception_type, severity, app_version
ORDER BY date DESC, total_count DESC;

-- 最近崩溃趋势视图
CREATE OR REPLACE VIEW recent_crash_trends AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_crashes,
    COUNT(*) FILTER (WHERE is_fatal = true) as fatal_crashes,
    COUNT(DISTINCT user_id) as affected_users,
    COUNT(DISTINCT exception_type) as unique_exception_types
FROM crash_reports
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 热点异常视图
CREATE OR REPLACE VIEW top_exceptions AS
SELECT 
    exception_type,
    COUNT(*) as occurrence_count,
    COUNT(*) FILTER (WHERE is_fatal = true) as fatal_count,
    COUNT(DISTINCT user_id) as affected_users,
    MAX(created_at) as last_occurrence,
    MIN(created_at) as first_occurrence
FROM crash_reports
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY exception_type
ORDER BY occurrence_count DESC
LIMIT 20;

-- 创建行级安全策略 (RLS)
ALTER TABLE crash_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_action_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE exception_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE exception_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

-- 创建策略：允许应用服务插入和查询数据
CREATE POLICY "Allow app service access" ON crash_reports
    FOR ALL USING (true);

CREATE POLICY "Allow app service access" ON user_action_logs
    FOR ALL USING (true);

CREATE POLICY "Allow app service access" ON exception_patterns
    FOR ALL USING (true);

CREATE POLICY "Allow app service access" ON exception_statistics
    FOR ALL USING (true);

CREATE POLICY "Allow app service access" ON performance_metrics
    FOR ALL USING (true);

-- 创建存储过程

-- 记录崩溃报告的存储过程
CREATE OR REPLACE FUNCTION record_crash_report(
    p_timestamp BIGINT,
    p_app_version TEXT,
    p_user_id TEXT,
    p_exception_type TEXT,
    p_exception_message TEXT,
    p_stack_trace TEXT,
    p_error_code TEXT,
    p_severity TEXT,
    p_context JSONB,
    p_thread TEXT,
    p_is_fatal BOOLEAN,
    p_device_info JSONB
) RETURNS UUID AS $$
DECLARE
    crash_id UUID;
    existing_crash_id UUID;
BEGIN
    -- 检查是否存在相似的崩溃报告（基于异常类型、错误代码和堆栈跟踪的哈希）
    SELECT id INTO existing_crash_id
    FROM crash_reports
    WHERE exception_type = p_exception_type
      AND error_code = p_error_code
      AND MD5(stack_trace) = MD5(p_stack_trace)
      AND created_at >= NOW() - INTERVAL '1 hour'
    LIMIT 1;
    
    IF existing_crash_id IS NOT NULL THEN
        -- 更新现有记录的计数和最后出现时间
        UPDATE crash_reports
        SET occurrence_count = occurrence_count + 1,
            last_seen_at = NOW()
        WHERE id = existing_crash_id;
        
        RETURN existing_crash_id;
    ELSE
        -- 插入新的崩溃报告
        INSERT INTO crash_reports (
            timestamp, app_version, user_id, exception_type, exception_message,
            stack_trace, error_code, severity, context, thread, is_fatal, device_info
        ) VALUES (
            p_timestamp, p_app_version, p_user_id, p_exception_type, p_exception_message,
            p_stack_trace, p_error_code, p_severity, p_context, p_thread, p_is_fatal, p_device_info
        ) RETURNING id INTO crash_id;
        
        RETURN crash_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 获取崩溃统计的存储过程
CREATE OR REPLACE FUNCTION get_crash_statistics(
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    p_end_date DATE DEFAULT CURRENT_DATE
) RETURNS TABLE (
    total_crashes BIGINT,
    fatal_crashes BIGINT,
    unique_users BIGINT,
    top_exception_type TEXT,
    crash_free_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT as total_crashes,
        COUNT(*) FILTER (WHERE is_fatal = true)::BIGINT as fatal_crashes,
        COUNT(DISTINCT user_id)::BIGINT as unique_users,
        (SELECT exception_type FROM crash_reports 
         WHERE DATE(created_at) BETWEEN p_start_date AND p_end_date
         GROUP BY exception_type 
         ORDER BY COUNT(*) DESC 
         LIMIT 1) as top_exception_type,
        CASE 
            WHEN COUNT(DISTINCT user_id) > 0 THEN
                (1.0 - COUNT(DISTINCT CASE WHEN is_fatal THEN user_id END)::NUMERIC / COUNT(DISTINCT user_id)::NUMERIC) * 100
            ELSE 100.0
        END as crash_free_rate
    FROM crash_reports
    WHERE DATE(created_at) BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- 创建注释
COMMENT ON TABLE crash_reports IS '崩溃报告主表，存储应用崩溃和异常信息';
COMMENT ON TABLE user_action_logs IS '用户操作日志表，记录崩溃前的用户行为';
COMMENT ON TABLE exception_patterns IS '异常模式表，定义异常检测规则';
COMMENT ON TABLE exception_statistics IS '异常统计表，按日期聚合的异常统计数据';
COMMENT ON TABLE performance_metrics IS '性能监控表，存储应用性能指标';

COMMENT ON COLUMN crash_reports.context IS '崩溃时的上下文信息，JSON格式';
COMMENT ON COLUMN crash_reports.device_info IS '设备信息，JSON格式';
COMMENT ON COLUMN crash_reports.occurrence_count IS '相同崩溃的出现次数';

-- 插入一些示例异常模式
INSERT INTO exception_patterns (pattern_name, description, exception_types, severity, detection_rules) VALUES
('网络超时模式', '检测网络超时相关的异常', ARRAY['NetworkException'], 'MEDIUM', '{"error_codes": ["NETWORK_TIMEOUT", "CONNECTION_TIMEOUT"]}'),
('内存不足模式', '检测内存不足相关的异常', ARRAY['SystemException'], 'HIGH', '{"error_codes": ["OUT_OF_MEMORY"], "stack_trace_contains": ["OutOfMemoryError"]}'),
('数据库错误模式', '检测数据库相关的异常', ARRAY['DatabaseException'], 'HIGH', '{"error_codes": ["DATABASE_ERROR", "SQL_ERROR"]}'),
('权限拒绝模式', '检测权限相关的异常', ARRAY['PermissionException'], 'MEDIUM', '{"error_codes": ["PERMISSION_DENIED", "ACCESS_DENIED"]}')
ON CONFLICT (pattern_name) DO NOTHING;
