# ==============================================================================
# Questicle Project: High-Performance Gradle Configuration (2025)
#
# Optimized for modern Android development on multi-core machines (16GB+ RAM).
# This file is the single source of truth for Gradle build-wide properties.
# Documentation: https://docs.gradle.org/current/userguide/build_environment.html
# ==============================================================================

# ------------------------------------------------------------------------------
# 1. CORE PERFORMANCE & CACHING
# These settings provide the most significant build speed improvements.
# ------------------------------------------------------------------------------

# Enable the configuration cache to avoid re-computing the task graph on every build.
# This is one of the most impactful performance settings in modern Gradle.
org.gradle.configuration-cache=true

# Treat configuration cache problems as warnings instead of build failures.
# Recommended during development to avoid blocking builds for new/unsupported plugins.
org.gradle.configuration-cache.problems=warn

# Enable the local build cache. Reuses outputs from previous builds for unchanged tasks.
org.gradle.caching=true

# Enable modern, efficient file-system watching. Reduces I/O overhead for faster builds.
org.gradle.vfs.watch=true

# ------------------------------------------------------------------------------
# 2. PARALLEL EXECUTION
# Configure Gradle to use multiple threads to execute tasks in parallel.
# ------------------------------------------------------------------------------

# Allow Gradle to execute tasks in parallel on projects that support it.
org.gradle.parallel=true

# (Optional) Explicitly set the number of concurrent workers.
# By default, Gradle uses the number of CPU cores. You can override it here if needed.
# org.gradle.workers.max=8

# ------------------------------------------------------------------------------
# 3. JVM & DAEMON CONFIGURATION
# Fine-tune the Java Virtual Machine that runs Gradle and its daemons.
# ------------------------------------------------------------------------------

# Enable the Gradle Daemon to keep the build environment warm between builds.
org.gradle.daemon=true

# Configure JVM arguments for the main Gradle Daemon.
# -Xmx8g:  Allocate 8 GB of heap space. Adjust based on your system RAM.
# -XX:+UseG1GC: Use the modern G1 Garbage Collector for better performance.
# -XX:MaxMetaspaceSize=1g: Prevent metaspace memory issues.
# -Dfile.encoding=UTF-8: Ensure consistent file encoding.
# Specifies the JVM arguments for the Gradle daemon. Fixed for Metaspace issues.
org.gradle.jvmargs=-Xmx8g -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:MaxMetaspaceSize=2g -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=100

# Set the daemon idle timeout to 1 hour (3,600,000 milliseconds).
# After this period of inactivity, the daemon will shut down to free up resources.
org.gradle.daemon.idletimeout=3600000

# ------------------------------------------------------------------------------
# 4. ANDROID-SPECIFIC OPTIMIZATIONS
# Settings tailored for the Android Gradle Plugin (AGP).
# ------------------------------------------------------------------------------

# Use AndroidX libraries, which is standard for all modern projects.
android.useAndroidX=true

# Disable Jetifier. It's a tool for migrating old support libraries to AndroidX.
# Since this is a modern project, it's not needed and disabling it improves performance.
android.enableJetifier=false

# Enable non-transitive R classes. This is a critical optimization for multi-module
# projects. It prevents R classes from leaking into the public API of sub-modules,
# reducing recompilation scope.
android.nonTransitiveRClass=true

# Disable build features that are not used in this project to save configuration time.
# Set to 'false' if you are certain the project does not use these features.
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.shaders=false
# android.defaults.buildfeatures.buildconfig=false # Set to false if you don't use BuildConfig.

# Disable running lint on release builds to speed up packaging during development.
# Re-enable for production builds.
android.lint.checkReleaseBuilds=false

# Disable unnecessary build features for faster builds
android.lint.checkDependencies=false

# ------------------------------------------------------------------------------
# 5. KOTLIN & KAPT COMPILER OPTIMIZATIONS
# ------------------------------------------------------------------------------

# Enable incremental compilation for Kotlin.
kotlin.incremental=true

# Run the Kotlin compiler in a separate daemon process for better performance.
kotlin.compiler.execution.strategy=daemon

# Allocate 4GB of memory to the Kotlin compiler daemon.
kotlin.daemon.jvmargs=-Xmx4g -XX:+UseG1GC

# ------------------------------------------------------------------------------
# 6. MODERN GRADLE 8.14.3 OPTIMIZATIONS (2025)
# ------------------------------------------------------------------------------

# Enable Kotlin incremental compilation for all modules
kotlin.incremental.multiplatform=true
kotlin.incremental.js=true
kotlin.incremental.useClasspathSnapshot=true

# Enable Kotlin build reports for performance analysis
kotlin.build.report.output=file
kotlin.build.report.file.output_dir=build/reports/kotlin-build

# Optimize test execution with dynamic worker allocation
org.gradle.workers.max=8

# Enable file system watching for faster incremental builds
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# Enable configuration cache for faster configuration phase
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn
org.gradle.configuration-cache.max-problems=1000

# Enable build cache for faster builds
org.gradle.caching=true
org.gradle.caching.debug=false

# Enable parallel project execution
org.gradle.parallel=true

# Enable Gradle daemon for faster builds
org.gradle.daemon=true

# ------------------------------------------------------------------------------
# 7. KOTLIN COMPILER OPTIMIZATIONS (2025)
# ------------------------------------------------------------------------------

# Use the latest Kotlin compiler optimizations
kotlin.code.style=official
kotlin.mpp.stability.nowarn=true

# Enable Kotlin compiler caching
kotlin.compiler.execution.strategy=daemon
kotlin.daemon.jvmargs=-Xmx6g -XX:+UseG1GC -XX:+UseStringDeduplication

# Enable experimental Kotlin features for better performance
kotlin.experimental.tryK2=false

# ------------------------------------------------------------------------------
# 8. ANDROID BUILD OPTIMIZATIONS (2025)
# ------------------------------------------------------------------------------

# Enable R8 full mode for better optimization (use with caution)
android.enableR8.fullMode=false

# Enable resource optimizations
android.enableResourceOptimizations=true

# Android build cache is now handled by Gradle build cache
# android.enableBuildCache=true  # Deprecated in AGP 7.0+

# Enable incremental annotation processing
kapt.incremental.apt=true
kapt.use.worker.api=true
kapt.include.compile.classpath=false

# Enable KSP optimizations
ksp.incremental=true
ksp.incremental.intermodule=true

# ------------------------------------------------------------------------------
# 9. DEVELOPMENT OPTIMIZATIONS
# ------------------------------------------------------------------------------

# Disable unnecessary checks during development
android.enableJetifier=false
android.useAndroidX=true

# Enable non-transitive R classes for faster builds
android.nonTransitiveRClass=true
android.nonFinalResIds=true

# Disable unused build features
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.shaders=false

# ------------------------------------------------------------------------------
# 10. PERFORMANCE MONITORING
# ------------------------------------------------------------------------------

# Enable build scan for performance analysis (uncomment when needed)
# gradle.enterprise.url=https://ge.gradle.org
# gradle.enterprise.allowUntrustedServer=true

# Enable Gradle profiling
# org.gradle.profile=true