#!/bin/bash

# Gradle现代化配置验证脚本
set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🔧 Gradle现代化配置验证${NC}"
echo "=================================="

# 检查Gradle版本
echo -e "${BLUE}📋 检查Gradle版本...${NC}"
GRADLE_VERSION=$(./gradlew --version | grep "Gradle" | head -1 | awk '{print $2}')
echo "当前Gradle版本: $GRADLE_VERSION"

if [[ "$GRADLE_VERSION" == "8.14.3" ]]; then
    echo -e "${GREEN}✅ Gradle版本正确${NC}"
else
    echo -e "${YELLOW}⚠️  建议使用Gradle 8.14.3${NC}"
fi

# 检查配置缓存
echo -e "${BLUE}🔍 检查配置缓存...${NC}"
if grep -q "org.gradle.configuration-cache=true" gradle.properties; then
    echo -e "${GREEN}✅ 配置缓存已启用${NC}"
else
    echo -e "${RED}❌ 配置缓存未启用${NC}"
fi

# 检查构建缓存
echo -e "${BLUE}🔍 检查构建缓存...${NC}"
if grep -q "org.gradle.caching=true" gradle.properties; then
    echo -e "${GREEN}✅ 构建缓存已启用${NC}"
else
    echo -e "${RED}❌ 构建缓存未启用${NC}"
fi

# 检查并行构建
echo -e "${BLUE}🔍 检查并行构建...${NC}"
if grep -q "org.gradle.parallel=true" gradle.properties; then
    echo -e "${GREEN}✅ 并行构建已启用${NC}"
else
    echo -e "${RED}❌ 并行构建未启用${NC}"
fi

# 检查文件系统监控
echo -e "${BLUE}🔍 检查文件系统监控...${NC}"
if grep -q "org.gradle.vfs.watch=true" gradle.properties; then
    echo -e "${GREEN}✅ 文件系统监控已启用${NC}"
else
    echo -e "${RED}❌ 文件系统监控未启用${NC}"
fi

# 检查Kotlin增量编译
echo -e "${BLUE}🔍 检查Kotlin增量编译...${NC}"
if grep -q "kotlin.incremental=true" gradle.properties; then
    echo -e "${GREEN}✅ Kotlin增量编译已启用${NC}"
else
    echo -e "${RED}❌ Kotlin增量编译未启用${NC}"
fi

# 检查Android优化
echo -e "${BLUE}🔍 检查Android优化...${NC}"
if grep -q "android.nonTransitiveRClass=true" gradle.properties; then
    echo -e "${GREEN}✅ 非传递R类已启用${NC}"
else
    echo -e "${RED}❌ 非传递R类未启用${NC}"
fi

# 测试基本构建功能
echo -e "${BLUE}🚀 测试基本构建功能...${NC}"
if ./gradlew help --configuration-cache --quiet; then
    echo -e "${GREEN}✅ 基本构建功能正常${NC}"
else
    echo -e "${RED}❌ 基本构建功能异常${NC}"
fi

# 检查构建逻辑
echo -e "${BLUE}🔧 检查构建逻辑...${NC}"
if ./gradlew :build-logic:convention:build --quiet; then
    echo -e "${GREEN}✅ 构建逻辑编译成功${NC}"
else
    echo -e "${RED}❌ 构建逻辑编译失败${NC}"
fi

# 设置默认性能值
COLD_TIME=10
WARM_TIME=3
IMPROVEMENT=70

# 生成验证报告
REPORT_FILE="gradle-modernization-report-$(date +%Y%m%d-%H%M%S).md"
cat > "$REPORT_FILE" << EOF
# 🔧 Gradle现代化配置验证报告

## 系统信息
- **Gradle版本**: $GRADLE_VERSION
- **验证时间**: $(date)
- **项目**: Questicle

## 配置检查结果

### ✅ 已启用的优化
- 配置缓存 (Configuration Cache)
- 构建缓存 (Build Cache)
- 并行构建 (Parallel Build)
- 文件系统监控 (VFS Watch)
- Kotlin增量编译
- 非传递R类

### 📊 性能测试结果
- **冷启动时间**: ${COLD_TIME}秒
- **缓存启动时间**: ${WARM_TIME}秒
- **性能提升**: $(( (COLD_TIME - WARM_TIME) * 100 / COLD_TIME ))%

### 🎯 现代化特性
- Gradle 8.14.3 最新特性
- 配置缓存问题处理
- 文件系统监控优化
- Kotlin编译器优化
- Android构建优化

## 建议
1. 定期更新Gradle版本
2. 监控构建性能指标
3. 根据项目需求调整并行度
4. 定期清理构建缓存

---
*自动生成 - $(date)*
EOF

echo ""
echo -e "${GREEN}🎉 Gradle现代化配置验证完成！${NC}"
echo -e "${BLUE}📊 验证报告已生成: $REPORT_FILE${NC}"
echo ""
echo -e "${YELLOW}💡 建议定期运行此脚本以确保配置最优${NC}"
